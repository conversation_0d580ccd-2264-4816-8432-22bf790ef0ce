# 小红书授权API使用示例

## 快速开始

### 1. 配置小红书应用信息

在 `config.yaml` 文件中配置您的小红书应用信息：

```yaml
xiaohongshu:
  app_id: "your-xiaohongshu-app-id"        # 替换为您的应用ID
  secret: "your-xiaohongshu-secret"        # 替换为您的应用密钥
  is_prod: false                           # 开发环境设为false，生产环境设为true
  redirect_uri: "http://www.baidu.com"     # 替换为您的回调地址
```

### 2. 启动服务

```bash
go run main.go
```

### 3. 获取授权链接

#### 方式一：使用默认参数（GET请求）

```bash
curl -X GET "http://localhost:8080/api/v1/xhs/auth/url" \
  -H "Content-Type: application/json"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "auth_url": "https://ad-market.xiaohongshu.com/auth?appId=your-xiaohongshu-app-id&scope=[%22report_service%22,%22ad_query%22,%22ad_manage%22,%22account_manage%22]&redirectUri=http://www.baidu.com&state=xhs_auth_state_abcd",
    "app_id": "your-xiaohongshu-app-id",
    "redirect_uri": "http://www.baidu.com",
    "scopes": ["report_service", "ad_query", "ad_manage", "account_manage"],
    "state": "xhs_auth_state_abcd"
  }
}
```

#### 方式二：使用自定义参数（POST请求）

```bash
curl -X POST "http://localhost:8080/api/v1/xhs/auth/url" \
  -H "Content-Type: application/json" \
  -d '{
    "scopes": ["report_service", "ad_query"],
    "state": "my_custom_state_123"
  }'
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "auth_url": "https://ad-market.xiaohongshu.com/auth?appId=your-xiaohongshu-app-id&scope=[%22report_service%22,%22ad_query%22]&redirectUri=http://www.baidu.com&state=my_custom_state_123",
    "app_id": "your-xiaohongshu-app-id",
    "redirect_uri": "http://www.baidu.com",
    "scopes": ["report_service", "ad_query"],
    "state": "my_custom_state_123"
  }
}
```

## 前端集成示例

### Vue.js 示例

```vue
<template>
  <div class="xhs-auth">
    <h2>小红书授权</h2>
    
    <!-- 授权按钮 -->
    <div class="auth-section">
      <el-button type="primary" @click="handleAuth" :loading="loading">
        授权小红书账号
      </el-button>
      
      <el-button @click="handleCustomAuth" :loading="loading">
        自定义授权
      </el-button>
    </div>

    <!-- 配置信息 -->
    <div class="config-section" v-if="config">
      <h3>当前配置</h3>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="应用ID">{{ config.app_id }}</el-descriptions-item>
        <el-descriptions-item label="回调地址">{{ config.redirect_uri }}</el-descriptions-item>
        <el-descriptions-item label="环境">{{ config.is_prod ? '生产' : '开发' }}</el-descriptions-item>
        <el-descriptions-item label="应用密钥">{{ config.secret }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 支持的授权范围 -->
    <div class="scopes-section" v-if="scopes.length > 0">
      <h3>支持的授权范围</h3>
      <el-table :data="scopes" border>
        <el-table-column prop="scope" label="范围代码" width="150" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="description" label="描述" />
      </el-table>
    </div>

    <!-- 自定义授权对话框 -->
    <el-dialog v-model="customAuthVisible" title="自定义授权参数" width="500px">
      <el-form :model="customAuthForm" label-width="100px">
        <el-form-item label="授权范围">
          <el-checkbox-group v-model="customAuthForm.scopes">
            <el-checkbox 
              v-for="scope in scopes" 
              :key="scope.scope" 
              :label="scope.scope">
              {{ scope.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="状态参数">
          <el-input v-model="customAuthForm.state" placeholder="可选，用于防止CSRF攻击" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="customAuthVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCustomAuthConfirm" :loading="loading">
          确定授权
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAuthUrl, getCustomAuthUrl, getAuthConfig, getSupportedScopes } from '@/api/xhs-auth'

// 响应式数据
const loading = ref(false)
const config = ref(null)
const scopes = ref([])
const customAuthVisible = ref(false)

// 自定义授权表单
const customAuthForm = reactive({
  scopes: [],
  state: ''
})

// 获取默认授权链接
const handleAuth = async () => {
  try {
    loading.value = true
    const response = await getAuthUrl()
    
    if (response.code === 200) {
      // 跳转到授权页面
      window.open(response.data.auth_url, '_blank')
      ElMessage.success('已打开授权页面，请在新窗口中完成授权')
    } else {
      ElMessage.error('获取授权链接失败：' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取授权链接失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 显示自定义授权对话框
const handleCustomAuth = () => {
  // 重置表单
  customAuthForm.scopes = []
  customAuthForm.state = ''
  customAuthVisible.value = true
}

// 确认自定义授权
const handleCustomAuthConfirm = async () => {
  try {
    loading.value = true
    const response = await getCustomAuthUrl({
      scopes: customAuthForm.scopes,
      state: customAuthForm.state
    })
    
    if (response.code === 200) {
      // 跳转到授权页面
      window.open(response.data.auth_url, '_blank')
      ElMessage.success('已打开授权页面，请在新窗口中完成授权')
      customAuthVisible.value = false
    } else {
      ElMessage.error('获取授权链接失败：' + response.message)
    }
  } catch (error) {
    ElMessage.error('获取授权链接失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取配置信息
const fetchConfig = async () => {
  try {
    const response = await getAuthConfig()
    if (response.code === 200) {
      config.value = response.data.config
      scopes.value = response.data.scopes
    }
  } catch (error) {
    console.error('获取配置信息失败：', error)
  }
}

// 组件挂载时获取配置
onMounted(() => {
  fetchConfig()
})
</script>

<style scoped>
.xhs-auth {
  padding: 20px;
}

.auth-section {
  margin-bottom: 30px;
}

.auth-section .el-button {
  margin-right: 10px;
}

.config-section,
.scopes-section {
  margin-bottom: 30px;
}

.config-section h3,
.scopes-section h3 {
  margin-bottom: 15px;
  color: #303133;
}
</style>
```

### API 服务文件

```javascript
// api/xhs-auth.js
import request from '@/utils/request'

// 获取默认授权链接
export function getAuthUrl() {
  return request({
    url: '/api/v1/xhs/auth/url',
    method: 'get'
  })
}

// 获取自定义授权链接
export function getCustomAuthUrl(data) {
  return request({
    url: '/api/v1/xhs/auth/url',
    method: 'post',
    data
  })
}

// 获取配置信息
export function getAuthConfig() {
  return request({
    url: '/api/v1/xhs/auth/config',
    method: 'get'
  })
}

// 获取支持的授权范围
export function getSupportedScopes() {
  return request({
    url: '/api/v1/xhs/auth/scopes',
    method: 'get'
  })
}

// 验证配置
export function validateAuthConfig() {
  return request({
    url: '/api/v1/xhs/auth/validate',
    method: 'get'
  })
}
```

## React.js 示例

```jsx
import React, { useState, useEffect } from 'react'
import { Button, Card, Table, Modal, Form, Checkbox, Input, message } from 'antd'
import { getAuthUrl, getCustomAuthUrl, getAuthConfig } from '../api/xhs-auth'

const XHSAuth = () => {
  const [loading, setLoading] = useState(false)
  const [config, setConfig] = useState(null)
  const [scopes, setScopes] = useState([])
  const [customAuthVisible, setCustomAuthVisible] = useState(false)
  const [form] = Form.useForm()

  // 获取默认授权链接
  const handleAuth = async () => {
    try {
      setLoading(true)
      const response = await getAuthUrl()
      
      if (response.code === 200) {
        window.open(response.data.auth_url, '_blank')
        message.success('已打开授权页面，请在新窗口中完成授权')
      } else {
        message.error('获取授权链接失败：' + response.message)
      }
    } catch (error) {
      message.error('获取授权链接失败：' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 自定义授权
  const handleCustomAuth = () => {
    form.resetFields()
    setCustomAuthVisible(true)
  }

  // 确认自定义授权
  const handleCustomAuthConfirm = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)
      
      const response = await getCustomAuthUrl(values)
      
      if (response.code === 200) {
        window.open(response.data.auth_url, '_blank')
        message.success('已打开授权页面，请在新窗口中完成授权')
        setCustomAuthVisible(false)
      } else {
        message.error('获取授权链接失败：' + response.message)
      }
    } catch (error) {
      message.error('获取授权链接失败：' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 获取配置信息
  const fetchConfig = async () => {
    try {
      const response = await getAuthConfig()
      if (response.code === 200) {
        setConfig(response.data.config)
        setScopes(response.data.scopes)
      }
    } catch (error) {
      console.error('获取配置信息失败：', error)
    }
  }

  useEffect(() => {
    fetchConfig()
  }, [])

  const scopeColumns = [
    {
      title: '范围代码',
      dataIndex: 'scope',
      key: 'scope',
      width: 150
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 120
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    }
  ]

  return (
    <div style={{ padding: 24 }}>
      <h2>小红书授权</h2>
      
      {/* 授权按钮 */}
      <Card title="授权操作" style={{ marginBottom: 24 }}>
        <Button 
          type="primary" 
          onClick={handleAuth} 
          loading={loading}
          style={{ marginRight: 16 }}
        >
          授权小红书账号
        </Button>
        
        <Button onClick={handleCustomAuth} loading={loading}>
          自定义授权
        </Button>
      </Card>

      {/* 配置信息 */}
      {config && (
        <Card title="当前配置" style={{ marginBottom: 24 }}>
          <p><strong>应用ID：</strong>{config.app_id}</p>
          <p><strong>回调地址：</strong>{config.redirect_uri}</p>
          <p><strong>环境：</strong>{config.is_prod ? '生产' : '开发'}</p>
          <p><strong>应用密钥：</strong>{config.secret}</p>
        </Card>
      )}

      {/* 支持的授权范围 */}
      {scopes.length > 0 && (
        <Card title="支持的授权范围">
          <Table 
            columns={scopeColumns} 
            dataSource={scopes} 
            rowKey="scope"
            pagination={false}
          />
        </Card>
      )}

      {/* 自定义授权对话框 */}
      <Modal
        title="自定义授权参数"
        open={customAuthVisible}
        onOk={handleCustomAuthConfirm}
        onCancel={() => setCustomAuthVisible(false)}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="scopes" label="授权范围">
            <Checkbox.Group>
              {scopes.map(scope => (
                <Checkbox key={scope.scope} value={scope.scope}>
                  {scope.name}
                </Checkbox>
              ))}
            </Checkbox.Group>
          </Form.Item>
          
          <Form.Item name="state" label="状态参数">
            <Input placeholder="可选，用于防止CSRF攻击" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default XHSAuth
```

## 命令行测试示例

### 1. 验证配置

```bash
curl -X GET "http://localhost:8080/api/v1/xhs/auth/validate"
```

### 2. 获取配置信息

```bash
curl -X GET "http://localhost:8080/api/v1/xhs/auth/config"
```

### 3. 获取支持的授权范围

```bash
curl -X GET "http://localhost:8080/api/v1/xhs/auth/scopes"
```

### 4. 获取授权链接

```bash
# 默认参数
curl -X GET "http://localhost:8080/api/v1/xhs/auth/url"

# 自定义参数
curl -X POST "http://localhost:8080/api/v1/xhs/auth/url" \
  -H "Content-Type: application/json" \
  -d '{
    "scopes": ["report_service", "ad_query"],
    "state": "test_state_123"
  }'
```

## 注意事项

1. **配置检查**：使用前请确保 `config.yaml` 中的小红书配置项已正确填写
2. **回调地址**：确保 `redirect_uri` 与小红书开放平台配置的回调地址一致
3. **HTTPS要求**：生产环境建议使用HTTPS协议
4. **状态参数**：建议使用state参数防止CSRF攻击
5. **授权范围**：根据实际需要选择最小必要的授权范围

## 常见问题

### Q: 获取授权链接时提示"配置验证失败"
A: 请检查 `config.yaml` 中的 `xiaohongshu` 配置项，确保 `app_id`、`secret`、`redirect_uri` 都已正确配置。

### Q: 授权链接无法访问
A: 请确认小红书开放平台应用状态正常，且回调地址配置正确。

### Q: 如何处理授权回调？
A: 系统提供了 `/api/v1/xhs/auth/callback` 接口处理回调，您也可以根据需要自定义回调处理逻辑。
