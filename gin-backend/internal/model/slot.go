package model

import (
	"time"
)

// Slot 资源位模型 - 对应数据库ad_slots表
type Slot struct {
	ID        int64     `gorm:"primarykey;column:id"` // ID
	CreatedAt time.Time `gorm:"column:created_at"`    // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`    // 更新时间
	DeletedAt time.Time `gorm:"column:deleted_at"`    // 删除时间

	Code          string  `gorm:"column:code;uniqueIndex;size:255"`    // 资源位编号
	Type          string  `gorm:"column:type;size:50"`                 // 资源位类型
	Name          string  `gorm:"column:name;size:255"`                // 资源位名称
	UserID        int64   `gorm:"column:user_id;index"`                // 用户ID
	MediaID       int64   `gorm:"column:media_id;index"`               // 媒体ID
	MediaName     string  `gorm:"column:media_name;size:255"`          // 媒体名称
	ScreenshotURL string  `gorm:"column:screenshot_url;size:500"`      // 截图URL
	VideoURL      string  `gorm:"column:video_url;size:500"`           // 视频URL
	AuditStatus   string  `gorm:"column:audit_status;default:pending"` // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	RejectReason  string  `gorm:"column:reject_reason;size:255"`       // 拒绝原因
	LeakRate      float64 `gorm:"column:leak_rate;default:0"`          // 漏损率
	DiscountRate  float64 `gorm:"column:discount_rate;default:0"`      // 折扣率
	Remark        string  `gorm:"column:remark;type:text"`             // 备注
	CreatedBy     int64   `gorm:"column:created_by"`                   // 创建人ID
	UpdatedBy     int64   `gorm:"column:updated_by"`                   // 更新人ID
}

// TableName 指定表名
func (Slot) TableName() string {
	return "ad_slots"
}
