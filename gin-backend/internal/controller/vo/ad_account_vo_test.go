package vo

import (
	"gin-backend/internal/service/domain"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGetAdAccountsReq_ToParam(t *testing.T) {
	req := GetAdAccountsReq{
		Page:                1,
		PageSize:            20,
		AccountName:         "测试账号",
		Platform:            1,
		AuthorizationStatus: 1,
		UsageStatus:         1,
		AccountType:         1,
		ParentId:            0,
	}

	param := req.ToParam()

	assert.Equal(t, req.Page, param.Page)
	assert.Equal(t, req.PageSize, param.PageSize)
	assert.Equal(t, req.AccountName, param.AccountName)
	assert.Equal(t, req.Platform, param.Platform)
	assert.Equal(t, req.AuthorizationStatus, param.AuthorizationStatus)
	assert.Equal(t, req.UsageStatus, param.UsageStatus)
	assert.Equal(t, req.AccountType, param.AccountType)
	assert.Equal(t, req.ParentId, param.ParentId)
}

func TestCreateAdAccountReq_ToParam(t *testing.T) {
	expireTime := time.Now().Add(24 * time.Hour)
	req := CreateAdAccountReq{
		AccountType:       1,
		ParentId:          0,
		Platform:          1,
		AccountName:       "测试主账号",
		PlatformAccountId: "xhs_123456",
		Token:             "test_token",
		TokenExpireTime:   expireTime.Format(time.DateTime),
		AccountBalance:    10000.50,
		Owner:             "张三",
	}

	param := req.ToParam()

	assert.Equal(t, req.AccountType, param.AccountType)
	assert.Equal(t, req.ParentId, param.ParentId)
	assert.Equal(t, req.Platform, param.Platform)
	assert.Equal(t, req.AccountName, param.AccountName)
	assert.Equal(t, req.PlatformAccountId, param.PlatformAccountId)
	assert.Equal(t, req.Token, param.Token)
	assert.Equal(t, expireTime, param.TokenExpireTime)
	assert.Equal(t, req.AccountBalance, param.AccountBalance)
	assert.Equal(t, req.Owner, param.Owner)
}

func TestCreateAdAccountReq_ToParam_NilExpireTime(t *testing.T) {
	req := CreateAdAccountReq{
		AccountType:       1,
		Platform:          1,
		AccountName:       "测试账号",
		PlatformAccountId: "xhs_123456",
		Owner:             "张三",
		TokenExpireTime:   "",
	}

	param := req.ToParam()

	assert.True(t, param.TokenExpireTime.IsZero())
}

func TestUpdateAdAccountReq_ToParam(t *testing.T) {
	expireTime := time.Now().Add(24 * time.Hour)
	req := UpdateAdAccountReq{
		ID:                1,
		AccountName:       "更新后的账号",
		PlatformAccountId: "xhs_updated",
		Token:             "new_token",
		TokenExpireTime:   &expireTime,
		UsageStatus:       1,
		AccountBalance:    15000.00,
		Owner:             "李四",
	}

	param := req.ToParam()

	assert.Equal(t, req.ID, param.ID)
	assert.Equal(t, req.AccountName, param.AccountName)
	assert.Equal(t, req.PlatformAccountId, param.PlatformAccountId)
	assert.Equal(t, req.Token, param.Token)
	assert.Equal(t, expireTime, param.TokenExpireTime)
	assert.Equal(t, req.UsageStatus, param.UsageStatus)
	assert.Equal(t, req.AccountBalance, param.AccountBalance)
	assert.Equal(t, req.Owner, param.Owner)
}

func TestAdAccountItemFromEntity(t *testing.T) {
	now := time.Now()
	entity := domain.AdAccountEntity{
		ID:                      1,
		AccountType:             1,
		AccountTypeName:         "主账号",
		ParentId:                0,
		ParentAccountName:       "",
		Platform:                1,
		PlatformName:            "小红书",
		AccountName:             "测试账号",
		PlatformAccountId:       "xhs_123456",
		AuthorizationStatus:     1,
		AuthorizationStatusName: "已授权",
		Token:                   "test_token",
		TokenExpireTime:         now,
		UsageStatus:             1,
		UsageStatusName:         "启用",
		AccountBalance:          10000.50,
		Owner:                   "张三",
		LastSync:                now,
		CreatedAt:               now,
		UpdatedAt:               now,
	}

	item := AdAccountItemFromEntity(entity)

	assert.Equal(t, entity.ID, item.ID)
	assert.Equal(t, entity.AccountType, item.AccountType)
	assert.Equal(t, entity.AccountTypeName, item.AccountTypeName)
	assert.Equal(t, entity.ParentId, item.ParentId)
	assert.Equal(t, entity.ParentAccountName, item.ParentAccountName)
	assert.Equal(t, entity.Platform, item.Platform)
	assert.Equal(t, entity.PlatformName, item.PlatformName)
	assert.Equal(t, entity.AccountName, item.AccountName)
	assert.Equal(t, entity.PlatformAccountId, item.PlatformAccountId)
	assert.Equal(t, entity.AuthorizationStatus, item.AuthorizationStatus)
	assert.Equal(t, entity.AuthorizationStatusName, item.AuthorizationStatusName)
	assert.Equal(t, entity.Token, item.Token)
	assert.Equal(t, entity.TokenExpireTime, item.TokenExpireTime)
	assert.Equal(t, entity.UsageStatus, item.UsageStatus)
	assert.Equal(t, entity.UsageStatusName, item.UsageStatusName)
	assert.Equal(t, entity.AccountBalance, item.AccountBalance)
	assert.Equal(t, entity.Owner, item.Owner)
	assert.Equal(t, entity.LastSync, item.LastSync)
	assert.Equal(t, entity.CreatedAt, item.CreatedAt)
	assert.Equal(t, entity.UpdatedAt, item.UpdatedAt)
}

func TestGetAdAccountsRespFromResult(t *testing.T) {
	now := time.Now()
	entities := []domain.AdAccountEntity{
		{
			ID:                      1,
			AccountType:             1,
			AccountTypeName:         "主账号",
			Platform:                1,
			PlatformName:            "小红书",
			AccountName:             "测试账号1",
			PlatformAccountId:       "xhs_123456",
			AuthorizationStatus:     1,
			AuthorizationStatusName: "已授权",
			UsageStatus:             1,
			UsageStatusName:         "启用",
			AccountBalance:          10000.50,
			Owner:                   "张三",
			CreatedAt:               now,
			UpdatedAt:               now,
		},
		{
			ID:                      2,
			AccountType:             2,
			AccountTypeName:         "子账号",
			ParentId:                1,
			ParentAccountName:       "测试账号1",
			Platform:                1,
			PlatformName:            "小红书",
			AccountName:             "测试子账号",
			PlatformAccountId:       "xhs_sub_123456",
			AuthorizationStatus:     3,
			AuthorizationStatusName: "继承授权",
			UsageStatus:             1,
			UsageStatusName:         "启用",
			AccountBalance:          5000.00,
			Owner:                   "李四",
			CreatedAt:               now,
			UpdatedAt:               now,
		},
	}

	result := domain.GetAdAccountsResult{
		List:  entities,
		Total: 2,
		Page:  1,
		Size:  20,
	}

	resp := GetAdAccountsRespFromResult(result)

	assert.Equal(t, result.Total, resp.Total)
	assert.Equal(t, result.Page, resp.Page)
	assert.Equal(t, result.Size, resp.Size)
	assert.Len(t, resp.List, 2)

	// 验证第一个账号
	assert.Equal(t, entities[0].ID, resp.List[0].ID)
	assert.Equal(t, entities[0].AccountName, resp.List[0].AccountName)
	assert.Equal(t, entities[0].AccountType, resp.List[0].AccountType)

	// 验证第二个账号
	assert.Equal(t, entities[1].ID, resp.List[1].ID)
	assert.Equal(t, entities[1].AccountName, resp.List[1].AccountName)
	assert.Equal(t, entities[1].ParentId, resp.List[1].ParentId)
}

func TestCreateAdAccountRespFromResult(t *testing.T) {
	result := domain.CreateAdAccountResult{
		ID: 123,
	}

	resp := CreateAdAccountRespFromResult(result)

	assert.Equal(t, result.ID, resp.ID)
}

func TestGetAdAccountOptionsRespFromResult(t *testing.T) {
	result := domain.GetAdAccountOptionsResult{
		Platforms: []domain.PlatformOption{
			{Value: 1, Label: "小红书"},
		},
		AccountTypes: []domain.AccountTypeOption{
			{Value: 1, Label: "主账号"},
			{Value: 2, Label: "子账号"},
		},
		ParentAccounts: []domain.ParentAccountOption{
			{Value: 1, Label: "测试主账号"},
		},
	}

	resp := GetAdAccountOptionsRespFromResult(result)

	assert.Len(t, resp.Platforms, 1)
	assert.Equal(t, result.Platforms[0].Value, resp.Platforms[0].Value)
	assert.Equal(t, result.Platforms[0].Label, resp.Platforms[0].Label)

	assert.Len(t, resp.AccountTypes, 2)
	assert.Equal(t, result.AccountTypes[0].Value, resp.AccountTypes[0].Value)
	assert.Equal(t, result.AccountTypes[0].Label, resp.AccountTypes[0].Label)

	assert.Len(t, resp.ParentAccounts, 1)
	assert.Equal(t, result.ParentAccounts[0].Value, resp.ParentAccounts[0].Value)
	assert.Equal(t, result.ParentAccounts[0].Label, resp.ParentAccounts[0].Label)
}

func TestGetAdAccountDetailRespFromEntity(t *testing.T) {
	now := time.Now()
	mainEntity := domain.AdAccountEntity{
		ID:                      1,
		AccountType:             1,
		AccountTypeName:         "主账号",
		Platform:                1,
		PlatformName:            "小红书",
		AccountName:             "测试主账号",
		PlatformAccountId:       "xhs_123456",
		AuthorizationStatus:     1,
		AuthorizationStatusName: "已授权",
		UsageStatus:             1,
		UsageStatusName:         "启用",
		AccountBalance:          10000.50,
		Owner:                   "张三",
		CreatedAt:               now,
		UpdatedAt:               now,
	}

	subEntities := []domain.AdAccountEntity{
		{
			ID:                      2,
			AccountType:             2,
			AccountTypeName:         "子账号",
			ParentId:                1,
			ParentAccountName:       "测试主账号",
			Platform:                1,
			PlatformName:            "小红书",
			AccountName:             "测试子账号",
			PlatformAccountId:       "xhs_sub_123456",
			AuthorizationStatus:     3,
			AuthorizationStatusName: "继承授权",
			UsageStatus:             1,
			UsageStatusName:         "启用",
			AccountBalance:          5000.00,
			Owner:                   "李四",
			CreatedAt:               now,
			UpdatedAt:               now,
		},
	}

	resp := GetAdAccountDetailRespFromEntity(mainEntity, subEntities)

	// 验证主账号信息
	assert.Equal(t, mainEntity.ID, resp.ID)
	assert.Equal(t, mainEntity.AccountName, resp.AccountName)
	assert.Equal(t, mainEntity.AccountType, resp.AccountType)

	// 验证子账号信息
	assert.Len(t, resp.SubAccounts, 1)
	assert.Equal(t, subEntities[0].ID, resp.SubAccounts[0].ID)
	assert.Equal(t, subEntities[0].AccountName, resp.SubAccounts[0].AccountName)
	assert.Equal(t, subEntities[0].ParentId, resp.SubAccounts[0].ParentId)
}

func TestGetAdAccountDetailRespFromEntity_NoSubAccounts(t *testing.T) {
	now := time.Now()
	entity := domain.AdAccountEntity{
		ID:                      2,
		AccountType:             2,
		AccountTypeName:         "子账号",
		ParentId:                1,
		Platform:                1,
		PlatformName:            "小红书",
		AccountName:             "测试子账号",
		PlatformAccountId:       "xhs_sub_123456",
		AuthorizationStatus:     3,
		AuthorizationStatusName: "继承授权",
		UsageStatus:             1,
		UsageStatusName:         "启用",
		AccountBalance:          5000.00,
		Owner:                   "李四",
		CreatedAt:               now,
		UpdatedAt:               now,
	}

	resp := GetAdAccountDetailRespFromEntity(entity, nil)

	// 验证账号信息
	assert.Equal(t, entity.ID, resp.ID)
	assert.Equal(t, entity.AccountName, resp.AccountName)
	assert.Equal(t, entity.AccountType, resp.AccountType)

	// 验证没有子账号
	assert.Nil(t, resp.SubAccounts)
}

func TestXiaohongshuAuthReq_ToParam(t *testing.T) {
	req := XiaohongshuAuthReq{
		AccountID: 123,
		AuthCode:  "test_auth_code_456",
	}

	param := req.ToParam()

	assert.Equal(t, req.AccountID, param.AccountID)
	assert.Equal(t, req.AuthCode, param.AuthCode)
}

func TestXiaohongshuAuthRespFromResult(t *testing.T) {
	result := domain.XiaohongshuAuthResult{
		AccessToken:           "access_token_123",
		RefreshToken:          "refresh_token_456",
		AccessTokenExpiresIn:  3600,
		RefreshTokenExpiresIn: 7200,
		AdvertiserID:          123456,
		AdvertiserName:        "测试广告主",
	}

	resp := XiaohongshuAuthRespFromResult(result)

	assert.Equal(t, result.AccessToken, resp.AccessToken)
	assert.Equal(t, result.RefreshToken, resp.RefreshToken)
	assert.Equal(t, result.AccessTokenExpiresIn, resp.AccessTokenExpiresIn)
	assert.Equal(t, result.RefreshTokenExpiresIn, resp.RefreshTokenExpiresIn)
	assert.Equal(t, result.AdvertiserID, resp.AdvertiserID)
	assert.Equal(t, result.AdvertiserName, resp.AdvertiserName)
	assert.Equal(t, "小红书账号授权成功", resp.Message)
}

func TestXiaohongshuRefreshTokenReq_ToParam(t *testing.T) {
	req := XiaohongshuRefreshTokenReq{
		AccountID:    789,
	}

	param := req.ToParam()

	assert.Equal(t, req.AccountID, param.AccountID)
}

func TestXiaohongshuRefreshTokenRespFromResult(t *testing.T) {
	result := domain.XiaohongshuRefreshTokenResult{
		AccessToken:           "new_access_token_123",
		RefreshToken:          "new_refresh_token_456",
		AccessTokenExpiresIn:  3600,
		RefreshTokenExpiresIn: 7200,
	}

	resp := XiaohongshuRefreshTokenRespFromResult(result)

	assert.Equal(t, result.AccessToken, resp.AccessToken)
	assert.Equal(t, result.RefreshToken, resp.RefreshToken)
	assert.Equal(t, result.AccessTokenExpiresIn, resp.AccessTokenExpiresIn)
	assert.Equal(t, result.RefreshTokenExpiresIn, resp.RefreshTokenExpiresIn)
	assert.Equal(t, "小红书令牌刷新成功", resp.Message)
}
