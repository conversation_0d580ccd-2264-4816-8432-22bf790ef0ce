package xiaohongshu

import (
	"bytes"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client 小红书广告API客户端
type Client struct {
	config *Config
	client *http.Client
}

// NewClient 创建小红书广告API客户端
func NewClient(config *Config) *Client {
	return &Client{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetAccessToken 获取访问令牌
func (c *Client) GetAccessToken(authCode string) (*TokenResponse, error) {
	request := &AccessTokenRequest{
		AppId:    c.config.AppId,
		Secret:   c.config.Secret,
		AuthCode: authCode,
	}

	return c.requestToken(c.config.GetAccessTokenUrl(), request)
}

// RefreshToken 刷新访问令牌
func (c *Client) RefreshToken(refreshToken string) (*TokenResponse, error) {
	request := &RefreshTokenRequest{
		AppId:        c.config.AppId,
		Secret:       c.config.Secret,
		RefreshToken: refreshToken,
	}

	return c.requestToken(c.config.GetRefreshTokenUrl(), request)
}

// requestToken 请求令牌的通用方法
func (c *Client) requestToken(url string, request interface{}) (*TokenResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 解析响应
	var tokenResponse TokenResponse
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !tokenResponse.Success || tokenResponse.Code != 0 {
		return &tokenResponse, fmt.Errorf("API请求失败: code=%d, msg=%s", tokenResponse.Code, tokenResponse.Msg)
	}

	return &tokenResponse, nil
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.client.Timeout = timeout
}

// Clone 克隆客户端
func (c *Client) Clone() *Client {
	return &Client{
		config: c.config,
		client: &http.Client{
			Timeout: c.client.Timeout,
		},
	}
}

// GetCreativeReport 获取创意层级离线报表数据
func (c *Client) GetCreativeReport(accessToken string, request *ReportRequest) (*ReportResponse, error) {
	return c.requestCreativeReport(c.config.GetCreativeReportUrl(), accessToken, request)
}

// requestCreativeReport 请求创意报表的内部方法（用于测试）
func (c *Client) requestCreativeReport(url, accessToken string, request *ReportRequest) (*ReportResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Access-Token", accessToken)
	req.Header.Set("Trace-ID", generateTraceID())
	req.Header.Set("Service-Tag", "openapi")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 解析响应
	var reportResponse ReportResponse
	if err := json.Unmarshal(body, &reportResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !reportResponse.Success || reportResponse.Code != 0 {
		return &reportResponse, fmt.Errorf("API请求失败: code=%d, msg=%s", reportResponse.Code, reportResponse.Msg)
	}

	return &reportResponse, nil
}

// GetCreativityRealtimeReport 获取创意层级实时报表数据
func (c *Client) GetCreativityRealtimeReport(accessToken string, request *CreativityRealtimeReportRequest) (*CreativityRealtimeReportResponse, error) {
	return c.requestCreativityRealtimeReport(c.config.GetCreativeRealtimeReportUrl(), accessToken, request)
}

// requestCreativityRealtimeReport 请求创意实时报表的内部方法（用于测试）
func (c *Client) requestCreativityRealtimeReport(url, accessToken string, request *CreativityRealtimeReportRequest) (*CreativityRealtimeReportResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Access-Token", accessToken)
	req.Header.Set("Trace-ID", generateTraceID())
	req.Header.Set("Service-Tag", "openapi")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 解析响应
	var realtimeResponse CreativityRealtimeReportResponse
	if err := json.Unmarshal(body, &realtimeResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !realtimeResponse.Success || realtimeResponse.Code != 0 {
		return &realtimeResponse, fmt.Errorf("API请求失败: code=%d, msg=%s", realtimeResponse.Code, realtimeResponse.Msg)
	}

	return &realtimeResponse, nil
}

// GetNoteList 获取笔记列表
func (c *Client) GetNoteList(accessToken string, request *NoteListRequest) (*NoteListResponse, error) {
	return c.requestNoteList(c.config.GetNoteListUrl(), accessToken, request)
}

// requestNoteList 请求笔记列表的内部方法（用于测试）
func (c *Client) requestNoteList(url, accessToken string, request *NoteListRequest) (*NoteListResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Access-Token", accessToken)
	req.Header.Set("Trace-ID", generateTraceID())
	req.Header.Set("Service-Tag", "openapi")

	// 发送请求
	resp, err := c.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 解析响应
	var noteListResponse NoteListResponse
	if err := json.Unmarshal(body, &noteListResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !noteListResponse.Success || noteListResponse.Code != 0 {
		return &noteListResponse, fmt.Errorf("API请求失败: code=%d, msg=%s", noteListResponse.Code, noteListResponse.Msg)
	}

	return &noteListResponse, nil
}

// generateTraceID 生成追踪ID
func generateTraceID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
