package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strings"

	"gorm.io/gorm"
)

// PlatformPlanService 平台计划服务层
type PlatformPlanService struct {
	db *gorm.DB
}

// NewPlatformPlanService 创建平台计划服务实例
func NewPlatformPlanService() *PlatformPlanService {
	return &PlatformPlanService{
		db: global.DB,
	}
}

// List 获取平台计划列表
func (s *PlatformPlanService) List(ctx context.Context, param domain.PlatformPlanListParam) (domain.PlatformPlanListResult, error) {
	// 参数验证
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = param.Size // 兼容size参数
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}
	if param.PageSize > 100 {
		param.PageSize = 100 // 限制最大每页数量
	}

	var total int64
	var items []model.PlatformPlan

	// 使用原生SQL方式查询，避免Preload和JOIN混用的问题
	baseQuery := `
		SELECT 
			p.id,
			p.name,
			p.data_id,
			p.platform_type,
			p.plan_type,
			p.market_target_name,
			p.principal_account,
			p.principal_name,
			p.scene_name,
			p.remark,
			p.created_at,
			p.updated_at,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name
		FROM platform_plans p
		LEFT JOIN ad_agents a ON p.agent_id = a.id  
		LEFT JOIN ad_media m ON p.media_id = m.id
		WHERE 1=1`

	countQuery := `
		SELECT COUNT(1) as total
		FROM platform_plans p
		WHERE 1=1`

	var conditions []string
	var args []interface{}
	var countArgs []interface{}

	// 应用筛选条件
	if param.PlatformType != "" {
		conditions = append(conditions, "p.platform_type = ?")
		args = append(args, param.PlatformType)
		countArgs = append(countArgs, param.PlatformType)
	}
	if param.AgentID > 0 {
		conditions = append(conditions, "p.agent_id = ?")
		args = append(args, param.AgentID)
		countArgs = append(countArgs, param.AgentID)
	}
	if param.MediaID > 0 {
		conditions = append(conditions, "p.media_id = ?")
		args = append(args, param.MediaID)
		countArgs = append(countArgs, param.MediaID)
	}
	if param.Keyword != "" {
		conditions = append(conditions, "(p.name LIKE ? OR p.data_id = ?)")
		keyword := "%" + param.Keyword + "%"
		args = append(args, keyword, param.Keyword)
		countArgs = append(countArgs, keyword, param.Keyword)
	}

	// 添加筛选条件到查询
	if len(conditions) > 0 {
		whereClause := " AND " + strings.Join(conditions, " AND ")
		baseQuery += whereClause
		countQuery += whereClause
	}

	// 获取总数
	var countResult struct {
		Total int64
	}
	if err := s.db.WithContext(ctx).Raw(countQuery, countArgs...).Scan(&countResult).Error; err != nil {
		return domain.PlatformPlanListResult{}, fmt.Errorf("查询平台计划总数失败: %v", err)
	}
	total = countResult.Total

	// 添加排序和分页
	baseQuery += " ORDER BY p.id DESC LIMIT ? OFFSET ?"
	args = append(args, param.PageSize, (param.Page-1)*param.PageSize)

	// 查询数据
	if err := s.db.WithContext(ctx).Raw(baseQuery, args...).Scan(&items).Error; err != nil {
		return domain.PlatformPlanListResult{}, fmt.Errorf("查询平台计划列表失败: %v", err)
	}

	// 转换结果
	result := domain.PlatformPlanListResult{
		Total: total,
		List:  make([]domain.PlatformPlanItem, 0, len(items)),
	}

	// 将model结果转换为domain结果
	for _, item := range items {

		result.List = append(result.List, domain.PlatformPlanItem{
			ID:               item.ID,
			DataID:           item.DataID,
			Name:             item.Name,
			PlatformType:     item.PlatformType,
			PlanType:         item.PlanType,
			MarketTargetName: item.MarketTargetName,
			AgentName:        item.Agent.Name,
			MediaName:        item.Media.Name,
			CreatedAt:        item.CreatedAt,
			UpdatedAt:        item.UpdatedAt,
		})
	}

	return result, nil
}

// GetByID 根据ID获取平台计划
func (s *PlatformPlanService) GetByID(ctx context.Context, id uint64) (domain.PlatformPlanItem, error) {
	if id == 0 {
		return domain.PlatformPlanItem{}, fmt.Errorf("平台计划ID不能为空")
	}

	var plan model.PlatformPlan
	if err := s.db.WithContext(ctx).
		Preload("Agent").
		Preload("Media").
		Where("id = ?", id).
		First(&plan).Error; err != nil {
		return domain.PlatformPlanItem{}, fmt.Errorf("查询平台计划失败: %v", err)
	}

	// 转换为领域实体
	entity := domain.PlatformPlanItem{
		ID:               plan.ID,
		AgentID:          plan.AgentID,
		MediaID:          plan.MediaID,
		PlatformType:     plan.PlatformType,
		Name:             plan.Name,
		PlanType:         plan.PlanType,
		MarketTargetName: plan.MarketTargetName,
		Remark:           plan.Remark,
		DataID:           plan.DataID,
		PrincipalAccount: plan.PrincipalAccount,
		PrincipalName:    plan.PrincipalName,
		SceneName:        plan.SceneName,
		CreatedAt:        plan.CreatedAt,
		UpdatedAt:        plan.UpdatedAt,
	}

	// 设置关联信息
	if plan.Agent != nil {
		entity.AgentName = plan.Agent.Name
	}
	if plan.Media != nil {
		entity.MediaName = plan.Media.Name
	}

	return entity, nil
}

// Create 创建平台计划
func (s *PlatformPlanService) Create(ctx context.Context, param domain.PlatformPlanCreateParam) (uint64, error) {
	// 转换为model
	plan := model.PlatformPlan{
		AgentID:          param.AgentID,
		MediaID:          param.MediaID,
		PlatformType:     param.PlatformType,
		Name:             param.Name,
		PlanType:         param.PlanType,
		MarketTargetName: param.MarketTargetName,
		Remark:           param.Remark,
		DataID:           param.DataID,
		PrincipalAccount: param.PrincipalAccount,
		PrincipalName:    param.PrincipalName,
		SceneName:        param.SceneName,
	}

	// 创建记录
	if err := s.db.WithContext(ctx).Create(&plan).Error; err != nil {
		return 0, fmt.Errorf("创建平台计划失败: %v", err)
	}

	return uint64(plan.ID), nil
}

// Update 更新平台计划
func (s *PlatformPlanService) Update(ctx context.Context, param domain.PlatformPlanUpdateParam) error {
	if param.ID <= 0 {
		return fmt.Errorf("平台计划ID不能为空")
	}

	// 检查是否存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.PlatformPlan{}).Where("id = ?", param.ID).Count(&count).Error; err != nil {
		return fmt.Errorf("查询平台计划是否存在失败: %v", err)
	}
	if count == 0 {
		return fmt.Errorf("平台计划不存在")
	}

	// 构建更新字段
	updates := map[string]interface{}{}
	if param.Name != "" {
		updates["name"] = param.Name
	}
	if param.PlanType != "" {
		updates["plan_type"] = param.PlanType
	}
	if param.MarketTargetName != "" {
		updates["market_target_name"] = param.MarketTargetName
	}
	if param.Remark != "" {
		updates["remark"] = param.Remark
	}
	if param.Status != 0 {
		updates["status"] = param.Status
	}

	// 执行更新
	if err := s.db.WithContext(ctx).Model(&model.PlatformPlan{}).Where("id = ?", param.ID).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新平台计划失败: %v", err)
	}

	return nil
}

// Delete 删除平台计划
func (s *PlatformPlanService) Delete(ctx context.Context, id uint64) error {
	if id == 0 {
		return fmt.Errorf("平台计划ID不能为空")
	}

	// 检查是否存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.PlatformPlan{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return fmt.Errorf("查询平台计划是否存在失败: %v", err)
	}
	if count == 0 {
		return fmt.Errorf("平台计划不存在")
	}

	// 执行删除
	if err := s.db.WithContext(ctx).Delete(&model.PlatformPlan{}, id).Error; err != nil {
		return fmt.Errorf("删除平台计划失败: %v", err)
	}

	return nil
}

// UpdatePlanType 更新计划类型
func (s *PlatformPlanService) UpdatePlanType(ctx context.Context, entity domain.UpdatePlanTypeEntity) error {
	if entity.ID == 0 {
		return fmt.Errorf("平台计划ID不能为空")
	}
	if entity.PlanType == "" {
		return fmt.Errorf("计划类型不能为空")
	}

	// 执行更新
	if err := s.db.WithContext(ctx).Model(&model.PlatformPlan{}).Where("id = ?", entity.ID).Update("plan_type", entity.PlanType).Error; err != nil {
		return fmt.Errorf("更新计划类型失败: %v", err)
	}

	return nil
}

// GetMarketTargets 获取营销目标列表
func (s *PlatformPlanService) GetMarketTargets(ctx context.Context, param domain.MarketTargetsParam) (domain.MarketTargetsResult, error) {
	if param.PlatformType == "" {
		return domain.MarketTargetsResult{}, fmt.Errorf("平台类型不能为空")
	}

	// 根据平台类型返回不同的营销目标
	var targets []domain.MarketTarget
	switch param.PlatformType {
	case "douyin":
		targets = []domain.MarketTarget{
			{Value: "convert", Label: "转化"},
			{Value: "click", Label: "点击"},
			{Value: "view", Label: "观看"},
		}
	case "kuaishou":
		targets = []domain.MarketTarget{
			{Value: "convert", Label: "转化"},
			{Value: "download", Label: "下载"},
		}
	case "bilibili":
		targets = []domain.MarketTarget{
			{Value: "click", Label: "点击"},
			{Value: "view", Label: "观看"},
		}
	default:
		return domain.MarketTargetsResult{}, fmt.Errorf("不支持的平台类型: %s", param.PlatformType)
	}

	return domain.MarketTargetsResult{
		Targets: targets,
	}, nil
}
