package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

type CreativeController struct {
	creativeService *service.CreativeService
}

func NewCreativeController() *CreativeController {
	return &CreativeController{
		creativeService: service.NewCreativeService(),
	}
}

// List 获取创意列表
// @Summary 获取创意列表
// @Description 分页获取创意列表，支持名称搜索
// @Tags 创意管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param name query string false "创意名称搜索"
// @Success 200 {object} response.Response{data=vo.CreativeListResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-creatives/list [get]
func (c *CreativeController) List(ctx *gin.Context) {
	var req vo.CreativeListReq

	if err := ctx.ShouldBind(&req); err != nil {
		response.InvalidParam(ctx, "参数格式错误："+err.Error())
		return
	}

	// 转换为domain层参数
	param := vo.ToCreativeListParam(req)

	// 调用服务层
	result, err := c.creativeService.GetList(param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取创意列表失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	voResponse := vo.FromCreativeListResult(result)

	response.Success(ctx, voResponse)
}

// Detail 获取创意详情
// @Summary 获取创意详情
// @Description 根据ID获取创意详情
// @Tags 创意管理
// @Accept json
// @Produce json
// @Param id query uint64 true "创意ID"
// @Success 200 {object} response.Response{data=vo.CreativeDetailResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-creatives/detail [get]
func (c *CreativeController) Detail(ctx *gin.Context) {
	var req vo.CreativeDetailReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.InvalidParam(ctx, "参数格式错误："+err.Error())
		return
	}

	// 调用服务层
	creative, err := c.creativeService.GetByID(req.ID)
	if err != nil {
		response.Error(ctx, response.CodeNotFound, "获取创意详情失败: "+err.Error())
		return
	}

	// 转换为VO响应
	voCreative := vo.FromCreativeEntity(creative)

	response.Success(ctx, vo.CreativeDetailResp{CreativeItem: voCreative})
}

// Create 创建创意
// @Summary 创建创意
// @Description 创建新的创意
// @Tags 创意管理
// @Accept json
// @Produce json
// @Param request body vo.CreativeCreateReq true "创建请求"
// @Success 200 {object} response.Response{data=map[string]uint64}
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-creatives/create [post]
func (c *CreativeController) Create(ctx *gin.Context) {
	var req vo.CreativeCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := vo.ToCreativeCreateEntity(req)

	// 调用服务层
	id, err := c.creativeService.Create(entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "创建创意失败: "+err.Error())
		return
	}

	response.Success(ctx, map[string]int64{"id": id})
}

// Update 更新创意
// @Summary 更新创意
// @Description 更新创意信息
// @Tags 创意管理
// @Accept json
// @Produce json
// @Param request body vo.CreativeUpdateReq true "更新请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-creatives/update [put]
func (c *CreativeController) Update(ctx *gin.Context) {
	var req vo.CreativeUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := vo.ToCreativeUpdateEntity(req)

	// 调用服务层
	err := c.creativeService.Update(entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "更新创意失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}
