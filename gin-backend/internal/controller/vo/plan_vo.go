package vo

import (
	"gin-backend/internal/service/domain"
	"strconv"
	"time"
)

// PlanRelateItemResp 计划关联关系项
type PlanRelateItemResp struct {
	Type int8  `json:"type" form:"type"` // 关联类型
	ID   int64 `json:"id" form:"id"`     // 关联ID
}

// CreateAdSlotPlanReq 创建广告位计划请求
type CreateAdSlotPlanReq struct {
	Type             string       `json:"type" form:"type" binding:"required"`                     // 计划类型
	AdProductID      int64        `json:"ad_product_id" form:"ad_product_id" binding:"required"`   // 广告产品ID
	AdCreativeID     int64        `json:"ad_creative_id" form:"ad_creative_id" binding:"required"` // 广告创意ID
	AdSlotID         int64        `json:"ad_slot_id" form:"ad_slot_id" binding:"required"`         // 广告位ID
	UserID           int64        `json:"user_id" form:"user_id" binding:"required"`               // 用户ID
	MediaID          int64        `json:"media_id" form:"media_id" binding:"required"`             // 媒体ID
	StartDate        time.Time    `json:"start_date" form:"start_date" binding:"required"`         // 开始日期
	EndDate          time.Time    `json:"end_date" form:"end_date"`                                // 结束日期
	IsEndDateEnabled bool         `json:"is_end_date_enabled" form:"is_end_date_enabled"`          // 是否启用结束日期
	Remark           string       `json:"remark" form:"remark"`                                    // 备注
	MaterialType     int8         `json:"material_type" form:"material_type" binding:"required"`   // 素材类型
	Rels             []PlanRelReq `json:"rels" form:"rels"`                                        // 关联关系
}

// UpdateAdSlotPlanReq 更新广告位计划请求
type UpdateAdSlotPlanReq struct {
	ID           int64        `json:"id" form:"id" binding:"required"`      // 计划ID
	AdCreativeID int64        `json:"ad_creative_id" form:"ad_creative_id"` // 广告创意ID
	Remark       string       `json:"remark" form:"remark"`                 // 备注
	MaterialType int8         `json:"material_type" form:"material_type"`   // 素材类型
	Rels         []PlanRelReq `json:"rels" form:"rels"`                     // 关联关系
}

// PlanRelReq 计划关联请求
type PlanRelReq struct {
	Type int8  `json:"type" form:"type" binding:"required"` // 关联类型
	ID   int64 `json:"id" form:"id" binding:"required"`     // 关联ID
}

// AdSlotPlanResp 广告位计划响应
type AdSlotPlanResp struct {
	ID                 int64            `json:"id"`                   // 计划ID
	Code               string           `json:"code"`                 // 计划编码
	UserID             int64            `json:"user_id"`              // 用户ID
	AdSlotID           int64            `json:"ad_slot_id"`           // 广告位ID
	StartDate          time.Time        `json:"start_date"`           // 开始日期
	EndDate            time.Time        `json:"end_date"`             // 结束日期
	Type               string           `json:"type"`                 // 计划类型
	MaterialType       int              `json:"material_type"`        // 素材类型
	AuditStatus        int              `json:"audit_status"`         // 审核状态
	DeliveryStatus     int              `json:"delivery_status"`      // 投放状态
	User               UserInfoResp     `json:"user"`                 // 用户信息
	Rels               []PlanRelateResp `json:"rels"`                 // 关联关系
	Name               string           `json:"name"`                 // 计划名称
	Budget             float64          `json:"budget"`               // 预算
	DailyBudget        float64          `json:"daily_budget"`         // 每日预算
	Status             int              `json:"status"`               // 状态
	AuditStatusText    string           `json:"audit_status_text"`    // 审核状态文本
	DeliveryStatusText string           `json:"delivery_status_text"` // 投放状态文本
	UserName           string           `json:"user_name"`            // 用户名称
}

// UserInfoResp 用户信息响应
type UserInfoResp struct {
	ID       int64  `json:"id"`        // 用户ID
	RealName string `json:"real_name"` // 真实姓名
}

// PlanRelateResp 计划关联响应
type PlanRelateResp struct {
	ID         int64  `json:"id"`          // 关联ID
	PlanID     int64  `json:"plan_id"`     // 计划ID
	RelateType string `json:"relate_type"` // 关联类型
	RelateID   int64  `json:"relate_id"`   // 关联ID
}

// AdSlotPlanQueryReq 广告位计划查询请求
type AdSlotPlanQueryReq struct {
	Page           int    `json:"page" form:"page"`                       // 页码
	Size           int    `json:"size" form:"size"`                       // 每页数量
	Code           string `json:"code" form:"code"`                       // 计划编码
	MediaID        int64  `json:"media_id" form:"media_id"`               // 媒体ID
	AdSlotID       int64  `json:"ad_slot_id" form:"ad_slot_id"`           // 广告位ID
	AuditStatus    string `json:"audit_status" form:"audit_status"`       // 审核状态
	DeliveryStatus string `json:"delivery_status" form:"delivery_status"` // 投放状态
	Type           string `json:"type" form:"type"`                       // 计划类型
}

// AdSlotPlanListResp 广告位计划列表响应
type AdSlotPlanListResp struct {
	Total int64            `json:"total"` // 总数量
	List  []AdSlotPlanResp `json:"list"`  // 计划列表
}

// PromotionLinkResp 推广链接响应
type PromotionLinkResp struct {
	PromotionLink   string `json:"promotion_link"`   // 推广链接
	PromotionQrcode string `json:"promotion_qrcode"` // 推广二维码
	Link            string `json:"link"`             // 链接
	ShortLink       string `json:"short_link"`       // 短链接
}

// RejectPlanReq 拒绝计划请求
type RejectPlanReq struct {
	Reason string `json:"reason" form:"reason" binding:"required"` // 拒绝原因
}

// UpdateDeliveryModeReq 更新投放策略请求
type UpdateDeliveryModeReq struct {
	ID   int64 `json:"id" form:"id" binding:"required"`     // 计划ID
	Mode int   `json:"mode" form:"mode" binding:"required"` // 投放模式
}

// GeneratePromotionLinkReq 生成推广链接请求
type GeneratePromotionLinkReq struct {
	ID        int64 `json:"id" form:"id" binding:"required"`                 // 计划ID
	ProductID int64 `json:"product_id" form:"product_id" binding:"required"` // 产品ID
}

// UpdatePromotionLinkReq 更新推广链接请求
type UpdatePromotionLinkReq struct {
	ID   int64  `json:"id" form:"id" binding:"required"`     // 计划ID
	Link string `json:"link" form:"link" binding:"required"` // 链接
}

// GenerateShortURLReq 生成短链接请求
type GenerateShortURLReq struct {
	ID int64 `json:"id" form:"id" binding:"required"` // 计划ID
}

// ShortURLResp 短链接响应
type ShortURLResp struct {
	ShortURL string `json:"short_url"` // 短链接
}

// UpdateShortURLReq 更新短链接请求
type UpdateShortURLReq struct {
	ID  int64  `json:"id" form:"id" binding:"required"`   // 对象ID
	URL string `json:"url" form:"url" binding:"required"` // 短链接
}

// UpdateMergeLinksReq 更新融合链接请求
type UpdateMergeLinksReq struct {
	ID         int64          `json:"id" form:"id" binding:"required"` // 对象ID
	MergeLinks map[string]any `json:"merge_links" form:"merge_links"`  // 融合链接
}

// MergeLinksResp 融合链接响应
type MergeLinksResp struct {
	MergeLinks map[string]any `json:"merge_links"` // 融合链接
}

// PlatformObjectItem 平台对象项
type PlatformObjectItem struct {
	ID     int64  `json:"id"`     // 对象ID
	Name   string `json:"name"`   // 对象名称
	Type   string `json:"type"`   // 对象类型
	Status string `json:"status"` // 状态
}

// PlatformObjectDataResp 平台对象数据响应
type PlatformObjectDataResp struct {
	Objects []PlatformObjectItem `json:"objects"` // 对象列表
}

// ========== 转换方法 ==========

// ToAdSlotPlanEntity 将创建广告位计划请求转换为领域实体
func (req *CreateAdSlotPlanReq) ToAdSlotPlanEntity() domain.AdSlotPlanEntity {
	entity := domain.AdSlotPlanEntity{
		AdSlotID:     req.AdSlotID,
		StartDate:    req.StartDate,
		UserID:       req.UserID,
		Type:         req.Type,
		MaterialType: int(req.MaterialType),
	}

	if req.IsEndDateEnabled {
		entity.EndDate = req.EndDate
	}

	// 转换关联关系
	if len(req.Rels) > 0 {
		entity.Rels = make([]domain.PlanRelateEntity, len(req.Rels))
		for i, rel := range req.Rels {
			entity.Rels[i] = domain.PlanRelateEntity{
				RelateType: strconv.FormatInt(int64(rel.Type), 10),
				RelateID:   rel.ID,
			}
		}
	}

	return entity
}

// ToUpdateAdSlotPlanEntity 将更新广告位计划请求转换为领域实体
func (req *UpdateAdSlotPlanReq) ToUpdateAdSlotPlanEntity() domain.AdSlotPlanEntity {
	entity := domain.AdSlotPlanEntity{
		ID:           req.ID,
		AdCreativeID: req.AdCreativeID,
		MaterialType: int(req.MaterialType),
	}

	// 转换关联关系
	if len(req.Rels) > 0 {
		entity.Rels = make([]domain.PlanRelateEntity, len(req.Rels))
		for i, rel := range req.Rels {
			entity.Rels[i] = domain.PlanRelateEntity{
				RelateType: strconv.FormatInt(int64(rel.Type), 10),
				RelateID:   rel.ID,
			}
		}
	}

	return entity
}

// FromAdSlotPlanEntity 将领域实体转换为广告位计划响应
func FromAdSlotPlanEntity(entity domain.AdSlotPlanEntity) AdSlotPlanResp {
	resp := AdSlotPlanResp{
		ID:             entity.ID,
		Code:           entity.Code,
		UserID:         entity.UserID,
		AdSlotID:       entity.AdSlotID,
		StartDate:      entity.StartDate,
		EndDate:        entity.EndDate,
		Type:           entity.Type,
		MaterialType:   entity.MaterialType,
		AuditStatus:    entity.AuditStatus,
		DeliveryStatus: entity.DeliveryStatus,
		Name:           entity.Name,
		Budget:         entity.Budget,
		DailyBudget:    entity.DailyBudget,
		Status:         entity.Status,
	}

	// 设置审核状态文本
	switch entity.AuditStatus {
	case 1:
		resp.AuditStatusText = "待审核"
	case 2:
		resp.AuditStatusText = "已通过"
	case 3:
		resp.AuditStatusText = "已拒绝"
	}

	// 设置投放状态文本
	switch entity.DeliveryStatus {
	case 1:
		resp.DeliveryStatusText = "初始化"
	case 2:
		resp.DeliveryStatusText = "配置中"
	case 3:
		resp.DeliveryStatusText = "等待投放"
	case 4:
		resp.DeliveryStatusText = "投放中"
	case 5:
		resp.DeliveryStatusText = "已停止"
	}

	// 转换用户信息
	if entity.User.ID > 0 {
		resp.User = UserInfoResp{
			ID:       entity.User.ID,
			RealName: entity.User.RealName,
		}
		resp.UserName = entity.User.RealName
	}

	// 转换关联关系
	if len(entity.Rels) > 0 {
		resp.Rels = make([]PlanRelateResp, len(entity.Rels))
		for i, rel := range entity.Rels {
			resp.Rels[i] = PlanRelateResp{
				ID:         rel.ID,
				PlanID:     rel.PlanID,
				RelateType: rel.RelateType,
				RelateID:   rel.RelateID,
			}
		}
	}

	return resp
}

// FromPromotionLinkEntity 将领域实体转换为推广链接响应
func FromPromotionLinkEntity(entity domain.PromotionLinkEntity) PromotionLinkResp {
	return PromotionLinkResp{
		PromotionLink:   entity.PromotionLink,
		PromotionQrcode: entity.PromotionQrcode,
	}
}

// FromMergeLinkEntity 将领域实体转换为融合链接响应
func FromMergeLinkEntity(entity domain.MergeLinkEntity) MergeLinksResp {
	return MergeLinksResp{
		MergeLinks: entity,
	}
}

// ToAdSlotPlanQueryParam 将广告位计划查询请求转换为领域查询参数
func (req *AdSlotPlanQueryReq) ToAdSlotPlanQueryParam() domain.AdSlotPlanQueryParam {
	return domain.AdSlotPlanQueryParam{
		Page:           req.Page,
		Size:           req.Size,
		Code:           req.Code,
		MediaID:        req.MediaID,
		AdSlotID:       req.AdSlotID,
		AuditStatus:    req.AuditStatus,
		DeliveryStatus: req.DeliveryStatus,
		Type:           req.Type,
	}
}
