package service

import (
	"context"
	"errors"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db          *gorm.DB
	roleService *RoleService
}

// NewAuthService 创建认证服务实例
func NewAuthService() *AuthService {
	return &AuthService{
		db:          global.DB,
		roleService: NewRoleService(),
	}
}

// stringsToPermissions 将权限代码字符串数组转换为Permission对象数组
func stringsToPermissions(codes []string) []domain.Permission {
	permissions := make([]domain.Permission, 0, len(codes))
	for _, code := range codes {
		permissions = append(permissions, domain.Permission{
			Code: code,
		})
	}
	return permissions
}

// Login 用户登录
func (s *AuthService) Login(ctx context.Context, loginEntity domain.LoginEntity) (domain.UserProfileEntity, error) {
	// 查询用户
	var user model.User
	if err := s.db.WithContext(ctx).Where("email = ?", loginEntity.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.UserProfileEntity{}, errors.New("用户不存在")
		}
		return domain.UserProfileEntity{}, fmt.Errorf("查询用户失败: %w", err)
	}

	// 检查用户状态
	if user.Status == domain.StatusInactive {
		return domain.UserProfileEntity{}, errors.New("用户已离职，无法登录")
	}

	if user.IsLocked {
		reason := "账号已被锁定"
		return domain.UserProfileEntity{}, errors.New(reason)
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(loginEntity.Password)); err != nil {
		return domain.UserProfileEntity{}, errors.New("密码错误")
	}

	// 生成JWT token
	token, err := s.generateToken(user)
	if err != nil {
		return domain.UserProfileEntity{}, err
	}

	// 更新登录信息
	updates := map[string]any{
		"last_login_at": gorm.Expr("NOW()"),
		"last_login_ip": "127.0.0.1", // 这里简化处理，实际应该获取真实IP
		"updated_at":    gorm.Expr("NOW()"),
	}

	if err := s.db.WithContext(ctx).Model(&model.User{}).
		Where("id = ?", user.ID).
		Updates(updates).Error; err != nil {
		// 记录日志，但不影响登录流程
	}

	// 获取用户权限
	permissionEntity, err := s.roleService.GetUserPermissions(ctx, user.ID)
	var profileEntity domain.UserProfileEntity

	if err != nil {
		// 如果获取权限失败，回退到基于角色的判断
		profileEntity = domain.UserProfileEntity{
			ID:              user.ID,
			Name:            user.Name,
			RealName:        user.RealName,
			Email:           user.Email,
			Phone:           user.Phone,
			Role:            user.Role,
			DefaultWorkMode: user.DefaultWorkMode,
			AvailableModes:  domain.GetAvailableModes(user.Role),
			Status:          user.Status,
			Department:      user.Department,
			Position:        user.Position,
		}
	} else {
		// 使用基于权限的判断
		// 转换权限代码字符串为Permission对象
		permissions := stringsToPermissions(permissionEntity.Permissions)
		profileEntity = domain.UserProfileEntity{
			ID:              user.ID,
			Name:            user.Name,
			RealName:        user.RealName,
			Email:           user.Email,
			Phone:           user.Phone,
			Role:            user.Role,
			DefaultWorkMode: user.DefaultWorkMode,
			AvailableModes:  domain.GetAvailableModesWithPermissions(user.Role, permissions),
			Status:          user.Status,
			Department:      user.Department,
			Position:        user.Position,
		}
	}

	// 设置token到entity中
	profileEntity.Token = token

	return profileEntity, nil
}

// generateToken 生成JWT token
func (s *AuthService) generateToken(user model.User) (string, error) {
	// 创建域模型中的JWT claims实体
	claimsEntity := domain.JWTClaimsEntity{
		UserID:    user.ID,
		Email:     user.Email,
		Role:      user.Role,
		ExpiresAt: time.Now().Add(time.Duration(config.AppConfig.JWT.Expire) * time.Second).Unix(),
		IssuedAt:  time.Now().Unix(),
		NotBefore: time.Now().Unix(),
	}

	// 转换为JWT库需要的claims格式
	claims := jwt.MapClaims{
		"user_id": claimsEntity.UserID,
		"email":   claimsEntity.Email,
		"role":    claimsEntity.Role,
		"exp":     claimsEntity.ExpiresAt,
		"iat":     claimsEntity.IssuedAt,
		"nbf":     claimsEntity.NotBefore,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(config.AppConfig.JWT.Secret))
}

// ParseToken 解析JWT token
func (s *AuthService) ParseToken(tokenString string) (domain.JWTClaimsEntity, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(config.AppConfig.JWT.Secret), nil
	})

	if err != nil {
		return domain.JWTClaimsEntity{}, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		userID, _ := claims["user_id"].(int64)
		email, _ := claims["email"].(string)
		role, _ := claims["role"].(float64)
		exp, _ := claims["exp"].(float64)
		iat, _ := claims["iat"].(float64)
		nbf, _ := claims["nbf"].(float64)

		return domain.JWTClaimsEntity{
			UserID:    userID,
			Email:     email,
			Role:      int(role),
			ExpiresAt: int64(exp),
			IssuedAt:  int64(iat),
			NotBefore: int64(nbf),
		}, nil
	}

	return domain.JWTClaimsEntity{}, errors.New("invalid token")
}

// GetUserInfo 获取用户信息
func (s *AuthService) GetUserInfo(ctx context.Context, userID int64) (domain.UserProfileEntity, error) {
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.UserProfileEntity{}, errors.New("用户不存在")
		}
		return domain.UserProfileEntity{}, fmt.Errorf("查询用户失败: %w", err)
	}

	// 获取用户权限
	permissionEntity, err := s.roleService.GetUserPermissions(ctx, user.ID)

	if err != nil {
		// 如果获取权限失败，回退到基于角色的判断
		profileEntity := domain.UserProfileEntity{
			ID:              user.ID,
			Name:            user.Name,
			RealName:        user.RealName,
			Email:           user.Email,
			Phone:           user.Phone,
			Role:            user.Role,
			DefaultWorkMode: user.DefaultWorkMode,
			AvailableModes:  domain.GetAvailableModes(user.Role),
			Status:          user.Status,
			Department:      user.Department,
			Position:        user.Position,
		}
		return profileEntity, nil
	}

	// 使用基于权限的判断
	// 转换权限代码字符串为Permission对象
	permissions := stringsToPermissions(permissionEntity.Permissions)
	profileEntity := domain.UserProfileEntity{
		ID:              user.ID,
		Name:            user.Name,
		RealName:        user.RealName,
		Email:           user.Email,
		Phone:           user.Phone,
		Role:            user.Role,
		DefaultWorkMode: user.DefaultWorkMode,
		AvailableModes:  domain.GetAvailableModesWithPermissions(user.Role, permissions),
		Status:          user.Status,
		Department:      user.Department,
		Position:        user.Position,
	}
	return profileEntity, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(ctx context.Context, changePasswordEntity domain.ChangePasswordEntity) error {
	// 查询用户
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", changePasswordEntity.UserID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("用户不存在")
		}
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(changePasswordEntity.OldPassword)); err != nil {
		return errors.New("旧密码错误")
	}

	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(changePasswordEntity.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	updates := map[string]any{
		"password":            string(hashedPassword),
		"password_changed_at": gorm.Expr("NOW()"),
	}

	if err := s.db.WithContext(ctx).Model(&model.User{}).
		Where("id = ?", changePasswordEntity.UserID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("更新密码失败: %w", err)
	}

	return nil
}

// GetUsersByRole 根据角色获取用户列表
func (s *AuthService) GetUsersByRole(ctx context.Context, role int) ([]model.User, error) {
	var users []model.User
	if err := s.db.WithContext(ctx).
		Where("role = ? AND status = ? AND is_locked = ?", role, domain.StatusActive, domain.UnLocked).
		Find(&users).Error; err != nil {
		return nil, fmt.Errorf("获取用户列表失败: %w", err)
	}
	return users, nil
}

// LockUser 锁定用户
func (s *AuthService) LockUser(ctx context.Context, userID uint64, reason string) error {
	updates := map[string]any{
		"is_locked":   domain.Locked,
		"lock_reason": reason,
		"locked_at":   gorm.Expr("NOW()"),
		"updated_at":  gorm.Expr("NOW()"),
	}

	if err := s.db.WithContext(ctx).Model(&model.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("锁定用户失败: %w", err)
	}

	return nil
}

// UnlockUser 解锁用户
func (s *AuthService) UnlockUser(ctx context.Context, userID uint64) error {
	updates := map[string]any{
		"is_locked":   domain.UnLocked,
		"lock_reason": "",
		"locked_at":   nil,
		"updated_at":  gorm.Expr("NOW()"),
	}

	if err := s.db.WithContext(ctx).Model(&model.User{}).
		Where("id = ?", userID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("解锁用户失败: %w", err)
	}

	return nil
}
