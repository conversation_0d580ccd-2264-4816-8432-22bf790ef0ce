package controller

import (
	"strconv"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/model"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// PlanController 计划控制器
type PlanController struct {
	planService       *service.PlanService
	permissionService *service.PermissionService
	roleService       *service.RoleService
}

func NewPlanController() *PlanController {
	return &PlanController{
		planService:       service.NewPlanService(),
		permissionService: service.NewPermissionService(),
		roleService:       service.NewRoleService(),
	}
}

// ========== 广告位计划相关接口 ==========

// CreateAdSlotPlan 创建广告位计划
// @Summary 创建广告位计划
// @Description 创建新的广告位计划
// @Tags 广告位计划
// @Accept json
// @Produce json
// @Param request body vo.CreateAdSlotPlanReq true "创建广告位计划请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/plans/ad-slot [post]
func (c *PlanController) CreateAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:create"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.CreateAdSlotPlanReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Type == "" {
		req.Type = "normal"
	}
	if req.MaterialType == 0 {
		req.MaterialType = 1
	}

	// 设置用户ID（从当前登录用户获取）
	req.UserID = userEntity.ID

	// VO转换为Domain实体
	planEntity := req.ToAdSlotPlanEntity()

	// 调用Service层
	plan, err := c.planService.CreateAdSlotPlan(ctx, planEntity, userEntity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "创建计划失败: "+err.Error())
		return
	}

	response.Success(ctx, map[string]interface{}{
		"id":   plan.ID,
		"code": plan.Code,
	})
}

// GetAdSlotPlan 获取广告位计划详情
// @Summary 获取广告位计划详情
// @Description 获取指定ID的广告位计划详情
// @Tags 广告位计划
// @Accept json
// @Produce json
// @Param id path int true "计划ID"
// @Success 200 {object} response.Response{data=vo.AdSlotPlanResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/plans/ad-slot/{id} [get]
func (c *PlanController) GetAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	id := ctx.Param("id")
	if id == "" {
		response.Error(ctx, response.CodeInvalidParam, "计划ID不能为空")
		return
	}

	// 参数转换
	planID, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "计划ID格式不正确")
		return
	}

	// 调用Service层
	planEntity, err := c.planService.GetAdSlotPlan(ctx, planID, userEntity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取计划失败: "+err.Error())
		return
	}

	// Domain实体转换为VO
	resp := vo.FromAdSlotPlanEntity(planEntity)

	response.Success(ctx, resp)
}

// UpdateAdSlotPlan 更新广告位计划
// @Summary 更新广告位计划
// @Description 更新广告位计划信息
// @Tags 广告位计划
// @Accept json
// @Produce json
// @Param id path uint64 true "计划ID"
// @Param request body vo.UpdateAdSlotPlanReq true "更新计划请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/plans/ad-slot/{id} [put]
func (c *PlanController) UpdateAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:update"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.UpdateAdSlotPlanReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 获取路径参数中的ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的计划ID")
		return
	}
	req.ID = id

	// VO转换为Domain实体
	planEntity := req.ToUpdateAdSlotPlanEntity()

	// 调用Service层
	err = c.planService.UpdateAdSlotPlan(ctx, planEntity, userEntity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "更新计划失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// DeleteAdSlotPlan 删除广告位计划
// @Summary 删除广告位计划
// @Description 删除广告位计划
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param id query uint64 true "计划ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/ad-slot-plans/delete [post]
func (c *PlanController) DeleteAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:delete"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "计划ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的计划ID")
		return
	}

	// 调用Service层
	if err := c.planService.DeleteAdSlotPlan(ctx, id, userEntity); err != nil {
		response.Error(ctx, response.CodeServerError, "删除计划失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ListAdSlotPlans 获取广告位计划列表
// @Summary 获取广告位计划列表
// @Description 获取符合条件的广告位计划列表
// @Tags 广告位计划
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1"
// @Param size query int false "每页数量，默认10"
// @Param code query string false "计划编号"
// @Param media_id query int false "媒体ID"
// @Param ad_slot_id query int false "广告位ID"
// @Param audit_status query string false "审核状态"
// @Param delivery_status query string false "投放状态"
// @Param type query string false "计划类型"
// @Success 200 {object} response.Response{data=vo.AdSlotPlanListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/plans/ad-slot [get]
func (c *PlanController) ListAdSlotPlans(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:list"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.AdSlotPlanQueryReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// VO转换为Domain查询参数
	queryParam := req.ToAdSlotPlanQueryParam()

	// 调用Service层
	plans, total, err := c.planService.ListAdSlotPlans(ctx, queryParam, userEntity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取计划列表失败: "+err.Error())
		return
	}

	// Domain实体转换为VO
	planList := make([]vo.AdSlotPlanResp, 0, len(plans))
	for _, plan := range plans {
		planList = append(planList, vo.FromAdSlotPlanEntity(plan))
	}

	response.Success(ctx, vo.AdSlotPlanListResp{
		Total: total,
		List:  planList,
	})
}

// ApproveAdSlotPlan 审核通过广告位计划
// @Summary 审核通过广告位计划
// @Description 审核通过广告位计划
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param id query uint64 true "计划ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/ad-slot-plans/approve [post]
func (c *PlanController) ApproveAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:approve"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "计划ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的计划ID")
		return
	}

	// 调用Service层
	if err := c.planService.ApproveAdSlotPlan(ctx, id, userEntity); err != nil {
		response.Error(ctx, response.CodeServerError, "审核通过计划失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// RejectAdSlotPlan 审核拒绝广告位计划
// @Summary 审核拒绝广告位计划
// @Description 审核拒绝广告位计划，需要提供拒绝原因
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.RejectPlanReq true "审核信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/ad-slot-plans/reject [post]
func (c *PlanController) RejectAdSlotPlan(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "plan:reject"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.RejectPlanReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 从请求中获取计划ID
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "计划ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的计划ID")
		return
	}

	// 调用Service层
	if err := c.planService.RejectAdSlotPlan(ctx, id, req.Reason, userEntity); err != nil {
		response.Error(ctx, response.CodeServerError, "审核拒绝计划失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// UpdateDeliveryMode 更新投放策略
// @Summary 更新投放策略
// @Description 更新投放策略
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.UpdateDeliveryModeReq true "请求参数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-slot-plans/update-delivery-mode [post]
func (c *PlanController) UpdateDeliveryMode(ctx *gin.Context) {
	var req vo.UpdateDeliveryModeReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 调用 service 层更新投放策略
	if err := c.planService.UpdateDeliveryMode(ctx, req.ID, req.Mode); err != nil {
		response.Error(ctx, response.CodeServerError, "更新投放策略失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GeneratePromotionLink 生成推广链接
// @Summary 生成推广链接
// @Description 生成推广链接
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.GeneratePromotionLinkReq true "请求参数"
// @Success 200 {object} response.Response{data=vo.PromotionLinkResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-slot-plans/generate-promotion-link [post]
func (c *PlanController) GeneratePromotionLink(ctx *gin.Context) {
	var req vo.GeneratePromotionLinkReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 调用 service 层生成推广链接
	result, err := c.planService.GeneratePromotionLink(ctx, req.ID, req.ProductID)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "生成推广链接失败: "+err.Error())
		return
	}

	// 转换为 VO 响应
	response.Success(ctx, vo.FromPromotionLinkEntity(result))
}

// UpdatePromotionLink 更新推广链接
// @Summary 更新推广链接
// @Description 更新推广链接
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.UpdatePromotionLinkReq true "请求参数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-slot-plans/update-promotion-link [post]
func (c *PlanController) UpdatePromotionLink(ctx *gin.Context) {
	var req vo.UpdatePromotionLinkReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 调用 service 层更新推广链接
	if err := c.planService.UpdatePromotionLink(ctx, req.ID, req.Link); err != nil {
		response.Error(ctx, response.CodeServerError, "更新推广链接失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GenerateShortURL 生成短链接
// @Summary 生成短链接
// @Description 生成短链接
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.GenerateShortURLReq true "请求参数"
// @Success 200 {object} response.Response{data=vo.ShortURLResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-slot-plans/generate-short-url [post]
func (c *PlanController) GenerateShortURL(ctx *gin.Context) {
	var req vo.GenerateShortURLReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 调用 service 层生成短链接
	shortURL, err := c.planService.GenerateShortURL(ctx, req.ID)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "生成短链接失败: "+err.Error())
		return
	}

	// 转换为 vo 响应
	response.Success(ctx, vo.ShortURLResp{
		ShortURL: shortURL,
	})
}

// UpdateShortURL 更新短链接
// @Summary 更新短链接
// @Description 更新短链接
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.UpdateShortURLReq true "请求参数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/ad-slot-plans/update-short-url [post]
func (c *PlanController) UpdateShortURL(ctx *gin.Context) {
	var req vo.UpdateShortURLReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 调用 service 层更新短链接
	if err := c.planService.UpdateShortURL(ctx, req.ID, req.URL); err != nil {
		response.Error(ctx, response.CodeServerError, "更新短链接失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetMergeLinks 获取融合链接
// @Summary 获取融合链接
// @Description 根据计划编号获取融合链接信息
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param code query string true "计划编号"
// @Success 200 {object} response.Response{data=vo.MergeLinksResp}
// @Failure 400 {object} response.Response
// @Router /api/plans/ad-slot/merge-links [get]
func (c *PlanController) GetMergeLinks(ctx *gin.Context) {
	code := ctx.Query("code")
	if code == "" {
		response.Error(ctx, response.CodeInvalidParam, "计划编号不能为空")
		return
	}

	// 调用 service 层获取融合链接
	mergeLinks, err := c.planService.GetMergeLinks(ctx, code)
	if err != nil {
		response.Error(ctx, response.CodeNotFound, "计划不存在")
		return
	}

	// 转换为 VO 响应
	response.Success(ctx, vo.MergeLinksResp{
		MergeLinks: mergeLinks,
	})
}

// UpdateMergeLinks 更新融合链接
// @Summary 更新融合链接
// @Description 更新计划的融合链接信息
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param request body vo.UpdateMergeLinksReq true "融合链接信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/ad-slot-plans/update-merge-links [post]
func (c *PlanController) UpdateMergeLinks(ctx *gin.Context) {
	var req vo.UpdateMergeLinksReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 将map[string]interface{}转换为model.MergeLinkJSON类型
	mergeLinkJSON := model.MergeLinkJSON(req.MergeLinks)

	// 调用 service 层更新融合链接
	if err := c.planService.UpdateMergeLinks(ctx, req.ID, mergeLinkJSON); err != nil {
		response.Error(ctx, response.CodeServerError, "更新融合链接失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetPlatformObjectData 获取平台对象数据
// @Summary 获取平台对象数据
// @Description 获取平台相关的对象数据，用于前端下拉选择等
// @Tags 投放计划
// @Accept json
// @Produce json
// @Param platform_type query string false "平台类型"
// @Success 200 {object} response.Response{data=vo.PlatformObjectDataResp}
// @Failure 400 {object} response.Response
// @Router /api/ad-slot-plans/platform_object_data [get]
func (c *PlanController) GetPlatformObjectData(ctx *gin.Context) {
	platformType := ctx.Query("platform_type")

	// 构建平台对象数据响应
	var platformObjects []vo.PlatformObjectItem

	// 根据不同平台类型返回不同数据
	// 这些数据应该从数据库或配置服务中获取
	switch platformType {
	case "toutiao":
		platformObjects = []vo.PlatformObjectItem{
			{ID: 1, Name: "今日头条信息流", Type: "feed", Status: "active"},
			{ID: 2, Name: "抖音短视频推广", Type: "video", Status: "active"},
			{ID: 3, Name: "西瓜视频推广", Type: "video", Status: "active"},
		}
	case "tencent":
		platformObjects = []vo.PlatformObjectItem{
			{ID: 1, Name: "微信朋友圈广告", Type: "moment", Status: "active"},
			{ID: 2, Name: "QQ空间广告", Type: "feed", Status: "active"},
			{ID: 3, Name: "腾讯视频广告", Type: "video", Status: "active"},
		}
	case "baidu":
		platformObjects = []vo.PlatformObjectItem{
			{ID: 1, Name: "百度搜索推广", Type: "search", Status: "active"},
			{ID: 2, Name: "百度信息流", Type: "feed", Status: "active"},
			{ID: 3, Name: "百度知道推广", Type: "qa", Status: "active"},
		}
	case "kuaishou":
		platformObjects = []vo.PlatformObjectItem{
			{ID: 1, Name: "快手信息流", Type: "feed", Status: "active"},
			{ID: 2, Name: "快手短视频", Type: "video", Status: "active"},
		}
	case "xiaohongshu":
		platformObjects = []vo.PlatformObjectItem{
			{ID: 1, Name: "小红书信息流", Type: "feed", Status: "active"},
			{ID: 2, Name: "小红书品牌推广", Type: "brand", Status: "active"},
		}
	}

	response.Success(ctx, vo.PlatformObjectDataResp{
		Objects: platformObjects,
	})
}
