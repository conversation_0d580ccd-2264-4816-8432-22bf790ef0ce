package domain

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestGetAccountTypeName(t *testing.T) {
	tests := []struct {
		accountType int8
		expected    string
	}{
		{AccountTypeMaster, "主账号"},
		{AccountTypeSub, "子账号"},
		{99, "未知"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := GetAccountTypeName(tt.accountType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetPlatformName(t *testing.T) {
	tests := []struct {
		platform int64
		expected string
	}{
		{PlatformXiaohongshu, "小红书"},
		{99, "未知平台"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := GetPlatformName(tt.platform)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetAuthorizationStatusName(t *testing.T) {
	tests := []struct {
		status   int8
		expected string
	}{
		{AuthStatusUnauthorized, "未授权"},
		{AuthStatusAuthorized, "已授权"},
		{AuthStatusExpired, "已过期"},
		{AuthStatusInherited, "继承授权"},
		{99, "未知状态"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := GetAuthorizationStatusName(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetUsageStatusName(t *testing.T) {
	tests := []struct {
		status   int8
		expected string
	}{
		{UsageStatusEnabled, "启用"},
		{UsageStatusDisabled, "禁用"},
		{99, "未知状态"},
	}

	for _, tt := range tests {
		t.Run(tt.expected, func(t *testing.T) {
			result := GetUsageStatusName(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdAccountEntity_IsTokenExpired(t *testing.T) {
	tests := []struct {
		name            string
		tokenExpireTime time.Time
		expected        bool
	}{
		{
			name:            "未设置过期时间",
			tokenExpireTime: time.Time{},
			expected:        false,
		},
		{
			name:            "未过期",
			tokenExpireTime: time.Now().Add(24 * time.Hour),
			expected:        false,
		},
		{
			name:            "已过期",
			tokenExpireTime: time.Now().Add(-24 * time.Hour),
			expected:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := AdAccountEntity{
				TokenExpireTime: tt.tokenExpireTime,
			}
			result := entity.IsTokenExpired()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdAccountEntity_CanCreateSubAccount(t *testing.T) {
	tests := []struct {
		name        string
		accountType int8
		usageStatus int8
		expected    bool
	}{
		{
			name:        "主账号且启用",
			accountType: AccountTypeMaster,
			usageStatus: UsageStatusEnabled,
			expected:    true,
		},
		{
			name:        "主账号但禁用",
			accountType: AccountTypeMaster,
			usageStatus: UsageStatusDisabled,
			expected:    false,
		},
		{
			name:        "子账号",
			accountType: AccountTypeSub,
			usageStatus: UsageStatusEnabled,
			expected:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := AdAccountEntity{
				AccountType: tt.accountType,
				UsageStatus: tt.usageStatus,
			}
			result := entity.CanCreateSubAccount()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdAccountEntity_IsActive(t *testing.T) {
	tests := []struct {
		name                string
		usageStatus         int8
		authorizationStatus int8
		expected            bool
	}{
		{
			name:                "启用且已授权",
			usageStatus:         UsageStatusEnabled,
			authorizationStatus: AuthStatusAuthorized,
			expected:            true,
		},
		{
			name:                "启用且继承授权",
			usageStatus:         UsageStatusEnabled,
			authorizationStatus: AuthStatusInherited,
			expected:            true,
		},
		{
			name:                "禁用",
			usageStatus:         UsageStatusDisabled,
			authorizationStatus: AuthStatusAuthorized,
			expected:            false,
		},
		{
			name:                "未授权",
			usageStatus:         UsageStatusEnabled,
			authorizationStatus: AuthStatusUnauthorized,
			expected:            false,
		},
		{
			name:                "已过期",
			usageStatus:         UsageStatusEnabled,
			authorizationStatus: AuthStatusExpired,
			expected:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := AdAccountEntity{
				UsageStatus:         tt.usageStatus,
				AuthorizationStatus: tt.authorizationStatus,
			}
			result := entity.IsActive()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAdAccountEntity_GetEffectiveAuthStatus(t *testing.T) {
	tests := []struct {
		name                string
		authorizationStatus int8
		tokenExpireTime     time.Time
		expected            int8
	}{
		{
			name:                "已授权且未过期",
			authorizationStatus: AuthStatusAuthorized,
			tokenExpireTime:     time.Now().Add(24 * time.Hour),
			expected:            AuthStatusAuthorized,
		},
		{
			name:                "已授权但token过期",
			authorizationStatus: AuthStatusAuthorized,
			tokenExpireTime:     time.Now().Add(-24 * time.Hour),
			expected:            AuthStatusExpired,
		},
		{
			name:                "未授权",
			authorizationStatus: AuthStatusUnauthorized,
			tokenExpireTime:     time.Now().Add(24 * time.Hour),
			expected:            AuthStatusUnauthorized,
		},
		{
			name:                "继承授权",
			authorizationStatus: AuthStatusInherited,
			tokenExpireTime:     time.Now().Add(-24 * time.Hour),
			expected:            AuthStatusInherited,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entity := AdAccountEntity{
				AuthorizationStatus: tt.authorizationStatus,
				TokenExpireTime:     tt.tokenExpireTime,
			}
			result := entity.GetEffectiveAuthStatus()
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidateCreateAdAccountParam(t *testing.T) {
	tests := []struct {
		name      string
		param     CreateAdAccountParam
		expectErr bool
		errMsg    string
	}{
		{
			name: "有效的主账号参数",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeMaster,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试主账号",
				PlatformAccountId: "xhs_123456",
				Owner:             "张三",
			},
			expectErr: false,
		},
		{
			name: "有效的子账号参数",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeSub,
				ParentId:          1,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试子账号",
				PlatformAccountId: "xhs_sub_123456",
				Owner:             "李四",
			},
			expectErr: false,
		},
		{
			name: "无效的账号类型",
			param: CreateAdAccountParam{
				AccountType:       99,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "账号类型必须是主账号或子账号",
		},
		{
			name: "子账号缺少父账号ID",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeSub,
				ParentId:          0,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试子账号",
				PlatformAccountId: "xhs_sub_123456",
				Owner:             "李四",
			},
			expectErr: true,
			errMsg:    "子账号必须指定父账号ID",
		},
		{
			name: "无效的平台",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeMaster,
				Platform:          0,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "必须指定有效的平台",
		},
		{
			name: "缺少账号名称",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeMaster,
				Platform:          PlatformXiaohongshu,
				AccountName:       "",
				PlatformAccountId: "xhs_123456",
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "账号名称不能为空",
		},
		{
			name: "缺少平台账号ID",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeMaster,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试账号",
				PlatformAccountId: "",
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "平台账号ID不能为空",
		},
		{
			name: "缺少归属人员",
			param: CreateAdAccountParam{
				AccountType:       AccountTypeMaster,
				Platform:          PlatformXiaohongshu,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				Owner:             "",
			},
			expectErr: true,
			errMsg:    "归属人员不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCreateAdAccountParam(tt.param)
			if tt.expectErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateUpdateAdAccountParam(t *testing.T) {
	tests := []struct {
		name      string
		param     UpdateAdAccountParam
		expectErr bool
		errMsg    string
	}{
		{
			name: "有效的更新参数",
			param: UpdateAdAccountParam{
				ID:                1,
				AccountName:       "更新后的账号",
				PlatformAccountId: "xhs_updated",
				UsageStatus:       UsageStatusEnabled,
				Owner:             "更新用户",
			},
			expectErr: false,
		},
		{
			name: "无效的ID",
			param: UpdateAdAccountParam{
				ID:                0,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				UsageStatus:       UsageStatusEnabled,
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "账号ID必须大于0",
		},
		{
			name: "缺少账号名称",
			param: UpdateAdAccountParam{
				ID:                1,
				AccountName:       "",
				PlatformAccountId: "xhs_123456",
				UsageStatus:       UsageStatusEnabled,
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "账号名称不能为空",
		},
		{
			name: "缺少平台账号ID",
			param: UpdateAdAccountParam{
				ID:                1,
				AccountName:       "测试账号",
				PlatformAccountId: "",
				UsageStatus:       UsageStatusEnabled,
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "平台账号ID不能为空",
		},
		{
			name: "无效的使用状态",
			param: UpdateAdAccountParam{
				ID:                1,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				UsageStatus:       99,
				Owner:             "张三",
			},
			expectErr: true,
			errMsg:    "使用状态必须是启用或禁用",
		},
		{
			name: "缺少归属人员",
			param: UpdateAdAccountParam{
				ID:                1,
				AccountName:       "测试账号",
				PlatformAccountId: "xhs_123456",
				UsageStatus:       UsageStatusEnabled,
				Owner:             "",
			},
			expectErr: true,
			errMsg:    "归属人员不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateUpdateAdAccountParam(tt.param)
			if tt.expectErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidationError(t *testing.T) {
	err := NewValidationError("测试错误消息")
	assert.Error(t, err)
	assert.Equal(t, "测试错误消息", err.Error())

	// 测试类型断言
	validationErr, ok := err.(ValidationError)
	assert.True(t, ok)
	assert.Equal(t, "测试错误消息", validationErr.Message)
}
