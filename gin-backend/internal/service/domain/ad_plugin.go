package domain

import "time"

// AdPlanEntity 广告计划实体
type AdPlanEntity struct {
	ID                int64     // 计划ID
	PromotionLinkInfo any       // 推广链接信息
	AdCreativeID      int64     // 创意ID
	StartTime         time.Time // 开始时间
	EndTime           time.Time // 结束时间
}

// AdPageEntity 广告页面实体
type AdPageEntity struct {
	BackgroundImage string // 背景图片
	BackgroundLink  any    // 背景链接
	DialogType      int8   // 对话框类型
	Dialog          any    // 对话框内容
	DialogLinkInfo  any    // 对话框链接信息
}

// AdLinkEntity 广告链接实体
type AdLinkEntity struct {
	ID                int64          // 链接ID
	PromotionLinkInfo any            // 推广链接信息
	CreativeInfo      map[string]any // 创意信息
}

// AdLinkParam 广告链接获取参数
type AdLinkParam struct {
	SlotID int64  // 插槽ID
	OpenID string // 开放ID
	Way    int    // 方式
}

// AdPageParam 广告页面获取参数
type AdPageParam struct {
	SlotID int64  // 插槽ID
	OpenID string // 开放ID
	Way    int    // 方式
}

// AdPlanCacheEntity 广告计划缓存实体
type AdPlanCacheEntity struct {
	ID                int64     // 计划ID
	PromotionLinkInfo any       // 推广链接信息
	AdCreativeID      int64     // 创意ID
	StartTime         time.Time // 开始时间
	EndTime           time.Time // 结束时间
}

// PromotionZoneEntity 推广位实体
type PromotionZoneEntity struct {
	PlanID          int64  // 计划ID
	PromotionLink   string // 推广链接
	PromotionParams string // 推广参数
}

// AdCreativeEntity 广告创意实体
type AdCreativeEntity struct {
	ID              int64  // 创意ID
	Name            string // 创意名称
	HotAreas        string // 热区
	ImageURL        string // 图片URL
	ImageArea       string // 图片区域
	BackgroundColor string // 背景色
}
