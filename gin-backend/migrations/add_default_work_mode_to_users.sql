-- 为用户表添加默认工作模式字段
-- 迁移说明：支持用户切换工作模式功能

-- 添加默认工作模式字段
ALTER TABLE users ADD COLUMN default_work_mode VARCHAR(20) DEFAULT 'traffic' COMMENT '默认工作模式：traffic-流量采买，ad-广告投放';

-- 根据现有角色设置默认工作模式
UPDATE users SET default_work_mode = CASE 
    WHEN role = 1 THEN 'traffic'  -- 媒介 -> 流量采买
    WHEN role = 4 THEN 'ad'       -- 投手 -> 广告投放  
    ELSE 'traffic'                -- 其他角色默认 -> 流量采买
END;

-- 为默认工作模式字段添加索引（如果需要按工作模式查询）
CREATE INDEX idx_users_default_work_mode ON users(default_work_mode); 