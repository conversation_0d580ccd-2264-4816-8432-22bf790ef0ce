package service

import (
	"encoding/json"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

type CreativeService struct {
	db *gorm.DB
}

func NewCreativeService() *CreativeService {
	return &CreativeService{
		db: global.DB,
	}
}

// GetList 获取创意列表
func (s *CreativeService) GetList(param domain.CreativeListParam) (domain.CreativeListResult, error) {
	// 设置默认值
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.Size <= 0 {
		param.Size = 10
	}
	if param.Size > 100 {
		param.Size = 100
	}

	var creatives []model.Creative
	var total int64

	// 基础查询
	query := s.db.Model(&model.Creative{})

	// 应用筛选条件
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return domain.CreativeListResult{}, fmt.Errorf("获取创意总数失败: %v", err)
	}

	// 分页查询
	offset := (param.Page - 1) * param.Size
	err = query.Order("id DESC").Offset(offset).Limit(param.Size).Find(&creatives).Error
	if err != nil {
		return domain.CreativeListResult{}, fmt.Errorf("获取创意列表失败: %v", err)
	}

	// 将model.Creative转换为domain.CreativeEntity
	domainCreatives := make([]domain.CreativeEntity, len(creatives))
	for i, creative := range creatives {
		// 解析热区JSON
		hotAreas, _ := s.parseHotAreas(creative.HotAreas)
		domainCreatives[i] = modelToDomainCreative(creative, hotAreas)
	}

	return domain.CreativeListResult{
		List:  domainCreatives,
		Total: total,
		Page:  param.Page,
		Size:  param.Size,
	}, nil
}

// GetByID 根据ID获取创意
func (s *CreativeService) GetByID(id uint64) (domain.CreativeEntity, error) {
	var creative model.Creative
	err := s.db.First(&creative, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.CreativeEntity{}, fmt.Errorf("创意不存在")
		}
		return domain.CreativeEntity{}, fmt.Errorf("获取创意失败: %v", err)
	}

	// 解析热区JSON
	hotAreas, _ := s.parseHotAreas(creative.HotAreas)

	return modelToDomainCreative(creative, hotAreas), nil
}

// Create 创建创意
func (s *CreativeService) Create(entity domain.CreativeCreateEntity) (int64, error) {
	// 检查创意名称是否已存在
	var count int64
	err := s.db.Model(&model.Creative{}).Where("name = ?", entity.Name).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("检查创意名称失败: %v", err)
	}
	if count > 0 {
		return 0, fmt.Errorf("创意名称已存在")
	}

	// 验证热区数据
	for _, area := range entity.HotAreasList {
		if area.Width <= 0 || area.Height <= 0 {
			return 0, fmt.Errorf("热区宽高必须大于0")
		}
	}

	// 序列化热区为JSON
	hotAreasJSON, err := s.serializeHotAreas(entity.HotAreasList)
	if err != nil {
		return 0, fmt.Errorf("序列化热区数据失败: %v", err)
	}

	// 构建创意模型
	creative := model.Creative{
		Name:            entity.Name,
		ImageURL:        entity.ImageURL,
		ImageArea:       entity.ImageArea,
		BackgroundColor: entity.BackgroundColor,
		HotAreas:        hotAreasJSON,
	}

	// 创建创意
	err = s.db.Create(&creative).Error
	if err != nil {
		return 0, fmt.Errorf("创建创意失败: %v", err)
	}

	return creative.ID, nil
}

// Update 更新创意
func (s *CreativeService) Update(entity domain.CreativeUpdateEntity) error {
	// 检查创意是否存在
	var existing model.Creative
	err := s.db.First(&existing, entity.ID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("创意不存在")
		}
		return fmt.Errorf("获取创意失败: %v", err)
	}

	// 检查创意名称是否与其他记录重复
	var count int64
	err = s.db.Model(&model.Creative{}).Where("name = ? AND id != ?", entity.Name, entity.ID).Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查创意名称失败: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("创意名称已存在")
	}

	// 验证热区数据
	for _, area := range entity.HotAreasList {
		if area.Width <= 0 || area.Height <= 0 {
			return fmt.Errorf("热区宽高必须大于0")
		}
	}

	// 序列化热区为JSON
	hotAreasJSON, err := s.serializeHotAreas(entity.HotAreasList)
	if err != nil {
		return fmt.Errorf("序列化热区数据失败: %v", err)
	}

	// 更新创意信息
	existing.Name = entity.Name
	existing.ImageURL = entity.ImageURL
	existing.ImageArea = entity.ImageArea
	existing.BackgroundColor = entity.BackgroundColor
	existing.HotAreas = hotAreasJSON

	// 保存更新
	err = s.db.Save(&existing).Error
	if err != nil {
		return fmt.Errorf("更新创意失败: %v", err)
	}

	return nil
}

// Delete 删除创意
func (s *CreativeService) Delete(id uint64) error {
	// 检查创意是否存在
	var creative model.Creative
	err := s.db.First(&creative, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("创意不存在")
		}
		return fmt.Errorf("获取创意失败: %v", err)
	}

	// TODO: 检查创意是否被使用，如果被计划引用则不允许删除
	// 这里可以添加与计划表的关联检查

	// 删除创意
	err = s.db.Delete(&creative).Error
	if err != nil {
		return fmt.Errorf("删除创意失败: %v", err)
	}

	return nil
}

// parseHotAreas 解析热区JSON到结构体
func (s *CreativeService) parseHotAreas(hotAreasJSON string) ([]domain.HotArea, error) {
	if hotAreasJSON == "" {
		return []domain.HotArea{}, nil
	}

	var hotAreas []domain.HotArea
	if err := json.Unmarshal([]byte(hotAreasJSON), &hotAreas); err != nil {
		return []domain.HotArea{}, err
	}

	return hotAreas, nil
}

// serializeHotAreas 序列化热区结构体到JSON
func (s *CreativeService) serializeHotAreas(hotAreas []domain.HotArea) (string, error) {
	// 验证热区数据
	for i, area := range hotAreas {
		if area.Width <= 0 || area.Height <= 0 {
			return "", gorm.ErrInvalidValue
		}
		if area.Unit == "" {
			hotAreas[i].Unit = "px" // 设置默认单位
		}
	}

	hotAreasJSON, err := json.Marshal(hotAreas)
	if err != nil {
		return "", err
	}

	return string(hotAreasJSON), nil
}

// modelToDomainCreative 将model.Creative转换为domain.CreativeEntity
func modelToDomainCreative(modelCreative model.Creative, hotAreas []domain.HotArea) domain.CreativeEntity {
	return domain.CreativeEntity{
		ID:              modelCreative.ID,
		Name:            modelCreative.Name,
		ImageURL:        modelCreative.ImageURL,
		ImageArea:       modelCreative.ImageArea,
		BackgroundColor: modelCreative.BackgroundColor,
		HotAreasList:    hotAreas,
		CreatedAt:       modelCreative.CreatedAt,
		UpdatedAt:       modelCreative.UpdatedAt,
	}
}
