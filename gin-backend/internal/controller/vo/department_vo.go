package vo

import (
	"time"
)

// DepartmentDetailResp 部门详情返回
type DepartmentDetailResp struct {
	ID          int64                  `json:"id"`                     // 部门ID
	Name        string                 `json:"name"`                   // 部门名称
	Code        string                 `json:"code"`                   // 部门编码
	ParentID    int64                  `json:"parent_id"`              // 上级部门ID
	ParentName  string                 `json:"parent_name,omitempty"`  // 上级部门名称
	ManagerID   int64                  `json:"manager_id"`             // 部门负责人ID
	ManagerName string                 `json:"manager_name,omitempty"` // 部门负责人姓名
	Description string                 `json:"description"`            // 部门描述
	SortOrder   int                    `json:"sort_order"`             // 排序
	Status      int                    `json:"status"`                 // 状态：1启用 0禁用
	CreatedAt   time.Time              `json:"created_at"`             // 创建时间
	UpdatedAt   time.Time              `json:"updated_at"`             // 更新时间
	Children    []DepartmentDetailResp `json:"children,omitempty"`     // 子部门
	HasChildren bool                   `json:"hasChildren"`            // 是否有子部门
}

// GetDepartmentsReq 获取部门列表请求
type GetDepartmentsReq struct {
	Keyword  string `json:"keyword" form:"keyword"`     // 关键词搜索（部门名称/编码）
	Status   int    `json:"status" form:"status"`       // 状态筛选
	ParentID int64  `json:"parent_id" form:"parent_id"` // 上级部门ID
	Page     int    `json:"page" form:"page"`           // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
}

// GetDepartmentsResp 获取部门列表响应
type GetDepartmentsResp struct {
	List  []DepartmentDetailResp `json:"list"`  // 部门列表
	Total int64                  `json:"total"` // 总数
	Page  int                    `json:"page"`  // 当前页码
	Size  int                    `json:"size"`  // 每页大小
}

// CreateDepartmentReq 创建部门请求
type CreateDepartmentReq struct {
	Name        string `json:"name" binding:"required,min=1,max=100"` // 部门名称
	Code        string `json:"code" binding:"required,min=1,max=50"`  // 部门编码
	ParentID    int64  `json:"parent_id"`                             // 上级部门ID
	ManagerID   int64  `json:"manager_id"`                            // 部门负责人ID
	Description string `json:"description" binding:"max=500"`         // 部门描述
	SortOrder   int    `json:"sort_order"`                            // 排序
	Status      int    `json:"status" binding:"oneof=0 1"`            // 状态：1启用 0禁用
}

// UpdateDepartmentReq 更新部门请求
type UpdateDepartmentReq struct {
	Name        string `json:"name" binding:"required,min=1,max=100"` // 部门名称
	Code        string `json:"code" binding:"required,min=1,max=50"`  // 部门编码
	ParentID    int64  `json:"parent_id"`                             // 上级部门ID
	ManagerID   int64  `json:"manager_id"`                            // 部门负责人ID
	Description string `json:"description" binding:"max=500"`         // 部门描述
	SortOrder   int    `json:"sort_order"`                            // 排序
	Status      int    `json:"status" binding:"oneof=0 1"`            // 状态：1启用 0禁用
}

// DepartmentTreeNodeResp 部门树节点返回
type DepartmentTreeNodeResp struct {
	ID       int64                    `json:"id"`                 // 部门ID
	Name     string                   `json:"name"`               // 部门名称
	Code     string                   `json:"code"`               // 部门编码
	ParentID int64                    `json:"parent_id"`          // 上级部门ID
	Children []DepartmentTreeNodeResp `json:"children,omitempty"` // 子部门
}

// DepartmentOptionResp 部门选项返回
type DepartmentOptionResp struct {
	ID       int64                  `json:"id"`                 // 部门ID
	Name     string                 `json:"name"`               // 部门名称
	Code     string                 `json:"code"`               // 部门编码
	ParentID int64                  `json:"parent_id"`          // 上级部门ID
	Children []DepartmentOptionResp `json:"children,omitempty"` // 子部门
}

// GetDepartmentOptionsReq 获取部门选项请求
type GetDepartmentOptionsReq struct {
	ExcludeID int64 `json:"exclude_id" form:"exclude_id"` // 排除的部门ID
}

// GetDepartmentOptionsResp 获取部门选项响应
type GetDepartmentOptionsResp struct {
	Options []DepartmentOptionResp `json:"options"` // 部门选项列表
}
