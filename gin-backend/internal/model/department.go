package model

import (
	"time"
)

// Department 部门实体
type Department struct {
	ID          int64     `gorm:"primaryKey;autoIncrement;comment:部门ID"`
	Name        string    `gorm:"type:varchar(100);not null;comment:部门名称"`
	Code        string    `gorm:"type:varchar(50);not null;uniqueIndex;comment:部门编码"`
	ParentID    int64     `gorm:"comment:上级部门ID"`
	ManagerID   int64     `gorm:"comment:部门负责人ID"`
	Description string    `gorm:"type:text;comment:部门描述"`
	SortOrder   int       `gorm:"default:0;comment:排序"`
	Status      int       `gorm:"default:1;comment:状态：1启用 0禁用"`
	CreatedAt   time.Time `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime;comment:更新时间"`

	// 关联字段
	Parent   *Department  `gorm:"foreignKey:ParentID"`
	Manager  *User        `gorm:"foreignKey:ManagerID"`
	Children []Department `gorm:"foreignKey:ParentID"`
}

// TableName 指定表名
func (Department) TableName() string {
	return "departments"
}
