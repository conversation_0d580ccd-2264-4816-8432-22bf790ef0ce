# 小红书创意报表数据同步API文档

## 概述

小红书创意报表数据同步功能提供了自动和手动同步小红书广告创意层级离线报表数据的能力。系统会每天凌晨2点自动同步昨天的数据，同时也提供了手动同步接口用于补数据或特殊情况下的数据同步。

## 自动同步机制

### 定时任务
- **执行时间**: 每天凌晨2点
- **同步范围**: 昨天的数据
- **任务名称**: `sync-xhs-creative-report`
- **超时时间**: 30分钟

### 同步逻辑
1. 获取所有已授权且有效的小红书广告账号
2. 检查账号的访问令牌是否有效
3. 调用小红书API获取创意层级离线报表数据
4. 转换数据格式并批量保存到数据库
5. 记录同步日志和统计信息

## 手动同步API

### 1. 同步昨天数据

#### 请求
```http
POST /api/v1/xhs-reports/sync/yesterday
```

#### 请求头
```
Authorization: Bearer your_jwt_token
Content-Type: application/json
```

#### 请求体
```json
{}
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "同步任务已启动，请查看日志了解执行结果"
  }
}
```

### 2. 同步指定日期数据

#### 请求
```http
POST /api/v1/xhs-reports/sync/date
```

#### 请求头
```
Authorization: Bearer your_jwt_token
Content-Type: application/json
```

#### 请求体
```json
{
  "date": "2024-01-15"
}
```

#### 字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| date | string | 是 | 要同步的日期，格式：YYYY-MM-DD |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "date": "2024-01-15",
    "message": "同步任务已启动，请查看日志了解执行结果"
  }
}
```

#### 限制条件
- 日期不能是未来日期
- 日期不能超过90天前
- 日期格式必须为 YYYY-MM-DD

### 3. 同步日期范围数据

#### 请求
```http
POST /api/v1/xhs-reports/sync/range
```

#### 请求头
```
Authorization: Bearer your_jwt_token
Content-Type: application/json
```

#### 请求体
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-07"
}
```

#### 字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | string | 是 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 是 | 结束日期，格式：YYYY-MM-DD |

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-07",
    "message": "同步任务已启动，请查看日志了解执行结果"
  }
}
```

#### 限制条件
- 开始日期不能晚于结束日期
- 结束日期不能是未来日期
- 日期范围不能超过30天
- 开始日期不能超过90天前

### 4. 获取同步状态

#### 请求
```http
GET /api/v1/xhs-reports/sync/status
```

#### 请求头
```
Authorization: Bearer your_jwt_token
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "last_sync_time": "2024-01-16 02:00:00",
    "total_accounts": 5,
    "total_reports": 12580,
    "last_7_days": 1250,
    "status": "running"
  }
}
```

#### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| last_sync_time | string | 最后同步时间 |
| total_accounts | int | 已授权账号数量 |
| total_reports | int | 总报表数量 |
| last_7_days | int | 最近7天数据量 |
| status | string | 同步状态 |

## 使用示例

### 1. 手动同步昨天数据
```bash
curl -X POST http://localhost:8080/api/v1/xhs-reports/sync/yesterday \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 补同步指定日期数据
```bash
curl -X POST http://localhost:8080/api/v1/xhs-reports/sync/date \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "date": "2024-01-15"
  }'
```

### 3. 批量补同步一周数据
```bash
curl -X POST http://localhost:8080/api/v1/xhs-reports/sync/range \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-07"
  }'
```

### 4. 查看同步状态
```bash
curl -X GET http://localhost:8080/api/v1/xhs-reports/sync/status \
  -H "Authorization: Bearer your_jwt_token"
```

## 数据结构

### 同步的数据字段

小红书创意报表包含以下主要数据：

#### 业务字段
- 计划ID、计划名称
- 单元ID、单元名称  
- 创意ID、创意名称、创意图片
- 笔记ID、笔记作者ID
- 广告类型、优化目标、推广标的等

#### 基础指标
- 消费金额、展现量、点击量
- 点击率、平均点击成本、千次曝光成本

#### 互动指标
- 点赞量、评论量、收藏量、关注量、分享量
- 互动量、平均互动成本
- 行动按钮点击量、截图次数等

#### 转化指标
- 进店访问量、商品访客量、加购量
- 订单量、支付金额、ROI
- 表单提交量、有效表单等

#### 其他指标
- 直播间相关指标
- 私信营销指标
- APP转化指标
- 关键词排名指标等

## 错误处理

### 常见错误

#### 1. 参数验证错误
```json
{
  "code": 400,
  "message": "参数验证失败: 日期格式错误，请使用 YYYY-MM-DD 格式"
}
```

#### 2. 日期限制错误
```json
{
  "code": 400,
  "message": "不能同步未来日期的数据"
}
```

#### 3. 权限错误
```json
{
  "code": 401,
  "message": "未授权访问"
}
```

#### 4. 系统错误
```json
{
  "code": 500,
  "message": "系统内部错误"
}
```

## 监控和日志

### 日志级别
- **INFO**: 正常的同步开始、完成信息
- **WARN**: 警告信息，如账号令牌即将过期
- **ERROR**: 错误信息，如API调用失败、数据保存失败

### 关键日志
```
2024-01-16 02:00:00 INFO 开始同步小红书创意报表数据 date=2024-01-15
2024-01-16 02:05:30 INFO 成功同步账号报表数据 account_id=123 account_name=测试账号 date=2024-01-15 count=150
2024-01-16 02:10:00 INFO 小红书创意报表数据同步完成 date=2024-01-15 success_count=5 error_count=0
```

## 最佳实践

### 1. 数据同步策略
- 依赖自动定时任务进行日常数据同步
- 使用手动同步接口进行补数据或应急处理
- 定期检查同步状态，确保数据完整性

### 2. 错误处理
- 监控同步任务的执行日志
- 对于失败的同步任务，及时进行手动补同步
- 检查账号授权状态，确保令牌有效

### 3. 性能优化
- 避免频繁调用手动同步接口
- 批量同步时选择合适的日期范围
- 在业务低峰期进行大批量数据同步

### 4. 数据质量
- 定期验证同步数据的准确性
- 对比API返回数据与数据库存储数据
- 监控数据量的变化趋势

## 故障排除

### 1. 同步任务不执行
- 检查定时任务是否正常启动
- 确认Redis连接是否正常（分布式锁需要）
- 查看应用日志中的错误信息

### 2. 数据同步失败
- 检查小红书账号授权状态
- 验证访问令牌是否过期
- 确认网络连接到小红书API是否正常

### 3. 数据不完整
- 检查API分页逻辑是否正确
- 验证数据转换逻辑是否有遗漏
- 确认数据库保存是否成功

### 4. 性能问题
- 监控API调用频率，避免触发限流
- 优化数据库批量插入性能
- 调整同步任务的超时时间

## 总结

小红书创意报表数据同步功能提供了完整的数据同步解决方案，包括：

1. **自动化同步**: 每天定时同步昨天的数据
2. **手动同步**: 支持指定日期和日期范围的数据补同步
3. **状态监控**: 提供同步状态查询接口
4. **错误处理**: 完善的错误处理和日志记录
5. **数据完整性**: 支持200+个报表字段的完整同步

通过这套同步机制，可以确保小红书广告数据的及时性和完整性，为业务分析和决策提供可靠的数据支撑。
