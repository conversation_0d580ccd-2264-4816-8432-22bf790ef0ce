package domain

import (
	"time"
)

// DepartmentEntity 部门实体
type DepartmentEntity struct {
	ID          int64              // 部门ID
	Name        string             // 部门名称
	Code        string             // 部门编码
	ParentID    int64              // 上级部门ID
	ParentName  string             // 上级部门名称
	ManagerID   int64              // 部门负责人ID
	ManagerName string             // 部门负责人名称
	Description string             // 部门描述
	SortOrder   int                // 排序
	Status      int                // 状态
	CreatedAt   time.Time          // 创建时间
	UpdatedAt   time.Time          // 更新时间
	Children    []DepartmentEntity // 子部门
	HasChildren bool               // 是否有子部门
}

// DepartmentTreeNode 部门树节点
type DepartmentTreeNode struct {
	ID       int64                // 部门ID
	Name     string               // 部门名称
	Code     string               // 部门编码
	ParentID int64                // 上级部门ID
	Children []DepartmentTreeNode // 子部门
}

// DepartmentOption 部门选项
type DepartmentOption struct {
	ID       int64              // 部门ID
	Name     string             // 部门名称
	Code     string             // 部门编码
	ParentID int64              // 上级部门ID
	Children []DepartmentOption // 子部门
}

// GetDepartmentsParam 获取部门列表参数
type GetDepartmentsParam struct {
	Keyword  string // 关键词搜索（部门名称/编码）
	Status   int    // 状态筛选（0:禁用 1:启用）
	ParentID int64  // 上级部门ID
	Page     int    // 页码
	PageSize int    // 每页数量
}

// GetDepartmentsResult 获取部门列表结果
type GetDepartmentsResult struct {
	List  []DepartmentEntity // 部门列表
	Total int64              // 总数
	Page  int                // 当前页码
	Size  int                // 每页大小
}

// GetDepartmentOptionsParam 获取部门选项参数
type GetDepartmentOptionsParam struct {
	ExcludeID int64 // 排除的部门ID
}

// GetDepartmentOptionsResult 获取部门选项结果
type GetDepartmentOptionsResult struct {
	Options []DepartmentOption // 部门选项列表
}

// CreateDepartmentEntity 创建部门实体
type CreateDepartmentEntity struct {
	Name        string // 部门名称
	Code        string // 部门编码
	ParentID    int64  // 上级部门ID
	ManagerID   int64  // 部门负责人ID
	Description string // 部门描述
	SortOrder   int    // 排序
	Status      int    // 状态：1启用 0禁用
}

// UpdateDepartmentEntity 更新部门实体
type UpdateDepartmentEntity struct {
	Name        string // 部门名称
	Code        string // 部门编码
	ParentID    int64  // 上级部门ID
	ManagerID   int64  // 部门负责人ID
	Description string // 部门描述
	SortOrder   int    // 排序
	Status      int    // 状态：1启用 0禁用
}
