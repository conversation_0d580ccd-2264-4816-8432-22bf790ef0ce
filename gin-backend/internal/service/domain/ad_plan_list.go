package domain

import (
	"errors"
	"time"
)

// AdPlanListParam 计划列表查询参数
type AdPlanListParam struct {
	// 筛选条件
	AccountID   *int64     // 账号ID
	AccountName string     // 账号名称（模糊查询）
	PlanName    string     // 计划名称（模糊查询）
	StartDate   *time.Time // 开始日期
	EndDate     *time.Time // 结束日期

	// 分页参数
	Page     int // 页码，从1开始
	PageSize int // 每页数量
}

// Validate 验证参数
func (p AdPlanListParam) Validate() error {
	if p.Page < 1 {
		return errors.New("页码必须大于0")
	}
	if p.PageSize < 1 || p.PageSize > 1000 {
		return errors.New("每页数量必须在1-1000之间")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// AdPlanListItem 计划列表项
type AdPlanListItem struct {
	PlanID       string  `json:"plan_id"`       // 计划编号
	PlanName     string  `json:"plan_name"`     // 计划名称
	AccountName  string  `json:"account_name"`  // 账号名称
	Consumption  float64 `json:"consumption"`   // 消费
	ActualCost   float64 `json:"actual_cost"`   // 实际消费
	Impressions  int64   `json:"impressions"`   // 展现量
	Clicks       int64   `json:"clicks"`        // 点击量
	ClickRate    float64 `json:"click_rate"`    // 点击率（%）
	LastUpdate   string  `json:"last_update"`   // 最后更新时间
}

// AdPlanListResult 计划列表查询结果
type AdPlanListResult struct {
	List  []AdPlanListItem `json:"list"`  // 计划列表
	Total int64            `json:"total"` // 总数量
	Page  int              `json:"page"`  // 当前页码
	Size  int              `json:"size"`  // 每页数量
}

// AdPlanExportParam 计划列表导出参数
type AdPlanExportParam struct {
	// 筛选条件
	AccountID   *int64     // 账号ID
	AccountName string     // 账号名称（模糊查询）
	PlanName    string     // 计划名称（模糊查询）
	StartDate   *time.Time // 开始日期
	EndDate     *time.Time // 结束日期

	// 导出参数
	Format string // 导出格式：xlsx 或 csv
}

// Validate 验证导出参数
func (p AdPlanExportParam) Validate() error {
	if p.Format != "xlsx" && p.Format != "csv" {
		return errors.New("导出格式只支持xlsx或csv")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	// 限制导出数据的时间范围，避免数据量过大
	if p.StartDate != nil && p.EndDate != nil {
		duration := p.EndDate.Sub(*p.StartDate)
		if duration > 365*24*time.Hour {
			return errors.New("导出数据的时间范围不能超过365天")
		}
	}
	return nil
}

// AdPlanExportResult 计划列表导出结果
type AdPlanExportResult struct {
	FileName string // 文件名
	FileURL  string // 文件下载URL
}

// AdPlanStatsParam 计划统计参数
type AdPlanStatsParam struct {
	// 筛选条件
	AccountID   *int64     // 账号ID
	AccountName string     // 账号名称
	StartDate   *time.Time // 开始日期
	EndDate     *time.Time // 结束日期
}

// Validate 验证统计参数
func (p AdPlanStatsParam) Validate() error {
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// AdPlanStatsResult 计划统计结果
type AdPlanStatsResult struct {
	TotalPlans      int64     `json:"total_plans"`      // 总计划数量
	TotalCost       float64   `json:"total_cost"`       // 总消费
	TotalClicks     int64     `json:"total_clicks"`     // 总点击量
	TotalImpressions int64    `json:"total_impressions"` // 总展现量
	AvgClickRate    float64   `json:"avg_click_rate"`   // 平均点击率
	LastUpdateTime  time.Time `json:"last_update_time"` // 最后更新时间
}

// ValidateAdPlanListParam 验证计划列表查询参数
func ValidateAdPlanListParam(param AdPlanListParam) error {
	return param.Validate()
}

// ValidateAdPlanExportParam 验证计划列表导出参数
func ValidateAdPlanExportParam(param AdPlanExportParam) error {
	return param.Validate()
}

// ValidateAdPlanStatsParam 验证计划统计参数
func ValidateAdPlanStatsParam(param AdPlanStatsParam) error {
	return param.Validate()
}

// GetTimeRangeDescription 获取时间范围描述
func GetTimeRangeDescription(startDate, endDate *time.Time) string {
	if startDate == nil && endDate == nil {
		return "全部时间"
	}
	if startDate != nil && endDate != nil {
		if startDate.Format("2006-01-02") == endDate.Format("2006-01-02") {
			return startDate.Format("2006-01-02") + "（单日）"
		}
		return startDate.Format("2006-01-02") + " 至 " + endDate.Format("2006-01-02")
	}
	if startDate != nil {
		return "从 " + startDate.Format("2006-01-02") + " 开始"
	}
	if endDate != nil {
		return "到 " + endDate.Format("2006-01-02") + " 结束"
	}
	return "未知时间范围"
}

// IsDateRangeValid 检查日期范围是否有效
func IsDateRangeValid(startDate, endDate *time.Time) bool {
	if startDate == nil || endDate == nil {
		return true
	}
	return !startDate.After(*endDate)
}

// GetDateRangeDays 获取日期范围的天数
func GetDateRangeDays(startDate, endDate *time.Time) int {
	if startDate == nil || endDate == nil {
		return 0
	}
	duration := endDate.Sub(*startDate)
	return int(duration.Hours()/24) + 1
}
