# Gin Backend - 广告投放管理系统后端

这是从GoFrame迁移到Gin+GORM的后端项目。

## 项目结构

```
gin-backend/
├── cmd/                    # 应用程序入口
│   └── main.go
├── internal/               # 内部模块
│   ├── config/            # 配置管理
│   ├── controller/        # 控制器层
│   ├── service/           # 服务层
│   ├── dao/               # 数据访问层
│   ├── model/             # 数据模型
│   ├── middleware/        # 中间件
│   └── utils/             # 工具函数
├── pkg/                   # 公共包
│   ├── response/          # 响应工具
│   ├── validator/         # 验证工具
│   └── logger/            # 日志工具
├── config/                # 配置文件
│   └── config.yaml
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── go.mod
└── README.md
```

## 技术栈

- **Web框架**: Gin
- **ORM**: GORM
- **数据库**: MySQL
- **缓存**: Redis
- **配置管理**: Viper
- **日志**: Logrus
- **JWT**: golang-jwt/jwt

## 快速开始

1. **初始化依赖**
```bash
cd gin-backend
go mod tidy
```

2. **配置数据库**
```bash
# 修改 config/config.yaml 中的数据库配置
```

3. **运行项目**
```bash
go run cmd/main.go
```

## API文档

- 健康检查: `GET /health`
- API测试: `GET /api/v1/ping`

## 迁移计划

### 阶段一：基础框架搭建 ✅
- [x] 创建项目目录结构
- [x] 配置Gin+GORM基础框架
- [x] 实现基础中间件
- [x] 统一响应格式

### 阶段二：核心模块迁移
- [ ] 用户认证模块
- [ ] Dashboard模块
- [ ] 创意管理模块
- [ ] 投放计划模块

### 阶段三：高级功能
- [ ] 数据分析模块
- [ ] 报表功能
- [ ] 文件上传
- [ ] 数据同步

### 阶段四：优化完善
- [ ] 性能优化
- [ ] 测试覆盖
- [ ] 文档完善
- [ ] 部署配置

## 开发规范

### 目录结构说明
- `cmd/`: 应用程序入口点
- `internal/controller/`: HTTP处理器，负责请求参数绑定和响应
- `internal/service/`: 业务逻辑层，处理具体的业务规则
- `internal/dao/`: 数据访问层，封装数据库操作
- `internal/model/`: 数据模型定义
- `pkg/`: 可复用的公共包

### 编码规范
1. 使用中文注释
2. 遵循Go代码规范
3. 统一错误处理
4. 使用结构体tag进行数据绑定和验证

## 配置说明

所有配置都在 `config/config.yaml` 文件中，包括：
- 服务器配置
- 数据库配置
- Redis配置
- JWT配置
- OSS配置 