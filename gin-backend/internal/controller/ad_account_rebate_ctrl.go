package controller

import (
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AdAccountRebateController 账号返利比例控制器
type AdAccountRebateController struct {
	rebateService *service.AdAccountRebateService
}

// NewAdAccountRebateController 创建账号返利比例控制器
func NewAdAccountRebateController() *AdAccountRebateController {
	return &AdAccountRebateController{
		rebateService: service.NewAdAccountRebateService(),
	}
}

// ChangeAccountRebateRequest 变更账号返利比例请求
type ChangeAccountRebateRequest struct {
	AccountId     int64   `json:"account_id" binding:"required"`     // 账号ID
	NewRate       float64 `json:"new_rate" binding:"required,min=0"` // 新返利比例
	EffectiveDate string  `json:"effective_date" binding:"required"` // 生效日期 YYYY-MM-DD
	ChangeReason  string  `json:"change_reason"`                     // 变更原因
	OperatorId    *int64  `json:"operator_id"`                       // 操作人ID
	OperatorName  string  `json:"operator_name"`                     // 操作人姓名
}

// ChangeAccountRebate 变更账号返利比例
// @Summary 变更账号返利比例
// @Description 变更账号返利比例，自动记录变更历史
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Param request body ChangeAccountRebateRequest true "变更参数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates/change [post]
func (ctrl *AdAccountRebateController) ChangeAccountRebate(c *gin.Context) {
	var req ChangeAccountRebateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 解析生效日期
	effectiveDate, err := time.Parse("2006-01-02", req.EffectiveDate)
	if err != nil {
		response.Error(c, 400, "生效日期格式错误，请使用YYYY-MM-DD格式")
		return
	}

	// 转换参数
	param := domain.AdAccountRebateChangeParam{
		AccountId:     req.AccountId,
		NewRate:       req.NewRate,
		EffectiveDate: effectiveDate,
		ChangeReason:  req.ChangeReason,
		OperatorId:    req.OperatorId,
		OperatorName:  req.OperatorName,
	}

	// 调用服务
	if err := ctrl.rebateService.ChangeAccountRebate(c.Request.Context(), param); err != nil {
		zap.L().Error("变更账号返利比例失败", zap.Error(err))
		response.Error(c, 500, "变更账号返利比例失败: "+err.Error())
		return
	}

	response.Success(c, map[string]string{
		"message": "账号返利比例变更成功",
	})
}

// GetAccountRebateHistory 获取账号返利比例变更记录
// @Summary 获取账号返利比例变更记录
// @Description 获取账号返利比例变更记录列表，支持多维度筛选
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param change_type query string false "变更类型：create/update/delete"
// @Param operator_name query string false "操作人姓名"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} response.Response{data=domain.AdAccountRebateHistoryResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates/history [get]
func (ctrl *AdAccountRebateController) GetAccountRebateHistory(c *gin.Context) {
	var param domain.AdAccountRebateHistoryParam
	if err := c.ShouldBindQuery(&param); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	// 调用服务
	result, err := ctrl.rebateService.GetAccountRebateHistory(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取账号返利比例变更记录失败", zap.Error(err))
		response.Error(c, 500, "获取账号返利比例变更记录失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// GetAccountRebateList 获取账号返利比例列表
// @Summary 获取账号返利比例列表
// @Description 获取账号返利比例列表，支持账号筛选
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} response.Response{data=domain.AdAccountRebateListResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates [get]
func (ctrl *AdAccountRebateController) GetAccountRebateList(c *gin.Context) {
	var param domain.AdAccountRebateListParam
	if err := c.ShouldBindQuery(&param); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	// 调用服务
	result, err := ctrl.rebateService.GetAccountRebateList(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取账号返利比例列表失败", zap.Error(err))
		response.Error(c, 500, "获取账号返利比例列表失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// GetAccountRebateStats 获取账号返利比例统计
// @Summary 获取账号返利比例统计
// @Description 获取账号返利比例统计信息
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=domain.AdAccountRebateStatsResult}
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates/stats [get]
func (ctrl *AdAccountRebateController) GetAccountRebateStats(c *gin.Context) {
	// 调用服务
	result, err := ctrl.rebateService.GetAccountRebateStats(c.Request.Context())
	if err != nil {
		zap.L().Error("获取账号返利比例统计失败", zap.Error(err))
		response.Error(c, 500, "获取账号返利比例统计失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// DeleteAccountRebateRequest 删除账号返利比例请求
type DeleteAccountRebateRequest struct {
	OperatorId   *int64 `json:"operator_id"`   // 操作人ID
	OperatorName string `json:"operator_name"` // 操作人姓名
	Reason       string `json:"reason"`        // 删除原因
}

// DeleteAccountRebate 删除账号返利比例
// @Summary 删除账号返利比例
// @Description 删除指定账号的返利比例，自动记录删除历史
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Param account_id path int true "账号ID"
// @Param request body DeleteAccountRebateRequest true "删除参数"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates/{account_id} [delete]
func (ctrl *AdAccountRebateController) DeleteAccountRebate(c *gin.Context) {
	// 获取账号ID
	accountIDStr := c.Param("account_id")
	accountID, err := strconv.ParseInt(accountIDStr, 10, 64)
	if err != nil {
		response.Error(c, 400, "账号ID格式错误")
		return
	}

	var req DeleteAccountRebateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 调用服务
	if err := ctrl.rebateService.DeleteAccountRebate(c.Request.Context(), accountID, req.OperatorId, req.OperatorName, req.Reason); err != nil {
		zap.L().Error("删除账号返利比例失败", zap.Error(err))
		response.Error(c, 500, "删除账号返利比例失败: "+err.Error())
		return
	}

	response.Success(c, map[string]string{
		"message": "账号返利比例删除成功",
	})
}

// GetAccountCurrentRebate 获取账号当前返利比例
// @Summary 获取账号当前返利比例
// @Description 获取指定账号的当前返利比例
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Param account_id path int true "账号ID"
// @Success 200 {object} response.Response{data=model.AdAccountRebate}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/account-rebates/{account_id}/current [get]
func (ctrl *AdAccountRebateController) GetAccountCurrentRebate(c *gin.Context) {
	// 获取账号ID
	accountIDStr := c.Param("account_id")
	accountID, err := strconv.ParseInt(accountIDStr, 10, 64)
	if err != nil {
		response.Error(c, 400, "账号ID格式错误")
		return
	}

	// 调用服务
	rebate, err := ctrl.rebateService.GetAccountCurrentRebate(c.Request.Context(), accountID)
	if err != nil {
		zap.L().Error("获取账号当前返利比例失败", zap.Error(err))
		response.Error(c, 500, "获取账号当前返利比例失败: "+err.Error())
		return
	}

	if rebate == nil {
		response.Error(c, 404, "该账号未设置返利比例")
		return
	}

	response.Success(c, rebate)
}

// GetChangeTypes 获取变更类型列表
// @Summary 获取变更类型列表
// @Description 获取支持的变更类型列表
// @Tags 账号返利比例
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]map[string]string}
// @Router /api/v1/account-rebates/change-types [get]
func (ctrl *AdAccountRebateController) GetChangeTypes(c *gin.Context) {
	changeTypes := []map[string]string{
		{"value": "create", "label": "新增返利比例"},
		{"value": "update", "label": "修改返利比例"},
		{"value": "delete", "label": "删除返利比例"},
	}

	response.Success(c, changeTypes)
}
