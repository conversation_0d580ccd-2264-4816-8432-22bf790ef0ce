package xiaohongshu

import (
	"time"
)

// RealtimeReportBuilder 实时报表请求构建器
type RealtimeReportBuilder struct {
	request *CreativityRealtimeReportRequest
}

// NewRealtimeReportBuilder 创建实时报表请求构建器
func NewRealtimeReportBuilder(advertiserID int64, startDate, endDate string) *RealtimeReportBuilder {
	return &RealtimeReportBuilder{
		request: &CreativityRealtimeReportRequest{
			AdvertiserID: advertiserID,
			StartDate:    startDate,
			EndDate:      endDate,
			PageNum:      1,
			PageSize:     20,
		},
	}
}

// WithPagination 设置分页参数
func (rb *RealtimeReportBuilder) WithPagination(pageNum, pageSize int) *RealtimeReportBuilder {
	rb.request.PageNum = pageNum
	rb.request.PageSize = pageSize
	return rb
}

// WithSort 设置排序
func (rb *RealtimeReportBuilder) WithSort(column, order string) *RealtimeReportBuilder {
	rb.request.SortColumn = column
	rb.request.Sort = order
	return rb
}

// WithPlacementList 设置广告类型过滤
func (rb *RealtimeReportBuilder) WithPlacementList(placements []int) *RealtimeReportBuilder {
	rb.request.PlacementList = placements
	return rb
}

// WithCreativityFilterState 设置创意状态过滤
func (rb *RealtimeReportBuilder) WithCreativityFilterState(state int) *RealtimeReportBuilder {
	rb.request.CreativityFilterState = state
	return rb
}

// WithCreativityCreateTimeRange 设置创意创建时间范围
func (rb *RealtimeReportBuilder) WithCreativityCreateTimeRange(beginTime, endTime string) *RealtimeReportBuilder {
	rb.request.CreativityCreateBeginTime = beginTime
	rb.request.CreativityCreateEndTime = endTime
	return rb
}

// WithConversionType 设置创意类型
func (rb *RealtimeReportBuilder) WithConversionType(conversionType int) *RealtimeReportBuilder {
	rb.request.ConversionType = conversionType
	return rb
}

// WithProgrammaticList 设置创意组合方式
func (rb *RealtimeReportBuilder) WithProgrammaticList(programmaticList []int) *RealtimeReportBuilder {
	rb.request.ProgrammaticList = programmaticList
	return rb
}

// WithCreativityAuditState 设置创意审核状态
func (rb *RealtimeReportBuilder) WithCreativityAuditState(state int) *RealtimeReportBuilder {
	rb.request.CreativityAuditState = state
	return rb
}

// WithName 设置搜索创意名称
func (rb *RealtimeReportBuilder) WithName(name string) *RealtimeReportBuilder {
	rb.request.Name = name
	return rb
}

// WithID 设置搜索创意ID
func (rb *RealtimeReportBuilder) WithID(id int) *RealtimeReportBuilder {
	rb.request.ID = id
	return rb
}

// WithDataCaliber 设置数据指标归因时间类型
func (rb *RealtimeReportBuilder) WithDataCaliber(caliber int) *RealtimeReportBuilder {
	rb.request.DataCaliber = caliber
	return rb
}

// WithHourlyData 设置是否拉取小时数据
func (rb *RealtimeReportBuilder) WithHourlyData(needHourlyData bool) *RealtimeReportBuilder {
	rb.request.NeedHourlyData = needHourlyData
	return rb
}

// Build 构建请求
func (rb *RealtimeReportBuilder) Build() *CreativityRealtimeReportRequest {
	return rb.request
}

// 预定义的广告类型常量
const (
	PlacementFeed   = 1 // 信息流
	PlacementSearch = 2 // 搜索
	PlacementVideo  = 7 // 视频流
)

// 预定义的创意状态常量
const (
	CreativityFilterStateAll     = 0 // 全部
	CreativityFilterStateActive  = 1 // 有效
	CreativityFilterStateDeleted = 2 // 已删除
)

// 预定义的创意审核状态常量
const (
	CreativityAuditStateAll      = 0 // 全部
	CreativityAuditStatePending  = 1 // 审核中
	CreativityAuditStateApproved = 2 // 审核通过
	CreativityAuditStateRejected = 3 // 审核拒绝
)

// 预定义的创意组合方式常量
const (
	ProgrammaticAuto   = 1 // 自动生成
	ProgrammaticManual = 2 // 手动上传
)

// CreateTodayRealtimeReport 创建今日实时报表请求
func CreateTodayRealtimeReport(advertiserID int64) *CreativityRealtimeReportRequest {
	today := time.Now().Format("2006-01-02")

	return NewRealtimeReportBuilder(advertiserID, today, today).
		WithPagination(1, 50).
		Build()
}

// CreateYesterdayRealtimeReport 创建昨日实时报表请求
func CreateYesterdayRealtimeReport(advertiserID int64) *CreativityRealtimeReportRequest {
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	return NewRealtimeReportBuilder(advertiserID, yesterday, yesterday).
		WithPagination(1, 50).
		Build()
}

// CreateLast7DaysRealtimeReport 创建最近7天实时报表请求
func CreateLast7DaysRealtimeReport(advertiserID int64) *CreativityRealtimeReportRequest {
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -7).Format("2006-01-02")

	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithPagination(1, 100).
		Build()
}

// CreateDateRangeRealtimeReport 创建日期范围实时报表请求
func CreateDateRangeRealtimeReport(advertiserID int64, days int) *CreativityRealtimeReportRequest {
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -days).Format("2006-01-02")

	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithPagination(1, 100).
		Build()
}

// CreateRealtimeReportWithHourlyData 创建带小时数据的实时报表请求
func CreateRealtimeReportWithHourlyData(advertiserID int64, startDate, endDate string) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithHourlyData(true).
		WithPagination(1, 50).
		Build()
}

// CreateFeedRealtimeReport 创建信息流实时报表请求
func CreateFeedRealtimeReport(advertiserID int64, startDate, endDate string) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithPlacementList([]int{PlacementFeed}).
		WithPagination(1, 50).
		Build()
}

// CreateSearchRealtimeReport 创建搜索实时报表请求
func CreateSearchRealtimeReport(advertiserID int64, startDate, endDate string) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithPlacementList([]int{PlacementSearch}).
		WithPagination(1, 50).
		Build()
}

// CreateActiveCreativityReport 创建有效创意实时报表请求
func CreateActiveCreativityReport(advertiserID int64, startDate, endDate string) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithCreativityFilterState(CreativityFilterStateActive).
		WithCreativityAuditState(CreativityAuditStateApproved).
		WithPagination(1, 50).
		Build()
}

// CreateCreativityReportByName 根据创意名称创建实时报表请求
func CreateCreativityReportByName(advertiserID int64, startDate, endDate, creativityName string) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithName(creativityName).
		WithPagination(1, 50).
		Build()
}

// CreateCreativityReportByID 根据创意ID创建实时报表请求
func CreateCreativityReportByID(advertiserID int64, startDate, endDate string, creativityID int) *CreativityRealtimeReportRequest {
	return NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithID(creativityID).
		WithPagination(1, 50).
		Build()
}

// GetRealtimeBasicMetrics 获取实时报表基础指标字段列表
func GetRealtimeBasicMetrics() []string {
	return []string{
		"fee", "impression", "click", "ctr", "acp", "cpm",
	}
}

// GetRealtimeInteractionMetrics 获取实时报表互动指标字段列表
func GetRealtimeInteractionMetrics() []string {
	return []string{
		"like", "comment", "collect", "follow", "share", "interaction", "cpi",
		"action_button_click", "action_button_ctr", "screenshot", "pic_save", "reserve_pv",
	}
}

// GetRealtimeConversionMetrics 获取实时报表转化指标字段列表
func GetRealtimeConversionMetrics() []string {
	return []string{
		"goods_order", "goods_order_price", "rgmv", "roi",
		"success_goods_order", "click_order_cvr",
	}
}

// GetRealtimeLiveMetrics 获取实时报表直播间指标字段列表
func GetRealtimeLiveMetrics() []string {
	return []string{
		"clk_live_entry_pv", "clk_live_entry_pv_cost", "clk_live_avg_view_time",
		"clk_live_all_follow", "clk_live_5s_entry_pv", "clk_live_5s_entry_uv_cost",
		"clk_live_comment", "clk_live_room_order_num", "live_average_order_cost",
		"clk_live_room_rgmv", "clk_live_room_roi",
	}
}

// GetRealtimeAllMetrics 获取实时报表所有常用指标字段列表
func GetRealtimeAllMetrics() []string {
	var metrics []string
	metrics = append(metrics, GetRealtimeBasicMetrics()...)
	metrics = append(metrics, GetRealtimeInteractionMetrics()...)
	metrics = append(metrics, GetRealtimeConversionMetrics()...)
	metrics = append(metrics, GetRealtimeLiveMetrics()...)
	return metrics
}
