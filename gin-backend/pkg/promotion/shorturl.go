package promotion

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
)

// GenerateShortURL 生成短链接
func (c *Client) GenerateShortURL(longURL string) (string, error) {
	// 构建请求参数
	reqData := map[string]interface{}{
		"long_url": longURL,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return "", fmt.<PERSON>rro<PERSON>("序列化请求数据失败: %v", err)
	}

	// 发送POST请求
	url := fmt.Sprintf("%s/api/short-url/generate", c.baseURL)
	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("调用短链接生成接口失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    struct {
			ShortURL string `json:"short_url"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &apiResp); err != nil {
		return "", fmt.Errorf("解析短链接响应失败: %v", err)
	}

	if apiResp.Code != 0 {
		return "", fmt.Errorf("生成短链接失败: %s", apiResp.Message)
	}

	return apiResp.Data.ShortURL, nil
}
