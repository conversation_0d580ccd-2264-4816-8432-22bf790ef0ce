-- 资源位权限补充配置脚本
-- 注意：基础的资源位权限已在 reset_permission_system_safe.sql 中配置
-- 此脚本仅添加缺失的权限

-- 检查表结构并添加缺失字段（如果需要）
DO $$
BEGIN
    -- 检查permissions表是否存在type字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'permissions' AND column_name = 'type') THEN
        ALTER TABLE permissions ADD COLUMN type VARCHAR(20) DEFAULT 'function';
    END IF;
    
    -- 检查permissions表是否存在data_scope字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'permissions' AND column_name = 'data_scope') THEN
        ALTER TABLE permissions ADD COLUMN data_scope VARCHAR(20);
    END IF;
END $$;

-- 只添加缺失的资源位审核权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('资源位审核', 'slot:audit', 'slot', 'function', '审核资源位状态', NULL, 1, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    type = EXCLUDED.type,
    data_scope = EXCLUDED.data_scope,
    updated_at = NOW();

-- 为相关角色分配新增的审核权限
DO $$
DECLARE
    role_record RECORD;
    audit_permission_id INTEGER;
BEGIN
    -- 获取审核权限ID
    SELECT id INTO audit_permission_id FROM permissions WHERE code = 'slot:audit';
    
    IF audit_permission_id IS NOT NULL THEN
        -- 为超级管理员和管理层分配审核权限
        FOR role_record IN 
            SELECT id FROM roles WHERE code IN ('super_admin', 'manager')
        LOOP
            INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
            VALUES (role_record.id, audit_permission_id, NOW(), NOW())
            ON CONFLICT (role_id, permission_id) DO NOTHING;
        END LOOP;
        
        -- 为运营角色分配审核权限
        FOR role_record IN 
            SELECT id FROM roles WHERE code = 'operator'
        LOOP
            INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
            VALUES (role_record.id, audit_permission_id, NOW(), NOW())
            ON CONFLICT (role_id, permission_id) DO NOTHING;
        END LOOP;
        
        RAISE NOTICE '已为相关角色分配资源位审核权限';
    ELSE
        RAISE NOTICE '未找到资源位审核权限，请检查权限配置';
    END IF;
END $$;

-- 显示新增权限的分配结果
SELECT 
    r.name as role_name,
    p.code as permission_code,
    p.name as permission_name,
    p.type as permission_type,
    p.data_scope
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.code = 'slot:audit'
ORDER BY r.name;

-- 显示所有资源位权限的完整分配情况
SELECT '=== 所有资源位权限分配情况 ===' as info;
SELECT 
    r.name as role_name,
    p.code as permission_code,
    p.name as permission_name,
    p.type as permission_type,
    p.data_scope
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.code LIKE 'slot:%'
ORDER BY r.name, p.type, p.code; 