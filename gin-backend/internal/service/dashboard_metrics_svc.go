package service

import (
	"strconv"
	"time"

	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// dashboardMetricsService 仪表盘指标服务
type dashboardMetricsService struct {
	dashboard *DashboardService
	db        *gorm.DB
}

// newDashboardMetricsService 创建仪表盘指标服务实例
func newDashboardMetricsService(d *DashboardService) *dashboardMetricsService {
	return &dashboardMetricsService{
		dashboard: d,
		db:        d.db,
	}
}

// GetMetrics 获取仪表盘指标数据
func (s *dashboardMetricsService) GetMetrics(req domain.DashboardMetricsParam, userID int, userRole int) (domain.DashboardMetricsEntity, error) {
	// 转换为内部使用的数据结构
	modelReq := &struct {
		StartDate string
		EndDate   string
		UserID    string
		MediaID   string
		PlanID    string
		ProductID string
		Type      string
		Category  string
	}{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		UserID:    req.UserID,
		MediaID:   req.MediaID,
		PlanID:    req.PlanID,
		ProductID: req.ProductID,
		Type:      req.Type,
		Category:  req.Category,
	}

	// 1. 参数验证
	if err := s.validateRequest(modelReq); err != nil {
		return domain.DashboardMetricsEntity{}, err
	}

	// 2. 设置默认日期（如果未指定）
	s.setDefaultDates(modelReq)

	// 3. 验证数据库参数
	if err := s.validateQueryParams(modelReq); err != nil {
		return domain.DashboardMetricsEntity{}, err
	}

	// 4. 应用数据权限控制
	scope, departmentUsers, err := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	if err != nil {
		return domain.DashboardMetricsEntity{}, err
	}

	// 5. 获取当前周期数据
	currentStats, err := s.getDailyStatsWithScope(modelReq, userID, userRole, scope, departmentUsers)
	if err != nil {
		return domain.DashboardMetricsEntity{}, err
	}

	// 6. 计算指标
	metrics := s.calculateMetrics(currentStats, modelReq, userRole)

	// 7. 计算环比和同比变化
	s.calculateChanges(metrics, modelReq, userID, userRole)

	// 转换为Domain实体
	domainMetrics := make(map[string]domain.MetricItem)
	for key, item := range metrics {
		domainMetrics[key] = domain.MetricItem{
			Label:      item.Label,
			Value:      item.Value,
			Change:     item.Change,
			WeekChange: item.WeekChange,
			HasTrend:   item.HasTrend,
		}
	}

	return domain.DashboardMetricsEntity{
		Metrics: domainMetrics,
	}, nil
}

// validateRequest 验证请求参数
func (s *dashboardMetricsService) validateRequest(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}) error {
	// 验证日期格式
	if req.StartDate != "" {
		if _, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			return err
		}
	}
	if req.EndDate != "" {
		if _, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			return err
		}
	}
	return nil
}

// setDefaultDates 设置默认日期
func (s *dashboardMetricsService) setDefaultDates(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}) {
	if req.StartDate == "" {
		// 默认过去7天
		req.StartDate = time.Now().AddDate(0, 0, -7).Format("2006-01-02")
	}
	if req.EndDate == "" {
		// 默认今天
		req.EndDate = time.Now().Format("2006-01-02")
	}
}

// validateQueryParams 验证查询参数
func (s *dashboardMetricsService) validateQueryParams(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}) error {
	// 验证类型参数
	if req.Type != "" && req.Type != "1" && req.Type != "2" && req.Type != "3" {
		return nil
	}

	// 验证渠道分类
	if req.Category != "" && req.Category != "alipay" && req.Category != "wechat" && req.Category != "other" {
		return nil
	}

	return nil
}

// getDailyStatsWithScope 获取带权限控制的每日统计数据
func (s *dashboardMetricsService) getDailyStatsWithScope(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}, userID int, userRole int, scope string, departmentUsers []uint64) (domain.DailyStatsData, error) {
	// 构建查询
	query := s.db.Model(&model.AdSlotPlanDailyStats{})
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 添加用户ID筛选
	var queryUserID int
	if req.UserID != "" {
		userIDInt, err := strconv.Atoi(req.UserID)
		if err == nil && userIDInt > 0 {
			queryUserID = userIDInt
		}
	} else {
		// 根据角色决定是否添加用户ID筛选
		if userRole != 2 && userRole != 3 { // 非管理员角色
			queryUserID = userID
		}
	}
	if queryUserID > 0 {
		query = query.Where("user_id = ?", queryUserID)
	}

	// 应用数据范围限制
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 根据合作类型添加筛选
	if req.Type != "" {
		var cooperationType string
		switch req.Type {
		case "1":
			cooperationType = "traffic"
		case "2":
			cooperationType = "cps"
		case "3":
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int

			s.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("media_id IN (?)", mediaIDs)
			}
		}
	}

	// 添加媒体ID筛选
	if req.MediaID != "" {
		query = query.Where("media_id = ?", req.MediaID)
	}

	// 添加计划ID筛选
	if req.PlanID != "" {
		query = query.Where("plan_id = ?", req.PlanID)
	}

	// 添加产品ID筛选
	if req.ProductID != "" {
		query = query.Where("ad_product_id = ?", req.ProductID)
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			s.db.Model(&model.AdSlots{}).
				Where("type IN (?)", slotTypes).
				Select("id").
				Scan(&slotIDs)

			if len(slotIDs) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIDs)
			}
		}
	}

	// 聚合查询
	var result struct {
		Orders           int     `json:"orders"`
		EstimatedRevenue float64 `json:"estimated_revenue"`
		SettledRevenue   float64 `json:"settled_revenue"`
		Cost             float64 `json:"cost"`
		EstimatedProfit  float64 `json:"estimated_profit"`
		SettledProfit    float64 `json:"settled_profit"`
		ClickPV          int     `json:"click_pv"`
		ClickUV          int     `json:"click_uv"`
	}

	err := query.Select(`
		SUM(bd_orders) as orders,
		SUM(bd_revenue) as estimated_revenue,
		SUM(bd_settle_revenue) as settled_revenue,
		SUM(cost) as cost,
		SUM(bd_profit) as estimated_profit,
		SUM(bd_settle_profit) as settled_profit,
		SUM(elm_click_pv) as click_pv,
		SUM(elm_click_uv) as click_uv
	`).Scan(&result).Error

	if err != nil {
		return domain.DailyStatsData{}, err
	}

	return domain.DailyStatsData{
		Orders:           result.Orders,
		EstimatedRevenue: result.EstimatedRevenue,
		SettledRevenue:   result.SettledRevenue,
		Cost:             result.Cost,
		EstimatedProfit:  result.EstimatedProfit,
		SettledProfit:    result.SettledProfit,
		ClickPV:          result.ClickPV,
		ClickUV:          result.ClickUV,
	}, nil
}

// calculateMetrics 计算指标
func (s *dashboardMetricsService) calculateMetrics(stats domain.DailyStatsData, req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}, userRole int) map[string]struct {
	Label      string
	Value      float64
	Change     float64
	WeekChange float64
	HasTrend   bool
} {
	metrics := make(map[string]struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	})

	// 判断是否是CPS合作类型
	isCpsType := req.Type == "2"

	// 页面访问次数
	metrics["click_pv"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "页面访问次数",
		Value:    float64(stats.ClickPV),
		HasTrend: true,
	}

	// 页面访问人数
	metrics["click_uv"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "页面访问人数",
		Value:    float64(stats.ClickUV),
		HasTrend: true,
	}

	// 订单量
	metrics["orders"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "订单量",
		Value:    float64(stats.Orders),
		HasTrend: true,
	}

	label := "预估佣金"
	if !isCpsType {
		label = "预估收入"
	}
	// 预估佣金/收入
	metrics["estimated_revenue"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    label,
		Value:    stats.EstimatedRevenue,
		HasTrend: true,
	}

	// 结算佣金/收入
	label = "结算佣金"
	if !isCpsType {
		label = "结算收入"
	}
	metrics["settled_revenue"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    label,
		Value:    stats.SettledRevenue,
		HasTrend: true,
	}

	// 成本
	metrics["cost"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "成本",
		Value:    stats.Cost,
		HasTrend: true,
	}

	// 预估利润
	metrics["estimated_profit"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "预估利润",
		Value:    stats.EstimatedProfit,
		HasTrend: true,
	}

	// 结算利润
	metrics["settled_profit"] = struct {
		Label      string
		Value      float64
		Change     float64
		WeekChange float64
		HasTrend   bool
	}{
		Label:    "结算利润",
		Value:    stats.SettledProfit,
		HasTrend: true,
	}

	return metrics
}

// calculateChanges 计算变化率
func (s *dashboardMetricsService) calculateChanges(metrics map[string]struct {
	Label      string
	Value      float64
	Change     float64
	WeekChange float64
	HasTrend   bool
}, req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}, userID int, userRole int) {
	// 计算环比时间范围
	startDate, _ := time.Parse("2006-01-02", req.StartDate)
	endDate, _ := time.Parse("2006-01-02", req.EndDate)
	duration := endDate.Sub(startDate)
	dayCount := int(duration.Hours()/24) + 1

	// 上一个周期
	prevStartDate := startDate.AddDate(0, 0, -dayCount).Format("2006-01-02")
	prevEndDate := endDate.AddDate(0, 0, -dayCount).Format("2006-01-02")

	// 获取上一个周期的数据
	prevReq := *req
	prevReq.StartDate = prevStartDate
	prevReq.EndDate = prevEndDate
	scope, departmentUsers, _ := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	prevStats, _ := s.getDailyStatsWithScope(&prevReq, userID, userRole, scope, departmentUsers)

	// 同比时间范围（去年同期）
	weekYearStartDate := startDate.AddDate(-1, 0, 0).Format("2006-01-02")
	weekYearEndDate := endDate.AddDate(-1, 0, 0).Format("2006-01-02")

	// 获取同比时间范围的数据
	weekYearReq := *req
	weekYearReq.StartDate = weekYearStartDate
	weekYearReq.EndDate = weekYearEndDate
	weekYearStats, _ := s.getDailyStatsWithScope(&weekYearReq, userID, userRole, scope, departmentUsers)

	// 计算环比变化
	if metrics["click_pv"].Value > 0 && float64(prevStats.ClickPV) > 0 {
		metrics["click_pv"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["click_pv"].Label,
			Value:      metrics["click_pv"].Value,
			Change:     calculateChange(metrics["click_pv"].Value, float64(prevStats.ClickPV)),
			WeekChange: calculateChange(metrics["click_pv"].Value, float64(weekYearStats.ClickPV)),
			HasTrend:   metrics["click_pv"].HasTrend,
		}
	}

	if metrics["click_uv"].Value > 0 && float64(prevStats.ClickUV) > 0 {
		metrics["click_uv"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["click_uv"].Label,
			Value:      metrics["click_uv"].Value,
			Change:     calculateChange(metrics["click_uv"].Value, float64(prevStats.ClickUV)),
			WeekChange: calculateChange(metrics["click_uv"].Value, float64(weekYearStats.ClickUV)),
			HasTrend:   metrics["click_uv"].HasTrend,
		}
	}

	// 订单量变化
	if metrics["orders"].Value > 0 && float64(prevStats.Orders) > 0 {
		metrics["orders"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["orders"].Label,
			Value:      metrics["orders"].Value,
			Change:     calculateChange(metrics["orders"].Value, float64(prevStats.Orders)),
			WeekChange: calculateChange(metrics["orders"].Value, float64(weekYearStats.Orders)),
			HasTrend:   metrics["orders"].HasTrend,
		}
	}

	// 预估佣金/收入变化
	if metrics["estimated_revenue"].Value > 0 && prevStats.EstimatedRevenue > 0 {
		metrics["estimated_revenue"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["estimated_revenue"].Label,
			Value:      metrics["estimated_revenue"].Value,
			Change:     calculateChange(metrics["estimated_revenue"].Value, prevStats.EstimatedRevenue),
			WeekChange: calculateChange(metrics["estimated_revenue"].Value, weekYearStats.EstimatedRevenue),
			HasTrend:   metrics["estimated_revenue"].HasTrend,
		}
	}

	// 结算佣金/收入变化
	if metrics["settled_revenue"].Value > 0 && prevStats.SettledRevenue > 0 {
		metrics["settled_revenue"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["settled_revenue"].Label,
			Value:      metrics["settled_revenue"].Value,
			Change:     calculateChange(metrics["settled_revenue"].Value, prevStats.SettledRevenue),
			WeekChange: calculateChange(metrics["settled_revenue"].Value, weekYearStats.SettledRevenue),
			HasTrend:   metrics["settled_revenue"].HasTrend,
		}
	}

	// 成本变化
	if metrics["cost"].Value > 0 && prevStats.Cost > 0 {
		metrics["cost"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["cost"].Label,
			Value:      metrics["cost"].Value,
			Change:     calculateChange(metrics["cost"].Value, prevStats.Cost),
			WeekChange: calculateChange(metrics["cost"].Value, weekYearStats.Cost),
			HasTrend:   metrics["cost"].HasTrend,
		}
	}

	// 预估利润变化
	if metrics["estimated_profit"].Value > 0 && prevStats.EstimatedProfit > 0 {
		metrics["estimated_profit"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["estimated_profit"].Label,
			Value:      metrics["estimated_profit"].Value,
			Change:     calculateChange(metrics["estimated_profit"].Value, prevStats.EstimatedProfit),
			WeekChange: calculateChange(metrics["estimated_profit"].Value, weekYearStats.EstimatedProfit),
			HasTrend:   metrics["estimated_profit"].HasTrend,
		}
	}

	// 结算利润变化
	if metrics["settled_profit"].Value > 0 && prevStats.SettledProfit > 0 {
		metrics["settled_profit"] = struct {
			Label      string
			Value      float64
			Change     float64
			WeekChange float64
			HasTrend   bool
		}{
			Label:      metrics["settled_profit"].Label,
			Value:      metrics["settled_profit"].Value,
			Change:     calculateChange(metrics["settled_profit"].Value, prevStats.SettledProfit),
			WeekChange: calculateChange(metrics["settled_profit"].Value, weekYearStats.SettledProfit),
			HasTrend:   metrics["settled_profit"].HasTrend,
		}
	}
}

// calculateChange 计算变化率
func calculateChange(current, previous float64) float64 {
	if previous == 0 {
		return 0
	}
	return (current - previous) / previous
}
