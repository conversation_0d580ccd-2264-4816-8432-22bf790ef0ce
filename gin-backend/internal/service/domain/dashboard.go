package domain

// 渠道分类常量（仪表盘专用）
const (
	DashboardCategoryAlipay = "alipay" // 支付宝
	DashboardCategoryWechat = "wechat" // 微信
	DashboardCategoryOther  = "other"  // 其他
)

// 指标类型常量
const (
	DashboardMetricClickPV          = "click_pv"
	DashboardMetricClickUV          = "click_uv"
	DashboardMetricOrders           = "orders"
	DashboardMetricEstimatedRevenue = "estimated_commission"
	DashboardMetricSettledRevenue   = "settled_commission"
	DashboardMetricCost             = "cost"
	DashboardMetricEstimatedProfit  = "estimated_profit"
	DashboardMetricSettledProfit    = "settled_profit"
)

// DailyStatsData 每日统计数据
type DailyStatsData struct {
	Orders           int     // 订单量
	EstimatedRevenue float64 // 预估佣金
	SettledRevenue   float64 // 结算佣金
	Cost             float64 // 成本
	EstimatedProfit  float64 // 预估利润
	SettledProfit    float64 // 结算利润
	ClickPV          int     // 页面访问次数
	ClickUV          int     // 页面访问人数
}

// DashboardMetricsParam 仪表盘指标查询参数
type DashboardMetricsParam struct {
	StartDate string // 开始日期，YYYY-MM-DD格式
	EndDate   string // 结束日期，YYYY-MM-DD格式
	UserID    string // 用户ID
	MediaID   string // 媒体ID
	PlanID    string // 计划ID
	ProductID string // 产品ID
	Type      string // 合作类型：1:普通投放 2:CPS合作 3:灯火
	Category  string // 渠道分类：alipay:支付宝 wechat:微信 other:其他
}

// MetricItem 指标项
type MetricItem struct {
	Label      string  // 指标名称
	Value      float64 // 指标值
	Change     float64 // 环比变化（小数形式，如0.1表示10%）
	WeekChange float64 // 周同比变化（小数形式）
	HasTrend   bool    // 是否支持趋势分析
}

// DashboardMetricsEntity 仪表盘指标实体
type DashboardMetricsEntity struct {
	Metrics map[string]MetricItem
}

// FilterOptionsEntity 筛选选项实体
type FilterOptionsEntity struct {
	ShowMediaList bool
	MediaList     []MediaItem
	ProductList   []ProductItem
}

// MediaItem 媒体项
type MediaItem struct {
	ID   string
	Name string
}

// ProductItem 产品项
type ProductItem struct {
	ID   string
	Name string
}

// ProductsEntity 产品列表实体
type ProductsEntity struct {
	List []ProductItem
}

// DashboardTrendParam 趋势数据参数
type DashboardTrendParam struct {
	UserID    int64
	MediaID   int64
	PlanID    int64
	ProductID int64
	Metric    string
	StartDate string
	EndDate   string
	Type      int
	Category  string
}

// TrendEntity 趋势数据实体
type TrendEntity struct {
	TimePoints []string
	Current    []float64
	Previous   []float64
	SamePeriod []float64
	Labels     struct {
		Current    string
		Previous   string
		SamePeriod string
	}
}

// DashboardPlanStatsParam 分计划统计参数
type DashboardPlanStatsParam struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}

// PlanStatsItem 分计划统计项
type PlanStatsItem struct {
	PlanID        string
	PlanCode      string
	MediaName     string
	ProductID     string
	Clicks        int
	Cost          float64
	Orders        int
	Revenue       float64
	Profit        float64
	ROI           float64
	SettledProfit float64
	PV            int
	UV            int
}

// PlanStatsEntity 分计划统计实体
type PlanStatsEntity struct {
	List []PlanStatsItem
}

// DashboardMediaListParam 仪表盘媒体列表参数
type DashboardMediaListParam struct {
	UserID   string
	Type     string
	Category string
}

// MediaListEntity 媒体列表实体
type MediaListEntity struct {
	List []MediaItem
}

// DashboardPlansParam 获取投放计划列表参数
type DashboardPlansParam struct {
	MediaAccountID string
}

// DashboardPlanItem 仪表盘计划项
type DashboardPlanItem struct {
	ID   string
	Name string
}

// PlansEntity 计划列表实体
type PlansEntity struct {
	List []DashboardPlanItem
}

// DynamicFilterParam 动态筛选选项参数
type DynamicFilterParam struct {
	Type            string
	UserID          string
	MediaAccountID  string
	CooperationType string
	Category        string
}

// DashboardFilterItem 筛选项
type DashboardFilterItem struct {
	ID   string
	Name string
}

// DynamicFilterEntity 动态筛选选项实体
type DynamicFilterEntity struct {
	List []DashboardFilterItem
}

// RegionParam 地域分析参数
type RegionParam struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
	Metric    string
}

// RegionDataItem 地域数据项
type RegionDataItem struct {
	Name  string
	Value float64
}

// RegionEntity 地域分析实体
type RegionEntity struct {
	Regions []RegionDataItem
}

// OrderTypeParam 订单类型分析参数
type OrderTypeParam struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
	Metric    string
}

// OrderTypeDataItem 订单类型数据项
type OrderTypeDataItem struct {
	Name  string
	Value float64
}

// OrderTypeEntity 订单类型分析实体
type OrderTypeEntity struct {
	OrderTypes []OrderTypeDataItem
}

// DailyStatsEntity 每日统计数据实体
type DailyStatsEntity struct {
	Orders           int
	EstimatedRevenue float64
	SettledRevenue   float64
	Cost             float64
	EstimatedProfit  float64
	SettledProfit    float64
	ClickPV          int
	ClickUV          int
}
