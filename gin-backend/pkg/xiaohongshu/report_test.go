package xiaohongshu

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// TestGetCreativeReport 测试获取创意报表
func TestGetCreativeReport(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			t.<PERSON><PERSON><PERSON>("期望POST请求，实际为%s", r.Method)
		}

		// 验证Content-Type
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望Content-Type为application/json，实际为%s", r.Header.Get("Content-Type"))
		}

		// 验证Access-Token
		if r.Header.Get("Access-Token") == "" {
			t.<PERSON><PERSON><PERSON>("缺少Access-Token头")
		}

		// 验证Trace-ID
		if r.Header.Get("Trace-ID") == "" {
			t.<PERSON><PERSON>("缺少Trace-ID头")
		}

		// 返回模拟响应
		response := ReportResponse{
			Data: struct {
				DataList        []ReportData `json:"data_list"`
				AggregationData ReportData   `json:"aggregation_data"`
				TotalCount      int64        `json:"total_count"`
			}{
				DataList: []ReportData{
					{
						CampaignID:   "123456",
						CampaignName: "测试计划",
						Fee:          "100.50",
						Impression:   "1000",
						Click:        "50",
						CTR:          "5.0",
					},
				},
				AggregationData: ReportData{
					Fee:        "100.50",
					Impression: "1000",
					Click:      "50",
					CTR:        "5.0",
				},
				TotalCount: 1,
			},
			RequestID: "test-request-id",
			Code:      0,
			Success:   true,
			Msg:       "成功",
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建测试客户端
	client := &Client{
		config: &Config{
			AppId:  "test_app_id",
			Secret: "test_secret",
			IsProd: false,
		},
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 创建测试请求
	request := &ReportRequest{
		AdvertiserID: 1234567890,
		StartDate:    "2024-01-01",
		EndDate:      "2024-01-01",
		TimeUnit:     TimeUnitSummary,
		PageNum:      1,
		PageSize:     20,
	}

	// 直接测试requestCreativeReport方法，使用测试服务器URL
	reportResp, err := client.requestCreativeReport(server.URL, "test_access_token", request)
	if err != nil {
		t.Fatalf("获取创意报表失败: %v", err)
	}

	if !reportResp.Success {
		t.Errorf("期望成功，实际失败: %s", reportResp.Msg)
	}

	if len(reportResp.Data.DataList) != 1 {
		t.Errorf("期望数据列表长度为1，实际为%d", len(reportResp.Data.DataList))
	}

	if reportResp.Data.DataList[0].CampaignID != "123456" {
		t.Errorf("期望计划ID为123456，实际为%s", reportResp.Data.DataList[0].CampaignID)
	}
}

// TestReportBuilder 测试报表构建器
func TestReportBuilder(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"

	// 测试基础构建
	builder := NewReportBuilder(advertiserID, startDate, endDate)
	request := builder.Build()

	if request.AdvertiserID != advertiserID {
		t.Errorf("期望广告主ID为%d，实际为%d", advertiserID, request.AdvertiserID)
	}

	if request.StartDate != startDate {
		t.Errorf("期望开始日期为%s，实际为%s", startDate, request.StartDate)
	}

	if request.EndDate != endDate {
		t.Errorf("期望结束日期为%s，实际为%s", endDate, request.EndDate)
	}

	// 测试链式调用
	request2 := NewReportBuilder(advertiserID, startDate, endDate).
		WithTimeUnit(TimeUnitDay).
		WithPagination(2, 50).
		WithSort("fee", SortDesc).
		WithDataCaliber(DataCaliberClickTime).
		Build()

	if request2.TimeUnit != TimeUnitDay {
		t.Errorf("期望时间维度为%s，实际为%s", TimeUnitDay, request2.TimeUnit)
	}

	if request2.PageNum != 2 {
		t.Errorf("期望页数为2，实际为%d", request2.PageNum)
	}

	if request2.PageSize != 50 {
		t.Errorf("期望页大小为50，实际为%d", request2.PageSize)
	}

	if request2.SortColumn != "fee" {
		t.Errorf("期望排序字段为fee，实际为%s", request2.SortColumn)
	}

	if request2.Sort != SortDesc {
		t.Errorf("期望排序方式为%s，实际为%s", SortDesc, request2.Sort)
	}

	if request2.DataCaliber != DataCaliberClickTime {
		t.Errorf("期望数据归因类型为%d，实际为%d", DataCaliberClickTime, request2.DataCaliber)
	}
}

// TestCreateDateRangeReport 测试创建日期范围报表
func TestCreateDateRangeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	days := 7

	request := CreateDateRangeReport(advertiserID, days)

	if request.AdvertiserID != advertiserID {
		t.Errorf("期望广告主ID为%d，实际为%d", advertiserID, request.AdvertiserID)
	}

	if request.TimeUnit != TimeUnitDay {
		t.Errorf("期望时间维度为%s，实际为%s", TimeUnitDay, request.TimeUnit)
	}

	if request.PageSize != 100 {
		t.Errorf("期望页大小为100，实际为%d", request.PageSize)
	}

	// 验证日期范围
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -days).Format("2006-01-02")

	if request.StartDate != startDate {
		t.Errorf("期望开始日期为%s，实际为%s", startDate, request.StartDate)
	}

	if request.EndDate != endDate {
		t.Errorf("期望结束日期为%s，实际为%s", endDate, request.EndDate)
	}
}

// TestCreateYesterdayReport 测试创建昨日报表
func TestCreateYesterdayReport(t *testing.T) {
	advertiserID := int64(1234567890)
	request := CreateYesterdayReport(advertiserID)

	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	if request.StartDate != yesterday {
		t.Errorf("期望开始日期为%s，实际为%s", yesterday, request.StartDate)
	}

	if request.EndDate != yesterday {
		t.Errorf("期望结束日期为%s，实际为%s", yesterday, request.EndDate)
	}

	if request.TimeUnit != TimeUnitSummary {
		t.Errorf("期望时间维度为%s，实际为%s", TimeUnitSummary, request.TimeUnit)
	}
}

// TestCreateFilters 测试创建过滤条件
func TestCreateFilters(t *testing.T) {
	// 测试按消费金额过滤
	feeFilter := CreateFilterByFee(FilterOperatorGreaterThan, []string{"100"})
	if feeFilter.Column != "fee" {
		t.Errorf("期望过滤字段为fee，实际为%s", feeFilter.Column)
	}

	if feeFilter.Operator != FilterOperatorGreaterThan {
		t.Errorf("期望操作符为%s，实际为%s", FilterOperatorGreaterThan, feeFilter.Operator)
	}

	// 测试按创意ID过滤
	creativityFilter := CreateFilterByCreativityID([]string{"123", "456"})
	if creativityFilter.Column != "creativityId" {
		t.Errorf("期望过滤字段为creativityId，实际为%s", creativityFilter.Column)
	}

	if creativityFilter.Operator != FilterOperatorIn {
		t.Errorf("期望操作符为%s，实际为%s", FilterOperatorIn, creativityFilter.Operator)
	}

	if len(creativityFilter.Values) != 2 {
		t.Errorf("期望过滤值数量为2，实际为%d", len(creativityFilter.Values))
	}
}

// TestGenerateTraceID 测试生成追踪ID
func TestGenerateTraceID(t *testing.T) {
	traceID1 := generateTraceID()
	traceID2 := generateTraceID()

	// 验证长度（16字节 = 32个十六进制字符）
	if len(traceID1) != 32 {
		t.Errorf("期望追踪ID长度为32，实际为%d", len(traceID1))
	}

	// 验证不同调用生成不同ID
	if traceID1 == traceID2 {
		t.Errorf("两次生成的追踪ID不应该相同")
	}
}
