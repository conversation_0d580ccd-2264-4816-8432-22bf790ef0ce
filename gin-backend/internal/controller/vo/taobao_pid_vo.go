package vo

import (
	"time"

	"gin-backend/internal/service/domain"
)

// TaobaoPidItem 淘联链接模型 (用于展示)
type TaobaoPidItem struct {
	ID           int64     `json:"id"`
	AccountName  string     `json:"account_name"`    // 联盟账号
	MemberID     int64     `json:"member_id"`       // 会员ID
	ZoneName     string     `json:"zone_name"`       // 推广位名称
	PID          string     `json:"pid"`             // 推广位ID
	IsUsed       int        `json:"is_used"`         // 是否使用: 0-未使用, 1-已使用
	UsedAt       time.Time `json:"used_at"`         // 使用时间
	UsedBy       int64     `json:"used_by"`         // 使用者ID
	AdSlotPlanID int64     `json:"ad_slot_plan_id"` // 关联计划ID
	AdCreativeID int64     `json:"ad_creative_id"`  // 关联创意ID
	ActType      int       `json:"act_type"`        // 活动类型: 1-飞猪, 2-福利购
	CreatedAt    time.Time  `json:"created_at"`      // 创建时间
	UpdatedAt    time.Time  `json:"updated_at"`      // 更新时间
	DeletedAt    time.Time `json:"deleted_at"`      // 删除时间
}

// TaobaoPidListReq 淘联链接列表请求
type TaobaoPidListReq struct {
	Page    int    `json:"page" form:"page"`         // 页码
	Size    int    `json:"size" form:"size"`         // 每页数量
	Keyword string `json:"keyword" form:"keyword"`   // 关键词搜索
	IsUsed  int    `json:"is_used" form:"is_used"`   // 是否已使用: -1-全部, 0-未使用, 1-已使用
	ActType int   `json:"act_type" form:"act_type"` // 活动类型
	Sort    string `json:"sort" form:"sort"`         // 排序
}

// TaobaoPidCreateReq 创建淘联链接请求
type TaobaoPidCreateReq struct {
	ZoneName string `json:"zone_name" binding:"required"` // 推广位名称
	PID      string `json:"pid" binding:"required"`       // 推广位ID
	ActType  int   `json:"act_type" binding:"required"`  // 活动类型
}

// TaobaoPidUpdateReq 更新淘联链接请求
type TaobaoPidUpdateReq struct {
	ID       int64 `json:"id" binding:"required"`        // ID
	ZoneName string `json:"zone_name" binding:"required"` // 推广位名称
	PID      string `json:"pid" binding:"required"`       // 推广位ID
	ActType  int   `json:"act_type" binding:"required"`  // 活动类型
}

// TaobaoPidListResp 淘联链接列表响应
type TaobaoPidListResp struct {
	Items       []TaobaoPidItem `json:"items"`        // 列表数据
	Total       int64            `json:"total"`        // 总数
	UsedCount   int64            `json:"used_count"`   // 已使用数量
	UnusedCount int64            `json:"unused_count"` // 未使用数量
}

// ToTaobaoPidListParam 将VO请求转换为domain参数
func (req TaobaoPidListReq) ToTaobaoPidListParam() domain.TaobaoPidListParam {
	return domain.TaobaoPidListParam{
		Page:    req.Page,
		Size:    req.Size,
		Keyword: req.Keyword,
		IsUsed:  req.IsUsed,
		ActType: req.ActType,
		Sort:    req.Sort,
	}
}

// ToTaobaoPidCreateParam 将VO请求转换为domain参数
func (req TaobaoPidCreateReq) ToTaobaoPidCreateParam() domain.TaobaoPidCreateParam {
	return domain.TaobaoPidCreateParam{
		ZoneName: req.ZoneName,
		PID:      req.PID,
		ActType:  req.ActType,
	}
}

// ToTaobaoPidUpdateParam 将VO请求转换为domain参数
func (req TaobaoPidUpdateReq) ToTaobaoPidUpdateParam() domain.TaobaoPidUpdateParam {
	return domain.TaobaoPidUpdateParam{
		ID:       req.ID,
		ZoneName: req.ZoneName,
		PID:      req.PID,
		ActType:  req.ActType,
	}
}

// TaobaoPidListRespFromEntity 将domain实体转换为VO响应
func TaobaoPidListRespFromEntity(entity domain.TaobaoPidListEntity) TaobaoPidListResp {
	items := make([]TaobaoPidItem, 0, len(entity.Items))
	for _, item := range entity.Items {
		items = append(items, TaobaoPidItemFromEntity(item))
	}

	return TaobaoPidListResp{
		Items:       items,
		Total:       entity.Total,
		UsedCount:   entity.UsedCount,
		UnusedCount: entity.UnusedCount,
	}
}

// TaobaoPidItemFromEntity 将domain实体转换为VO项
func TaobaoPidItemFromEntity(entity domain.TaobaoPidEntity) TaobaoPidItem {
	return TaobaoPidItem{
		ID:           entity.ID,
		AccountName:  entity.AccountName,
		MemberID:     entity.MemberID,
		ZoneName:     entity.ZoneName,
		PID:          entity.PID,
		IsUsed:       entity.IsUsed,
		UsedAt:       entity.UsedAt,
		UsedBy:       entity.UsedBy,
		AdSlotPlanID: entity.AdSlotPlanID,
		AdCreativeID: entity.AdCreativeID,
		ActType:      entity.ActType,
		CreatedAt:    entity.CreatedAt,
		UpdatedAt:    entity.UpdatedAt,
		DeletedAt:    entity.DeletedAt,
	}
}
