package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type WorkModeController struct {
	workModeService *service.WorkModeService
	userService     *service.UserService
}

func NewWorkModeController() *WorkModeController {
	return &WorkModeController{
		workModeService: service.NewWorkModeService(),
		userService:     service.NewUserService(),
	}
}

// GetUserModes 获取用户可用的工作模式
// @Summary 获取用户可用的工作模式
// @Description 获取当前用户可以访问的工作模式列表
// @Tags 工作模式
// @Accept json
// @Produce json
// @Success 200 {object} vo.GetUserModesResp
// @Router /api/work-modes [get]
func (c *WorkModeController) GetUserModes(ctx *gin.Context) {
	// 获取当前用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    http.StatusUnauthorized,
			"message": "未登录或登录已过期",
			"data":    nil,
		})
		return
	}

	// 获取用户详细信息
	userEntity, err := c.userService.GetByID(ctx.Request.Context(), userInfo.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取用户信息失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 获取用户可用模式
	modesData, err := c.workModeService.GetUserModes(ctx.Request.Context(), userEntity)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 转换为VO并返回
	response := vo.NewGetUserModesRespFromDomain(modesData)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "获取成功",
		"data":    response,
	})
}

// SwitchWorkMode 切换工作模式
// @Summary 切换工作模式
// @Description 切换到指定的工作模式，返回对应的菜单和权限
// @Tags 工作模式
// @Accept json
// @Produce json
// @Param request body vo.SwitchWorkModeReq true "切换模式请求"
// @Success 200 {object} vo.SwitchWorkModeResp
// @Router /api/work-modes/switch [post]
func (c *WorkModeController) SwitchWorkMode(ctx *gin.Context) {
	var req vo.SwitchWorkModeReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 获取当前用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    http.StatusUnauthorized,
			"message": "未登录或登录已过期",
			"data":    nil,
		})
		return
	}

	// 获取用户详细信息
	userEntity, err := c.userService.GetByID(ctx.Request.Context(), userInfo.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取用户信息失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 转换参数
	param := req.ConvertToDomainWorkModeSwitchParam()

	// 切换工作模式
	workModeData, err := c.workModeService.SwitchWorkMode(ctx.Request.Context(), userEntity, param.ModeCode)
	if err != nil {
		ctx.JSON(http.StatusForbidden, gin.H{
			"code":    http.StatusForbidden,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 转换为VO并返回
	response := vo.NewSwitchWorkModeRespFromDomain(workModeData)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "切换成功",
		"data":    response,
	})
}

// CheckModePermission 检查指定模式的权限
// @Summary 检查指定模式的权限
// @Description 检查用户是否有权限访问指定的工作模式
// @Tags 工作模式
// @Accept json
// @Produce json
// @Param mode query string true "工作模式" Enums(traffic, ad)
// @Success 200 {object} vo.CheckModePermissionResp
// @Router /api/work-modes/check [get]
func (c *WorkModeController) CheckModePermission(ctx *gin.Context) {
	mode := ctx.Query("mode")
	if mode == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "工作模式参数不能为空",
			"data":    nil,
		})
		return
	}

	// 创建请求参数
	req := vo.CheckModePermissionReq{ModeCode: mode}

	// 获取当前用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{
			"code":    http.StatusUnauthorized,
			"message": "未登录或登录已过期",
			"data":    nil,
		})
		return
	}

	// 获取用户详细信息
	userEntity, err := c.userService.GetByID(ctx.Request.Context(), userInfo.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "获取用户信息失败: " + err.Error(),
			"data":    nil,
		})
		return
	}

	// 转换参数
	param := req.ConvertToDomainCheckModePermissionParam()

	// 检查权限
	hasPermission, err := c.workModeService.CheckModePermission(ctx.Request.Context(), userEntity, param.ModeCode)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": err.Error(),
			"data":    nil,
		})
		return
	}

	// 转换返回结果
	response := vo.NewCheckModePermissionRespFromDomain(hasPermission)

	ctx.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "检查成功",
		"data":    response,
	})
}
