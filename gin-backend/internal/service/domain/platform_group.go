package domain

import (
	"time"
)

// PlatformGroupEntity 广告组实体
type PlatformGroupEntity struct {
	ID           int64     // 主键ID
	PlatformType string    // 平台类型
	AgentID      int64     // 代理商ID
	MediaID      int64     // 媒体ID
	DataID       string    // 平台数据ID
	PlanID       int64     // 计划ID
	Name         string    // 广告组名称
	Remark       string    // 备注
	CreatedAt    time.Time // 创建时间
	UpdatedAt    time.Time // 更新时间
}

// PlatformGroupQueryParam 广告组查询参数
type PlatformGroupQueryParam struct {
	Page             int    // 页码
	PageSize         int    // 每页数量
	AgentID          int64  // 代理商ID
	MediaID          int64  // 媒体ID
	MarketTargetName string // 营销目标名称
	PlanKeyword      string // 计划关键词
	Keyword          string // 广告组关键词
	WithPlan         bool   // 是否加载计划信息
	PlatformType     string // 平台类型
}

// PlatformGroupItemEntity 广告组列表项实体
type PlatformGroupItemEntity struct {
	ID           int64     // 主键ID
	Name         string    // 广告组名称
	DataID       string    // 平台数据ID
	PlatformType string    // 平台类型
	PlanName     string    // 计划名称
	MediaName    string    // 媒体名称
	AgentName    string    // 代理商名称
	Remark       string    // 备注
	CreatedAt    time.Time // 创建时间
	UpdatedAt    time.Time // 更新时间
}

// PlatformGroupCreateEntity 创建广告组实体
type PlatformGroupCreateEntity struct {
	Name         string // 广告组名称
	PlanID       int64  // 计划ID
	PlatformType string // 平台类型
	AgentID      int64  // 代理商ID
	MediaID      int64  // 媒体ID
	DataID       string // 平台数据ID
	Remark       string // 备注
}

// PlatformGroupUpdateEntity 更新广告组实体
type PlatformGroupUpdateEntity struct {
	ID           int64  // 主键ID
	Name         string // 广告组名称
	Remark       string // 备注
	PlatformType string // 平台类型
}

// PlatformGroupDeleteEntity 删除广告组实体
type PlatformGroupDeleteEntity struct {
	ID int64 // 主键ID
}

// PlatformGroupPageEntity 广告组分页数据实体
type PlatformGroupPageEntity struct {
	List  []PlatformGroupItemEntity // 广告组列表
	Total int64                     // 总数量
}

// PlatformGroupIDEntity 广告组ID实体
type PlatformGroupIDEntity struct {
	ID int64 // 主键ID
}
