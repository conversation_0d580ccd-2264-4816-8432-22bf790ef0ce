-- ========================================
-- 分配超级管理员权限给用户ID为1的用户
-- ========================================

-- 显示当前数据库
SELECT DATABASE() as '当前数据库';

-- 检查用户表结构
SELECT '=== 检查用户表结构 ===' as '信息';
SELECT 
    column_name as '字段名',
    data_type as '类型',
    is_nullable as '允许空',
    column_default as '默认值'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'users'
ORDER BY ordinal_position;

-- 检查是否存在user_roles表
SELECT '=== 检查用户角色关联表 ===' as '信息';
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ user_roles表存在'
        ELSE '✗ user_roles表不存在'
    END as 'user_roles表检查'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'user_roles';

-- 如果user_roles表存在，显示其结构
SELECT 
    column_name as '字段名',
    data_type as '类型',
    is_nullable as '允许空',
    column_default as '默认值'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'user_roles'
ORDER BY ordinal_position;

-- 检查用户ID为1的用户是否存在
SELECT '=== 检查目标用户 ===' as '信息';
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 用户ID为1的用户存在'
        ELSE '✗ 用户ID为1的用户不存在'
    END as '用户检查'
FROM users WHERE id = 1;

-- 显示用户ID为1的用户信息
SELECT 
    id as '用户ID',
    username as '用户名',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'role_id')
        THEN (SELECT CONCAT('role_id: ', COALESCE(role_id, 'NULL')) FROM users WHERE id = 1)
        ELSE '用户表中没有role_id字段'
    END as '当前角色信息'
FROM users WHERE id = 1;

-- 检查超级管理员角色是否存在
SELECT '=== 检查超级管理员角色 ===' as '信息';
SELECT 
    id as '角色ID',
    name as '角色名称',
    code as '角色编码'
FROM roles WHERE code = 'super_admin';

-- ========================================
-- 开始分配权限
-- ========================================

-- 方案1：如果users表中有role_id字段，直接更新
UPDATE users 
SET role_id = (SELECT id FROM roles WHERE code = 'super_admin')
WHERE id = 1 
AND EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = DATABASE() 
    AND table_name = 'users' 
    AND column_name = 'role_id'
);

-- 方案2：如果存在user_roles表，插入关联记录
INSERT IGNORE INTO user_roles (user_id, role_id, created_at)
SELECT 
    1,
    (SELECT id FROM roles WHERE code = 'super_admin'),
    NOW()
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'user_roles'
)
AND EXISTS (
    SELECT 1 FROM users WHERE id = 1
)
AND EXISTS (
    SELECT 1 FROM roles WHERE code = 'super_admin'
);

-- ========================================
-- 验证分配结果
-- ========================================

SELECT '=== 权限分配结果 ===' as '信息';

-- 检查users表中的role_id（如果存在）
SELECT 
    u.id as '用户ID',
    u.username as '用户名',
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'role_id')
        THEN (
            SELECT CONCAT(r.name, ' (', r.code, ')') 
            FROM roles r 
            WHERE r.id = u.role_id
        )
        ELSE '用户表中没有role_id字段'
    END as '分配的角色'
FROM users u WHERE u.id = 1;

-- 检查user_roles表中的关联（如果存在）
SELECT 
    ur.user_id as '用户ID',
    u.username as '用户名',
    r.name as '角色名称',
    r.code as '角色编码',
    ur.created_at as '分配时间'
FROM user_roles ur
JOIN users u ON ur.user_id = u.id
JOIN roles r ON ur.role_id = r.id
WHERE ur.user_id = 1
AND EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name = 'user_roles'
);

-- 显示用户拥有的权限数量
SELECT 
    '用户ID为1的用户权限统计:' as '信息',
    COUNT(DISTINCT p.id) as '权限总数'
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN roles r ON rp.role_id = r.id
WHERE r.code = 'super_admin';

SELECT '超级管理员权限分配完成！' as '完成信息'; 