package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// HotArea 热区结构
type HotArea struct {
	ID        int    `json:"id"`         // 热区ID
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	Unit      string `json:"unit"`       // 单位，默认是px
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	EventType int    `json:"event_type"` // 事件类型：1-确定 2-取消
}

// CreativeItem 创意数据项
type CreativeItem struct {
	ID              int64     `json:"id"`
	Name            string    `json:"name"`             // 创意名称
	ImageURL        string    `json:"image_url"`        // 素材图片地址
	ImageArea       string    `json:"image_area"`       // 图片区域
	BackgroundColor string    `json:"background_color"` // 背景颜色
	HotAreasList    []HotArea `json:"hotAreas"`         // 热区列表（API使用）
	CreatedAt       time.Time `json:"createdAt"`        // 创建时间
	UpdatedAt       time.Time `json:"updatedAt"`        // 更新时间
}

// CreativeListReq 获取创意列表请求
type CreativeListReq struct {
	Page int    `json:"page" form:"page"` // 页码
	Size int    `json:"size" form:"size"` // 每页数量
	Name string `json:"name" form:"name"` // 创意名称，支持模糊查询
}

// CreativeListResp 获取创意列表响应
type CreativeListResp struct {
	List  []CreativeItem `json:"list"`  // 列表数据
	Total int64          `json:"total"` // 总数
	Page  int            `json:"page"`  // 页码
	Size  int            `json:"size"`  // 每页数量
}

// CreativeCreateReq 创建创意请求
type CreativeCreateReq struct {
	Name            string    `json:"name" binding:"required"`      // 创意名称
	ImageURL        string    `json:"image_url" binding:"required"` // 素材图片地址
	ImageArea       string    `json:"image_area"`                   // 图片区域
	BackgroundColor string    `json:"background_color"`             // 背景颜色
	HotAreas        []HotArea `json:"hotAreas"`                     // 热区列表
}

// CreativeUpdateReq 更新创意请求
type CreativeUpdateReq struct {
	ID              int64     `json:"id" binding:"required"`        // 创意ID
	Name            string    `json:"name" binding:"required"`      // 创意名称
	ImageURL        string    `json:"image_url" binding:"required"` // 素材图片地址
	ImageArea       string    `json:"image_area"`                   // 图片区域
	BackgroundColor string    `json:"background_color"`             // 背景颜色
	HotAreas        []HotArea `json:"hotAreas"`                     // 热区列表
}

// CreativeDetailResp 创意详情响应
type CreativeDetailResp struct {
	CreativeItem
}

// 以下是VO层转换函数，用于Controller中调用

// ToCreativeListParam 将VO请求转换为domain参数
func ToCreativeListParam(req CreativeListReq) domain.CreativeListParam {
	return domain.CreativeListParam{
		Page: req.Page,
		Size: req.Size,
		Name: req.Name,
	}
}

// FromCreativeListResult 将domain结果转换为VO响应
func FromCreativeListResult(result domain.CreativeListResult) CreativeListResp {
	voCreatives := make([]CreativeItem, len(result.List))
	for i, entity := range result.List {
		voCreatives[i] = CreativeItem{
			ID:              entity.ID,
			Name:            entity.Name,
			ImageURL:        entity.ImageURL,
			ImageArea:       entity.ImageArea,
			BackgroundColor: entity.BackgroundColor,
			HotAreasList:    fromDomainHotAreas(entity.HotAreasList),
			CreatedAt:       entity.CreatedAt,
			UpdatedAt:       entity.UpdatedAt,
		}
	}

	return CreativeListResp{
		List:  voCreatives,
		Total: result.Total,
		Page:  result.Page,
		Size:  result.Size,
	}
}

// FromCreativeEntity 将domain实体转换为VO对象
func FromCreativeEntity(entity domain.CreativeEntity) CreativeItem {
	return CreativeItem{
		ID:              entity.ID,
		Name:            entity.Name,
		ImageURL:        entity.ImageURL,
		ImageArea:       entity.ImageArea,
		BackgroundColor: entity.BackgroundColor,
		HotAreasList:    fromDomainHotAreas(entity.HotAreasList),
		CreatedAt:       entity.CreatedAt,
		UpdatedAt:       entity.UpdatedAt,
	}
}

// ToCreativeCreateEntity 将VO请求转换为domain实体
func ToCreativeCreateEntity(req CreativeCreateReq) domain.CreativeCreateEntity {
	return domain.CreativeCreateEntity{
		Name:            req.Name,
		ImageURL:        req.ImageURL,
		ImageArea:       req.ImageArea,
		BackgroundColor: req.BackgroundColor,
		HotAreasList:    toDomainHotAreas(req.HotAreas),
	}
}

// ToCreativeUpdateEntity 将VO请求转换为domain实体
func ToCreativeUpdateEntity(req CreativeUpdateReq) domain.CreativeUpdateEntity {
	return domain.CreativeUpdateEntity{
		ID:              req.ID,
		Name:            req.Name,
		ImageURL:        req.ImageURL,
		ImageArea:       req.ImageArea,
		BackgroundColor: req.BackgroundColor,
		HotAreasList:    toDomainHotAreas(req.HotAreas),
	}
}

// toDomainHotAreas 将VO热区数组转换为domain热区数组
func toDomainHotAreas(voHotAreas []HotArea) []domain.HotArea {
	domainHotAreas := make([]domain.HotArea, len(voHotAreas))
	for i, area := range voHotAreas {
		domainHotAreas[i] = domain.HotArea{
			ID:        area.ID,
			Width:     area.Width,
			Height:    area.Height,
			Unit:      area.Unit,
			X:         area.X,
			Y:         area.Y,
			EventType: area.EventType,
		}
	}
	return domainHotAreas
}

// fromDomainHotAreas 将domain热区数组转换为VO热区数组
func fromDomainHotAreas(domainHotAreas []domain.HotArea) []HotArea {
	voHotAreas := make([]HotArea, len(domainHotAreas))
	for i, area := range domainHotAreas {
		voHotAreas[i] = HotArea{
			ID:        area.ID,
			Width:     area.Width,
			Height:    area.Height,
			Unit:      area.Unit,
			X:         area.X,
			Y:         area.Y,
			EventType: area.EventType,
		}
	}
	return voHotAreas
}
