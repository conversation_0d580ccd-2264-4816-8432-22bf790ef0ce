package model

import "time"

// PromotionReport 推广报表模型
type PromotionReport struct {
	ID              int64     `gorm:"primaryKey;column:id"`    // 主键ID
	ReportDate      string    `gorm:"column:report_date"`      // 报表日期
	GroupID         int64     `gorm:"column:group_id"`         // 口令组ID
	CategoryID      int64     `gorm:"column:category_id"`      // 分类ID
	Cost            float64   `gorm:"column:cost"`             // 成本
	TotalOrders     int       `gorm:"column:total_orders"`     // 总订单数
	TotalCommission float64   `gorm:"column:total_commission"` // 总佣金
	CreatedAt       time.Time `gorm:"column:created_at"`       // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at"`       // 更新时间
}

// TableName 设置表名
func (PromotionReport) TableName() string {
	return "promotion_reports"
}

// PromotionReportDO 推广报表数据对象模型
type PromotionReportDO struct {
	ID         int64     `gorm:"primaryKey;column:id"` // 主键ID
	ReportDate string    `gorm:"column:report_date"`   // 报表日期
	GroupID    int64     `gorm:"column:group_id"`      // 口令组ID
	CategoryID int64     `gorm:"column:category_id"`   // 分类ID
	Cost       float64   `gorm:"column:cost"`          // 成本
	Orders     int       `gorm:"column:orders"`        // 订单数
	Commission float64   `gorm:"column:commission"`    // 佣金
	Profit     float64   `gorm:"column:profit"`        // 利润
	ROI        float64   `gorm:"column:roi"`           // 投资回报率
	OrderRate  float64   `gorm:"column:order_rate"`    // 订单率
	ProfitRate float64   `gorm:"column:profit_rate"`   // 利润率
	CreatedAt  time.Time `gorm:"column:created_at"`    // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at"`    // 更新时间
}

// TableName 设置 PromotionReportDO 表名
func (PromotionReportDO) TableName() string {
	return "promotion_reports"
}
