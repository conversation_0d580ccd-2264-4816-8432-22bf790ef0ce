package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"math"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 全局导入锁
var importMutex sync.Mutex

// PromotionReportService 推广报表服务
type PromotionReportService struct {
	db *gorm.DB
}

// NewPromotionReportService 创建推广报表服务实例
func NewPromotionReportService() *PromotionReportService {
	return &PromotionReportService{
		db: global.DB,
	}
}

// GetCostList 获取成本列表
func (s *PromotionReportService) GetCostList(ctx context.Context, param domain.PromotionReportCostListParam) (domain.PromotionReportCostListResult, error) {
	var total int64
	var data []struct {
		model.PromotionReport
		GroupName string
	}

	// 构建查询
	query := s.db.WithContext(ctx).Table("promotion_reports as pr").
		Select("pr.*, pg.name as group_name").
		Joins("LEFT JOIN password_groups pg ON pr.group_id = pg.id") // 应用过滤条件
	if param.StartDate != "" {
		query = query.Where("pr.report_date >= ?", param.StartDate)
	}
	if param.EndDate != "" {
		query = query.Where("pr.report_date <= ?", param.EndDate)
	}
	if param.GroupId > 0 {
		query = query.Where("pr.group_id = ?", param.GroupId)
	}
	if param.CategoryId > 0 {
		query = query.Where("pr.category_id = ?", param.CategoryId)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return domain.PromotionReportCostListResult{}, errors.New(err.Error())
	}

	// 应用排序和分页
	query = query.Order("pr.report_date DESC")
	if param.Page > 0 && param.Size > 0 {
		offset := (param.Page - 1) * param.Size
		query = query.Offset(offset).Limit(param.Size)
	}

	// 执行查询
	if err := query.Find(&data).Error; err != nil {
		return domain.PromotionReportCostListResult{}, errors.New(err.Error())
	}

	items := make([]domain.PromotionReportCostEntity, 0, len(data))
	for _, item := range data {
		newItem := domain.PromotionReportCostEntity{
			ID:         item.ID,
			ReportDate: item.ReportDate,
			GroupId:    item.GroupID,
			GroupName:  item.GroupName,
			CategoryId: item.CategoryID,
			Cost:       item.Cost,
		}
		items = append(items, newItem)
	}

	return domain.PromotionReportCostListResult{
		Total: total,
		List:  items,
	}, nil
}

// UpdateCost 更新推广成本
func (s *PromotionReportService) UpdateCost(ctx context.Context, groupId int64, reportDate string, cost float64) error {
	// 1. 先查询密码组是否存在
	var group model.PasswordGroup
	err := s.db.WithContext(ctx).
		Table("password_groups").
		Where("id = ?", groupId).
		First(&group).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("分组ID不存在")
		}
		return err
	}

	// 2. 更新报表数据
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 使用upsert更新或插入报表记录
		err = tx.Table("promotion_reports").
			Where(map[string]interface{}{
				"report_date": reportDate,
				"group_id":    groupId,
			}).
			Assign(map[string]interface{}{
				"cost":        cost,
				"report_date": reportDate,
				"group_id":    groupId,
				"category_id": group.CategoryID,
			}).
			FirstOrCreate(&model.PromotionReport{}).Error

		if err != nil {
			return err
		}

		// 3. 获取密码组对应的PID列表
		var pids []string
		err = tx.Table("passwords").
			Select("pid").
			Where("group_id = ? AND pid != ''", groupId).
			Pluck("pid", &pids).Error

		if err != nil {
			return err
		}

		if len(pids) == 0 {
			return nil
		}

		// 4. 构造订单表名（按月份分表）
		if len(reportDate) != 8 {
			return errors.New("报表日期格式错误")
		}
		tableName := "warehouse.cps_orders_" + reportDate[:6]

		// 5. 统计订单数和佣金
		var result struct {
			TotalOrders     int
			TotalCommission float64
		}

		err = tx.Table(tableName).
			Where("pid IN ?", pids).
			Where("order_status IN ?", []int{2, 3, 4}).
			Select(
				"COUNT(1) as total_orders",
				"SUM(commission - third_service_fee + activity_fee - activity_service_fee) as total_commission",
			).Scan(&result).Error

		if err != nil {
			return err
		}

		// 6. 更新统计数据
		err = tx.Table("promotion_reports").
			Where(map[string]interface{}{
				"report_date": reportDate,
				"group_id":    groupId,
			}).
			Updates(map[string]interface{}{
				"total_orders":     result.TotalOrders,
				"total_commission": result.TotalCommission,
			}).Error

		return err
	})

	return err
}

// ImportCost 导入推广成本
func (s *PromotionReportService) ImportCost(ctx context.Context, categoryId int64, file *multipart.FileHeader) (bool, error) {
	// 1. 检查文件是否存在
	if file == nil {
		return false, errors.New("请上传Excel文件")
	}

	// 2. 使用互斥锁实现导入限制
	if !importMutex.TryLock() {
		return false, errors.New("当前有导入任务正在进行中，请稍后再试")
	}
	// 确保在函数结束时释放锁
	defer importMutex.Unlock()

	// 3. 打开文件
	fileReader, err := file.Open()
	if err != nil {
		return false, err
	}
	defer fileReader.Close()

	// 4. 使用 excelize 读取 Excel
	f, err := excelize.OpenReader(fileReader)
	if err != nil {
		return false, err
	}
	defer f.Close()

	// 5. 获取所有sheet名称
	sheets := f.GetSheetList()

	// 6. 存储所有需要验证的口令组名称
	var groupNames []string
	for _, sheet := range sheets {
		// 检查sheet名称格式是否符合要求
		if !strings.HasSuffix(sheet, "-总") {
			continue
		}
		// 提取口令组名称并去除前后空格
		groupName := strings.TrimSpace(strings.TrimSuffix(sheet, "-总"))
		if groupName != "" {
			groupNames = append(groupNames, groupName)
		}
	}

	if len(groupNames) == 0 {
		return false, errors.New("Excel中没有找到符合格式的sheet页，sheet名称必须以'-总'结尾")
	}

	// 7. 验证所有口令组是否存在
	var groups []model.PasswordGroup
	err = s.db.WithContext(ctx).
		Where("name IN ? AND category_id = ?", groupNames, categoryId).
		Find(&groups).Error
	if err != nil {
		return false, err
	}

	// 创建口令组名称到ID的映射
	groupMap := make(map[string]int64)
	for _, group := range groups {
		groupMap[group.Name] = int64(group.ID)
	}

	// 检查是否有未入库的口令组
	var missingGroups []string
	for _, name := range groupNames {
		if _, exists := groupMap[name]; !exists {
			missingGroups = append(missingGroups, name)
		}
	}

	if len(missingGroups) > 0 {
		return false, errors.New("以下口令组未入库，请先入库后再导入：" + strings.Join(missingGroups, "、"))
	}

	// 用于跟踪是否创建了任务
	var hasTask bool

	// 8. 开启事务处理数据导入
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		currentYear := time.Now().Year()
		currentMonth := int(time.Now().Month())

		// 收集组信息
		var groupInfos []map[string]interface{}

		for sheetName, groupId := range groupMap {
			// 获取sheet数据
			rows, err := f.GetRows(sheetName + "-总")
			if err != nil {
				return fmt.Errorf("读取sheet[%s]失败: %v", sheetName, err)
			}

			// 获取数据库中该组的最大日期（用于增量同步）
			var result struct {
				MaxDate string
			}
			err = tx.Table("promotion_reports").
				Where("group_id = ?", groupId).
				Select("MAX(report_date) as max_date").
				Scan(&result).Error
			if err != nil {
				return err
			}

			// 解析Excel中的所有日期和成本
			dailyCosts := make(map[string]float64)
			var minDate, maxExcelDate string

			// 跳过表头，处理所有数据行
			for i := 1; i < len(rows); i++ {
				row := rows[i]
				if len(row) < 2 {
					continue
				}

				// 解析日期
				dateStr := strings.TrimSpace(row[0])
				if dateStr == "" || strings.TrimSpace(dateStr) == "总计" {
					break
				}

				var year, month, day int
				// 尝试解析 Excel 数值日期
				if numDate, err := strconv.ParseFloat(dateStr, 64); err == nil {
					// Excel 的日期是从 1900-01-01 开始的天数
					// 将 Excel 数值转换为时间
					excelEpoch := time.Date(1899, time.December, 30, 0, 0, 0, 0, time.UTC)
					date := excelEpoch.Add(time.Duration(numDate) * 24 * time.Hour)
					year = date.Year()
					month = int(date.Month())
					day = date.Day()
				} else {
					// 如果不是数值，尝试解析 "MM月DD日" 格式
					parts := strings.Split(dateStr, "月")
					if len(parts) != 2 {
						zap.L().Error("日期格式错误", zap.String("date", dateStr))
						return fmt.Errorf("sheet[%s]第%d行日期格式错误，应为'MM月DD日'格式或Excel日期格式", sheetName, i+1)
					}

					var err error
					month, err = strconv.Atoi(parts[0])
					if err != nil {
						return fmt.Errorf("sheet[%s]第%d行月份格式错误: %v", sheetName, i+1, err)
					}

					day, err = strconv.Atoi(strings.TrimSuffix(parts[1], "日"))
					if err != nil {
						return fmt.Errorf("sheet[%s]第%d行日期格式错误: %v", sheetName, i+1, err)
					}

					// 确定年份
					year = currentYear
					if month > currentMonth {
						year--
					}
				}

				// 构造报表日期
				reportDate := fmt.Sprintf("%d%02d%02d", year, month, day)

				// 如果日期超过今天则忽略
				if reportDate > time.Now().Format("20060102") {
					continue
				}

				// 解析成本
				costStr := strings.TrimSpace(row[1])
				var cost float64
				if costStr == "" {
					cost = 0
				} else {
					cost, err = strconv.ParseFloat(costStr, 64)
					if err != nil {
						cost = 0
					}
				}

				dailyCosts[reportDate] = cost

				// 更新最小和最大日期
				if minDate == "" || reportDate < minDate {
					minDate = reportDate
				}
				if maxExcelDate == "" || reportDate > maxExcelDate {
					maxExcelDate = reportDate
				}
			}

			// 如果没有数据，跳过处理
			if minDate == "" || maxExcelDate == "" {
				continue
			}

			// 获取5天前的日期
			daysAgo := time.Now().AddDate(0, 0, -5).Format("20060102")

			// 记录需要更新订单数据的日期范围
			var needUpdateStartDate, needUpdateEndDate string

			// 处理每个日期的数据
			for reportDate, cost := range dailyCosts {
				// 只处理10天内的数据
				if reportDate < daysAgo {
					continue
				}

				// 检查记录是否存在
				var count int64
				err = tx.Table("promotion_reports").
					Where("report_date = ? AND group_id = ?", reportDate, groupId).
					Count(&count).Error
				if err != nil {
					return err
				}

				// 判断是否需要更新数据
				needUpdate := false
				if count == 0 { // 记录不存在，需要新增
					needUpdate = true
				} else if reportDate >= daysAgo { // 记录存在且在5天内，需要更新
					needUpdate = true
				}

				if needUpdate {
					// 更新或插入报表数据
					err = tx.Table("promotion_reports").
						Where(map[string]interface{}{
							"report_date": reportDate,
							"group_id":    groupId,
						}).
						Assign(map[string]interface{}{
							"cost":        cost,
							"report_date": reportDate,
							"group_id":    groupId,
							"category_id": categoryId,
						}).
						FirstOrCreate(&model.PromotionReport{}).Error
					if err != nil {
						return err
					}

					// 更新需要处理订单数据的日期范围
					if needUpdateStartDate == "" || reportDate < needUpdateStartDate {
						needUpdateStartDate = reportDate
					}
					if needUpdateEndDate == "" || reportDate > needUpdateEndDate {
						needUpdateEndDate = reportDate
					}
				}
			}

			// 如果有需要更新的数据，则收集组信息
			if needUpdateStartDate != "" && needUpdateEndDate != "" {
				groupInfos = append(groupInfos, map[string]interface{}{
					"group_id":   groupId,
					"sheet_name": sheetName,
					"start_date": needUpdateStartDate,
					"end_date":   needUpdateEndDate,
				})
			}
		}

		// 如果有需要更新的数据，创建一个总的任务
		if len(groupInfos) > 0 {
			paramsJson, err := json.Marshal(groupInfos)
			if err != nil {
				return err
			}

			// 生成任务名称：年月日+文件名（不含扩展名）
			fileName := strings.TrimSuffix(file.Filename, filepath.Ext(file.Filename))
			taskName := time.Now().Format("20060102") + "_" + fileName

			err = tx.Table("tasks").
				Create(map[string]interface{}{
					"task_type": 1, // 费用导入场景
					"params":    string(paramsJson),
					"status":    "pending",
					"name":      taskName,
				}).Error
			if err != nil {
				return err
			}
			hasTask = true
		}
		return nil
	})

	return hasTask, err
}

// GetTaskList 获取任务列表
func (s *PromotionReportService) GetTaskList(ctx context.Context) ([]domain.PromotionReportTaskEntity, error) {
	var tasks []domain.PromotionReportTaskEntity

	err := s.db.WithContext(ctx).
		Table("tasks").
		Select("id, name, status, created_at").
		Order("created_at DESC").
		Limit(10).
		Scan(&tasks).
		Error

	if err != nil {
		return nil, err
	}

	// 格式化时间
	for i := range tasks {
		if t, err := time.Parse("2006-01-02 15:04:05", tasks[i].CreatedAt); err == nil {
			tasks[i].CreatedAt = t.Format("2006-01-02 15:04:05")
		}
	}

	return tasks, nil
}

// GetModelReport 获取模型报表数据
func (s *PromotionReportService) GetModelReport(ctx context.Context, param domain.ModelReportParam) (*domain.ModelReportResult, error) {
	// 验证模型ID
	var modelExists bool
	err := s.db.WithContext(ctx).
		Table("models").
		Select("1").
		Where("id = ?", param.ModelId).
		Scan(&modelExists).
		Error

	if err != nil {
		return nil, err
	}

	if !modelExists {
		return nil, errors.New("模型不存在")
	}

	// 1. 获取衰减记录
	var decayRecords []struct {
		ID        uint64
		ModelID   uint64
		Percent   float64
		CreatedAt time.Time
	}

	err = s.db.WithContext(ctx).
		Table("model_decays").
		Where("model_id = ?", param.ModelId).
		Order("id ASC").
		Find(&decayRecords).Error

	if err != nil {
		return nil, err
	}

	// 2. 获取报表数据
	var reports []struct {
		ReportDate      string
		GroupID         int64
		CategoryID      int64
		TotalOrders     int
		Cost            float64
		TotalCommission float64
	}

	query := s.db.WithContext(ctx).
		Table("promotion_reports")

	if param.GroupId > 0 {
		// 如果指定了口令组ID，则按口令组ID查询
		query = query.Where("group_id = ?", param.GroupId)
	} else if param.CategoryId > 0 {
		// 如果指定了分类ID，则按分类ID查询
		query = query.Where("category_id = ?", param.CategoryId)
	}

	err = query.Order("report_date ASC").
		Find(&reports).Error

	if err != nil {
		return nil, err
	}

	// 3. 构建日期到报表数据的映射
	reportMap := make(map[string]struct {
		TotalOrders     int
		Cost            float64
		TotalCommission float64
	})
	var minDate, maxDate string
	for _, report := range reports {
		date := report.ReportDate
		if minDate == "" || date < minDate {
			minDate = date
		}
		if maxDate == "" || date > maxDate {
			maxDate = date
		}
		if existingData, ok := reportMap[date]; ok {
			// 如果同一天有多条数据，则累加
			existingData.TotalOrders += report.TotalOrders
			existingData.Cost += report.Cost
			existingData.TotalCommission += report.TotalCommission
			reportMap[date] = existingData
		} else {
			reportMap[date] = struct {
				TotalOrders     int
				Cost            float64
				TotalCommission float64
			}{
				TotalOrders:     report.TotalOrders,
				Cost:            report.Cost,
				TotalCommission: report.TotalCommission,
			}
		}
	}

	if minDate == "" || maxDate == "" {
		// 如果没有数据，返回空结果
		return &domain.ModelReportResult{
			List: make([]domain.ModelReportEntity, 0),
		}, nil
	}

	// 4. 生成连续日期列表并填充数据
	var result []domain.ModelReportEntity
	startTime, _ := time.Parse("20060102", minDate)
	endTime, _ := time.Parse("20060102", maxDate)

	// 先生成连续的日期列表
	for d := startTime; !d.After(endTime); d = d.AddDate(0, 0, 1) {
		date := d.Format("20060102")
		result = append(result, domain.ModelReportEntity{
			Date: date,
		})
	}

	// 填充报表数据
	for i := range result {
		date := result[i].Date
		reportData, exists := reportMap[date]
		if !exists {
			// 如果这一天没有数据，所有值都设为0
			result[i].TotalOrders = 0
			result[i].Cost = 0
			result[i].TotalCommission = 0
			result[i].NewOrders = 0
			result[i].DailyCost = 0
			result[i].PaybackDays = -1
		} else {
			// 填充实际数据
			result[i].TotalOrders = reportData.TotalOrders
			result[i].Cost = reportData.Cost
			result[i].TotalCommission = reportData.TotalCommission
		}

		// 计算单均佣金
		if result[i].TotalOrders > 0 {
			result[i].AverageCommission = result[i].TotalCommission / float64(result[i].TotalOrders)
		} else {
			result[i].AverageCommission = 0
		}

		if i == 0 {
			// 第一天的新订单数就是总订单数
			result[i].NewOrders = result[i].TotalOrders
			if result[i].NewOrders > 0 {
				result[i].DailyCost = result[i].Cost / float64(result[i].NewOrders)
			} else {
				result[i].DailyCost = 0
			}
		} else {
			// 计算历史订单在当天的衰减订单数
			var decayOrders int
			// 遍历之前的每一天，计算它们在当天产生的订单数
			for j := 0; j < i; j++ {
				prevDay := result[j]
				// 获取正确的衰减比例
				// 比如：第三天计算第一天的衰减时用第二天的衰减比例，计算第二天的衰减时用第一天的衰减比例
				decayIndex := i - j - 1 // 第几天的衰减比例
				if decayIndex < len(decayRecords) {
					// 计算第j天在第i天产生的订单数
					// 使用第decayIndex天的衰减比例
					decay := int(math.Round(float64(prevDay.NewOrders) * decayRecords[decayIndex].Percent / 100))
					decayOrders += decay
				}
			}

			// 当日新订单数 = 总订单数 - 历史订单衰减数
			newOrders := result[i].TotalOrders - decayOrders
			result[i].NewOrders = newOrders
			if newOrders > 0 {
				// 计算当日订单成本
				result[i].DailyCost = result[i].Cost / float64(newOrders)
			} else {
				result[i].DailyCost = 0
			}
		}

		// 计算回本周期天数
		if result[i].DailyCost > 0 && result[i].AverageCommission > 0 {
			// 计算累计收益
			var totalRevenue float64 = result[i].AverageCommission
			var paybackDays int = 1

			// 遍历衰减记录，直到累计收益大于等于当日订单成本
			for j := 0; j < len(decayRecords); j++ {
				if totalRevenue >= result[i].DailyCost {
					break
				}
				// 将衰减比例转换为小数并累加收益
				decayPercent := decayRecords[j].Percent / 100
				totalRevenue += result[i].AverageCommission * decayPercent
				paybackDays++
			}

			// 如果遍历完所有衰减记录后仍未回本，则设置为-1表示无法回本
			if totalRevenue < result[i].DailyCost {
				result[i].PaybackDays = -1
			} else {
				result[i].PaybackDays = paybackDays
			}
		} else {
			result[i].PaybackDays = -1
		}
	}

	// 返回数据
	return &domain.ModelReportResult{
		List: result,
	}, nil
}

// UpdateOrderDataBatch 批量更新订单数据
func (s *PromotionReportService) UpdateOrderDataBatch(ctx context.Context, tx *gorm.DB, groupID int64, startDate, endDate string) error {
	// 1. 获取密码组对应的PID列表
	var pidResults []struct {
		Pid string `json:"pid"`
	}
	err := tx.WithContext(ctx).
		Table("passwords").
		Select("pid").
		Where("group_id = ? AND pid != ''", groupID).
		Find(&pidResults).Error
	if err != nil {
		return err
	}

	// 将结果转换为 []string
	pids := make([]string, 0, len(pidResults))
	for _, result := range pidResults {
		if result.Pid != "" {
			pids = append(pids, result.Pid)
		}
	}

	if len(pids) == 0 {
		return nil
	}

	// 2. 按日期统计数据
	type DailyStats struct {
		TotalOrders     int     `json:"total_orders"`
		TotalCommission float64 `json:"total_commission"`
	}
	dailyData := make(map[string]DailyStats)

	// 4. 处理查询逻辑
	type QueryResult struct {
		OrderDate       string  `json:"order_date"`
		TotalOrders     int     `json:"total_orders"`
		TotalCommission float64 `json:"total_commission"`
	}

	// 定义查询函数
	queryOrders := func(tableName, startTime, endTime string) ([]QueryResult, error) {
		var results []QueryResult
		// 直接使用tx，表名前加warehouse.前缀
		warehouseTable := "warehouse." + tableName
		err := tx.WithContext(ctx).
			Table(warehouseTable).
			Select(
				"DATE_FORMAT(create_time, '%Y%m%d') as order_date",
				"COUNT(1) as total_orders",
				"SUM(commission - third_service_fee + activity_fee - activity_service_fee) as total_commission",
			).
			Where("pid IN ?", pids).
			Where("order_status IN ?", []int{2, 3, 4}).
			Where("create_time >= ?", startTime).
			Where("create_time <= ?", endTime).
			Group("DATE_FORMAT(create_time, '%Y%m%d')").
			Order("order_date ASC").
			Find(&results).Error
		return results, err
	}

	// 判断是否跨月
	startMonth := startDate[:6]
	endMonth := endDate[:6]

	// 按月份处理数据
	processByMonth := func(monthStr, queryStartDate, queryEndDate string) error {
		tableName := "cps_orders_" + monthStr
		results, err := queryOrders(tableName, queryStartDate, queryEndDate)
		if err != nil {
			return err
		}

		// 保存结果
		for _, result := range results {
			dailyData[result.OrderDate] = DailyStats{
				TotalOrders:     result.TotalOrders,
				TotalCommission: result.TotalCommission,
			}
		}
		return nil
	}

	// 构建查询的起止时间
	startTime := startDate[:4] + "-" + startDate[4:6] + "-" + startDate[6:] + " 00:00:00"
	endTime := endDate[:4] + "-" + endDate[4:6] + "-" + endDate[6:] + " 23:59:59"

	if startMonth == endMonth {
		// 同月查询
		err = processByMonth(startMonth, startTime, endTime)
		if err != nil {
			return err
		}
	} else {
		// 跨月查询，需要按月分别查询
		// 实际应用中需要处理月份迭代
		// 这里简化为只处理两个月的情况
		// 第一个月：从startDate到月末
		firstMonthEndDay := "31" // 简化处理，实际应根据月份计算
		firstMonthEndTime := startDate[:6] + firstMonthEndDay + " 23:59:59"
		err = processByMonth(startMonth, startTime, firstMonthEndTime)
		if err != nil {
			return err
		}

		// 第二个月：从月初到endDate
		secondMonthStartTime := endDate[:6] + "01" + " 00:00:00"
		err = processByMonth(endMonth, secondMonthStartTime, endTime)
		if err != nil {
			return err
		}
	}

	// 5. 批量更新数据
	for date, data := range dailyData {
		// 先查询密码组是否存在
		var group model.PasswordGroup
		err := tx.WithContext(ctx).
			Table("password_groups").
			Where("id = ?", groupID).
			First(&group).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				continue // 跳过不存在的组
			}
			return err
		}

		// 使用Upsert语法更新或插入数据
		err = tx.WithContext(ctx).
			Table("promotion_reports").
			Where(map[string]interface{}{
				"report_date": date,
				"group_id":    groupID,
			}).
			Assign(map[string]interface{}{
				"report_date":      date,
				"group_id":         groupID,
				"total_orders":     data.TotalOrders,
				"total_commission": data.TotalCommission,
				"category_id":      group.CategoryID,
			}).
			FirstOrCreate(&model.PromotionReport{}).Error

		if err != nil {
			return err
		}
	}

	return nil
}

// ImportXhsCost 导入小红书成本
func (s *PromotionReportService) ImportXhsCost(ctx context.Context) error {
	// 1. 查询小红书成本数据
	var results []struct {
		CreateTime string
		RealCost   float64
		PwdName    string
	}

	// 设置查询范围为最近10天
	minDate := time.Now().AddDate(0, 0, -10).Format(time.DateTime)
	maxDate := time.Now().Format(time.DateTime)

	// 从外部数据源查询小红书成本数据
	err := s.db.WithContext(ctx).Raw(`
		SELECT
			create_time,
			CASE
				WHEN type in (1,2) AND create_time < '2024-12-16' THEN costs * 0.909
				WHEN type in (1,2) AND create_time >= '2024-12-16' AND create_time < '2025-2-16' THEN costs * 0.87
				WHEN type in (1,2) AND create_time >= '2025-2-16' THEN costs * 0.877
				WHEN type = 3 THEN costs * 0.83
				WHEN type = 4 THEN costs * 0.87
				WHEN type = 5 THEN costs * 0.862
				WHEN type = 6 THEN costs * 0.885
                WHEN type = 7 THEN costs * 0.909
				WHEN type = 8 THEN costs * 0.85
				ELSE 0
			END AS real_cost,
			CASE
				WHEN koulinggroup IS NULL OR koulinggroup = '' THEN
					(SELECT pwd_name
					FROM warehouse.elm_pwds
					WHERE SUBSTRING_INDEX(pid,"_",-1) = ad_zone_id
					LIMIT 1)
				ELSE koulinggroup
			END AS pwd_name
		FROM
			byn_data.cx_costs
		WHERE
			create_time >= ? and create_time <= ?
		ORDER BY create_time ASC
	`, minDate, maxDate).Scan(&results).Error

	if err != nil {
		return err
	}

	// 2. 按日期和口令组名称聚合数据
	type DailyCost struct {
		Cost float64
	}
	dailyCosts := make(map[string]map[string]DailyCost) // map[date]map[pwdName]DailyCost

	// 获取5天前的日期
	fiveDaysAgo := time.Now().AddDate(0, 0, -5).Format("20060102")
	// 转换最小日期为 YYYYMMDD 格式，用于后续过滤
	minDateYMD := "20241020" // 保守固定值以避免异常数据

	// 处理每一条成本数据
	for _, result := range results {
		// 转换日期格式从 YYYY-MM-DD 到 YYYYMMDD
		t, err := time.Parse("2006-01-02", result.CreateTime[:10])
		if err != nil {
			zap.L().Warn("无效的日期格式", zap.String("date", result.CreateTime))
			continue
		}
		date := t.Format("20060102")

		// 确保日期不早于最小日期
		if date < minDateYMD {
			continue
		}

		// 初始化日期的map
		if dailyCosts[date] == nil {
			dailyCosts[date] = make(map[string]DailyCost)
		}

		// 累加同一天同一口令组的成本
		cost := dailyCosts[date][result.PwdName]
		cost.Cost += result.RealCost
		dailyCosts[date][result.PwdName] = cost
	}

	// 3. 开启事务处理数据导入
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查询所有小红书分类(ID为55)的口令组
		var groups []model.PasswordGroup
		err := tx.Where("category_id = ?", 55).Find(&groups).Error
		if err != nil {
			return err
		}

		// 创建口令组名称到ID的映射
		groupMap := make(map[string]uint64)
		for _, group := range groups {
			groupMap[group.Name] = uint64(group.ID)
		}

		// 按口令组聚合数据，记录每个口令组的日期范围
		type GroupDateRange struct {
			MinDate string
			MaxDate string
		}
		groupDateRanges := make(map[uint64]GroupDateRange)

		// 处理每个日期的每个口令组数据
		for date, pwdCosts := range dailyCosts {
			for pwdName, cost := range pwdCosts {
				// 查找对应的口令组ID
				groupID, exists := groupMap[pwdName]
				if !exists {
					// 如果口令组不存在，记录日志并继续
					zap.L().Info("未找到口令组", zap.String("group_name", pwdName))
					continue
				}

				// 检查记录是否存在
				var count int64
				err := tx.Model(&model.PromotionReport{}).
					Where("report_date = ? AND group_id = ?", date, groupID).
					Count(&count).Error
				if err != nil {
					return err
				}

				// 如果记录存在且日期在5天前，则跳过
				if count > 0 && date < fiveDaysAgo {
					continue
				}

				// 更新或插入报表数据
				err = tx.Where(map[string]interface{}{
					"report_date": date,
					"group_id":    groupID,
				}).
					Assign(map[string]interface{}{
						"cost":        cost.Cost,
						"report_date": date,
						"group_id":    groupID,
						"category_id": 55, // 小红书分类ID
					}).
					FirstOrCreate(&model.PromotionReport{}).Error

				if err != nil {
					return err
				}

				// 只有新记录或5天内的记录才更新日期范围
				if count == 0 || date >= fiveDaysAgo {
					// 更新日期范围
					dateRange := groupDateRanges[groupID]
					if dateRange.MinDate == "" || date < dateRange.MinDate {
						dateRange.MinDate = date
					}
					if dateRange.MaxDate == "" || date > dateRange.MaxDate {
						dateRange.MaxDate = date
					}
					groupDateRanges[groupID] = dateRange
				}
			}
		}

		// 批量更新每个口令组的订单数据
		for groupID, dateRange := range groupDateRanges {
			// 如果日期范围为空，说明该组没有需要更新的数据
			if dateRange.MinDate == "" || dateRange.MaxDate == "" {
				continue
			}

			// 更新订单数据
			if err := s.UpdateOrderDataBatch(ctx, tx, int64(groupID), dateRange.MinDate, dateRange.MaxDate); err != nil {
				return err
			}
		}

		return nil
	})

	return err
}

// processImportTasks 处理导入任务
func (s *PromotionReportService) processImportTasks(ctx context.Context) error {
	// 查询待处理的任务
	type Task struct {
		ID     uint   `json:"id"`
		Params string `json:"params"`
		Status string `json:"status"`
		Name   string `json:"name"`
	}

	var tasks []Task
	err := s.db.WithContext(ctx).
		Table("tasks").
		Where("task_type = ?", 1). // 费用导入场景
		Where("status = ?", "pending").
		Order("id ASC").
		Find(&tasks).Error
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		return nil
	}

	// 处理每个任务
	for _, task := range tasks {
		// 更新任务状态为处理中
		err = s.db.WithContext(ctx).
			Table("tasks").
			Where("id = ?", task.ID).
			Update("status", "processing").Error
		if err != nil {
			zap.L().Error("Update task status error:", zap.Error(err))
			continue
		}

		// 解析任务参数
		var groupInfos []map[string]interface{}
		err = json.Unmarshal([]byte(task.Params), &groupInfos)
		if err != nil {
			zap.L().Error("Parse task params error:", zap.Error(err))
			s.updateTaskStatus(ctx, task.ID, "failed", err.Error())
			continue
		}

		// 开启事务处理数据
		err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			// 处理每个组的数据
			for _, info := range groupInfos {
				groupId := int64(info["group_id"].(float64))
				startDate := info["start_date"].(string)
				endDate := info["end_date"].(string)

				// 更新订单数据
				err = s.UpdateOrderDataBatch(ctx, tx, groupId, startDate, endDate)
				if err != nil {
					return err
				}
			}
			return nil
		})

		if err != nil {
			zap.L().Error("Process task error:", zap.Error(err))
			s.updateTaskStatus(ctx, task.ID, "failed", err.Error())
			continue
		}

		// 更新任务状态为完成
		s.updateTaskStatus(ctx, task.ID, "completed", "")
	}

	return nil
}

// updateTaskStatus 更新任务状态
func (s *PromotionReportService) updateTaskStatus(ctx context.Context, taskId uint, status string, errMsg string) {
	data := map[string]interface{}{
		"status": status,
	}
	if errMsg != "" {
		data["error_msg"] = errMsg
	}
	err := s.db.WithContext(ctx).
		Table("tasks").
		Where("id = ?", taskId).
		Updates(data).Error
	if err != nil {
		zap.L().Error("Update task status error:", zap.Error(err))
	}
}
