package job

import (
	"context"
	"fmt"
	"gin-backend/internal/service"
	"time"

	"go.uber.org/zap"
)

// XHSCreativeReportSyncJob 小红书创意报表数据同步定时任务
type XHSCreativeReportSyncJob struct {
	reportService *service.XHSCreativeReportService
}

// NewXHSCreativeReportSyncJob 创建小红书创意报表数据同步定时任务
func NewXHSCreativeReportSyncJob() *XHSCreativeReportSyncJob {
	return &XHSCreativeReportSyncJob{
		reportService: service.NewXHSCreativeReportService(),
	}
}

// Name 任务名称
func (j *XHSCreativeReportSyncJob) Name() string {
	return "sync-xhs-creative-report"
}

// Run 任务执行
func (j *XHSCreativeReportSyncJob) Run() error {
	startTime := time.Now()
	zap.L().Info("开始执行小红书创意报表数据同步任务")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	// 同步昨天的数据
	if err := j.reportService.SyncYesterdayReports(ctx); err != nil {
		zap.L().Error("小红书创意报表数据同步失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("小红书创意报表数据同步任务完成",
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// SyncSpecificDate 同步指定日期的数据（用于手动补数据）
func (j *XHSCreativeReportSyncJob) SyncSpecificDate(date time.Time) error {
	startTime := time.Now()
	zap.L().Info("开始同步指定日期的小红书创意报表数据",
		zap.String("date", date.Format("2006-01-02")))

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Minute)
	defer cancel()

	if err := j.reportService.SyncReportsByDate(ctx, date); err != nil {
		zap.L().Error("同步指定日期的小红书创意报表数据失败",
			zap.String("date", date.Format("2006-01-02")),
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("同步指定日期的小红书创意报表数据完成",
		zap.String("date", date.Format("2006-01-02")),
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// SyncDateRange 同步日期范围内的数据（用于批量补数据）
func (j *XHSCreativeReportSyncJob) SyncDateRange(startDate, endDate time.Time) error {
	startTime := time.Now()
	zap.L().Info("开始同步日期范围内的小红书创意报表数据",
		zap.String("start_date", startDate.Format("2006-01-02")),
		zap.String("end_date", endDate.Format("2006-01-02")))

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Minute)
	defer cancel()

	successCount := 0
	errorCount := 0

	// 逐日同步
	for date := startDate; !date.After(endDate); date = date.AddDate(0, 0, 1) {
		if err := j.reportService.SyncReportsByDate(ctx, date); err != nil {
			zap.L().Error("同步日期数据失败",
				zap.String("date", date.Format("2006-01-02")),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			zap.L().Warn("同步任务被取消", zap.Error(ctx.Err()))
			return ctx.Err()
		default:
			// 继续执行
		}

		// 每次同步后稍作休息，避免API调用过于频繁
		time.Sleep(1 * time.Second)
	}

	zap.L().Info("日期范围内的小红书创意报表数据同步完成",
		zap.String("start_date", startDate.Format("2006-01-02")),
		zap.String("end_date", endDate.Format("2006-01-02")),
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount),
		zap.Duration("duration", time.Since(startTime)))

	if errorCount > 0 {
		return fmt.Errorf("部分日期同步失败，成功: %d, 失败: %d", successCount, errorCount)
	}

	return nil
}
