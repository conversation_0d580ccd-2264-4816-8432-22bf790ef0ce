package vo

import (
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestXHSCreativeReportListReq_ToParam(t *testing.T) {
	req := XHSCreativeReportListReq{
		AccountName:   "测试账号",
		CampaignName:  "春季推广",
		Title:         "新品推荐",
		Pwd:           "特惠",
		StartDate:     "2024-01-01",
		EndDate:       "2024-01-31",
		AggregateType: "summary",
		Page:          1,
		PageSize:      20,
	}

	param := req.ToParam()

	assert.Equal(t, "测试账号", param.AccountName)
	assert.Equal(t, "春季推广", param.CampaignName)
	assert.Equal(t, "新品推荐", param.Title)
	assert.Equal(t, "特惠", param.Pwd)
	assert.Equal(t, "summary", param.AggregateType)
	assert.Equal(t, 1, param.<PERSON>)
	assert.Equal(t, 20, param.PageSize)
	assert.NotNil(t, param.StartDate)
	assert.NotNil(t, param.EndDate)
}

func TestXHSCreativeReportExportReq_ToParam(t *testing.T) {
	req := XHSCreativeReportExportReq{
		AccountName: "测试账号",
		Format:      "xlsx",
		StartDate:   "2024-01-01",
		EndDate:     "2024-01-31",
	}

	param := req.ToParam()

	assert.Equal(t, "测试账号", param.AccountName)
	assert.Equal(t, "xlsx", param.Format)
	assert.NotNil(t, param.StartDate)
	assert.NotNil(t, param.EndDate)
}

func TestXHSCreativeReportListRespFromResult(t *testing.T) {
	// 创建模拟的领域结果
	result := domain.XHSCreativeReportListResult{
		List:  []model.XHSCreativeReports{}, // 空列表用于测试
		Total: 100,
		Page:  1,
		Size:  20,
	}

	resp := XHSCreativeReportListRespFromResult(result)

	assert.Equal(t, int64(100), resp.Total)
	assert.Equal(t, 1, resp.Page)
	assert.Equal(t, 20, resp.Size)
	assert.NotNil(t, resp.List)
	assert.Len(t, resp.List, 0) // 空列表
}

func TestXHSCreativeReportExportRespFromResult(t *testing.T) {
	result := domain.XHSCreativeReportExportResult{
		FileName: "test_export.xlsx",
		FileURL:  "/exports/test_export.xlsx",
	}

	resp := XHSCreativeReportExportRespFromResult(result)

	assert.Equal(t, "test_export.xlsx", resp.FileName)
	assert.Equal(t, "/exports/test_export.xlsx", resp.FileURL)
	assert.Equal(t, "导出成功", resp.Message)
}

func TestParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		param       domain.XHSCreativeReportListParam
		expectValid bool
	}{
		{
			name: "有效参数-分日数据",
			param: domain.XHSCreativeReportListParam{
				Page:          1,
				PageSize:      20,
				AggregateType: "daily",
			},
			expectValid: true,
		},
		{
			name: "有效参数-汇总数据",
			param: domain.XHSCreativeReportListParam{
				Page:          1,
				PageSize:      20,
				AggregateType: "summary",
			},
			expectValid: true,
		},
		{
			name: "有效参数-空聚合类型",
			param: domain.XHSCreativeReportListParam{
				Page:          1,
				PageSize:      20,
				AggregateType: "",
			},
			expectValid: true,
		},
		{
			name: "无效页码",
			param: domain.XHSCreativeReportListParam{
				Page:     0,
				PageSize: 20,
			},
			expectValid: false,
		},
		{
			name: "页面大小超限",
			param: domain.XHSCreativeReportListParam{
				Page:     1,
				PageSize: 2000,
			},
			expectValid: false,
		},
		{
			name: "无效聚合类型",
			param: domain.XHSCreativeReportListParam{
				Page:          1,
				PageSize:      20,
				AggregateType: "invalid",
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := domain.ValidateXHSCreativeReportListParam(tt.param)

			if tt.expectValid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestExportParameterValidation(t *testing.T) {
	tests := []struct {
		name        string
		param       domain.XHSCreativeReportExportParam
		expectValid bool
	}{
		{
			name: "有效Excel导出",
			param: domain.XHSCreativeReportExportParam{
				Format: "xlsx",
			},
			expectValid: true,
		},
		{
			name: "有效CSV导出",
			param: domain.XHSCreativeReportExportParam{
				Format: "csv",
			},
			expectValid: true,
		},
		{
			name: "无效格式",
			param: domain.XHSCreativeReportExportParam{
				Format: "pdf",
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := domain.ValidateXHSCreativeReportExportParam(tt.param)

			if tt.expectValid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}

func TestEnumHelpers(t *testing.T) {
	// 测试枚举值转换函数
	assert.Equal(t, "信息流", domain.GetPlacementName(1))
	assert.Equal(t, "搜索", domain.GetPlacementName(2))
	assert.Equal(t, "未知", domain.GetPlacementName(99))

	assert.Equal(t, "点击量", domain.GetOptimizeTargetName(1))
	assert.Equal(t, "转化量", domain.GetOptimizeTargetName(2))
	assert.Equal(t, "未知", domain.GetOptimizeTargetName(99))

	assert.Equal(t, "笔记", domain.GetPromotionTargetName(1))
	assert.Equal(t, "直播间", domain.GetPromotionTargetName(2))
	assert.Equal(t, "未知", domain.GetPromotionTargetName(99))

	assert.Equal(t, "点击出价", domain.GetBiddingStrategyName(1))
	assert.Equal(t, "曝光出价", domain.GetBiddingStrategyName(2))
	assert.Equal(t, "未知", domain.GetBiddingStrategyName(99))

	assert.Equal(t, "手动搭建", domain.GetBuildTypeName(1))
	assert.Equal(t, "自动搭建", domain.GetBuildTypeName(2))
	assert.Equal(t, "未知", domain.GetBuildTypeName(99))

	assert.Equal(t, "品牌推广", domain.GetMarketingTargetName(1))
	assert.Equal(t, "产品种草", domain.GetMarketingTargetName(4))
	assert.Equal(t, "未知", domain.GetMarketingTargetName(99))
}
