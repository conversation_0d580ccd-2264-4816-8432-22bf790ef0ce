package model

import (
	"time"
)

// AdProduct 投放产品
type AdProduct struct {
	ID          int64     `gorm:"primaryKey;autoIncrement;comment:产品ID"`           // 产品ID
	Code        string    `gorm:"uniqueIndex;not null;comment:产品编号"`               // 产品编号
	Name        string    `gorm:"not null;comment:广告产品名称"`                         // 产品名称
	Image       string    `gorm:"comment:广告产品图片"`                                  // 产品图片
	Description string    `gorm:"comment:广告产品描述"`                                  // 产品描述
	Status      string    `gorm:"default:active;comment:状态：active-启用,inactive-停用"` // 状态：active-启用,inactive-停用
	CreatedBy   int64     `gorm:"comment:创建人ID"`                                   // 创建人ID
	UpdatedBy   int64     `gorm:"comment:更新人ID"`                                   // 更新人ID
	CreatedAt   time.Time `gorm:"autoCreateTime;comment:创建时间"`                     // 创建时间
	UpdatedAt   time.Time `gorm:"autoUpdateTime;comment:更新时间"`                     // 更新时间
	DeletedAt   time.Time `gorm:"index;comment:删除时间"`                              // 删除时间
}

// TableName 表名
func (AdProduct) TableName() string {
	return "ad_products"
}
