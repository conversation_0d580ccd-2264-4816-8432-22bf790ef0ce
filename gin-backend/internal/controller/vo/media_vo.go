package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// MediaTypes 媒体类型
type MediaTypes []string

// MediaPlatformConfig 媒体平台配置
type MediaPlatformConfig struct {
	Platform string      `json:"platform"` // 平台类型
	Info     interface{} `json:"info"`     // 平台配置信息（根据平台类型决定具体结构）
}

// DenghuoPlatformInfo 灯火平台信息
type DenghuoPlatformInfo struct {
	PID   string `json:"pid"`   // 项目ID（字符串类型避免精度丢失）
	Token string `json:"token"` // API Token
}

// XiaohongshuPlatformInfo 小红书平台信息
type XiaohongshuPlatformInfo struct {
	AppID     string `json:"appId"`     // 应用ID
	AppSecret string `json:"appSecret"` // 应用密钥
}

// MediaListReq 媒体列表请求结构
type MediaListReq struct {
	Name              string   `json:"name" form:"name"`                             // 媒体名称
	UserID            int64    `json:"user_id" form:"user_id"`                       // 媒介ID
	Types             []string `json:"types" form:"types"`                           // 媒体类型列表
	Industry          string   `json:"industry" form:"industry"`                     // 所属行业
	AuditStatus       string   `json:"audit_status" form:"audit_status"`             // 审核状态
	CooperationStatus string   `json:"cooperation_status" form:"cooperation_status"` // 合作状态
	CooperationType   string   `json:"cooperation_type" form:"cooperation_type"`     // 合作类型
	Page              int      `json:"page" form:"page"`                             // 页码
	PageSize          int      `json:"page_size" form:"size"`                        // 每页数量，前端使用size参数
}

// ToMediaListParam 转换为domain层参数
func (r MediaListReq) ToMediaListParam() domain.MediaListParam {
	return domain.MediaListParam{
		Name:              r.Name,
		UserID:            r.UserID,
		Types:             r.Types,
		Industry:          r.Industry,
		AuditStatus:       r.AuditStatus,
		CooperationStatus: r.CooperationStatus,
		CooperationType:   r.CooperationType,
		Page:              r.Page,
		PageSize:          r.PageSize,
	}
}

// MediaListResp 媒体列表响应结构
type MediaListResp struct {
	List     []MediaItem `json:"list"`      // 媒体列表
	Total    int64       `json:"total"`     // 总数量
	Page     int         `json:"page"`      // 当前页码
	PageSize int         `json:"page_size"` // 每页数量
}

// FromMediaListResult 从domain层结果转换
func FromMediaListResult(result domain.MediaListResult) MediaListResp {
	resp := MediaListResp{
		Total:    result.Total,
		Page:     result.Page,
		PageSize: result.PageSize,
		List:     make([]MediaItem, 0, len(result.List)),
	}

	for _, entity := range result.List {
		item := FromMediaEntity(entity)
		resp.List = append(resp.List, item)
	}

	return resp
}

// MediaItem 媒体信息结构（替换原来的MediaInfoItem）
type MediaItem struct {
	ID                int64               `json:"id"`                 // 媒体ID
	Code              string              `json:"code"`               // 媒体编号
	AdAgentID         int64               `json:"ad_agent_id"`        // 代理ID
	Name              string              `json:"name"`               // 媒体名称
	AuditStatus       string              `json:"audit_status"`       // 审核状态
	CooperationStatus string              `json:"cooperation_status"` // 合作状态
	CooperationType   string              `json:"cooperation_type"`   // 合作类型
	Account           string              `json:"account"`            // 账号
	LastLoginAt       string              `json:"last_login_at"`      // 最后登录时间
	Balance           float64             `json:"balance"`            // 余额
	Types             MediaTypes          `json:"types"`              // 媒体类型
	Industry          string              `json:"industry"`           // 所属行业
	CustomIndustry    string              `json:"custom_industry"`    // 自定义行业
	DailyActivity     string              `json:"daily_activity"`     // 日活
	TransactionVolume string              `json:"transaction_volume"` // 交易量
	CompanyName       string              `json:"company_name"`       // 公司名称
	CompanyAddress    string              `json:"company_address"`    // 公司地址
	ContactName       string              `json:"contact_name"`       // 联系人
	ContactPhone      string              `json:"contact_phone"`      // 联系电话
	RejectReason      string              `json:"reject_reason"`      // 拒绝原因
	UserID            int64               `json:"user_id"`            // 用户ID
	Remark            string              `json:"remark"`             // 备注
	PlatformConfig    MediaPlatformConfig `json:"platform_config"`    // 平台配置
	CreatedAt         time.Time           `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time           `json:"updated_at"`         // 更新时间
}

// FromMediaEntity 从domain层实体转换
func FromMediaEntity(entity domain.MediaEntity) MediaItem {
	// 转换平台配置
	platformConfig := MediaPlatformConfig{}
	if entity.PlatformConfig.Platform != "" {
		platformConfig = MediaPlatformConfig{
			Platform: entity.PlatformConfig.Platform,
			Info:     entity.PlatformConfig.Info,
		}
	}

	return MediaItem{
		ID:                entity.ID,
		Code:              entity.Code,
		AdAgentID:         entity.AdAgentID,
		Name:              entity.Name,
		AuditStatus:       entity.AuditStatus,
		CooperationStatus: entity.CooperationStatus,
		CooperationType:   entity.CooperationType,
		Account:           entity.Account,
		LastLoginAt:       entity.LastLoginAt,
		Balance:           entity.Balance,
		Types:             MediaTypes(entity.Types),
		Industry:          entity.Industry,
		CustomIndustry:    entity.CustomIndustry,
		DailyActivity:     entity.DailyActivity,
		TransactionVolume: entity.TransactionVolume,
		CompanyName:       entity.CompanyName,
		CompanyAddress:    entity.CompanyAddress,
		ContactName:       entity.ContactName,
		ContactPhone:      entity.ContactPhone,
		RejectReason:      entity.RejectReason,
		UserID:            entity.UserID,
		Remark:            entity.Remark,
		PlatformConfig:    platformConfig,
		CreatedAt:         entity.CreatedAt,
		UpdatedAt:         entity.UpdatedAt,
	}
}

type MediaDetailReq struct {
	ID int64 `json:"id" form:"id" binding:"required"` // 媒体ID
}

// MediaCreateReq 创建媒体请求结构
type MediaCreateReq struct {
	Name              string               `json:"name" binding:"required"`                // 媒体名称
	Types             []string             `json:"types" binding:"required,dive,required"` // 媒体类型列表
	Industry          string               `json:"industry" binding:"required"`            // 所属行业
	CustomIndustry    string               `json:"custom_industry"`                        // 自定义行业
	DailyActivity     string               `json:"daily_activity"`                         // 日活
	TransactionVolume string               `json:"transaction_volume"`                     // 交易量
	RegionCodes       []string             `json:"region_codes"`                           // 地区编码列表
	CompanyName       string               `json:"company_name"`                           // 公司名称
	CompanyAddress    string               `json:"company_address"`                        // 公司地址
	ContactName       string               `json:"contact_name"`                           // 联系人
	ContactPhone      string               `json:"contact_phone"`                          // 联系电话
	CooperationType   string               `json:"cooperation_type" binding:"required"`    // 合作类型
	Account           string               `json:"account"`                                // 账号
	Password          string               `json:"password"`                               // 密码
	PlatformConfig    *MediaPlatformConfig `json:"platform_config"`                        // 平台配置
	AdAgentID         int64                `json:"ad_agent_id"`                            // 代理ID
	Remark            string               `json:"remark"`                                 // 备注
}

// ToMediaCreateEntity 转换为domain层创建实体
func (r MediaCreateReq) ToMediaCreateEntity() domain.MediaCreateEntity {
	// 处理可空字段
	var adAgentID int64
	if r.AdAgentID > 0 {
		adAgentID = r.AdAgentID
	}

	// 处理平台配置
	var platformConfig domain.MediaPlatformConfig
	if r.PlatformConfig != nil {
		platformConfig = domain.MediaPlatformConfig{
			Platform: r.PlatformConfig.Platform,
			Info:     r.PlatformConfig.Info,
		}
	}

	return domain.MediaCreateEntity{
		Name:              r.Name,
		Types:             r.Types,
		Industry:          r.Industry,
		CustomIndustry:    r.CustomIndustry,
		DailyActivity:     r.DailyActivity,
		TransactionVolume: r.TransactionVolume,
		RegionCodes:       r.RegionCodes,
		CompanyName:       r.CompanyName,
		CompanyAddress:    r.CompanyAddress,
		ContactName:       r.ContactName,
		ContactPhone:      r.ContactPhone,
		CooperationType:   r.CooperationType,
		Account:           r.Account,
		Password:          r.Password,
		PlatformConfig:    platformConfig,
		AdAgentID:         adAgentID,
		Remark:            r.Remark,
	}
}

// MediaCreateResp 创建媒体响应结构
type MediaCreateResp struct {
	ID int64 `json:"id"` // 媒体ID
}

// FromMediaCreateResult 从domain层结果转换
func FromMediaCreateResult(result domain.MediaCreateResult) MediaCreateResp {
	return MediaCreateResp{
		ID: result.ID,
	}
}

// MediaUpdateReq 更新媒体请求结构
type MediaUpdateReq struct {
	ID                int64               `json:"id" binding:"required"`                  // 媒体ID
	Name              string              `json:"name" binding:"required"`                // 媒体名称
	Types             []string            `json:"types" binding:"required,dive,required"` // 媒体类型列表
	Industry          string              `json:"industry" binding:"required"`            // 所属行业
	CustomIndustry    string              `json:"custom_industry"`                        // 自定义行业
	DailyActivity     string              `json:"daily_activity"`                         // 日活
	TransactionVolume string              `json:"transaction_volume"`                     // 交易量
	RegionCodes       []string            `json:"region_codes"`                           // 地区编码列表
	CompanyName       string              `json:"company_name"`                           // 公司名称
	CompanyAddress    string              `json:"company_address"`                        // 公司地址
	ContactName       string              `json:"contact_name"`                           // 联系人
	ContactPhone      string              `json:"contact_phone"`                          // 联系电话
	CooperationType   string              `json:"cooperation_type" binding:"required"`    // 合作类型
	Account           string              `json:"account"`                                // 账号
	Password          string              `json:"password"`                               // 密码
	PlatformConfig    MediaPlatformConfig `json:"platform_config"`                        // 平台配置
	AdAgentID         int64               `json:"ad_agent_id"`                            // 代理ID
	Remark            string              `json:"remark"`                                 // 备注
}

// ToMediaUpdateEntity 转换为domain层更新实体
func (r MediaUpdateReq) ToMediaUpdateEntity() domain.MediaUpdateEntity {
	// 处理可空字段
	var adAgentID int64
	if r.AdAgentID > 0 {
		adAgentID = r.AdAgentID
	}

	// 处理平台配置
	var platformConfig domain.MediaPlatformConfig
	if r.PlatformConfig.Platform != "" {
		platformConfig = domain.MediaPlatformConfig{
			Platform: r.PlatformConfig.Platform,
			Info:     r.PlatformConfig.Info,
		}
	}

	return domain.MediaUpdateEntity{
		ID:                r.ID,
		Name:              r.Name,
		Types:             r.Types,
		Industry:          r.Industry,
		CustomIndustry:    r.CustomIndustry,
		DailyActivity:     r.DailyActivity,
		TransactionVolume: r.TransactionVolume,
		RegionCodes:       r.RegionCodes,
		CompanyName:       r.CompanyName,
		CompanyAddress:    r.CompanyAddress,
		ContactName:       r.ContactName,
		ContactPhone:      r.ContactPhone,
		CooperationType:   r.CooperationType,
		Account:           r.Account,
		Password:          r.Password,
		PlatformConfig:    platformConfig,
		AdAgentID:         adAgentID,
		Remark:            r.Remark,
	}
}

// MediaAuditReq 审核媒体请求结构
type MediaAuditReq struct {
	ID           int64  `json:"id" binding:"required"`                                   // 媒体ID
	AuditStatus  string `json:"audit_status" binding:"required,oneof=approved rejected"` // 审核状态
	RejectReason string `json:"reject_reason"`                                           // 拒绝原因
	Remark       string `json:"remark"`                                                  // 备注
}

// ToMediaAuditEntity 转换为domain层审核实体
func (r MediaAuditReq) ToMediaAuditEntity() domain.MediaAuditEntity {
	return domain.MediaAuditEntity{
		ID:           r.ID,
		AuditStatus:  r.AuditStatus,
		RejectReason: r.RejectReason,
		Remark:       r.Remark,
	}
}

// MediaSelectItem 媒体选择器选项结构（用于下拉选择器）
type MediaSelectItem struct {
	ID   int64  `json:"id"`   // 媒体ID
	Name string `json:"name"` // 媒体名称
}

// FromMediaSelectOption 从domain层选项转换
func FromMediaSelectOption(option domain.MediaSelectOption) MediaSelectItem {
	return MediaSelectItem{
		ID:   option.ID,
		Name: option.Name,
	}
}

// FromMediaSelectOptions 从domain层选项数组转换
func FromMediaSelectOptions(options []domain.MediaSelectOption) []MediaSelectItem {
	result := make([]MediaSelectItem, 0, len(options))
	for _, option := range options {
		result = append(result, FromMediaSelectOption(option))
	}
	return result
}
