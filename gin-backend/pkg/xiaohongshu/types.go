package xiaohongshu

// AccessTokenRequest 获取访问令牌请求
type AccessTokenRequest struct {
	AppId    string `json:"app_id"`    // 应用ID
	Secret   string `json:"secret"`    // 应用密钥
	AuthCode string `json:"auth_code"` // 授权码
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	AppId        string `json:"app_id"`        // 应用ID
	Secret       string `json:"secret"`        // 应用密钥
	RefreshToken string `json:"refresh_token"` // 刷新令牌
}

// Advertiser 广告主信息
type Advertiser struct {
	AdvertiserId   int64  `json:"advertiser_id"`   // 广告主ID
	AdvertiserName string `json:"advertiser_name"` // 广告主名称
}

// TokenData 令牌数据
type TokenData struct {
	UserId                string       `json:"user_id"`                  // 用户ID
	RoleType              int          `json:"role_type"`                // 角色类型
	ApprovalAdvertisers   []Advertiser `json:"approval_advertisers"`     // 授权的广告主列表
	RefreshToken          string       `json:"refresh_token"`            // 刷新令牌
	AdvertiserId          int64        `json:"advertiser_id"`            // 广告主ID
	RefreshTokenExpiresIn int64        `json:"refresh_token_expires_in"` // 刷新令牌过期时间(秒)
	ApprovalRoleType      int          `json:"approval_role_type"`       // 授权角色类型
	PlatformType          int          `json:"platform_type"`            // 平台类型
	AccessToken           string       `json:"access_token"`             // 访问令牌
	AccessTokenExpiresIn  int64        `json:"access_token_expires_in"`  // 访问令牌过期时间(秒)
}

// TokenResponse 令牌响应
type TokenResponse struct {
	Code    int        `json:"code"`    // 状态码
	Success bool       `json:"success"` // 是否成功
	Msg     string     `json:"msg"`     // 消息
	Data    *TokenData `json:"data"`    // 数据
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`    // 错误码
	Success bool   `json:"success"` // 是否成功
	Msg     string `json:"msg"`     // 错误消息
}

// ==================== 创意层级离线报表相关类型 ====================

// ReportRequest 报表请求参数
type ReportRequest struct {
	// 必填参数
	AdvertiserID int64  `json:"advertiser_id"` // 广告主ID
	StartDate    string `json:"start_date"`    // 开始时间，格式 yyyy-MM-dd
	EndDate      string `json:"end_date"`      // 结束时间，格式 yyyy-MM-dd

	// 可选参数
	TimeUnit        string   `json:"time_unit,omitempty"`        // 时间维度: "DAY"(分天), "HOUR"(分时), "SUMMARY"(汇总)
	MarketingTarget []int    `json:"marketing_target,omitempty"` // 营销目标过滤
	BiddingStrategy []int    `json:"bidding_strategy,omitempty"` // 出价方式过滤
	OptimizeTarget  []int    `json:"optimize_target,omitempty"`  // 推广目标过滤
	Placement       []int    `json:"placement,omitempty"`        // 广告类型过滤
	PromotionTarget []int    `json:"promotion_target,omitempty"` // 推广标的类型过滤
	Programmatic    []int    `json:"programmatic,omitempty"`     // 创意组合方式过滤
	BuildType       []int    `json:"build_type,omitempty"`       // 搭建方式过滤
	DeliveryMode    []int    `json:"delivery_mode,omitempty"`    // 投放模式过滤
	SplitColumns    []string `json:"split_columns,omitempty"`    // 细分条件
	SortColumn      string   `json:"sort_column,omitempty"`      // 排序字段
	Sort            string   `json:"sort,omitempty"`             // 排序方式: "asc"(升序), "desc"(降序)
	PageNum         int      `json:"page_num,omitempty"`         // 页数，默认1
	PageSize        int      `json:"page_size,omitempty"`        // 页大小，默认20，最大500
	DataCaliber     int      `json:"data_caliber,omitempty"`     // 数据指标归因时间类型: 0-点击时间, 1-转化时间
	Filters         []Filter `json:"filters,omitempty"`          // 过滤条件
	NoteUserID      string   `json:"note_user_id,omitempty"`     // 笔记作者ID
}

// Filter 过滤条件
type Filter struct {
	Column   string   `json:"column"`   // 筛选列名: creativityId, fee, impression等
	Operator string   `json:"operator"` // 操作符: ">", "<", "in"
	Values   []string `json:"values"`   // 筛选值
}

// ReportResponse 报表API响应
type ReportResponse struct {
	Data struct {
		DataList        []ReportData `json:"data_list"`        // 报表数据列表
		AggregationData ReportData   `json:"aggregation_data"` // 汇总数据
		TotalCount      int64        `json:"total_count"`      // 总记录数
	} `json:"data"`
	RequestID string `json:"request_id"` // 请求ID
	Code      int    `json:"code"`       // 返回码
	Success   bool   `json:"success"`    // 是否成功
	Msg       string `json:"msg"`        // 返回信息
}
