package domain

import (
	"mime/multipart"
	"time"
)

// ModelEntity 模型实体
type ModelEntity struct {
	ID        int64  // 模型ID
	ModelName string // 模型名称
	CreatorID int64  // 创建者ID
}

// ModelParam 模型查询参数
type ModelParam struct {
	Page      int    // 页码
	PageSize  int    // 每页大小
	ModelName string // 模型名称(用于过滤)
}

// ModelListResult 模型列表结果
type ModelListResult struct {
	Total int64       // 总记录数
	Items []ModelItem // 模型列表项
}

// ModelItem 模型列表项
type ModelItem struct {
	ID        int64     // 模型ID
	ModelName string    // 模型名称
	CreatedAt time.Time // 创建时间
	UpdatedAt time.Time // 更新时间
}

// ModelImportParam 模型导入参数
type ModelImportParam struct {
	ModelName string                // 模型名称
	File      *multipart.FileHeader // Excel文件
	CreatorID int64                 // 创建者ID
}

// ModelDetailResult 模型详情
type ModelDetailResult struct {
	ID        int64           // 模型ID
	ModelName string          // 模型名称
	Creator   string          // 创建者
	CreatedAt string          // 创建时间
	UpdatedAt string          // 更新时间
	Data      []ModelDataItem // 模型数据列表
}

// ModelDataItem 模型数据项
type ModelDataItem struct {
	Day   int     // 天数
	Value float64 // 衰减百分比值
}

// ModelDecayInfo 模型衰减记录
type ModelDecayInfo struct {
	ID        uint64  // 记录ID
	ModelID   uint64  // 模型ID
	Percent   float64 // 衰减百分比
	CreatedAt string  // 创建时间
	UpdatedAt string  // 更新时间
}

// DeliveryReportItem 投放报表项
type DeliveryReportItem struct {
	Date              string  // 日期(YYYYMMDD格式)
	TotalOrders       int     // 总订单数
	TotalCommission   float64 // 总佣金
	AverageCommission float64 // 单均佣金
	Cost              float64 // 费用
	NewOrders         int     // 新订单数
	DailyCost         float64 // 今日订单成本
	PaybackDays       int     // 回本周期(天数，-1表示无法回本)
}

// DeliveryReportParam 投放报表查询参数
type DeliveryReportParam struct {
	ModelID    uint64 // 模型ID
	CategoryID uint64 // 分类ID
	GroupID    int64  // 口令组ID
}

// DeliveryReportResult 投放报表结果
type DeliveryReportResult struct {
	Total int64                // 总数
	Items []DeliveryReportItem // 报表项列表
}
