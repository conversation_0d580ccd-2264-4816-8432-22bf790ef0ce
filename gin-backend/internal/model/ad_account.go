package model

import "time"

// AdAccounts  广告投放账号表
type AdAccounts struct {
	ID                  int64     //  主键id
	AccountType         int8      //  账号类型: 1-master主账号 2-sub子账号
	ParentId            int64     //  父账号id（子账号时使用）
	Platform            int64     //  所属平台 1-小红书
	AccountName         string    //  账号名称
	PlatformAccountId   string    //  平台账号id（如xhs_account_id）
	AuthorizationStatus int8      //  授权状态：0未授权 1-已授权 2-已过期 3-继承授权
	Token               string    //  token
	RefreshToken        string    //  新的刷新令牌
	TokenExpireTime     time.Time //  token过期时间
	UsageStatus         int8      //  使用状态：1-启用 2-禁用
	AccountBalance      float64   //  账户余额（最多百亿级）
	RebateRate          float64   //  返利比例
	Owner               string    //  归属人员
	LastSync            time.Time //  最后同步时间
	CreatedAt           time.Time //  创建时间
	UpdatedAt           time.Time //  更新时间
	AccountEmail        string    //  账号邮箱
	MainCompany         string    //  账号主体名称
	MerchantMark        string    //  商户标记
	AccountAppId        string    //  账号appid
	PublicKey           string    //  公钥
	PrivateKey          string    //  私钥
	AdAgentId           int64     //  代理id
	UserId              int64     //  投放运营id
	Remark              string    //  备注

	MediaId  int64 // 媒体ID
	AdSlotId int64 // 广告位ID

	User    User    `gorm:"foreignKey:UserId"`    // 用户
	AdAgent AdAgent `gorm:"foreignKey:AdAgentId"` // 代理商
	AdSlot  Slot    `gorm:"foreignKey:AdSlotId"`  // 广告位
	Media   Media   `gorm:"foreignKey:MediaId"`   // 媒体
}

func (m *AdAccounts) TableName() string {
	return "ad_accounts"
}
