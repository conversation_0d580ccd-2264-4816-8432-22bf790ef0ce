package model

import (
	"time"

	"gorm.io/gorm"
)

// Task 任务模型，用于管理异步任务
type Task struct {
	ID        int64          `gorm:"column:id;primaryKey;autoIncrement"`            // 任务ID
	Name      string         `gorm:"column:name;type:varchar(255);not null"`        // 任务名称
	TaskType  int            `gorm:"column:task_type;not null;index"`               // 任务类型: 1=费用导入, 2=...
	Params    string         `gorm:"column:params;type:text"`                       // 任务参数，JSON格式
	Status    string         `gorm:"column:status;type:varchar(20);not null;index"` // 任务状态: pending, processing, completed, failed
	ErrorMsg  string         `gorm:"column:error_msg;type:text"`                    // 错误信息
	CreatedBy int64          `gorm:"column:created_by"`                             // 创建人
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime"`              // 创建时间
	UpdatedAt time.Time      `gorm:"column:updated_at;autoUpdateTime"`              // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index"`                       // 删除时间
}

// TableName 指定表名
func (Task) TableName() string {
	return "tasks"
}
