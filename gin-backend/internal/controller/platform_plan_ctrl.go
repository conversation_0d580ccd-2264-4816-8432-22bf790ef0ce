package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PlatformPlanController 平台计划控制器
type PlatformPlanController struct {
	service *service.PlatformPlanService
}

// NewPlatformPlanController 创建平台计划控制器
func NewPlatformPlanController() *PlatformPlanController {
	return &PlatformPlanController{
		service: service.NewPlatformPlanService(),
	}
}

// RegisterRoutes 注册路由
func (c *PlatformPlanController) RegisterRoutes(router *gin.Engine) {
	v1 := router.Group("/api/v1")
	v1.Use(middleware.JWTAuth()) // 需要认证

	deliveryGroup := v1.Group("/delivery")
	{
		plansGroup := deliveryGroup.Group("/plans")
		{
			plansGroup.GET("/list", c.List)
			plansGroup.GET("/market-targets", c.MarketTargets)
			plansGroup.PUT("/:id", c.UpdatePlanType)
			plansGroup.PUT("/update", c.Update)
		}
	}
}

// List 获取平台计划列表
// @Summary 获取平台计划列表
// @Description 获取平台计划列表，支持分页和筛选
// @Tags 平台计划
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Param platformType query string false "平台类型"
// @Param planName query string false "计划名称"
// @Param agentId query int false "代理商ID"
// @Param mediaId query int false "媒体ID"
// @Param marketTargetName query string false "营销目标"
// @Param keyword query string false "关键词"
// @Success 200 {object} response.Response{data=vo.PlatformPlanListResp}
// @Router /api/v1/delivery/plans/list [get]
func (c *PlatformPlanController) List(ctx *gin.Context) {
	var req vo.PlatformPlanListReq

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换参数并调用服务层
	result, err := c.service.List(ctx.Request.Context(), req.ToPlatformPlanQueryParam())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取平台计划列表失败: "+err.Error())
		return
	}

	// 转换结果并返回
	resp := vo.FromPlatformPlanItems(result.Total, result.List)
	response.Success(ctx, resp)
}

// Update 更新平台计划
// @Summary 更新平台计划
// @Description 更新现有的平台计划
// @Tags 平台计划
// @Accept json
// @Produce json
// @Param request body vo.UpdatePlatformPlanReq true "更新请求"
// @Success 200 {object} response.Response
// @Router /api/v1/delivery/plans/update [put]
func (c *PlatformPlanController) Update(ctx *gin.Context) {
	var req vo.UpdatePlatformPlanReq

	// 绑定请求体参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换并调用服务层
	if err := c.service.Update(ctx.Request.Context(), req.ToPlatformPlanUpdateParam()); err != nil {
		response.Error(ctx, http.StatusInternalServerError, "更新平台计划失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "更新成功"})
}

// UpdatePlanType 更新计划类型
// @Summary 更新计划类型
// @Description 更新计划的类型
// @Tags 平台计划
// @Accept json
// @Produce json
// @Param id path uint64 true "计划ID"
// @Param request body vo.UpdatePlatformPlanTypeReq true "更新请求"
// @Success 200 {object} response.Response
// @Router /api/v1/delivery/plans/{id} [put]
func (c *PlatformPlanController) UpdatePlanType(ctx *gin.Context) {
	// 从路径获取ID
	idStr := ctx.Param("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "计划ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的计划ID")
		return
	}

	var req vo.PlatformPlanUpdateTypeReq

	// 绑定请求体参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 设置ID
	req.ID = id

	// 调用服务层
	if err := c.service.UpdatePlanType(ctx.Request.Context(), req.ToPlatformPlanUpdateTypeParam()); err != nil {
		response.Error(ctx, http.StatusInternalServerError, "更新计划类型失败: "+err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "更新成功"})
}

// MarketTargets 获取市场目标列表
// @Summary 获取市场目标列表
// @Description 根据平台类型获取可选的市场目标列表
// @Tags 平台计划
// @Accept json
// @Produce json
// @Param platform_type query string true "平台类型"
// @Success 200 {object} response.Response{data=vo.MarketTargetsDetailResp}
// @Router /api/v1/delivery/plans/market-targets [get]
func (c *PlatformPlanController) MarketTargets(ctx *gin.Context) {
	var req vo.MarketTargetsDetailReq

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	result, err := c.service.GetMarketTargets(ctx.Request.Context(), req.ToMarketTargetsDetailParam())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取市场目标列表失败: "+err.Error())
		return
	}

	// 转换并返回结果
	resp := vo.FromMarketTargetsDetail(result.Targets)
	response.Success(ctx, resp)
}
