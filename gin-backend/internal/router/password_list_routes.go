package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupPasswordListRoutes 设置口令列表路由
func SetupPasswordListRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewPasswordListController()

	// 口令列表相关路由
	passwords := router.Group("/passwords")
	{
		// 获取口令列表
		passwords.GET("", ctrl.GetPasswordList)
		
		// 获取口令统计信息
		passwords.GET("/stats", ctrl.GetPasswordStats)
		
		// 导出口令列表
		passwords.POST("/export", ctrl.ExportPasswordList)
		
		// 获取口令详情
		passwords.GET("/:password_name", ctrl.GetPasswordDetail)
		
		// 获取账号列表（用于筛选）
		passwords.GET("/accounts", ctrl.GetAccountList)
	}
}
