package model

import "time"

// AdPlanStats  查询时间范围
type AdPlanStats struct {
	ID               int64     `json:"id"`                 //  自增主键
	PlanId           string    `json:"plan_id"`            //  计划编号
	PlanName         string    `json:"plan_name"`          //  计划名称
	Account          string    `json:"account"`            //  账号名称
	Cost             float64   `json:"cost"`               //  计划消费金额
	ActualCost       float64   `json:"actual_cost"`        //  实际消费金额
	TotalCost        float64   `json:"total_cost"`         //  总消费金额（聚合值）
	Impressions      int64     `json:"impressions"`        //  展现量
	Clicks           int64     `json:"clicks"`             //  点击量
	ClickThroughRate float64   `json:"click_through_rate"` //  点击率（百分比值）
	StatDate         time.Time `json:"stat_date"`          //  统计日期
	CreatedAt        time.Time `json:"created_at"`         //  创建时间
	UpdatedAt        time.Time `json:"updated_at"`         //  更新时间
	AccountId        int64     `json:"account_id"`         //  账号id
}

func (AdPlanStats) TableName() string {
	return "ad_plan_stats"
}
