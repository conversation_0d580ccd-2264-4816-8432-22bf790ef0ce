package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务实现
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务实例
func NewUserService() *UserService {
	return &UserService{
		db: global.DB,
	}
}

// GetMediaUsers 获取媒介用户列表
func (s *UserService) GetMediaUsers(ctx context.Context, userInfo domain.UserEntity) (domain.MediaUserListResult, error) {
	// 权限检查：只有非媒介角色可以查看媒介列表
	if userInfo.Role == domain.RoleMedia {
		return domain.MediaUserListResult{}, fmt.Errorf("媒介用户无权限查看媒介列表")
	}

	var users []model.User
	if err := s.db.WithContext(ctx).
		Select("id, name, real_name").
		Where("role = ? AND status = ? AND is_locked = ?", domain.RoleMedia, domain.StatusActive, domain.UnLocked).
		Order("id DESC").
		Find(&users).Error; err != nil {
		return domain.MediaUserListResult{}, fmt.Errorf("获取媒介用户列表失败: %w", err)
	}

	// 转换为MediaUserEntity结构
	mediaUsers := make([]domain.MediaUserEntity, len(users))
	for i, user := range users {
		mediaUsers[i] = domain.MediaUserEntity{
			ID:       user.ID,
			Username: user.Name,
			RealName: user.RealName,
		}
	}

	return domain.MediaUserListResult{
		List: mediaUsers,
	}, nil
}

// GetUsers 获取用户列表
func (s *UserService) GetUsers(ctx context.Context, param domain.GetUsersParam) (domain.GetUsersResult, error) {
	// 参数校验和默认值设置
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 || param.PageSize > 100 {
		param.PageSize = 20
	}

	// 构建筛选条件
	filters := make(map[string]interface{})
	if param.Name != "" {
		filters["name"] = param.Name
	}
	if param.Role > 0 {
		filters["role"] = param.Role
	}
	if param.Status > 0 {
		filters["status"] = param.Status
	}
	if param.Department != "" {
		filters["department"] = param.Department
	}
	if param.IsLocked >= 0 {
		filters["is_locked"] = param.IsLocked
	}

	// 计算偏移量
	offset := (param.Page - 1) * param.PageSize

	// 获取用户列表
	var users []model.User
	var total int64

	query := s.db.WithContext(ctx).Model(&model.User{})

	// 应用筛选条件
	for key, value := range filters {
		switch key {
		case "name":
			query = query.Where("name LIKE ?", "%"+value.(string)+"%")
		case "real_name":
			query = query.Where("real_name LIKE ?", "%"+value.(string)+"%")
		case "email":
			query = query.Where("email LIKE ?", "%"+value.(string)+"%")
		case "role":
			query = query.Where("role = ?", value)
		case "status":
			query = query.Where("status = ?", value)
		case "department":
			query = query.Where("department = ?", value)
		case "is_locked":
			query = query.Where("is_locked = ?", value)
		case "phone":
			query = query.Where("phone LIKE ?", "%"+value.(string)+"%")
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.GetUsersResult{}, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 分页查询
	err := query.Offset(offset).Limit(param.PageSize).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return domain.GetUsersResult{}, fmt.Errorf("获取用户列表失败: %w", err)
	}

	// 转换为响应格式并填充上级姓名、角色信息和部门名称
	userEntities := make([]domain.UserEntity, len(users))
	for i, user := range users {
		// 创建用户实体
		userEntity := domain.UserEntity{
			ID:           user.ID,
			Name:         user.Name,
			RealName:     user.RealName,
			Email:        user.Email,
			Phone:        user.Phone,
			Department:   user.Department,
			Position:     user.Position,
			SupervisorID: 0, // 默认值，如果有上级ID再更新
			Role:         user.Role,
			RoleID:       0, // 默认值，如果有角色ID再更新
			Status:       user.Status,
			IsLocked:     user.IsLocked,
			LockReason:   user.LockReason,
			Remark:       user.Remark,
			LastLoginIP:  user.LastLoginIP,
			CreatedAt:    user.CreatedAt,
			UpdatedAt:    user.UpdatedAt,
		}

		// 处理可能为空的字段
		userEntity.LastLoginAt = user.LastLoginAt

		// 获取上级姓名
		if user.SupervisorID != 0 {
			userEntity.SupervisorID = user.SupervisorID
			var supervisor model.User
			if err := s.db.WithContext(ctx).Where("id = ?", user.SupervisorID).First(&supervisor).Error; err == nil {
				userEntity.SupervisorName = supervisor.RealName
			}
		}

		// 获取角色信息
		if user.RoleID != 0 {
			userEntity.RoleID = user.RoleID
			var role model.Role
			if err := s.db.WithContext(ctx).Where("id = ?", user.RoleID).First(&role).Error; err == nil {
				userEntity.RoleName = role.Name
			}
		}

		// 获取部门名称
		if user.Department != "" {
			var department model.Department
			if err := s.db.WithContext(ctx).Where("code = ?", user.Department).First(&department).Error; err == nil {
				userEntity.DepartmentName = department.Name
			}
		}

		userEntities[i] = userEntity
	}

	return domain.GetUsersResult{
		List:  userEntities,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, param domain.UserCreateParam) (domain.CreateUserResult, error) {
	// 检查用户名是否存在（通过邮箱检查）
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("email = ?", param.Email).Count(&count).Error; err != nil {
		return domain.CreateUserResult{}, fmt.Errorf("检查邮箱失败: %w", err)
	}
	if count > 0 {
		return domain.CreateUserResult{}, fmt.Errorf("邮箱已存在")
	}

	// 验证上级是否存在且有效
	supervisorID := param.SupervisorID
	if supervisorID > 0 {
		var supervisor model.User
		if err := s.db.WithContext(ctx).Where("id = ?", supervisorID).First(&supervisor).Error; err != nil {
			return domain.CreateUserResult{}, fmt.Errorf("指定的上级用户不存在")
		}
		if supervisor.Status != domain.StatusActive {
			return domain.CreateUserResult{}, fmt.Errorf("指定的上级用户已离职")
		}
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(param.Password), bcrypt.DefaultCost)
	if err != nil {
		return domain.CreateUserResult{}, fmt.Errorf("密码加密失败: %w", err)
	}

	// 创建用户对象
	now := time.Now()
	roleID := param.RoleID

	user := model.User{
		Name:       param.Name,
		RealName:   param.RealName,
		Email:      param.Email,
		Phone:      param.Phone,
		Department: param.Department,
		Position:   param.Position,
		Role:       param.Role,
		Password:   string(hashedPassword),
		Status:     domain.StatusActive,
		IsLocked:   false,
		Remark:     param.Remark,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	// 设置可能为空的字段
	if supervisorID > 0 {
		user.SupervisorID = supervisorID
	}
	if roleID > 0 {
		user.RoleID = roleID
	}

	// 创建用户
	if err := s.db.WithContext(ctx).Create(&user).Error; err != nil {
		return domain.CreateUserResult{}, fmt.Errorf("创建用户失败: %w", err)
	}

	return domain.CreateUserResult{
		ID: user.ID,
	}, nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(ctx context.Context, param domain.UserUpdateParam) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", param.ID).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 检查邮箱是否已被其他用户使用
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("email = ? AND id != ?", param.Email, param.ID).Count(&count).Error; err != nil {
		return fmt.Errorf("检查邮箱失败: %w", err)
	}
	if count > 0 {
		return fmt.Errorf("邮箱已被其他用户使用")
	}

	// 验证上级是否存在且有效
	supervisorID := param.SupervisorID
	if supervisorID > 0 {
		var supervisor model.User
		if err := s.db.WithContext(ctx).Where("id = ?", supervisorID).First(&supervisor).Error; err != nil {
			return fmt.Errorf("指定的上级用户不存在")
		}
		if supervisor.Status != domain.StatusActive {
			return fmt.Errorf("指定的上级用户已离职")
		}
		// 检查是否形成循环引用
		if supervisorID == param.ID {
			return fmt.Errorf("不能将自己设为上级")
		}
	}

	// 更新用户信息
	updates := map[string]interface{}{
		"name":       param.Name,
		"real_name":  param.RealName,
		"email":      param.Email,
		"phone":      param.Phone,
		"department": param.Department,
		"position":   param.Position,
		"role":       param.Role,
		"status":     param.Status,
		"remark":     param.Remark,
		"updated_at": time.Now(),
	}

	// 设置可能为空的字段
	if supervisorID > 0 {
		updates["supervisor_id"] = supervisorID
	} else {
		updates["supervisor_id"] = nil
	}

	roleID := param.RoleID
	if roleID > 0 {
		updates["role_id"] = roleID
	} else {
		updates["role_id"] = nil
	}

	if err := s.db.WithContext(ctx).Model(&user).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新用户失败: %w", err)
	}

	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(ctx context.Context, id int64) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 检查是否有下属引用该用户作为上级
	var subordinateCount int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).Where("supervisor_id = ?", id).Count(&subordinateCount).Error; err != nil {
		return fmt.Errorf("检查下属关系失败: %w", err)
	}
	if subordinateCount > 0 {
		return fmt.Errorf("该用户仍有%d个下属引用，请先解除关联", subordinateCount)
	}

	// 删除用户
	if err := s.db.WithContext(ctx).Delete(&user).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// SetUserInactive 标记用户为离职状态
func (s *UserService) SetUserInactive(ctx context.Context, id int64) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 标记为离职
	if err := s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"status":     domain.StatusInactive,
		"updated_at": time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("标记用户离职失败: %w", err)
	}

	return nil
}

// LockUser 锁定用户
func (s *UserService) LockUser(ctx context.Context, id int64, param domain.LockUserParam) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 锁定用户
	if err := s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"is_locked":   true,
		"lock_reason": param.LockReason,
		"updated_at":  time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("锁定用户失败: %w", err)
	}

	return nil
}

// UnlockUser 解锁用户
func (s *UserService) UnlockUser(ctx context.Context, id int64) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 解锁用户
	if err := s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"is_locked":   false,
		"lock_reason": "",
		"updated_at":  time.Now(),
	}).Error; err != nil {
		return fmt.Errorf("解锁用户失败: %w", err)
	}

	return nil
}

// ResetPassword 重置用户密码
func (s *UserService) ResetPassword(ctx context.Context, id int64, param domain.ResetPasswordParam) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return fmt.Errorf("用户不存在: %w", err)
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(param.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("密码加密失败: %w", err)
	}

	// 更新密码
	now := time.Now()
	if err := s.db.WithContext(ctx).Model(&user).Updates(map[string]interface{}{
		"password":              string(hashedPassword),
		"password_changed_at":   now,
		"force_change_password": 0,
		"updated_at":            now,
	}).Error; err != nil {
		return fmt.Errorf("重置密码失败: %w", err)
	}

	return nil
}

// GetUserOptions 获取用户可选项（角色、部门、上级）
func (s *UserService) GetUserOptions(ctx context.Context) (domain.GetUserOptionsResult, error) {
	// 获取角色选项
	roles := []domain.UserRoleOption{
		{Value: domain.RoleMedia, Label: domain.GetRoleName(domain.RoleMedia)},
		{Value: domain.RoleOperator, Label: domain.GetRoleName(domain.RoleOperator)},
		{Value: domain.RoleAdmin, Label: domain.GetRoleName(domain.RoleAdmin)},
		{Value: domain.RoleTrader, Label: domain.GetRoleName(domain.RoleTrader)},
		{Value: domain.RoleFinance, Label: domain.GetRoleName(domain.RoleFinance)},
		{Value: domain.RoleTech, Label: domain.GetRoleName(domain.RoleTech)},
		{Value: domain.RoleProduct, Label: domain.GetRoleName(domain.RoleProduct)},
		{Value: domain.RoleBI, Label: domain.GetRoleName(domain.RoleBI)},
	}

	// 获取部门选项
	var departments []model.Department
	if err := s.db.WithContext(ctx).Where("status = ?", 1).Order("sort_order").Find(&departments).Error; err != nil {
		return domain.GetUserOptionsResult{}, fmt.Errorf("获取部门列表失败: %w", err)
	}

	// 构建部门ID映射
	deptMap := make(map[int64]*model.Department)
	for i := range departments {
		deptMap[departments[i].ID] = &departments[i]
	}

	// 转换为DepartmentOption结构
	deptOptions := make([]domain.DepartmentOption, len(departments))
	for i, dept := range departments {
		deptOptions[i] = s.toDepartmentOption(&dept, deptMap)
	}

	// 获取上级选项（在职用户）
	var supervisors []model.User
	if err := s.db.WithContext(ctx).
		Select("id, real_name").
		Where("status = ? AND is_locked = ?", domain.StatusActive, false).
		Order("id").
		Find(&supervisors).Error; err != nil {
		return domain.GetUserOptionsResult{}, fmt.Errorf("获取上级用户列表失败: %w", err)
	}

	// 转换为UserOption结构
	supervisorOptions := make([]domain.UserOption, len(supervisors))
	for i, supervisor := range supervisors {
		supervisorOptions[i] = domain.UserOption{
			Value: supervisor.ID,
			Label: supervisor.RealName,
		}
	}

	return domain.GetUserOptionsResult{
		Roles:       roles,
		Departments: deptOptions,
		Supervisors: supervisorOptions,
	}, nil
}

// toDepartmentOption 将Department转换为DepartmentOption
func (s *UserService) toDepartmentOption(dept *model.Department, deptMap map[int64]*model.Department) domain.DepartmentOption {
	option := domain.DepartmentOption{
		ID:       dept.ID,
		Name:     dept.Name,
		Code:     dept.Code,
		ParentID: dept.ParentID,
	}

	// 递归添加子部门
	for _, d := range deptMap {
		if d.ParentID == dept.ID {
			childOption := s.toDepartmentOption(d, deptMap)
			option.Children = append(option.Children, childOption)
		}
	}

	return option
}

// GetByID 通过ID获取用户信息
func (s *UserService) GetByID(ctx context.Context, id int64) (domain.UserEntity, error) {
	var user model.User
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&user).Error; err != nil {
		return domain.UserEntity{}, fmt.Errorf("用户不存在: %w", err)
	}

	entity := domain.UserEntity{
		ID:              user.ID,
		Name:            user.Name,
		RealName:        user.RealName,
		Email:           user.Email,
		Phone:           user.Phone,
		Department:      user.Department,
		Position:        user.Position,
		SupervisorID:    0, // 默认值
		Role:            user.Role,
		RoleID:          0, // 默认值
		Status:          user.Status,
		IsLocked:        user.IsLocked,
		LockReason:      user.LockReason,
		Remark:          user.Remark,
		DefaultWorkMode: user.DefaultWorkMode,
		LastLoginIP:     user.LastLoginIP,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
	}

	// 处理可能为空的字段
	entity.LastLoginAt = user.LastLoginAt

	// 获取上级信息
	if user.SupervisorID != 0 {
		entity.SupervisorID = user.SupervisorID
		var supervisor model.User
		if err := s.db.WithContext(ctx).Where("id = ?", user.SupervisorID).First(&supervisor).Error; err == nil {
			entity.SupervisorName = supervisor.RealName
		}
	}

	// 获取角色信息
	if user.RoleID != 0 {
		entity.RoleID = user.RoleID
		var role model.Role
		if err := s.db.WithContext(ctx).Where("id = ?", user.RoleID).First(&role).Error; err == nil {
			entity.RoleName = role.Name
		}
	}

	// 获取部门名称
	if user.Department != "" {
		var department model.Department
		if err := s.db.WithContext(ctx).Where("code = ?", user.Department).First(&department).Error; err == nil {
			entity.DepartmentName = department.Name
		}
	}

	return entity, nil
}

// getCurrentUser 获取当前登录用户信息
func (s *UserService) getCurrentUser(ctx context.Context) (domain.UserEntity, error) {
	// 从上下文中获取用户ID
	userID, ok := ctx.Value("user_id").(int64)
	if !ok || userID == 0 {
		return domain.UserEntity{}, fmt.Errorf("未登录或用户信息无效")
	}

	// 获取用户详细信息
	return s.GetByID(ctx, userID)
}
