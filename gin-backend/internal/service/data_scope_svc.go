package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// DataScopeService 数据权限服务
type DataScopeService struct {
	db                *gorm.DB
	permissionService *PermissionService
}

// NewDataScopeService 创建数据权限服务实例
func NewDataScopeService() *DataScopeService {
	return &DataScopeService{
		db:                global.DB,
		permissionService: NewPermissionService(),
	}
}

// GetDataScopeInfo 获取用户的数据权限信息
func (s *DataScopeService) GetDataScopeInfo(ctx context.Context, userID int64, module string) (domain.DataScopeEntity, error) {
	// 获取用户信息
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		return domain.DataScopeEntity{}, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 获取数据权限范围
	scope, err := s.permissionService.GetUserDataScope(domain.DataScopeParam{
		UserID: userID,
		Module: module,
	})
	if err != nil {
		// 如果获取权限失败，默认为个人权限
		scope = "self"
	}

	dataScope := domain.DataScopeEntity{
		Scope:         scope,
		CurrentUserID: userID,
		Department:    user.Department,
	}

	// 根据权限范围获取用户ID列表
	switch scope {
	case "all":
		// 全部数据权限，不限制用户ID
		dataScope.UserIDs = nil
	case "dept":
		// 部门数据权限，获取部门下所有用户ID
		if user.Department != "" {
			departmentUserIDs, err := s.permissionService.GetDepartmentUsers(domain.DepartmentUsersParam{
				Department: user.Department,
			})
			if err != nil {
				// 如果获取部门用户失败，降级为个人权限
				dataScope.Scope = "self"
				dataScope.UserIDs = []int64{userID}
			} else {
				dataScope.UserIDs = departmentUserIDs
			}
		} else {
			// 没有部门信息，降级为个人权限
			dataScope.Scope = "self"
			dataScope.UserIDs = []int64{userID}
		}
	case "self":
		// 个人数据权限
		dataScope.UserIDs = []int64{userID}
	default:
		// 默认个人权限
		dataScope.Scope = "self"
		dataScope.UserIDs = []int64{userID}
	}

	return dataScope, nil
}

// ApplyDataScopeToQuery 将数据权限应用到查询中
func (s *DataScopeService) ApplyDataScopeToQuery(query *gorm.DB, dataScope domain.DataScopeEntity, userIDColumn string) *gorm.DB {
	if userIDColumn == "" {
		userIDColumn = "user_id" // 默认用户ID字段
	}

	switch dataScope.Scope {
	case "all":
		// 全部数据权限，不添加过滤条件
		return query
	case "dept", "self":
		// 部门或个人权限，使用用户ID列表过滤
		if len(dataScope.UserIDs) == 1 {
			return query.Where(userIDColumn+" = ?", dataScope.UserIDs[0])
		} else if len(dataScope.UserIDs) > 1 {
			return query.Where(userIDColumn+" IN ?", dataScope.UserIDs)
		} else {
			// 没有可访问的用户，返回空结果
			return query.Where("1 = 0")
		}
	default:
		// 默认个人权限
		return query.Where(userIDColumn+" = ?", dataScope.CurrentUserID)
	}
}

// CheckDataPermission 检查用户是否有权限访问指定用户的数据
func (s *DataScopeService) CheckDataPermission(dataScope domain.DataScopeEntity, targetUserID int64) error {
	switch dataScope.Scope {
	case "all":
		// 全部数据权限，允许访问
		return nil
	case "dept", "self":
		// 检查目标用户ID是否在允许访问的列表中
		for _, userID := range dataScope.UserIDs {
			if userID == targetUserID {
				return nil
			}
		}
		return fmt.Errorf("无权限访问该数据")
	default:
		// 默认只能访问自己的数据
		if targetUserID == dataScope.CurrentUserID {
			return nil
		}
		return fmt.Errorf("无权限访问该数据")
	}
}

// GetDataScopeForUser 为指定用户获取数据权限范围（简化版本）
func (s *DataScopeService) GetDataScopeForUser(ctx context.Context, userEntity domain.UserEntity, module string) (string, []int64, error) {
	dataScope, err := s.GetDataScopeInfo(ctx, userEntity.ID, module)
	if err != nil {
		return "self", []int64{userEntity.ID}, err
	}
	return dataScope.Scope, dataScope.UserIDs, nil
}
