package model

import (
	"time"
)

// Model 模型实体 - 对应数据库models表
type Model struct {
	ID        uint64    `gorm:"primarykey;column:id"`          // 主键ID
	Name      string    `gorm:"column:name;size:255;not null"` // 模型名称
	CreatedAt time.Time `gorm:"column:created_at"`             // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`             // 更新时间

	// 关联关系
	DecayRecords []ModelDecay `gorm:"foreignKey:ModelID"` // 衰减记录列表
}

// TableName 指定表名
func (Model) TableName() string {
	return "models"
}

// ModelDecay 模型衰减记录实体 - 对应数据库model_decay表
type ModelDecay struct {
	ID        uint64    `gorm:"primarykey;column:id"`           // 主键ID
	ModelID   uint64    `gorm:"column:model_id;not null;index"` // 模型ID
	Percent   float64   `gorm:"column:percent;not null"`        // 衰减百分比
	CreatedAt time.Time `gorm:"column:created_at"`              // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`              // 更新时间

	// 关联关系
	Model *Model `gorm:"foreignKey:ModelID"` // 关联的模型
}

// TableName 指定表名
func (ModelDecay) TableName() string {
	return "model_decays"
}
