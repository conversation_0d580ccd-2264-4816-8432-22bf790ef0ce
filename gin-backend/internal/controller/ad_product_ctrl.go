package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdProductController 投放产品控制器
type AdProductController struct {
	adProductService *service.AdProductService
}

// NewAdProductController 创建投放产品控制器实例
func NewAdProductController() *AdProductController {
	return &AdProductController{
		adProductService: service.NewAdProductService(),
	}
}

// GetList 获取投放产品列表
// @Summary 获取投放产品列表
// @Description 分页获取投放产品列表
// @Tags 投放产品
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param name query string false "产品名称"
// @Param status query string false "产品状态"
// @Success 200 {object} response.Response{data=vo.AdProductListResp}
// @Failure 400 {object} response.Response
// @Router /api/ad-products/list [get]
func (c *AdProductController) GetList(ctx *gin.Context) {
	// 创建VO请求对象
	request := vo.AdProductListReq{
		Page:   1,
		Size:   10,
		Name:   ctx.Query("name"),
		Status: ctx.Query("status"),
	}

	// 获取分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
		request.Page = page
	}

	sizeStr := ctx.DefaultQuery("size", "10")
	if size, err := strconv.Atoi(sizeStr); err == nil && size > 0 {
		if size > 100 {
			size = 100
		}
		request.Size = size
	}

	// 转换为领域层参数
	param := request.ToAdProductParam()

	// 调用service获取数据
	result, err := c.adProductService.GetList(ctx, param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取投放产品列表失败: "+err.Error())
		return
	}

	// 转换为VO响应
	listResponse := vo.FromDomainAdProductListEntity(result)
	response.Success(ctx, listResponse)
}

// GetDetail 获取投放产品详情
// @Summary 获取投放产品详情
// @Description 根据ID获取投放产品详情
// @Tags 投放产品
// @Accept json
// @Produce json
// @Param id query uint64 true "产品ID"
// @Success 200 {object} response.Response{data=vo.AdProductDetailResp}
// @Failure 400 {object} response.Response
// @Router /api/ad-products/get [get]
func (c *AdProductController) GetDetail(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "产品ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的产品ID")
		return
	}

	product, err := c.adProductService.GetByID(ctx, id)
	if err != nil {
		response.Error(ctx, response.CodeNotFound, "投放产品不存在")
		return
	}

	// 转换为VO响应
	result := vo.FromDomainAdProductEntity(product)
	response.Success(ctx, result)
}

// GetActiveProducts 获取活跃的投放产品列表
// @Summary 获取活跃的投放产品列表
// @Description 获取状态为活跃的投放产品列表，用于下拉选择等
// @Tags 投放产品
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.AdProductInfoItem}
// @Failure 400 {object} response.Response
// @Router /api/ad-products/active [get]
func (c *AdProductController) GetActiveProducts(ctx *gin.Context) {
	products, err := c.adProductService.GetActiveProducts(ctx)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取活跃投放产品列表失败: "+err.Error())
		return
	}

	// 转换为VO响应
	productInfos := make([]vo.AdProductInfoItem, 0, len(products))
	for _, info := range products {
		productInfos = append(productInfos, vo.FromDomainAdProductInfo(info))
	}

	response.Success(ctx, productInfos)
}
