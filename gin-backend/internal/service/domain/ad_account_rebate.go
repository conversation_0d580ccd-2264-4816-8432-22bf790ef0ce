package domain

import (
	"errors"
	"fmt"
	"time"
)

// AdAccountRebateChangeParam 账号返利比例变更参数
type AdAccountRebateChangeParam struct {
	AccountId     int64     `json:"account_id" binding:"required"`     // 账号ID
	NewRate       float64   `json:"new_rate" binding:"required,min=0"` // 新返利比例
	EffectiveDate time.Time `json:"effective_date" binding:"required"` // 生效日期
	ChangeReason  string    `json:"change_reason"`                     // 变更原因
	OperatorId    *int64    `json:"operator_id"`                       // 操作人ID
	OperatorName  string    `json:"operator_name"`                     // 操作人姓名
}

// Validate 验证参数
func (p AdAccountRebateChangeParam) Validate() error {
	if p.AccountId <= 0 {
		return errors.New("账号ID必须大于0")
	}
	if p.NewRate < 0 || p.NewRate > 100 {
		return errors.New("返利比例必须在0-100之间")
	}
	if p.EffectiveDate.IsZero() {
		return errors.New("生效日期不能为空")
	}
	// 生效日期不能早于今天
	today := time.Now().Truncate(24 * time.Hour)
	if p.EffectiveDate.Before(today) {
		return errors.New("生效日期不能早于今天")
	}
	if len(p.ChangeReason) > 500 {
		return errors.New("变更原因不能超过500个字符")
	}
	if len(p.OperatorName) > 100 {
		return errors.New("操作人姓名不能超过100个字符")
	}
	return nil
}

// AdAccountRebateHistoryItem 账号返利比例变更记录项
type AdAccountRebateHistoryItem struct {
	ID               int64     `json:"id"`                // 记录ID
	AccountId        int64     `json:"account_id"`        // 账号ID
	AccountName      string    `json:"account_name"`      // 账号名称
	OldRate          *float64  `json:"old_rate"`          // 原返利比例
	NewRate          float64   `json:"new_rate"`          // 新返利比例
	RateChange       string    `json:"rate_change"`       // 比例变化描述
	EffectiveDate    string    `json:"effective_date"`    // 生效日期
	ChangeType       string    `json:"change_type"`       // 变更类型
	ChangeTypeDesc   string    `json:"change_type_desc"`  // 变更类型描述
	ChangeReason     string    `json:"change_reason"`     // 变更原因
	OperatorId       *int64    `json:"operator_id"`       // 操作人ID
	OperatorName     string    `json:"operator_name"`     // 操作人姓名
	CreatedAt        string    `json:"created_at"`        // 创建时间
}

// AdAccountRebateHistoryParam 账号返利比例变更记录查询参数
type AdAccountRebateHistoryParam struct {
	AccountId    *int64 `form:"account_id"`    // 账号ID
	AccountName  string `form:"account_name"`  // 账号名称（模糊查询）
	ChangeType   string `form:"change_type"`   // 变更类型
	OperatorName string `form:"operator_name"` // 操作人姓名（模糊查询）
	StartDate    string `form:"start_date"`    // 开始日期 YYYY-MM-DD
	EndDate      string `form:"end_date"`      // 结束日期 YYYY-MM-DD
	Page         int    `form:"page"`          // 页码
	PageSize     int    `form:"page_size"`     // 每页数量
}

// Validate 验证查询参数
func (p AdAccountRebateHistoryParam) Validate() error {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 || p.PageSize > 1000 {
		p.PageSize = 20
	}
	if p.ChangeType != "" && !IsValidChangeType(p.ChangeType) {
		return errors.New("无效的变更类型")
	}
	if p.StartDate != "" {
		if _, err := time.Parse("2006-01-02", p.StartDate); err != nil {
			return errors.New("开始日期格式错误，请使用YYYY-MM-DD格式")
		}
	}
	if p.EndDate != "" {
		if _, err := time.Parse("2006-01-02", p.EndDate); err != nil {
			return errors.New("结束日期格式错误，请使用YYYY-MM-DD格式")
		}
	}
	if p.StartDate != "" && p.EndDate != "" {
		startDate, _ := time.Parse("2006-01-02", p.StartDate)
		endDate, _ := time.Parse("2006-01-02", p.EndDate)
		if startDate.After(endDate) {
			return errors.New("开始日期不能晚于结束日期")
		}
	}
	return nil
}

// AdAccountRebateHistoryResult 账号返利比例变更记录查询结果
type AdAccountRebateHistoryResult struct {
	List  []AdAccountRebateHistoryItem `json:"list"`  // 记录列表
	Total int64                        `json:"total"` // 总数量
	Page  int                          `json:"page"`  // 当前页码
	Size  int                          `json:"size"`  // 每页数量
}

// AdAccountRebateListParam 账号返利比例列表查询参数
type AdAccountRebateListParam struct {
	AccountId   *int64 `form:"account_id"`   // 账号ID
	AccountName string `form:"account_name"` // 账号名称（模糊查询）
	Page        int    `form:"page"`         // 页码
	PageSize    int    `form:"page_size"`    // 每页数量
}

// Validate 验证列表查询参数
func (p AdAccountRebateListParam) Validate() error {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 || p.PageSize > 1000 {
		p.PageSize = 20
	}
	return nil
}

// AdAccountRebateListItem 账号返利比例列表项
type AdAccountRebateListItem struct {
	ID            int64   `json:"id"`             // 主键ID
	AccountId     int64   `json:"account_id"`     // 账号ID
	AccountName   string  `json:"account_name"`   // 账号名称
	Rate          float64 `json:"rate"`           // 返利比例
	EffectiveDate string  `json:"effective_date"` // 生效日期
	CreatedAt     string  `json:"created_at"`     // 创建时间
	UpdatedAt     string  `json:"updated_at"`     // 更新时间
}

// AdAccountRebateListResult 账号返利比例列表查询结果
type AdAccountRebateListResult struct {
	List  []AdAccountRebateListItem `json:"list"`  // 列表
	Total int64                     `json:"total"` // 总数量
	Page  int                       `json:"page"`  // 当前页码
	Size  int                       `json:"size"`  // 每页数量
}

// AdAccountRebateStatsResult 账号返利比例统计结果
type AdAccountRebateStatsResult struct {
	TotalAccounts    int64   `json:"total_accounts"`    // 总账号数
	ActiveAccounts   int64   `json:"active_accounts"`   // 有效账号数
	AverageRate      float64 `json:"average_rate"`      // 平均返利比例
	MaxRate          float64 `json:"max_rate"`          // 最高返利比例
	MinRate          float64 `json:"min_rate"`          // 最低返利比例
	RecentChanges    int64   `json:"recent_changes"`    // 最近30天变更次数
}

// ValidateAdAccountRebateChangeParam 验证账号返利比例变更参数
func ValidateAdAccountRebateChangeParam(param AdAccountRebateChangeParam) error {
	return param.Validate()
}

// ValidateAdAccountRebateHistoryParam 验证账号返利比例变更记录查询参数
func ValidateAdAccountRebateHistoryParam(param AdAccountRebateHistoryParam) error {
	return param.Validate()
}

// ValidateAdAccountRebateListParam 验证账号返利比例列表查询参数
func ValidateAdAccountRebateListParam(param AdAccountRebateListParam) error {
	return param.Validate()
}

// IsValidChangeType 验证变更类型是否有效
func IsValidChangeType(changeType string) bool {
	return changeType == "create" || 
		   changeType == "update" || 
		   changeType == "delete"
}

// FormatRateChange 格式化比例变化描述
func FormatRateChange(oldRate *float64, newRate float64) string {
	if oldRate == nil {
		return fmt.Sprintf("新增: %.2f%%", newRate)
	}
	if *oldRate == newRate {
		return fmt.Sprintf("无变化: %.2f%%", newRate)
	}
	change := newRate - *oldRate
	if change > 0 {
		return fmt.Sprintf("%.2f%% → %.2f%% (+%.2f%%)", *oldRate, newRate, change)
	} else {
		return fmt.Sprintf("%.2f%% → %.2f%% (%.2f%%)", *oldRate, newRate, change)
	}
}

// GetChangeTypeDescription 获取变更类型描述
func GetChangeTypeDescription(changeType string) string {
	switch changeType {
	case "create":
		return "新增返利比例"
	case "update":
		return "修改返利比例"
	case "delete":
		return "删除返利比例"
	default:
		return "未知操作"
	}
}
