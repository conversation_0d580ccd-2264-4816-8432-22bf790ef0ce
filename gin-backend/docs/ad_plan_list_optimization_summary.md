# 计划列表功能优化总结

## 优化概述

本次优化主要针对计划列表功能进行了两个重要改进：
1. **单日查询账号名称获取优化** - 从账号表读取真实账号名称
2. **多日查询数据源优化** - 改为从统计表聚合，提升性能和一致性

## 修改详情

### 1. 单日查询优化

#### 问题
- 单日查询时，账号名称字段为空字符串
- 用户无法看到计划对应的账号信息

#### 解决方案
- 添加 `getAccountNameByID` 方法从账号表获取账号名称
- 修改单日查询逻辑，为每个计划获取真实的账号名称

#### 代码变更
```go
// 新增方法
func (s *AdPlanListService) getAccountNameByID(ctx context.Context, accountID int64) string {
    var accountName string
    s.db.WithContext(ctx).Model(&model.AdAccounts{}).
        Where("id = ?", accountID).
        Pluck("account_name", &accountName)
    return accountName
}

// 修改单日查询逻辑
for _, stat := range stats {
    // 从账号表获取账号名称
    accountName := s.getAccountNameByID(ctx, stat.AccountId)
    
    plans = append(plans, domain.AdPlanListItem{
        AccountName: accountName, // 真实账号名称
        // ... 其他字段
    })
}
```

### 2. 多日查询优化

#### 问题
- 多日查询从 `xhs_creative_reports` 创意报表聚合，性能较差
- 与单日查询数据源不一致，可能存在数据差异

#### 解决方案
- 改为从 `ad_plan_stats` 统计表聚合
- 通过 `plan_id` 进行跨时间维度聚合
- 统一单日和多日查询的数据源

#### 核心聚合SQL
```sql
SELECT 
    plan_id,
    MAX(plan_name) as plan_name,
    MAX(account_id) as account_id,
    SUM(cost) as consumption,
    SUM(actual_cost) as actual_cost,
    SUM(impressions) as impressions,
    SUM(clicks) as clicks,
    CASE 
        WHEN SUM(impressions) > 0 THEN (SUM(clicks) * 100.0 / SUM(impressions))
        ELSE 0 
    END as click_rate,
    MAX(updated_at) as last_update
FROM ad_plan_stats
WHERE DATE(stat_date) BETWEEN ? AND ?
GROUP BY plan_id
ORDER BY consumption DESC, plan_id ASC
```

### 3. 新增筛选条件处理

添加了专门的多日查询筛选条件处理方法：

```go
func (s *AdPlanListService) addPlanStatsFilterConditions(query *gorm.DB, param domain.AdPlanListParam) {
    // 账号ID筛选
    if param.AccountID != nil {
        query.Where("account_id = ?", *param.AccountID)
    }
    
    // 账号名称筛选（先查询账号表获取ID）
    if param.AccountName != "" {
        var accountIDs []int64
        s.db.Model(&model.AdAccounts{}).
            Where("account_name LIKE ?", "%"+param.AccountName+"%").
            Pluck("id", &accountIDs)
        if len(accountIDs) > 0 {
            query.Where("account_id IN ?", accountIDs)
        } else {
            query.Where("account_id = -1")
        }
    }
    
    // 计划名称筛选
    if param.PlanName != "" {
        query.Where("plan_name LIKE ?", "%"+param.PlanName+"%")
    }
    
    // 时间范围筛选
    if param.StartDate != nil {
        query.Where("DATE(stat_date) >= ?", param.StartDate.Format("2006-01-02"))
    }
    if param.EndDate != nil {
        query.Where("DATE(stat_date) <= ?", param.EndDate.Format("2006-01-02"))
    }
}
```

## 影响的功能模块

### 1. 计划列表查询
- **单日查询**：现在显示真实账号名称
- **多日查询**：从统计表聚合，性能提升

### 2. 计划列表导出
- **单日导出**：包含真实账号名称
- **多日导出**：从统计表聚合，与查询逻辑一致

### 3. 计划统计信息
- **单日统计**：保持原有逻辑
- **多日统计**：从统计表聚合，数据更准确

## 性能优化效果

### 1. 查询性能提升
- **多日查询**：从大表（创意报表）改为小表（统计表）查询
- **索引利用**：统计表的索引设计更适合聚合查询
- **数据量减少**：统计表数据量远小于创意报表

### 2. 数据一致性
- **统一数据源**：单日和多日查询都基于统计表
- **计算逻辑统一**：避免不同数据源的计算差异
- **实时性保证**：统计表数据及时更新

## 数据查询策略对比

| 查询类型 | 修改前 | 修改后 |
|---------|--------|--------|
| 单日查询 | ad_plan_stats（账号名称为空） | ad_plan_stats + ad_accounts |
| 多日查询 | xhs_creative_reports 聚合 | ad_plan_stats 按plan_id聚合 |
| 账号名称 | 创意报表中的account_name | 账号表中的account_name |
| 性能 | 多日查询较慢 | 统一快速查询 |
| 一致性 | 可能存在差异 | 数据源统一 |

## 使用示例

### 1. 单日查询
```bash
curl -X GET "http://localhost:8080/api/v1/plans?start_date=2024-06-16&end_date=2024-06-16"
```

**响应**：
```json
{
  "data": {
    "list": [
      {
        "plan_id": "plan_001",
        "plan_name": "测试计划",
        "account_name": "营销树长大", // 现在显示真实账号名称
        "consumption": 1000.0,
        "actual_cost": 900.0
      }
    ]
  }
}
```

### 2. 多日查询
```bash
curl -X GET "http://localhost:8080/api/v1/plans?start_date=2024-06-10&end_date=2024-06-16"
```

**响应**：
```json
{
  "data": {
    "list": [
      {
        "plan_id": "plan_001",
        "plan_name": "测试计划",
        "account_name": "营销树长大", // 从账号表获取
        "consumption": 7000.0,       // 7天消费总和
        "actual_cost": 6300.0,       // 7天实际消费总和
        "impressions": 70000,        // 7天展现量总和
        "clicks": 700,               // 7天点击量总和
        "click_rate": 1.0            // 重新计算的点击率
      }
    ]
  }
}
```

## 测试验证

### 1. 单元测试
- `TestAdPlanListService_getAccountNameByID` - 验证账号名称获取
- `TestAdPlanListService_GetPlanList` - 验证单日查询
- `TestAdPlanListService_GetPlanList_MultiDay` - 验证多日聚合

### 2. 功能测试
- 单日查询显示正确账号名称
- 多日查询正确聚合数据
- 筛选条件正常工作
- 导出功能数据一致

## 注意事项

### 1. 数据依赖
- 确保 `ad_plan_stats` 表数据及时更新
- 监控数据聚合任务的执行状态

### 2. 性能考虑
- 账号名称查询：每个计划都会查询一次
- 如果性能成为问题，可以考虑批量查询优化

### 3. 兼容性
- API接口保持不变
- 响应数据结构不变
- 前端无需修改

## 总结

本次优化显著提升了计划列表功能的用户体验和系统性能：

1. **用户体验提升**：单日查询现在显示真实的账号名称
2. **性能优化**：多日查询从统计表聚合，查询速度更快
3. **数据一致性**：统一数据源，避免数据差异
4. **代码维护性**：逻辑更清晰，易于维护和扩展

这些改进确保了计划列表功能在各种查询场景下都能提供准确、快速的数据服务。
