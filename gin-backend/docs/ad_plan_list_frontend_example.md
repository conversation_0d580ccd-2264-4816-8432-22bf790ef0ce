# 计划列表前端集成示例

## Vue.js 集成示例

### 1. 计划列表页面组件

```vue
<template>
  <div class="plan-list-container">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="账号">
          <el-select v-model="filterForm.account_id" placeholder="全部" clearable>
            <el-option
              v-for="account in accountOptions"
              :key="account.id"
              :label="account.name"
              :value="account.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="计划名称">
          <el-input v-model="filterForm.plan_name" placeholder="请输入计划名称"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="总计划数" :value="stats.total_plans" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总消费" :value="stats.total_cost" :precision="2" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="总点击量" :value="stats.total_clicks" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均点击率" :value="stats.avg_click_rate" suffix="%" :precision="2" />
        </el-col>
      </el-row>
    </div>

    <!-- 计划列表表格 -->
    <div class="table-section">
      <el-table
        :data="planList"
        v-loading="loading"
        stripe
        border>
        <el-table-column prop="plan_id" label="计划编号" width="120" />
        <el-table-column prop="plan_name" label="计划名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="account_name" label="账号" width="120" />
        <el-table-column prop="consumption" label="消费" width="100" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.consumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="actual_cost" label="实际消费" width="100" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.actual_cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="impressions" label="展现量" width="100" align="right" />
        <el-table-column prop="clicks" label="点击量" width="100" align="right" />
        <el-table-column prop="click_rate" label="点击率" width="100" align="right">
          <template #default="scope">
            {{ scope.row.click_rate.toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="last_update" label="最后更新" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleViewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPlanList, getPlanStats, exportPlanList, getAccountList } from '@/api/plan'

// 响应式数据
const loading = ref(false)
const planList = ref([])
const accountOptions = ref([])
const stats = ref({
  total_plans: 0,
  total_cost: 0,
  total_clicks: 0,
  avg_click_rate: 0
})

// 筛选表单
const filterForm = reactive({
  account_id: '',
  plan_name: ''
})

// 日期范围
const dateRange = ref([])

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 计算属性：获取查询参数
const queryParams = computed(() => {
  const params = {
    page: pagination.page,
    page_size: pagination.page_size
  }
  
  if (filterForm.account_id) {
    params.account_id = filterForm.account_id
  }
  
  if (filterForm.plan_name) {
    params.plan_name = filterForm.plan_name
  }
  
  if (dateRange.value && dateRange.value.length === 2) {
    params.start_date = dateRange.value[0]
    params.end_date = dateRange.value[1]
  }
  
  return params
})

// 获取计划列表
const fetchPlanList = async () => {
  try {
    loading.value = true
    const response = await getPlanList(queryParams.value)
    planList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取计划列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const params = {}
    if (filterForm.account_id) {
      params.account_id = filterForm.account_id
    }
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const response = await getPlanStats(params)
    stats.value = response.data
  } catch (error) {
    console.error('获取统计信息失败：', error)
  }
}

// 获取账号列表
const fetchAccountList = async () => {
  try {
    const response = await getAccountList()
    accountOptions.value = response.data
  } catch (error) {
    console.error('获取账号列表失败：', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPlanList()
  fetchStats()
}

// 重置
const handleReset = () => {
  filterForm.account_id = ''
  filterForm.plan_name = ''
  dateRange.value = []
  pagination.page = 1
  fetchPlanList()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      format: 'xlsx'
    }
    
    if (filterForm.account_id) {
      params.account_id = filterForm.account_id
    }
    
    if (filterForm.plan_name) {
      params.plan_name = filterForm.plan_name
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    await exportPlanList(params)
    ElMessage.success('导出任务已启动，请稍后查看导出结果')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

// 查看详情
const handleViewDetail = (row) => {
  // 跳转到详情页面或打开详情弹窗
  console.log('查看详情：', row)
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.page_size = size
  pagination.page = 1
  fetchPlanList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page
  fetchPlanList()
}

// 格式化货币
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountList()
  fetchPlanList()
  fetchStats()
})
</script>

<style scoped>
.plan-list-container {
  padding: 20px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.table-section {
  background: white;
  border-radius: 4px;
}

.pagination-section {
  padding: 20px;
  text-align: right;
}
</style>
```

### 2. API 服务文件

```javascript
// api/plan.js
import request from '@/utils/request'

// 获取计划列表
export function getPlanList(params) {
  return request({
    url: '/api/v1/ad-plans',
    method: 'get',
    params
  })
}

// 获取计划统计信息
export function getPlanStats(params) {
  return request({
    url: '/api/v1/ad-plans/stats',
    method: 'get',
    params
  })
}

// 导出计划列表
export function exportPlanList(data) {
  return request({
    url: '/api/v1/ad-plans/export',
    method: 'post',
    data
  })
}

// 获取计划详情
export function getPlanDetail(planId) {
  return request({
    url: `/api/v1/ad-plans/${planId}`,
    method: 'get'
  })
}

// 获取账号列表
export function getAccountList() {
  return request({
    url: '/api/v1/ad-plans/accounts',
    method: 'get'
  })
}
```

## React.js 集成示例

### 1. 计划列表组件

```jsx
import React, { useState, useEffect, useCallback } from 'react'
import {
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Card,
  Statistic,
  Row,
  Col,
  Pagination,
  message
} from 'antd'
import { getPlanList, getPlanStats, exportPlanList, getAccountList } from '../api/plan'

const { RangePicker } = DatePicker
const { Option } = Select

const PlanListPage = () => {
  const [loading, setLoading] = useState(false)
  const [planList, setPlanList] = useState([])
  const [accountOptions, setAccountOptions] = useState([])
  const [stats, setStats] = useState({
    total_plans: 0,
    total_cost: 0,
    total_clicks: 0,
    avg_click_rate: 0
  })
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  
  const [form] = Form.useForm()

  // 表格列定义
  const columns = [
    {
      title: '计划编号',
      dataIndex: 'plan_id',
      key: 'plan_id',
      width: 120
    },
    {
      title: '计划名称',
      dataIndex: 'plan_name',
      key: 'plan_name',
      ellipsis: true
    },
    {
      title: '账号',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 120
    },
    {
      title: '消费',
      dataIndex: 'consumption',
      key: 'consumption',
      width: 100,
      align: 'right',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '实际消费',
      dataIndex: 'actual_cost',
      key: 'actual_cost',
      width: 100,
      align: 'right',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '展现量',
      dataIndex: 'impressions',
      key: 'impressions',
      width: 100,
      align: 'right'
    },
    {
      title: '点击量',
      dataIndex: 'clicks',
      key: 'clicks',
      width: 100,
      align: 'right'
    },
    {
      title: '点击率',
      dataIndex: 'click_rate',
      key: 'click_rate',
      width: 100,
      align: 'right',
      render: (value) => `${value.toFixed(2)}%`
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      width: 160
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Button type="link" onClick={() => handleViewDetail(record)}>
          详情
        </Button>
      )
    }
  ]

  // 获取计划列表
  const fetchPlanList = useCallback(async () => {
    try {
      setLoading(true)
      const values = form.getFieldsValue()
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        ...values
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      const response = await getPlanList(params)
      setPlanList(response.data.list)
      setPagination(prev => ({
        ...prev,
        total: response.data.total
      }))
    } catch (error) {
      message.error('获取计划列表失败：' + error.message)
    } finally {
      setLoading(false)
    }
  }, [form, pagination.current, pagination.pageSize])

  // 获取统计信息
  const fetchStats = useCallback(async () => {
    try {
      const values = form.getFieldsValue()
      const params = {}
      
      if (values.account_id) {
        params.account_id = values.account_id
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      const response = await getPlanStats(params)
      setStats(response.data)
    } catch (error) {
      console.error('获取统计信息失败：', error)
    }
  }, [form])

  // 获取账号列表
  const fetchAccountList = useCallback(async () => {
    try {
      const response = await getAccountList()
      setAccountOptions(response.data)
    } catch (error) {
      console.error('获取账号列表失败：', error)
    }
  }, [])

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPlanList()
    fetchStats()
  }

  // 重置
  const handleReset = () => {
    form.resetFields()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPlanList()
    fetchStats()
  }

  // 导出
  const handleExport = async () => {
    try {
      const values = form.getFieldsValue()
      const params = {
        format: 'xlsx',
        ...values
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      await exportPlanList(params)
      message.success('导出任务已启动，请稍后查看导出结果')
    } catch (error) {
      message.error('导出失败：' + error.message)
    }
  }

  // 查看详情
  const handleViewDetail = (record) => {
    console.log('查看详情：', record)
  }

  // 分页改变
  const handleTableChange = (page, pageSize) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize
    }))
  }

  useEffect(() => {
    fetchAccountList()
  }, [fetchAccountList])

  useEffect(() => {
    fetchPlanList()
    fetchStats()
  }, [fetchPlanList, fetchStats])

  return (
    <div style={{ padding: 24 }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="inline">
          <Form.Item name="account_id" label="账号">
            <Select placeholder="全部" allowClear style={{ width: 120 }}>
              {accountOptions.map(option => (
                <Option key={option.id} value={option.value}>
                  {option.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="dateRange" label="时间">
            <RangePicker format="YYYY-MM-DD" />
          </Form.Item>
          
          <Form.Item name="plan_name" label="计划名称">
            <Input placeholder="请输入计划名称" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleReset}>
              重置
            </Button>
            <Button type="default" style={{ marginLeft: 8 }} onClick={handleExport}>
              导出
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总计划数" value={stats.total_plans} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总消费" value={stats.total_cost} precision={2} prefix="¥" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总点击量" value={stats.total_clicks} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="平均点击率" value={stats.avg_click_rate} precision={2} suffix="%" />
          </Card>
        </Col>
      </Row>

      {/* 计划列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={planList}
          loading={loading}
          rowKey="plan_id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default PlanListPage
```

## 关键实现要点

### 1. 数据查询优化
- 根据时间范围自动选择查询策略（单日/多日）
- 合理设置分页参数，避免一次加载过多数据
- 统计信息与列表数据分离查询，提升用户体验

### 2. 用户体验优化
- 提供实时的统计信息展示
- 支持多维度筛选条件
- 异步导出避免页面阻塞
- 响应式设计适配不同屏幕

### 3. 错误处理
- 网络请求异常处理
- 用户友好的错误提示
- 加载状态指示

### 4. 性能优化
- 使用 useCallback 避免不必要的重渲染
- 合理的数据缓存策略
- 分页加载减少内存占用
