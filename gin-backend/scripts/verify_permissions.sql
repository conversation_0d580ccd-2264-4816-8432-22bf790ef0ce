-- ========================================
-- 权限系统验证脚本
-- 检查权限系统的完整性和正确性
-- 执行时间：$(date '+%Y-%m-%d %H:%M:%S')
-- ========================================

-- 显示当前数据库
SELECT DATABASE() as '当前数据库';
SELECT NOW() as '检查时间';

-- ========================================
-- 基础表检查
-- ========================================
SELECT '=== 基础表检查 ===' as '检查项目';

SELECT 
    table_name as '表名',
    table_rows as '记录数',
    CASE 
        WHEN table_rows > 0 THEN '✓ 有数据'
        ELSE '⚠️  无数据'
    END as '状态'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('roles', 'permissions', 'role_permissions', 'users')
ORDER BY table_name;

-- ========================================
-- 权限模块统计
-- ========================================
SELECT '=== 权限模块统计 ===' as '检查项目';

SELECT 
    module as '模块',
    COUNT(*) as '权限数量',
    COUNT(CASE WHEN type = 'function' THEN 1 END) as '功能权限',
    COUNT(CASE WHEN type = 'data' THEN 1 END) as '数据权限'
FROM permissions 
WHERE status = 1
GROUP BY module
ORDER BY module;

-- ========================================
-- 角色权限统计
-- ========================================
SELECT '=== 角色权限统计 ===' as '检查项目';

SELECT 
    r.name as '角色名称',
    r.code as '角色编码',
    COUNT(rp.permission_id) as '拥有权限数',
    CASE 
        WHEN COUNT(rp.permission_id) > 0 THEN '✓ 已配置'
        ELSE '⚠️  未配置权限'
    END as '状态'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
WHERE r.status = 1
GROUP BY r.id, r.name, r.code
ORDER BY r.sort_order;

-- ========================================
-- 报表权限详细检查
-- ========================================
SELECT '=== 报表权限详细检查 ===' as '检查项目';

-- 检查日报表权限
SELECT 
    '日报表权限' as '权限类型',
    COUNT(*) as '权限数量'
FROM permissions 
WHERE code LIKE 'report:daily:%' AND status = 1;

-- 检查周报表权限
SELECT 
    '周报表权限' as '权限类型',
    COUNT(*) as '权限数量'
FROM permissions 
WHERE code LIKE 'report:weekly:%' AND status = 1;

-- 显示报表权限详情
SELECT 
    code as '权限编码',
    name as '权限名称',
    type as '权限类型',
    data_scope as '数据范围'
FROM permissions 
WHERE code LIKE 'report:%' AND status = 1
ORDER BY code;

-- ========================================
-- 权限分配矩阵检查
-- ========================================
SELECT '=== 权限分配矩阵检查 ===' as '检查项目';

SELECT 
    r.name as '角色',
    COUNT(CASE WHEN p.code LIKE 'dashboard:%' THEN 1 END) as '仪表盘',
    COUNT(CASE WHEN p.code LIKE 'report:daily:%' THEN 1 END) as '日报表',
    COUNT(CASE WHEN p.code LIKE 'report:weekly:%' THEN 1 END) as '周报表',
    COUNT(CASE WHEN p.code LIKE 'agent:%' THEN 1 END) as '代理管理',
    COUNT(CASE WHEN p.code LIKE 'media:%' THEN 1 END) as '媒体管理',
    COUNT(CASE WHEN p.code LIKE 'plan:%' THEN 1 END) as '投放计划',
    COUNT(CASE WHEN p.code LIKE 'delivery:%' THEN 1 END) as '投放管理',
    COUNT(CASE WHEN p.code LIKE 'system:%' THEN 1 END) as '系统管理'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.status = 1
WHERE r.status = 1
GROUP BY r.id, r.name
ORDER BY r.sort_order;

-- ========================================
-- 数据权限范围检查
-- ========================================
SELECT '=== 数据权限范围检查 ===' as '检查项目';

SELECT 
    r.name as '角色',
    COUNT(CASE WHEN p.data_scope = 'all' THEN 1 END) as '全部数据权限',
    COUNT(CASE WHEN p.data_scope = 'dept' THEN 1 END) as '部门数据权限',
    COUNT(CASE WHEN p.data_scope = 'self' THEN 1 END) as '个人数据权限'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.status = 1 AND p.type = 'data'
WHERE r.status = 1
GROUP BY r.id, r.name
ORDER BY r.sort_order;

-- ========================================
-- 潜在问题检查
-- ========================================
SELECT '=== 潜在问题检查 ===' as '检查项目';

-- 检查是否有权限未分配给任何角色
SELECT 
    '未分配权限' as '问题类型',
    COUNT(*) as '数量'
FROM permissions p
LEFT JOIN role_permissions rp ON p.id = rp.permission_id
WHERE p.status = 1 AND rp.permission_id IS NULL;

-- 检查是否有角色没有任何权限
SELECT 
    '无权限角色' as '问题类型',
    COUNT(*) as '数量'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
WHERE r.status = 1 AND rp.role_id IS NULL;

-- 检查重复的权限编码
SELECT 
    '重复权限编码' as '问题类型',
    COUNT(*) as '数量'
FROM (
    SELECT code
    FROM permissions
    GROUP BY code
    HAVING COUNT(*) > 1
) t;

-- 显示具体的未分配权限
SELECT '未分配给任何角色的权限:' as '详情';
SELECT 
    p.code as '权限编码',
    p.name as '权限名称',
    p.module as '模块'
FROM permissions p
LEFT JOIN role_permissions rp ON p.id = rp.permission_id
WHERE p.status = 1 AND rp.permission_id IS NULL
ORDER BY p.module, p.code;

-- ========================================
-- 权限继承关系检查
-- ========================================
SELECT '=== 权限继承关系检查 ===' as '检查项目';

-- 检查超级管理员是否拥有所有权限
SELECT 
    'super_admin权限覆盖率' as '检查项目',
    CONCAT(
        COUNT(rp.permission_id), '/', 
        (SELECT COUNT(*) FROM permissions WHERE status = 1), 
        ' (', 
        ROUND(COUNT(rp.permission_id) * 100.0 / (SELECT COUNT(*) FROM permissions WHERE status = 1), 1), 
        '%)'
    ) as '覆盖情况'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.status = 1
WHERE r.code = 'super_admin' AND r.status = 1;

-- ========================================
-- 权限命名规范检查
-- ========================================
SELECT '=== 权限命名规范检查 ===' as '检查项目';

-- 检查权限编码命名规范
SELECT 
    '不规范权限编码' as '问题类型',
    COUNT(*) as '数量'
FROM permissions 
WHERE status = 1 
AND code NOT REGEXP '^[a-z]+:[a-z_:]+$';

-- 显示不规范的权限编码
SELECT '不规范的权限编码:' as '详情';
SELECT 
    code as '权限编码',
    name as '权限名称'
FROM permissions 
WHERE status = 1 
AND code NOT REGEXP '^[a-z]+:[a-z_:]+$'
ORDER BY code;

-- ========================================
-- 总结报告
-- ========================================
SELECT '=== 权限系统健康度报告 ===' as '检查项目';

SELECT 
    CONCAT('权限总数: ', COUNT(*)) as '统计信息'
FROM permissions WHERE status = 1;

SELECT 
    CONCAT('角色总数: ', COUNT(*)) as '统计信息'
FROM roles WHERE status = 1;

SELECT 
    CONCAT('权限分配总数: ', COUNT(*)) as '统计信息'
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.id AND r.status = 1
JOIN permissions p ON rp.permission_id = p.id AND p.status = 1;

-- 计算权限系统健康度评分
SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM permissions p
            LEFT JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE p.status = 1 AND rp.permission_id IS NULL
        ) = 0 
        AND (
            SELECT COUNT(*) FROM roles r
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            WHERE r.status = 1 AND rp.role_id IS NULL
        ) = 0
        THEN '✅ 权限系统健康度: 优秀'
        WHEN (
            SELECT COUNT(*) FROM permissions p
            LEFT JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE p.status = 1 AND rp.permission_id IS NULL
        ) <= 3
        THEN '⚠️  权限系统健康度: 良好'
        ELSE '❌ 权限系统健康度: 需要改进'
    END as '健康度评估';

SELECT '✅ 权限系统验证完成！' as '完成信息'; 