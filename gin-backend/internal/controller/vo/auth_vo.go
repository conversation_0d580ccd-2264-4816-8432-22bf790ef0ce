package vo

import (
	"gin-backend/internal/service/domain"
)

// LoginReq 登录请求结构
type LoginReq struct {
	Email    string `json:"email" form:"email" binding:"required,email"`       // 用户邮箱
	Password string `json:"password" form:"password" binding:"required,min=6"` // 用户密码
}

// LoginResp 登录响应结构
type LoginResp struct {
	Token string          `json:"token"` // 登录令牌
	User  UserProfileItem `json:"user"`  // 用户信息
}

// UserProfileItem 用户资料结构
type UserProfileItem struct {
	ID              int64    `json:"id"`                // 用户ID
	Name            string   `json:"name"`              // 用户名称
	RealName        string   `json:"real_name"`         // 用户真实姓名
	Email           string   `json:"email"`             // 用户邮箱
	Phone           string   `json:"phone"`             // 用户电话
	Role            int      `json:"role"`              // 用户角色
	DefaultWorkMode string   `json:"default_work_mode"` // 默认工作模式
	AvailableModes  []string `json:"available_modes"`   // 用户可访问的工作模式列表
	Status          int      `json:"status"`            // 用户状态
	Department      string   `json:"department"`        // 用户所属部门
	Position        string   `json:"position"`          // 用户职位
}

// ChangePasswordReq 修改密码请求结构
type ChangePasswordReq struct {
	OldPassword string `json:"old_password" form:"old_password" binding:"required"`       // 旧密码
	NewPassword string `json:"new_password" form:"new_password" binding:"required,min=6"` // 新密码
}

// ChangePasswordResp 修改密码响应结构
type ChangePasswordResp struct {
	Message string `json:"message"` // 响应消息
}

// SwitchWorkModeReq 切换工作模式请求
type SwitchWorkModeReq struct {
	Mode string `json:"mode" form:"mode" binding:"required,oneof=traffic ad"` // 工作模式
}

// SwitchWorkModeResp 切换工作模式响应
type SwitchWorkModeResp struct {
	Mode        string     `json:"mode"`        // 当前工作模式
	MenuItems   []MenuItem `json:"menu_items"`  // 菜单项列表
	Permissions []string   `json:"permissions"` // 权限列表
}

// MenuItem 菜单项结构
type MenuItem struct {
	Path      string     `json:"path"`                // 菜单路径
	Name      string     `json:"name"`                // 菜单名称
	Component string     `json:"component,omitempty"` // 菜单组件
	Redirect  string     `json:"redirect,omitempty"`  // 重定向路径
	Meta      MenuMeta   `json:"meta"`                // 菜单元数据
	Children  []MenuItem `json:"children,omitempty"`  // 子菜单项
	Hidden    bool       `json:"hidden,omitempty"`    // 是否隐藏
}

// MenuMeta 菜单元数据
type MenuMeta struct {
	Title      string `json:"title"`                 // 菜单标题
	Icon       string `json:"icon,omitempty"`        // 菜单图标
	Permission string `json:"permission,omitempty"`  // 菜单权限
	ActiveMenu string `json:"active_menu,omitempty"` // 激活菜单
}

// ToDomainLoginEntity 将登录请求转换为域实体
func (r LoginReq) ToDomainLoginEntity() domain.LoginEntity {
	return domain.LoginEntity{
		Email:    r.Email,
		Password: r.Password,
	}
}

// ToDomainChangePasswordEntity 将修改密码请求转换为域实体
func (r ChangePasswordReq) ToDomainChangePasswordEntity(userID int64) domain.ChangePasswordEntity {
	return domain.ChangePasswordEntity{
		UserID:      userID,
		OldPassword: r.OldPassword,
		NewPassword: r.NewPassword,
	}
}

// FromDomainUserProfile 从域实体转换为用户资料VO
func FromDomainUserProfile(entity domain.UserProfileEntity) UserProfileItem {
	return UserProfileItem{
		ID:              entity.ID,
		Name:            entity.Name,
		RealName:        entity.RealName,
		Email:           entity.Email,
		Phone:           entity.Phone,
		Role:            entity.Role,
		DefaultWorkMode: entity.DefaultWorkMode,
		AvailableModes:  entity.AvailableModes,
		Status:          entity.Status,
		Department:      entity.Department,
		Position:        entity.Position,
	}
}

// NewLoginResp 从用户资料域实体创建登录响应VO
func NewLoginResp(entity domain.UserProfileEntity) LoginResp {
	return LoginResp{
		Token: entity.Token,
		User:  FromDomainUserProfile(entity),
	}
}
