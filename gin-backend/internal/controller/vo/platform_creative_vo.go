package vo

import (
	"gin-backend/internal/service/domain"
)

// PlatformCreativeListReq 获取广告创意列表请求
type PlatformCreativeListReq struct {
	Page             int    `form:"page" json:"page"`                         // 页码
	PageSize         int    `form:"pageSize" json:"pageSize"`                 // 每页数量
	Size             int    `form:"size" json:"size"`                         // 兼容size参数
	AgentID          int    `form:"agentId" json:"agentId"`                   // 代理ID
	MediaID          int    `form:"mediaId" json:"mediaId"`                   // 媒体ID
	MarketTargetName string `form:"marketTargetName" json:"marketTargetName"` // 营销目标名称
	PlanKeyword      string `form:"planKeyword" json:"planKeyword"`           // 计划关键词
	GroupKeyword     string `form:"groupKeyword" json:"groupKeyword"`         // 广告组关键词
	Keyword          string `form:"keyword" json:"keyword"`                   // 关键词
	WithPlan         bool   `form:"withPlan" json:"withPlan"`                 // 是否包含计划
	PlatformType     string `form:"platformType" json:"platformType"`         // 平台类型
}

// PlatformCreativeListResp 广告创意列表响应
type PlatformCreativeListResp struct {
	Total int64                  `json:"total"` // 总数量
	List  []PlatformCreativeItem `json:"list"`  // 广告创意列表
}

// PlatformCreativeItem 广告创意视图对象
type PlatformCreativeItem struct {
	ID               int64  `json:"id"`                         // 主键ID
	AgentID          int64  `json:"agentId"`                    // 代理ID
	MediaID          int64  `json:"mediaId"`                    // 媒体ID
	PlanID           int64  `json:"planId"`                     // 计划ID
	GroupID          int64  `json:"groupId"`                    // 广告组ID
	PlatformType     string `json:"platformType"`               // 平台类型
	DataID           string `json:"dataId"`                     // 数据ID
	Title            string `json:"title"`                      // 标题
	Description      string `json:"description"`                // 描述
	ImageURL         string `json:"imageUrl"`                   // 图片URL
	VideoURL         string `json:"videoUrl"`                   // 视频URL
	LandingPageURL   string `json:"landingPageUrl"`             // 落地页URL
	Status           int    `json:"status"`                     // 状态
	PlanName         string `json:"planName,omitempty"`         // 计划名称
	GroupName        string `json:"groupName,omitempty"`        // 广告组名称
	AgentName        string `json:"agentName,omitempty"`        // 代理名称
	MediaName        string `json:"mediaName,omitempty"`        // 媒体名称
	MarketTargetName string `json:"marketTargetName,omitempty"` // 营销目标名称
	CreatedAt        int64  `json:"createdAt"`                  // 创建时间
	UpdatedAt        int64  `json:"updatedAt"`                  // 更新时间
}

// PlatformCreativeCreateReq 创建广告创意请求
type PlatformCreativeCreateReq struct {
	AgentID        int64  `json:"agentId" binding:"required"`      // 代理ID
	MediaID        int64  `json:"mediaId" binding:"required"`      // 媒体ID
	PlanID         int64  `json:"planId" binding:"required"`       // 计划ID
	GroupID        int64  `json:"groupId" binding:"required"`      // 广告组ID
	PlatformType   string `json:"platformType" binding:"required"` // 平台类型
	DataID         string `json:"dataId"`                          // 数据ID
	Title          string `json:"title" binding:"required"`        // 标题
	Description    string `json:"description"`                     // 描述
	ImageURL       string `json:"imageUrl"`                        // 图片URL
	VideoURL       string `json:"videoUrl"`                        // 视频URL
	LandingPageURL string `json:"landingPageUrl"`                  // 落地页URL
	Status         int    `json:"status"`                          // 状态
}

// PlatformCreativeCreateResp 创建广告创意响应
type PlatformCreativeCreateResp struct {
	ID int64 `json:"id"`
}

// PlatformCreativeDetailResp 广告创意详情响应
type PlatformCreativeDetailResp struct {
	PlatformCreativeItem
}

// PlatformCreativeUpdateReq 更新广告创意请求
type PlatformCreativeUpdateReq struct {
	ID             int64  `json:"id" binding:"required"` // 主键ID
	Title          string `json:"title"`                 // 标题
	Description    string `json:"description"`           // 描述
	ImageURL       string `json:"imageUrl"`              // 图片URL
	VideoURL       string `json:"videoUrl"`              // 视频URL
	LandingPageURL string `json:"landingPageUrl"`        // 落地页URL
	Status         int    `json:"status"`                // 状态
	DataID         string `json:"dataId"`                // 数据ID
}

// PlatformCreativeUpdateResp 更新广告创意响应
type PlatformCreativeUpdateResp struct {
	ID int64 `json:"id"`
}

// PlatformCreativeDeleteReq 删除广告创意请求
type PlatformCreativeDeleteReq struct {
	ID int64 `json:"id" binding:"required"`
}

// PlatformCreativeDeleteResp 删除广告创意响应
type PlatformCreativeDeleteResp struct {
	ID int64 `json:"id"`
}

// ToDomain 将PlatformCreativeCreateReq转换为domain层对象
func (req PlatformCreativeCreateReq) ToDomain() domain.PlatformCreativeCreateParam {
	return domain.PlatformCreativeCreateParam{
		AgentID:        req.AgentID,
		MediaID:        req.MediaID,
		PlanID:         req.PlanID,
		GroupID:        req.GroupID,
		PlatformType:   req.PlatformType,
		DataID:         req.DataID,
		Title:          req.Title,
		Description:    req.Description,
		ImageURL:       req.ImageURL,
		VideoURL:       req.VideoURL,
		LandingPageURL: req.LandingPageURL,
		Status:         req.Status,
	}
}

// ToDomain 将PlatformCreativeUpdateReq转换为domain层对象
func (req PlatformCreativeUpdateReq) ToDomain() domain.PlatformCreativeUpdateParam {
	return domain.PlatformCreativeUpdateParam{
		ID:             req.ID,
		Title:          req.Title,
		Description:    req.Description,
		ImageURL:       req.ImageURL,
		VideoURL:       req.VideoURL,
		LandingPageURL: req.LandingPageURL,
		Status:         req.Status,
		DataID:         req.DataID,
	}
}

// ToDomain 将PlatformCreativeDeleteReq转换为domain层对象
func (req PlatformCreativeDeleteReq) ToDomain() domain.PlatformCreativeDeleteParam {
	return domain.PlatformCreativeDeleteParam{
		ID: req.ID,
	}
}

// ToDomain 将PlatformCreativeListReq转换为domain层对象
func (req PlatformCreativeListReq) ToDomain() domain.PlatformCreativeListParam {
	return domain.PlatformCreativeListParam{
		Page:             req.Page,
		PageSize:         req.PageSize,
		Size:             req.Size,
		AgentID:          req.AgentID,
		MediaID:          req.MediaID,
		MarketTargetName: req.MarketTargetName,
		PlanKeyword:      req.PlanKeyword,
		GroupKeyword:     req.GroupKeyword,
		Keyword:          req.Keyword,
		WithPlan:         req.WithPlan,
		PlatformType:     req.PlatformType,
	}
}

// FromDomainToItem 将domain层对象转换为PlatformCreativeItem
func FromDomainToItem(entity domain.PlatformCreativeEntity) PlatformCreativeItem {
	return PlatformCreativeItem{
		ID:               entity.ID,
		AgentID:          entity.AgentID,
		MediaID:          entity.MediaID,
		PlanID:           entity.PlanID,
		GroupID:          entity.GroupID,
		PlatformType:     entity.PlatformType,
		DataID:           entity.DataID,
		Title:            entity.Title,
		Description:      entity.Description,
		ImageURL:         entity.ImageURL,
		VideoURL:         entity.VideoURL,
		LandingPageURL:   entity.LandingPageURL,
		Status:           entity.Status,
		PlanName:         entity.PlanName,
		GroupName:        entity.GroupName,
		AgentName:        entity.AgentName,
		MediaName:        entity.MediaName,
		MarketTargetName: entity.MarketTargetName,
		CreatedAt:        entity.CreatedAt,
		UpdatedAt:        entity.UpdatedAt,
	}
}

// FromDomainToItemList 将domain层对象列表转换为PlatformCreativeItem列表
func FromDomainToItemList(entities []domain.PlatformCreativeEntity) []PlatformCreativeItem {
	items := make([]PlatformCreativeItem, len(entities))
	for i, entity := range entities {
		items[i] = FromDomainToItem(entity)
	}
	return items
}

// ToListResp 将domain层对象转换为PlatformCreativeListResp
func ToListResp(total int64, entities []domain.PlatformCreativeEntity) PlatformCreativeListResp {
	return PlatformCreativeListResp{
		Total: total,
		List:  FromDomainToItemList(entities),
	}
}

// ToDetailResp 将domain层对象转换为PlatformCreativeDetailResp
func ToDetailResp(entity domain.PlatformCreativeEntity) PlatformCreativeDetailResp {
	return PlatformCreativeDetailResp{
		PlatformCreativeItem: FromDomainToItem(entity),
	}
}

// ToCreateResp 将domain层创建结果转换为PlatformCreativeCreateResp
func ToCreateResp(id int64) PlatformCreativeCreateResp {
	return PlatformCreativeCreateResp{
		ID: id,
	}
}

// ToUpdateResp 将domain层更新结果转换为PlatformCreativeUpdateResp
func ToUpdateResp(id int64) PlatformCreativeUpdateResp {
	return PlatformCreativeUpdateResp{
		ID: id,
	}
}

// ToDeleteResp 将domain层删除结果转换为PlatformCreativeDeleteResp
func ToDeleteResp(id int64) PlatformCreativeDeleteResp {
	return PlatformCreativeDeleteResp{
		ID: id,
	}
}
