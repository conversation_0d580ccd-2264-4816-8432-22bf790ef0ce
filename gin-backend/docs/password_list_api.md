# 口令列表API文档

## 功能概述

口令列表功能提供了完整的口令数据管理和查询能力，支持账号筛选、时间筛选、数据导出等功能。根据时间维度的不同，系统会自动选择最优的数据查询策略：

- **单日查询**：直接从 `password_stats` 统计表查询，性能更优
- **多日查询**：从 `xhs_creative_reports` 创意报表聚合数据，确保数据准确性

## API接口列表

### 1. 获取口令列表

**接口地址**：`GET /api/v1/passwords`

**功能描述**：获取口令列表，支持多维度筛选和分页查询

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称（模糊查询） |
| password_name | string | 否 | 口令名称（模糊查询） |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "password_name": "加码补贴",
        "account_name": "营销树长大",
        "consumption": 33366.68,
        "actual_consumption": 23366.68,
        "impressions": 1272,
        "click_count": 25,
        "click_rate": 2.00,
        "search_count": 1233,
        "new_orders_today": 4431,
        "today_order_cost": 19.78,
        "cumulative_orders": 23231,
        "cumulative_income": 124141,
        "cumulative_recovery": 124.11,
        "last_update": "2024-06-16 12:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 2. 获取口令统计信息

**接口地址**：`GET /api/v1/passwords/stats`

**功能描述**：获取口令的汇总统计信息

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_passwords": 156,
    "total_consumption": 125000.50,
    "total_orders": 8500,
    "total_income": 450000.00,
    "total_recovery_rate": 360.00,
    "avg_click_rate": 1.89,
    "last_update_time": "2024-06-16T12:00:00Z"
  }
}
```

### 3. 导出口令列表

**接口地址**：`POST /api/v1/passwords/export`

**功能描述**：导出口令列表数据，支持Excel和CSV格式

**请求参数**：
```json
{
  "account_id": 1,
  "account_name": "测试账号",
  "password_name": "测试口令",
  "start_date": "2024-06-01",
  "end_date": "2024-06-16",
  "format": "xlsx"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称 |
| password_name | string | 否 | 口令名称 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| format | string | 否 | 导出格式：xlsx 或 csv，默认xlsx |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "导出任务已启动，请稍后查看导出结果"
  }
}
```

### 4. 获取口令详情

**接口地址**：`GET /api/v1/passwords/{password_name}`

**功能描述**：根据口令名称获取口令详细信息

**路径参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| password_name | string | 是 | 口令名称 |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "password_name": "加码补贴",
    "account_name": "营销树长大",
    "consumption": 1000.0,
    "actual_consumption": 1000.0,
    "impressions": 10000,
    "click_count": 100,
    "click_rate": 1.0,
    "search_count": 1233,
    "new_orders_today": 4431,
    "today_order_cost": 19.78,
    "cumulative_orders": 23231,
    "cumulative_income": 124141,
    "cumulative_recovery": 124.11,
    "last_update": "2024-06-16 12:00:00"
  }
}
```

### 5. 获取账号列表

**接口地址**：`GET /api/v1/passwords/accounts`

**功能描述**：获取可用的账号列表，用于筛选条件

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {"id": "", "name": "全部", "value": ""},
    {"id": 1, "name": "账号1", "value": "1"},
    {"id": 2, "name": "账号2", "value": "2"}
  ]
}
```

## 数据聚合策略

### 单日查询（start_date = end_date）

当查询的开始日期和结束日期相同时，系统认为是单日查询，会直接从 `password_stats` 统计表获取数据：

**优势**：
- 查询性能更优
- 数据已预聚合，响应速度快
- 适合实时查看当日数据

**数据来源**：`password_stats` 表

### 多日查询（start_date ≠ end_date）

当查询跨越多个日期时，系统会从 `xhs_creative_reports` 创意报表表聚合数据：

**优势**：
- 数据更准确，基于原始创意数据聚合
- 支持任意时间范围查询
- 按口令进行跨时间维度聚合

**聚合逻辑**：
```sql
SELECT 
  pwd as password_name,
  MAX(account_name) as account_name,
  SUM(fee) as consumption,
  SUM(fee) as actual_consumption,
  SUM(impression) as impressions,
  SUM(click) as click_count,
  CASE 
    WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
    ELSE 0 
  END as click_rate,
  SUM(goods_order) as new_orders_today,
  SUM(success_goods_order) as cumulative_orders,
  SUM(rgmv) as cumulative_income,
  CASE 
    WHEN SUM(fee) > 0 THEN (SUM(rgmv) * 100.0 / SUM(fee))
    ELSE 0 
  END as cumulative_recovery,
  MAX(updated_at) as last_update
FROM xhs_creative_reports
WHERE DATE(time) BETWEEN ? AND ?
  AND pwd IS NOT NULL AND pwd != ''
GROUP BY pwd
ORDER BY consumption DESC
```

## 字段说明

### 口令列表字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| password_name | string | 口令名称 |
| account_name | string | 账号名称 |
| consumption | float64 | 消费金额 |
| actual_consumption | float64 | 实际消费金额 |
| impressions | int64 | 展现量 |
| click_count | int64 | 点击量 |
| click_rate | float64 | 点击率（%） |
| search_count | int64 | 搜索人数 |
| new_orders_today | int64 | 今日新订单数 |
| today_order_cost | float64 | 今日订单成本 |
| cumulative_orders | int64 | 累计订单数 |
| cumulative_income | float64 | 累计收入 |
| cumulative_recovery | float64 | 累计回收率（%） |
| last_update | string | 最后更新时间 |

### 统计信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_passwords | int64 | 总口令数量 |
| total_consumption | float64 | 总消费金额 |
| total_orders | int64 | 总订单数 |
| total_income | float64 | 总收入 |
| total_recovery_rate | float64 | 总回收率（%） |
| avg_click_rate | float64 | 平均点击率（%） |
| last_update_time | time.Time | 最后更新时间 |

## 筛选条件说明

### 账号筛选
- **account_id**：精确匹配账号ID
- **account_name**：模糊匹配账号名称

### 口令筛选
- **password_name**：模糊匹配口令名称

### 时间筛选
- **start_date**：开始日期（包含）
- **end_date**：结束日期（包含）
- 支持单日查询和多日查询
- 日期格式：YYYY-MM-DD

### 分页参数
- **page**：页码，从1开始
- **page_size**：每页数量，范围1-1000

## 导出功能

### 支持格式
- **xlsx**：Excel格式，适合数据分析
- **csv**：CSV格式，适合数据导入

### 导出限制
- 最大导出10,000条记录
- 时间范围不超过365天
- 异步处理，避免请求超时

### 导出字段
包含口令列表的所有字段，与查询接口返回的字段一致。

## 使用示例

### 查询今日口令数据
```bash
curl -X GET "http://localhost:8080/api/v1/passwords?start_date=2024-06-16&end_date=2024-06-16&page=1&page_size=20" \
  -H "Authorization: Bearer your_token"
```

### 查询指定账号的口令数据
```bash
curl -X GET "http://localhost:8080/api/v1/passwords?account_id=1&start_date=2024-06-01&end_date=2024-06-16" \
  -H "Authorization: Bearer your_token"
```

### 搜索口令名称
```bash
curl -X GET "http://localhost:8080/api/v1/passwords?password_name=加码&start_date=2024-06-16&end_date=2024-06-16" \
  -H "Authorization: Bearer your_token"
```

### 导出口令数据
```bash
curl -X POST "http://localhost:8080/api/v1/passwords/export" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "start_date": "2024-06-01",
    "end_date": "2024-06-16",
    "format": "xlsx"
  }'
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | 未授权 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 性能优化建议

1. **使用单日查询**：当只需要查看某一天的数据时，建议使用单日查询以获得更好的性能
2. **合理设置分页**：避免一次查询过多数据，建议每页20-50条
3. **缓存账号列表**：账号列表变化不频繁，可以在前端缓存
4. **异步导出**：大量数据导出采用异步处理，避免前端等待超时
