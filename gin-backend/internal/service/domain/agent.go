package domain

import (
	"encoding/json"
	"time"
)

// PlatformConfig 平台配置结构
type PlatformConfig struct {
	Platform string         // 平台类型 denghuoplus:灯火 xiaohongshu:小红书
	Info     map[string]any // 平台配置信息
}

// GetPlatformConfigFromJSON 从JSON字符串获取平台配置
func GetPlatformConfigFromJSON(configJSON string) (PlatformConfig, error) {
	if configJSON == "" {
		return PlatformConfig{}, nil
	}

	var config PlatformConfig
	err := json.Unmarshal([]byte(configJSON), &config)
	if err != nil {
		return PlatformConfig{}, err
	}
	return config, nil
}

// ConvertToJSON 将平台配置转为JSON字符串
func (c *PlatformConfig) ConvertToJSON() (string, error) {
	if c == nil {
		return "", nil
	}

	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// AgentParam 代理查询参数
type AgentParam struct {
	Name        string // 代理名称
	Type        string // 代理类型
	AuditStatus string // 审核状态
	MediaID     int64  // 媒介ID
	Page        int    // 页码
	PageSize    int    // 每页数量
}

// AgentListResult 代理列表结果
type AgentListResult struct {
	List     []AgentInfo // 代理列表
	Total    int64       // 总数
	Page     int         // 页码
	PageSize int         // 每页数量
}

// AgentInfo 代理信息实体
type AgentInfo struct {
	ID                int64          // 代理ID
	Code              string         // 代理编号
	Name              string         // 代理名称
	Type              string         // 代理类型
	UserID            int64          // 用户ID
	AuditStatus       string         // 审核状态
	CooperationStatus string         // 合作状态
	CompanyName       string         // 公司名称
	CompanyAddress    string         // 公司地址
	ContactName       string         // 联系人
	ContactPhone      string         // 联系电话
	RejectReason      string         // 拒绝原因
	Remarks           string         // 备注
	PlatformConfig    PlatformConfig // 平台配置
	CreatedAt         time.Time      // 创建时间
	UpdatedAt         time.Time      // 更新时间
}

// AgentEntity 代理实体
type AgentEntity struct {
	ID             int64          // 代理ID
	Name           string         // 代理名称
	Type           string         // 代理类型
	CompanyName    string         // 公司名称
	CompanyAddress string         // 公司地址
	ContactName    string         // 联系人
	ContactPhone   string         // 联系电话
	Remarks        string         // 备注
	PlatformConfig PlatformConfig // 平台配置
}

// AgentAuditEntity 代理审核实体
type AgentAuditEntity struct {
	ID           int64  // 代理ID
	AuditStatus  string // 审核状态
	RejectReason string // 拒绝原因
}

// 代理类型常量
const (
	AgentTypeTraffic  = "traffic"  // 流量采买
	AgentTypeDelivery = "delivery" // 投放
)

// 审核状态常量
const (
	AuditStatusPending  = "pending"  // 审核中
	AuditStatusApproved = "approved" // 已通过
	AuditStatusRejected = "rejected" // 已拒绝
)

// 合作状态常量
const (
	CooperationStatusNotStarted = "not_started" // 未开始
	CooperationStatusActive     = "active"      // 合作中
	CooperationStatusTerminated = "terminated"  // 已终止
)
