package service

import (
	"errors"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// ModelService 模型管理服务
type ModelService struct {
	db *gorm.DB
}

// NewModelService 创建模型管理服务实例
func NewModelService() *ModelService {
	return &ModelService{
		db: global.DB,
	}
}

// ExistsByName 检查指定名称的模型是否存在
func (s *ModelService) ExistsByName(name string, excludeIDs ...uint64) (bool, error) {
	query := s.db.Model(&model.Model{}).Where("name = ?", name)
	if len(excludeIDs) > 0 && excludeIDs[0] > 0 {
		query = query.Where("id <> ?", excludeIDs[0])
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}

// Exists 检查指定ID的模型是否存在
func (s *ModelService) Exists(id uint64) (bool, error) {
	var count int64
	err := s.db.Model(&model.Model{}).Where("id = ?", id).Count(&count).Error
	return count > 0, err
}

// GetByID 根据ID获取模型
func (s *ModelService) GetByID(id uint64) (model.Model, error) {
	var m model.Model
	err := s.db.First(&m, id).Error
	if err != nil {
		return model.Model{}, err
	}
	return m, nil
}

// CreateModel 创建模型
func (s *ModelService) CreateModel(entity domain.ModelEntity) (domain.ModelEntity, error) {
	// 检查模型名称是否已存在
	exists, err := s.ExistsByName(entity.ModelName)
	if err != nil {
		return domain.ModelEntity{}, err
	}
	if exists {
		return domain.ModelEntity{}, errors.New("模型名称已存在")
	}

	// 创建模型
	now := time.Now()
	m := model.Model{
		Name:      entity.ModelName,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err = s.db.Create(&m).Error
	if err != nil {
		return domain.ModelEntity{}, err
	}

	return domain.ModelEntity{
		ID:        m.ID,
		ModelName: m.Name,
		CreatorID: entity.CreatorID,
	}, nil
}

// UpdateModel 更新模型
func (s *ModelService) UpdateModel(entity domain.ModelEntity) error {
	// 检查模型是否存在
	exists, err := s.Exists(entity.ID)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("模型不存在")
	}

	// 检查名称是否与其他模型重复
	nameExists, err := s.ExistsByName(entity.ModelName, entity.ID)
	if err != nil {
		return err
	}
	if nameExists {
		return errors.New("模型名称已存在")
	}

	// 获取现有模型
	m, err := s.GetByID(entity.ID)
	if err != nil {
		return err
	}

	// 更新字段
	m.Name = entity.ModelName
	m.UpdatedAt = time.Now()

	return s.db.Save(&m).Error
}

// DeleteModel 删除模型
func (s *ModelService) DeleteModel(id uint64) error {
	// 检查模型是否存在
	exists, err := s.Exists(id)
	if err != nil {
		return err
	}
	if !exists {
		return errors.New("模型不存在")
	}

	// 开启事务
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 删除相关的衰减记录
		if err := tx.Where("model_id = ?", id).Delete(&model.ModelDecay{}).Error; err != nil {
			return err
		}

		// 删除模型
		if err := tx.Delete(&model.Model{}, id).Error; err != nil {
			return err
		}

		return nil
	})
}

// GetModelList 获取模型列表
func (s *ModelService) GetModelList(param domain.ModelParam) (domain.ModelListResult, error) {
	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}

	// 创建查询
	query := s.db.Model(&model.Model{})

	// 应用筛选条件
	if param.ModelName != "" {
		query = query.Where("name LIKE ?", "%"+param.ModelName+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.ModelListResult{}, err
	}

	// 获取数据
	var models []model.Model
	if err := query.Order("id DESC").
		Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Find(&models).Error; err != nil {
		return domain.ModelListResult{}, err
	}

	// 转换为响应格式
	var items []domain.ModelItem
	for _, m := range models {
		item := domain.ModelItem{
			ID:        m.ID,
			ModelName: m.Name,
			Creator:   "系统", // 因为原模型没有存储创建者信息，所以这里使用固定值
			CreatedAt: m.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: m.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		items = append(items, item)
	}

	return domain.ModelListResult{
		Total: total,
		Items: items,
	}, nil
}

// GetDecayList 获取模型衰减记录
func (s *ModelService) GetDecayList(modelID uint64) ([]model.ModelDecay, error) {
	var decays []model.ModelDecay
	err := s.db.Where("model_id = ?", modelID).Order("id ASC").Find(&decays).Error
	return decays, err
}

// AddDecay 添加模型衰减记录
func (s *ModelService) AddDecay(modelID uint64, percent float64) (uint64, error) {
	// 检查模型是否存在
	exists, err := s.Exists(modelID)
	if err != nil {
		return 0, err
	}
	if !exists {
		return 0, errors.New("模型不存在")
	}

	// 创建衰减记录
	now := time.Now()
	decay := model.ModelDecay{
		ModelID:   modelID,
		Percent:   percent,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err = s.db.Create(&decay).Error
	if err != nil {
		return 0, err
	}

	return decay.ID, nil
}

// ImportModel 导入模型
func (s *ModelService) ImportModel(importParam domain.ModelImportParam) error {
	// 1. 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(importParam.File.Filename))
	if ext != ".xlsx" && ext != ".xls" {
		return errors.New("只支持Excel文件格式(.xlsx/.xls)")
	}

	// 2. 检查模型名称是否已存在
	exists, err := s.ExistsByName(importParam.ModelName)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("模型名称已存在")
	}

	// 3. 打开文件
	src, err := importParam.File.Open()
	if err != nil {
		return errors.New("打开文件失败: " + err.Error())
	}
	defer src.Close()

	// 4. 解析Excel文件
	f, err := excelize.OpenReader(src)
	if err != nil {
		return errors.New("解析Excel文件失败: " + err.Error())
	}
	defer f.Close()

	// 5. 获取工作表
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return errors.New("Excel文件中没有工作表")
	}

	// 使用第一个工作表
	sheetName := sheets[0]
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return errors.New("读取工作表失败: " + err.Error())
	}

	if len(rows) < 2 {
		return errors.New("Excel文件内容不能为空，至少需要包含表头和一行数据")
	}

	// 6. 解析衰减数据
	var decayRecords []model.ModelDecay

	// 跳过表头，从第二行开始处理
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) == 0 {
			continue // 跳过空行
		}

		// 解析百分比值
		percentStr := strings.TrimSpace(row[0])
		if percentStr == "" {
			continue
		}

		// 去掉百分号
		percentStr = strings.TrimSuffix(percentStr, "%")
		percent, err := strconv.ParseFloat(percentStr, 64)
		if err != nil {
			continue // 跳过无法解析的行
		}

		// 跳过第二行的100%（如果存在）
		if i == 1 && percent >= 100 {
			continue
		}

		decayRecords = append(decayRecords, model.ModelDecay{
			Percent: percent,
		})
	}

	if len(decayRecords) == 0 {
		return errors.New("没有找到有效的衰减数据")
	}

	// 7. 创建模型和衰减记录（使用事务）
	now := time.Now()
	m := model.Model{
		Name:      importParam.ModelName,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// 创建模型
		if err := tx.Create(&m).Error; err != nil {
			return err
		}

		// 为每条衰减记录设置模型ID
		for i := range decayRecords {
			decayRecords[i].ModelID = m.ID
			decayRecords[i].CreatedAt = now
			decayRecords[i].UpdatedAt = now
		}

		// 批量创建衰减记录
		return tx.Create(&decayRecords).Error
	})
}

// GetModelDetail 获取模型详情
func (s *ModelService) GetModelDetail(id uint64) (domain.ModelDetailResult, error) {
	// 获取模型基本信息
	m, err := s.GetByID(id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.ModelDetailResult{}, errors.New("模型不存在")
		}
		return domain.ModelDetailResult{}, err
	}

	// 获取衰减记录
	decays, err := s.GetDecayList(id)
	if err != nil {
		return domain.ModelDetailResult{}, err
	}

	// 转换为domain层数据
	var dataItems []domain.ModelDataItem
	for i, decay := range decays {
		dataItem := domain.ModelDataItem{
			Day:   i + 1, // 假设天数是按顺序排列的
			Value: decay.Percent,
		}
		dataItems = append(dataItems, dataItem)
	}

	return domain.ModelDetailResult{
		ID:        m.ID,
		ModelName: m.Name,
		Creator:   "系统", // 因为原模型没有存储创建者信息，所以这里使用固定值
		CreatedAt: m.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: m.UpdatedAt.Format("2006-01-02 15:04:05"),
		Data:      dataItems,
	}, nil
}

// GetReport 获取模型报表数据
func (s *ModelService) GetReport(param domain.DeliveryReportParam) (domain.DeliveryReportResult, error) {
	// 1. 获取衰减记录
	var decayRecords []model.ModelDecay
	err := s.db.Model(&model.ModelDecay{}).
		Where("model_id = ?", param.ModelID).
		Order("id ASC").
		Find(&decayRecords).Error
	if err != nil {
		return domain.DeliveryReportResult{}, err
	}

	// 2. 获取报表数据
	var reports []model.PromotionReport
	m := s.db.Model(&model.PromotionReport{})

	if param.GroupID > 0 {
		// 如果指定了口令组ID，则按口令组ID查询
		m = m.Where("group_id = ?", param.GroupID)
	} else if param.CategoryID > 0 {
		// 如果指定了分类ID，则按分类ID查询
		m = m.Where("category_id = ?", param.CategoryID)
	}

	err = m.Order("report_date ASC").Find(&reports).Error
	if err != nil {
		return domain.DeliveryReportResult{}, err
	}

	// 3. 构建日期到报表数据的映射
	reportMap := make(map[string]*model.PromotionReport)
	var minDate, maxDate string
	for i, report := range reports {
		date := report.ReportDate
		if minDate == "" || date < minDate {
			minDate = date
		}
		if maxDate == "" || date > maxDate {
			maxDate = date
		}
		if existingReport, ok := reportMap[date]; ok {
			// 如果同一天有多条数据，则累加
			existingReport.TotalOrders += report.TotalOrders
			existingReport.Cost += report.Cost
			existingReport.TotalCommission += report.TotalCommission
		} else {
			reportMap[date] = &reports[i]
		}
	}

	if minDate == "" || maxDate == "" {
		// 如果没有数据，返回空结果
		return domain.DeliveryReportResult{
			Items: []domain.DeliveryReportItem{},
		}, nil
	}

	// 4. 生成连续日期列表并填充数据
	var result []domain.DeliveryReportItem
	startTime, _ := time.ParseInLocation("20060102", minDate, time.Local)
	endTime, _ := time.ParseInLocation("20060102", maxDate, time.Local)

	// 先生成连续的日期列表
	for d := startTime; d.Before(endTime) || d.Equal(endTime); d = d.AddDate(0, 0, 1) {
		date := d.Format("20060102")
		result = append(result, domain.DeliveryReportItem{
			Date: date,
		})
	}

	// 填充报表数据
	for i := range result {
		date := result[i].Date
		if _, exists := reportMap[date]; !exists {
			// 如果这一天没有数据，所有值都设为0
			result[i].TotalOrders = 0
			result[i].Cost = 0
			result[i].TotalCommission = 0
			result[i].NewOrders = 0
			result[i].DailyCost = 0
			result[i].PaybackDays = -1
		} else {
			// 填充实际数据
			result[i].TotalOrders = reportMap[date].TotalOrders
			result[i].Cost = reportMap[date].Cost
			result[i].TotalCommission = reportMap[date].TotalCommission
		}

		report := result[i]

		// 计算单均佣金
		if report.TotalOrders > 0 {
			result[i].AverageCommission = report.TotalCommission / float64(report.TotalOrders)
		} else {
			result[i].AverageCommission = 0
		}

		if i == 0 {
			// 第一天的新订单数就是总订单数
			result[i].NewOrders = report.TotalOrders
			if result[i].NewOrders > 0 {
				result[i].DailyCost = report.Cost / float64(result[i].NewOrders)
			} else {
				result[i].DailyCost = 0
			}
		} else {
			// 计算历史订单在当天的衰减订单数
			var decayOrders int
			// 遍历之前的每一天，计算它们在当天产生的订单数
			for j := 0; j < i; j++ {
				prevDay := result[j]
				daysDiff := i - j
				if daysDiff > len(decayRecords) {
					// 如果天数差超过了衰减记录的长度，则使用最后一个衰减值
					daysDiff = len(decayRecords)
				}
				// 获取衰减率
				var decayRate float64
				if daysDiff > 0 && daysDiff <= len(decayRecords) {
					decayRate = decayRecords[daysDiff-1].Percent / 100.0
				} else {
					// 默认为0
					decayRate = 0
				}
				// 计算这一天的衰减订单数
				dailyDecayOrders := int(float64(prevDay.NewOrders) * decayRate)
				decayOrders += dailyDecayOrders
			}

			// 当天的新订单数 = 总订单数 - 历史衰减订单数
			result[i].NewOrders = report.TotalOrders - decayOrders
			if result[i].NewOrders < 0 {
				result[i].NewOrders = 0
			}

			// 计算当天成本
			if result[i].NewOrders > 0 {
				result[i].DailyCost = report.Cost / float64(result[i].NewOrders)
			} else {
				result[i].DailyCost = 0
			}
		}

		// 计算回本周期
		if result[i].DailyCost > 0 {
			// 查找衰减记录，计算未来每天可能的佣金总和
			var totalFutureCommission float64
			var daysToPayback int

			// 从第一天开始累加佣金，直到超过成本或者达到最大天数
			for k := 1; k <= 365; k++ { // 最多计算一年
				if k > len(decayRecords) {
					break
				}
				decayRate := decayRecords[k-1].Percent / 100.0
				dailyCommission := result[i].AverageCommission * float64(result[i].NewOrders) * decayRate
				totalFutureCommission += dailyCommission
				daysToPayback++

				if totalFutureCommission >= result[i].DailyCost {
					break
				}
			}

			if totalFutureCommission >= result[i].DailyCost {
				result[i].PaybackDays = daysToPayback
			} else {
				result[i].PaybackDays = -1 // 无法回本
			}
		} else {
			result[i].PaybackDays = 0 // 成本为0，立即回本
		}
	}

	return domain.DeliveryReportResult{
		Total: int64(len(result)),
		Items: result,
	}, nil
}
