package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// AgentController 代理控制器
type AgentController struct {
	agentService      *service.AgentService
	roleService       *service.RoleService
	permissionService *service.PermissionService
}

// NewAgentController 创建代理控制器实例
func NewAgentController() *AgentController {
	return &AgentController{
		agentService:      service.NewAgentService(),
		permissionService: service.NewPermissionService(),
		roleService:       service.NewRoleService(),
	}
}

// GetList 获取代理列表
// @Summary 获取代理列表
// @Description 支持按名称、类型、审核状态筛选，支持分页，支持数据权限控制
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param name query string false "代理名称"
// @Param type query string false "代理类型：traffic-流量采买,delivery-投放"
// @Param audit_status query string false "审核状态：pending-审核中,approved-已通过,rejected-已拒绝"
// @Param media_id query uint64 false "媒介ID"
// @Param page query int true "页码"
// @Param page_size query int true "每页数量"
// @Success 200 {object} response.Response{data=vo.AgentListResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/list [get]
func (ctrl *AgentController) GetList(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)
	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:list"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	// 解析请求参数
	var req vo.AgentListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 转换参数并调用服务
	domainParam := req.ToDomain()
	result, err := ctrl.agentService.GetList(c.Request.Context(), userEntity, domainParam)
	if err != nil {
		response.Error(c, response.CodeServerError, "获取代理列表失败: "+err.Error())
		return
	}

	// 转换响应
	var respVO vo.AgentListResp
	respVO.FromDomain(result)

	// 返回成功响应
	response.Success(c, respVO)
}

// GetDetail 获取代理详情
// @Summary 获取代理详情
// @Description 根据代理ID获取详细信息，支持数据权限控制
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param id query uint64 true "代理ID"
// @Success 200 {object} response.Response{data=vo.AgentItem}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/detail [get]
func (ctrl *AgentController) GetDetail(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)

	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:detail"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.AgentDetailReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 调用服务
	agentInfo, err := ctrl.agentService.GetByID(c.Request.Context(), userEntity, req.ID)
	if err != nil {
		response.Error(c, response.CodeServerError, "获取代理详情失败: "+err.Error())
		return
	}

	// 转换为VO
	var respVO vo.AgentItem
	respVO.FromDomain(agentInfo)

	// 返回成功响应
	response.Success(c, respVO)
}

// Create 创建代理
// @Summary 创建代理
// @Description 创建新的代理
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentCreateReq true "代理信息"
// @Success 200 {object} response.Response{data=vo.AgentCreateResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/create [post]
func (ctrl *AgentController) Create(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)

	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:create"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	// 解析请求参数
	var req vo.AgentCreateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 转换为领域模型
	agentEntity := req.ToDomain()

	// 调用服务创建代理
	id, err := ctrl.agentService.Create(c.Request.Context(), userEntity, agentEntity)
	if err != nil {
		response.Error(c, response.CodeServerError, "创建代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, vo.AgentCreateResp{ID: id})
}

// Update 更新代理
// @Summary 更新代理
// @Description 更新代理信息
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentUpdateReq true "代理信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/update [put]
func (ctrl *AgentController) Update(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)

	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:update"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	// 解析请求参数
	var req vo.AgentUpdateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}
	// 转换为领域模型
	agentEntity := req.ToDomain()

	// 调用服务更新代理
	if err := ctrl.agentService.Update(c.Request.Context(), userEntity, agentEntity); err != nil {
		response.Error(c, response.CodeServerError, "更新代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}

// Delete 删除代理
// @Summary 删除代理
// @Description 根据代理ID删除代理
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param id query uint64 true "代理ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/delete [delete]
func (ctrl *AgentController) Delete(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)

	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:delete"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.AgentDeleteReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 调用服务删除代理
	if err := ctrl.agentService.Delete(c.Request.Context(), userEntity, req.ID); err != nil {
		response.Error(c, response.CodeServerError, "删除代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}

// Audit 审核代理
// @Summary 审核代理
// @Description 审核代理信息
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentAuditReq true "审核信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/audit [post]
func (ctrl *AgentController) Audit(c *gin.Context) {
	userEntity := middleware.GetCurrentUser(c)

	if ok, _ := ctrl.roleService.CheckUserPermission(c, userEntity.ID, "agent:audit"); !ok {
		response.Error(c, response.CodeForbidden, "没有权限")
		return
	}

	// 解析请求参数
	var req vo.AgentAuditReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}
	// 转换为领域模型
	auditEntity := req.ToDomain()

	// 调用服务审核代理
	if err := ctrl.agentService.Audit(c.Request.Context(), userEntity, auditEntity); err != nil {
		response.Error(c, response.CodeServerError, "审核代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}
