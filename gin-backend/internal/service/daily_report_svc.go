package service

import (
	"fmt"
	"strconv"
	"time"

	"gin-backend/internal/global"
	"gin-backend/internal/service/domain"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// DailyReportService 日报表服务
type DailyReportService struct {
	db                *gorm.DB
	permissionService *PermissionService
}

// NewDailyReportService 创建日报表服务实例
func NewDailyReportService() *DailyReportService {
	return &DailyReportService{
		db:                global.DB,
		permissionService: NewPermissionService(),
	}
}

// GetDailyReport 获取日报表汇总数据
func (s *DailyReportService) GetDailyReport(param domain.DailyReportParam) (domain.DailyReportEntity, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return domain.DailyReportEntity{}, fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证日期范围
	start, err := time.Parse("2006-01-02", param.StartDate)
	if err != nil {
		return domain.DailyReportEntity{}, fmt.Errorf("开始日期格式错误: %v", err)
	}

	end, err := time.Parse("2006-01-02", param.EndDate)
	if err != nil {
		return domain.DailyReportEntity{}, fmt.Errorf("结束日期格式错误: %v", err)
	}

	if end.Before(start) {
		return domain.DailyReportEntity{}, fmt.Errorf("结束日期不能早于开始日期")
	}

	// 获取日报表数据
	reportData, err := s.getDailyReportData(param, scope, departmentUsers)
	if err != nil {
		return domain.DailyReportEntity{}, fmt.Errorf("获取日报表数据失败: %v", err)
	}

	return domain.DailyReportEntity{
		List: reportData.List,
	}, nil
}

// getDailyReportData 从数据库获取日报表数据
func (s *DailyReportService) getDailyReportData(param domain.DailyReportParam, scope string, departmentUsers []int64) (domain.DailyReportEntity, error) {
	var results []struct {
		Date          string  `json:"date"`
		AlipayProfit  float64 `json:"alipay_profit"`
		WechatProfit  float64 `json:"wechat_profit"`
		AlipayOrders  int     `json:"alipay_orders"`
		WechatOrders  int     `json:"wechat_orders"`
		AlipayRevenue float64 `json:"alipay_revenue"`
		WechatRevenue float64 `json:"wechat_revenue"`
		AlipayCost    float64 `json:"alipay_cost"`
		WechatCost    float64 `json:"wechat_cost"`
		OtherRevenue  float64 `json:"other_revenue"`
		OtherCost     float64 `json:"other_cost"`
		OtherProfit   float64 `json:"other_profit"`
	}

	// 构建基础查询SQL
	query := `
		SELECT
			t.date as date,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.profit END), 0) as alipay_profit,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.profit END), 0) as wechat_profit,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.orders END), 0) as alipay_orders,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.orders END), 0) as wechat_orders,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.revenue END), 0) as alipay_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.revenue END), 0) as wechat_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.cost END), 0) as alipay_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.cost END), 0) as wechat_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.revenue END), 0) as other_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.cost END), 0) as other_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.profit END), 0) as other_profit
		FROM (
			SELECT
				a.date,
				c.media_name as media_name,
				c.type as promotion_channel,
				SUM(a.bd_orders) as orders,
				SUM(a.admin_revenue) as revenue,
				SUM(a.admin_profit) as profit,
				SUM(a.cost) as cost,
				SUM(a.clicks) as clicks,
				ROUND(SUM(a.admin_revenue)/NULLIF(SUM(a.cost), 0), 2) as roi
			FROM ad_slot_plan_daily_stats a
			LEFT JOIN ad_slot_plans b ON (a.plan_id = b.id)
			LEFT JOIN ad_slots c ON (a.ad_slot_id = c.id)
			WHERE a.date BETWEEN ? AND ? 
				AND a.user_id NOT IN (12,13,16,21)
				AND a.deleted_at IS NULL
				AND b.deleted_at IS NULL
				AND c.deleted_at IS NULL
	`

	// 动态参数
	var params []interface{}
	params = append(params, param.StartDate, param.EndDate)

	// 添加权限过滤
	if scope == "self" {
		query += " AND a.user_id = ?"
		params = append(params, param.UserID)
	} else if scope == "department" && len(departmentUsers) > 0 {
		query += " AND a.user_id IN (?)"
		params = append(params, departmentUsers)
	}

	// 添加媒体过滤
	if param.MediaID > 0 {
		query += " AND a.media_id = ?"
		params = append(params, param.MediaID)
	}

	// 完成子查询
	query += `
			GROUP BY a.date, c.type
		) t
		GROUP BY DATE(t.date)
		ORDER BY DATE(t.date) DESC
	`

	// 执行查询
	if err := s.db.Raw(query, params...).Scan(&results).Error; err != nil {
		return domain.DailyReportEntity{}, fmt.Errorf("查询日报表数据失败: %v", err)
	}

	// 转换为响应格式
	var reportItems []domain.DailyReportItemEntity
	for _, item := range results {
		// 计算总计数据
		totalProfit := item.AlipayProfit + item.WechatProfit + item.OtherProfit
		totalOrders := item.AlipayOrders + item.WechatOrders
		totalRevenue := item.AlipayRevenue + item.WechatRevenue + item.OtherRevenue
		totalCost := item.AlipayCost + item.WechatCost + item.OtherCost

		// 计算ROI和利润率
		var roi float64
		var profitMarginRate float64
		if totalCost > 0 {
			roi = totalRevenue / totalCost
		}
		if totalRevenue > 0 {
			profitMarginRate = (totalProfit / totalRevenue) * 100
		}

		reportItem := domain.DailyReportItemEntity{
			BaseReportItemEntity: domain.BaseReportItemEntity{
				AlipayProfit:     item.AlipayProfit,
				AlipayOrders:     item.AlipayOrders,
				AlipayRevenue:    item.AlipayRevenue,
				AlipayCost:       item.AlipayCost,
				WechatProfit:     item.WechatProfit,
				WechatOrders:     item.WechatOrders,
				WechatRevenue:    item.WechatRevenue,
				WechatCost:       item.WechatCost,
				OtherProfit:      item.OtherProfit,
				OtherRevenue:     item.OtherRevenue,
				OtherCost:        item.OtherCost,
				TotalProfit:      totalProfit,
				TotalOrders:      totalOrders,
				TotalRevenue:     totalRevenue,
				TotalCost:        totalCost,
				ROI:              roi,
				ProfitMarginRate: profitMarginRate,
			},
			Date: item.Date,
		}
		reportItems = append(reportItems, reportItem)
	}

	return domain.DailyReportEntity{
		List: reportItems,
	}, nil
}

// GetDailyDetail 获取日报表详情数据
func (s *DailyReportService) GetDailyDetail(param domain.DailyDetailParam) (domain.DailyDetailEntity, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return domain.DailyDetailEntity{}, fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证日期格式
	_, err = time.Parse("2006-01-02", param.Date)
	if err != nil {
		return domain.DailyDetailEntity{}, fmt.Errorf("日期格式错误: %v", err)
	}

	// 获取详情数据
	detailData, err := s.getDailyDetailData(param, scope, departmentUsers)
	if err != nil {
		return domain.DailyDetailEntity{}, fmt.Errorf("获取日报表详情失败: %v", err)
	}

	return domain.DailyDetailEntity{
		Date:    detailData.Date,
		List:    detailData.List,
		Summary: detailData.Summary,
	}, nil
}

// getDailyDetailData 从数据库获取日报表详情数据
func (s *DailyReportService) getDailyDetailData(param domain.DailyDetailParam, scope string, departmentUsers []int64) (domain.DailyDetailEntity, error) {
	var results []struct {
		MediaID          int     `json:"media_id"`
		MediaName        string  `json:"media_name"`
		PromotionChannel string  `json:"promotion_channel"`
		IsProfit         bool    `json:"is_profit"`
		Orders           int     `json:"orders"`
		Revenue          float64 `json:"revenue"`
		Cost             float64 `json:"cost"`
		Profit           float64 `json:"profit"`
		Clicks           int     `json:"clicks"`
		ROI              float64 `json:"roi"`
		UserID           int     `json:"user_id"`
		UserName         string  `json:"user_name"`
	}

	// 构建详情查询SQL
	query := `
		SELECT
			a.media_id,
			a.user_id,
			c.media_name as media_name,
			c.type as promotion_channel,
			COALESCE(u.real_name, u.name, '未知') as user_name,
			SUM(a.bd_orders) as orders,
			SUM(a.admin_revenue) as revenue,
			SUM(a.cost) as cost,
			SUM(a.admin_profit) as profit,
			SUM(a.clicks) as clicks,
			ROUND(SUM(a.admin_revenue)/NULLIF(SUM(a.cost), 0), 2) as roi
		FROM ad_slot_plan_daily_stats a
		LEFT JOIN ad_slot_plans b ON (a.plan_id = b.id)
		LEFT JOIN ad_slots c ON (a.ad_slot_id = c.id)
		LEFT JOIN users u ON (a.user_id = u.id)
		WHERE a.date = ? 
			AND a.user_id NOT IN (12,13,16,21)
			AND a.deleted_at IS NULL
			AND b.deleted_at IS NULL
			AND c.deleted_at IS NULL
	`

	// 动态参数
	var params []interface{}
	params = append(params, param.Date)

	// 添加权限过滤
	if scope == "self" {
		query += " AND a.user_id = ?"
		params = append(params, param.UserID)
	} else if scope == "department" && len(departmentUsers) > 0 {
		query += " AND a.user_id IN (?)"
		params = append(params, departmentUsers)
	}

	// 添加媒体过滤
	if param.MediaID > 0 {
		query += " AND a.media_id = ?"
		params = append(params, param.MediaID)
	}

	// 完成查询
	query += `
		GROUP BY a.media_id, a.user_id, c.media_name, c.type
		ORDER BY profit DESC
	`

	// 执行查询
	if err := s.db.Raw(query, params...).Scan(&results).Error; err != nil {
		return domain.DailyDetailEntity{}, fmt.Errorf("查询日报表详情失败: %v", err)
	}

	// 转换为实体
	var detailItems []domain.DetailItemEntity
	profitMediaCount := 0
	lossMediaCount := 0

	for _, item := range results {
		// 计算利润率
		var profitMarginRate float64
		if item.Revenue > 0 {
			profitMarginRate = (item.Profit / item.Revenue) * 100
		}

		// 判断是否盈利
		isProfit := item.Profit >= 0
		if isProfit {
			profitMediaCount++
		} else {
			lossMediaCount++
		}

		detailItem := domain.DetailItemEntity{
			MediaID:          item.MediaID,
			MediaName:        item.MediaName,
			PromotionChannel: item.PromotionChannel,
			IsProfit:         isProfit,
			Orders:           item.Orders,
			Revenue:          item.Revenue,
			Cost:             item.Cost,
			Profit:           item.Profit,
			Clicks:           item.Clicks,
			ROI:              item.ROI,
			ProfitMarginRate: profitMarginRate,
			UserID:           item.UserID,
			UserName:         item.UserName,
		}
		detailItems = append(detailItems, detailItem)
	}

	// 汇总数据
	var totalProfit float64
	var totalOrders int
	for _, item := range detailItems {
		totalProfit += item.Profit
		totalOrders += item.Orders
	}

	summary := domain.DetailSummaryEntity{
		TotalProfit:      totalProfit,
		TotalOrders:      totalOrders,
		ProfitMediaCount: profitMediaCount,
		LossMediaCount:   lossMediaCount,
	}

	return domain.DailyDetailEntity{
		Date:    param.Date,
		List:    detailItems,
		Summary: summary,
	}, nil
}

// ExportDailyReport 导出日报表数据
func (s *DailyReportService) ExportDailyReport(param domain.DailyReportParam) ([]byte, string, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return nil, "", fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证日期范围
	start, err := time.Parse("2006-01-02", param.StartDate)
	if err != nil {
		return nil, "", fmt.Errorf("开始日期格式错误: %v", err)
	}

	end, err := time.Parse("2006-01-02", param.EndDate)
	if err != nil {
		return nil, "", fmt.Errorf("结束日期格式错误: %v", err)
	}

	if end.Before(start) {
		return nil, "", fmt.Errorf("结束日期不能早于开始日期")
	}

	// 导出数据
	fileData, filename, err := s.exportDailyReportData(param, scope, departmentUsers)
	if err != nil {
		return nil, "", fmt.Errorf("导出日报表数据失败: %v", err)
	}

	return fileData, filename, nil
}

// exportDailyReportData 导出日报表数据为Excel
func (s *DailyReportService) exportDailyReportData(param domain.DailyReportParam, scope string, departmentUsers []int64) ([]byte, string, error) {
	// 获取数据
	reportData, err := s.getDailyReportData(param, scope, departmentUsers)
	if err != nil {
		return nil, "", err
	}

	// 创建Excel文件
	f := excelize.NewFile()
	sheetName := "日报表"

	// 删除默认的Sheet1，创建新的工作表
	f.DeleteSheet("Sheet1")
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, "", fmt.Errorf("创建工作表失败: %v", err)
	}
	f.SetActiveSheet(index)

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   12,
			Family: "微软雅黑",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建表头样式失败: %v", err)
	}

	// 设置数据样式
	dataStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   11,
			Family: "微软雅黑",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 数字格式，保留两位小数
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建数据样式失败: %v", err)
	}

	// 设置日期样式
	dateStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   11,
			Family: "微软雅黑",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
		NumFmt: 14, // 日期格式 yyyy-mm-dd
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建日期样式失败: %v", err)
	}

	// 设置表头
	headers := []string{
		"日期", "媒体名称", "媒体ID",
		"支付宝利润", "支付宝订单数", "支付宝佣金收入", "支付宝成本",
		"微信利润", "微信订单数", "微信佣金收入", "微信成本",
		"其他利润", "其他佣金收入", "其他成本",
		"总利润", "总订单数", "总佣金收入", "总成本", "ROI", "利润率(%)",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// 写入数据
	for i, item := range reportData.List {
		row := i + 2 // 从第2行开始写入数据

		// 解析日期
		var date time.Time
		if item.Date != "" {
			date, _ = time.Parse("2006-01-02", item.Date)
		}

		// 写入各列数据
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), date)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.MediaName)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.MediaID)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.AlipayProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.AlipayOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.AlipayRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.AlipayCost)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.WechatProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.WechatOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.WechatRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.WechatCost)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.OtherProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.OtherRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), item.OtherCost)
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", row), item.TotalProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("P%d", row), item.TotalOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("Q%d", row), item.TotalRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("R%d", row), item.TotalCost)
		f.SetCellValue(sheetName, fmt.Sprintf("S%d", row), item.ROI)
		f.SetCellValue(sheetName, fmt.Sprintf("T%d", row), item.ProfitMarginRate)

		// 应用样式
		// 日期列
		f.SetCellStyle(sheetName, fmt.Sprintf("A%d", row), fmt.Sprintf("A%d", row), dateStyle)

		// 媒体名称和ID列
		f.SetCellStyle(sheetName, fmt.Sprintf("B%d", row), fmt.Sprintf("C%d", row), dataStyle)

		// 数据列
		f.SetCellStyle(sheetName, fmt.Sprintf("D%d", row), fmt.Sprintf("T%d", row), dataStyle)
	}

	// 设置列宽
	columnWidths := map[string]float64{
		"A": 15, // 日期
		"B": 20, // 媒体名称
		"C": 10, // 媒体ID
		"D": 15, // 支付宝利润
		"E": 12, // 支付宝订单数
		"F": 15, // 支付宝佣金收入
		"G": 15, // 支付宝成本
		"H": 15, // 微信利润
		"I": 12, // 微信订单数
		"J": 15, // 微信佣金收入
		"K": 15, // 微信成本
		"L": 15, // 其他利润
		"M": 15, // 其他佣金收入
		"N": 15, // 其他成本
		"O": 15, // 总利润
		"P": 12, // 总订单数
		"Q": 15, // 总佣金收入
		"R": 15, // 总成本
		"S": 10, // ROI
		"T": 12, // 利润率
	}

	for col, width := range columnWidths {
		f.SetColWidth(sheetName, col, col, width)
	}

	// 冻结首行
	f.SetPanes(sheetName, &excelize.Panes{
		Freeze:      true,
		XSplit:      0,
		YSplit:      1,
		TopLeftCell: "A2",
	})

	// 生成Excel文件内容
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, "", fmt.Errorf("生成Excel文件失败: %v", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("日报表_%s_%s_%s.xlsx",
		param.StartDate,
		param.EndDate,
		time.Now().Format("20060102_150405"))

	return buf.Bytes(), filename, nil
}

// getDataScopeInfo 获取数据权限范围信息
func (s *DailyReportService) getDataScopeInfo(userID int64, module string) (string, []int64, error) {
	scope, err := s.permissionService.GetUserDataScope(domain.DataScopeParam{
		UserID: userID,
		Module: module,
	})
	if err != nil {
		return "self", []int64{userID}, err
	}

	var departmentUsers []int64

	if scope == "dept" {
		// 获取用户部门
		department, err := s.permissionService.GetUserDepartment(domain.UserDepartmentParam{
			UserID: userID,
		})
		if err == nil && department != "" {
			// 获取部门内的所有用户
			departmentUsers, err = s.permissionService.GetDepartmentUsers(domain.DepartmentUsersParam{
				Department: department,
			})
			if err != nil {
				departmentUsers = []int64{userID}
			}
		} else {
			departmentUsers = []int64{userID}
		}
	} else if scope == "self" {
		departmentUsers = []int64{userID}
	}

	return scope, departmentUsers, nil
}

// ============= 周报表相关方法 =============

// GetWeeklyReport 获取周报表数据
func (s *DailyReportService) GetWeeklyReport(param domain.WeeklyReportParam) (domain.WeeklyReportEntity, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return domain.WeeklyReportEntity{}, fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证日期范围
	start, err := time.Parse("2006-01-02", param.StartDate)
	if err != nil {
		return domain.WeeklyReportEntity{}, fmt.Errorf("开始日期格式错误: %v", err)
	}

	end, err := time.Parse("2006-01-02", param.EndDate)
	if err != nil {
		return domain.WeeklyReportEntity{}, fmt.Errorf("结束日期格式错误: %v", err)
	}

	if end.Before(start) {
		return domain.WeeklyReportEntity{}, fmt.Errorf("结束日期不能早于开始日期")
	}

	// 获取周报表数据
	reportData, err := s.getWeeklyReportData(param, scope, departmentUsers)
	if err != nil {
		return domain.WeeklyReportEntity{}, fmt.Errorf("获取周报表数据失败: %v", err)
	}

	return domain.WeeklyReportEntity{
		List: reportData.List,
	}, nil
}

// getWeeklyReportData 从数据库获取周报表数据
func (s *DailyReportService) getWeeklyReportData(param domain.WeeklyReportParam, scope string, departmentUsers []int64) (domain.WeeklyReportEntity, error) {
	var results []struct {
		YearWeek      string  `json:"year_week"`
		WeekStart     string  `json:"week_start"`
		WeekEnd       string  `json:"week_end"`
		AlipayProfit  float64 `json:"alipay_profit"`
		WechatProfit  float64 `json:"wechat_profit"`
		AlipayOrders  int     `json:"alipay_orders"`
		WechatOrders  int     `json:"wechat_orders"`
		AlipayRevenue float64 `json:"alipay_revenue"`
		WechatRevenue float64 `json:"wechat_revenue"`
		AlipayCost    float64 `json:"alipay_cost"`
		WechatCost    float64 `json:"wechat_cost"`
		OtherRevenue  float64 `json:"other_revenue"`
		OtherCost     float64 `json:"other_cost"`
		OtherProfit   float64 `json:"other_profit"`
	}

	// 构建查询SQL
	query := `
		SELECT
			DATE_FORMAT(t.date, '%Y%u') as year_week,
			MIN(t.date) as week_start,
			MAX(t.date) as week_end,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.profit END), 0) as alipay_profit,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.profit END), 0) as wechat_profit,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.orders END), 0) as alipay_orders,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.orders END), 0) as wechat_orders,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.revenue END), 0) as alipay_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.revenue END), 0) as wechat_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('alipay_h5', 'alipay_mp') THEN t.cost END), 0) as alipay_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('wechat_mp', 'wechat_plugin') THEN t.cost END), 0) as wechat_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.revenue END), 0) as other_revenue,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.cost END), 0) as other_cost,
			COALESCE(SUM(CASE WHEN t.promotion_channel IN ('other') THEN t.profit END), 0) as other_profit
		FROM (
			SELECT
				a.date,
				c.type as promotion_channel,
				SUM(a.bd_orders) as orders,
				SUM(a.admin_revenue) as revenue,
				SUM(a.admin_profit) as profit,
				SUM(a.cost) as cost
			FROM ad_slot_plan_daily_stats a
			LEFT JOIN ad_slot_plans b ON (a.plan_id = b.id)
			LEFT JOIN ad_slots c ON (a.ad_slot_id = c.id)
			WHERE a.date BETWEEN ? AND ? 
				AND a.user_id NOT IN (12,13,16,21)
				AND a.deleted_at IS NULL
				AND b.deleted_at IS NULL
				AND c.deleted_at IS NULL
	`

	// 动态参数
	var params []interface{}
	params = append(params, param.StartDate, param.EndDate)

	// 添加权限过滤
	if scope == "self" {
		query += " AND a.user_id = ?"
		params = append(params, param.UserID)
	} else if scope == "department" && len(departmentUsers) > 0 {
		query += " AND a.user_id IN (?)"
		params = append(params, departmentUsers)
	}

	// 添加媒体过滤
	if param.MediaID > 0 {
		query += " AND a.media_id = ?"
		params = append(params, param.MediaID)
	}

	// 完成子查询
	query += `
			GROUP BY a.date, c.type
		) t
		GROUP BY DATE_FORMAT(t.date, '%Y%u')
		ORDER BY year_week DESC
	`

	// 执行查询
	if err := s.db.Raw(query, params...).Scan(&results).Error; err != nil {
		return domain.WeeklyReportEntity{}, fmt.Errorf("查询周报表数据失败: %v", err)
	}

	// 转换为响应格式
	var reportItems []domain.WeeklyReportItemEntity
	for _, item := range results {
		// 计算总计数据
		totalProfit := item.AlipayProfit + item.WechatProfit + item.OtherProfit
		totalOrders := item.AlipayOrders + item.WechatOrders
		totalRevenue := item.AlipayRevenue + item.WechatRevenue + item.OtherRevenue
		totalCost := item.AlipayCost + item.WechatCost + item.OtherCost

		// 计算ROI和利润率
		var roi float64
		var profitMarginRate float64
		if totalCost > 0 {
			roi = totalRevenue / totalCost
		}
		if totalRevenue > 0 {
			profitMarginRate = (totalProfit / totalRevenue) * 100
		}

		reportItem := domain.WeeklyReportItemEntity{
			BaseReportItemEntity: domain.BaseReportItemEntity{
				AlipayProfit:     item.AlipayProfit,
				AlipayOrders:     item.AlipayOrders,
				AlipayRevenue:    item.AlipayRevenue,
				AlipayCost:       item.AlipayCost,
				WechatProfit:     item.WechatProfit,
				WechatOrders:     item.WechatOrders,
				WechatRevenue:    item.WechatRevenue,
				WechatCost:       item.WechatCost,
				OtherProfit:      item.OtherProfit,
				OtherRevenue:     item.OtherRevenue,
				OtherCost:        item.OtherCost,
				TotalProfit:      totalProfit,
				TotalOrders:      totalOrders,
				TotalRevenue:     totalRevenue,
				TotalCost:        totalCost,
				ROI:              roi,
				ProfitMarginRate: profitMarginRate,
			},
			YearWeek:  item.YearWeek,
			WeekStart: item.WeekStart,
			WeekEnd:   item.WeekEnd,
			WeekRange: fmt.Sprintf("%s ~ %s", item.WeekStart, item.WeekEnd),
		}
		reportItems = append(reportItems, reportItem)
	}

	return domain.WeeklyReportEntity{
		List: reportItems,
	}, nil
}

// GetWeeklyDetail 获取周报表详情数据
func (s *DailyReportService) GetWeeklyDetail(param domain.WeeklyDetailParam) (domain.WeeklyDetailEntity, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return domain.WeeklyDetailEntity{}, fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证年周格式（YYYYWW）
	if len(param.YearWeek) != 6 {
		return domain.WeeklyDetailEntity{}, fmt.Errorf("年周格式错误，应为YYYYWW格式")
	}

	// 获取详情数据
	detailData, err := s.getWeeklyDetailData(param, scope, departmentUsers)
	if err != nil {
		return domain.WeeklyDetailEntity{}, err
	}

	return domain.WeeklyDetailEntity{
		YearWeek:  detailData.YearWeek,
		WeekStart: detailData.WeekStart,
		WeekEnd:   detailData.WeekEnd,
		List:      detailData.List,
		Summary:   detailData.Summary,
	}, nil
}

// getWeeklyDetailData 从数据库获取周报表详情数据
func (s *DailyReportService) getWeeklyDetailData(param domain.WeeklyDetailParam, scope string, departmentUsers []int64) (domain.WeeklyDetailEntity, error) {
	// 解析年周参数
	year, _ := strconv.Atoi(param.YearWeek[:4])
	week, _ := strconv.Atoi(param.YearWeek[4:])

	// 获取该周的开始和结束日期
	yearWeek := fmt.Sprintf("%04d-W%02d", year, week)
	weekStart, weekEnd, err := s.getWeekStartEndDates(yearWeek)
	if err != nil {
		return domain.WeeklyDetailEntity{}, fmt.Errorf("解析周日期失败: %v", err)
	}

	// 查询媒体级别详细数据
	var results []struct {
		MediaID          int     `json:"media_id"`
		MediaName        string  `json:"media_name"`
		PromotionChannel string  `json:"promotion_channel"`
		IsProfit         bool    `json:"is_profit"`
		Orders           int     `json:"orders"`
		Revenue          float64 `json:"revenue"`
		Cost             float64 `json:"cost"`
		Profit           float64 `json:"profit"`
		Clicks           int     `json:"clicks"`
		ROI              float64 `json:"roi"`
		UserID           int     `json:"user_id"`
		UserName         string  `json:"user_name"`
	}

	// 构建查询SQL
	query := `
		SELECT
			a.media_id,
			a.user_id,
			c.media_name as media_name,
			c.type as promotion_channel,
			COALESCE(u.real_name, u.name, '未知') as user_name,
			SUM(a.bd_orders) as orders,
			SUM(a.admin_revenue) as revenue,
			SUM(a.cost) as cost,
			SUM(a.admin_profit) as profit,
			SUM(a.clicks) as clicks,
			ROUND(SUM(a.admin_revenue)/NULLIF(SUM(a.cost), 0), 2) as roi
		FROM ad_slot_plan_daily_stats a
		LEFT JOIN ad_slot_plans b ON (a.plan_id = b.id)
		LEFT JOIN ad_slots c ON (a.ad_slot_id = c.id)
		LEFT JOIN users u ON (a.user_id = u.id)
		WHERE a.date BETWEEN ? AND ? 
			AND a.user_id NOT IN (12,13,16,21)
			AND a.deleted_at IS NULL
			AND b.deleted_at IS NULL
			AND c.deleted_at IS NULL
	`

	// 动态参数
	var params []interface{}
	params = append(params, weekStart, weekEnd)

	// 添加权限过滤
	if scope == "self" {
		query += " AND a.user_id = ?"
		params = append(params, param.UserID)
	} else if scope == "department" && len(departmentUsers) > 0 {
		query += " AND a.user_id IN (?)"
		params = append(params, departmentUsers)
	}

	// 添加媒体过滤
	if param.MediaID > 0 {
		query += " AND a.media_id = ?"
		params = append(params, param.MediaID)
	}

	// 完成查询
	query += `
		GROUP BY a.media_id, a.user_id, c.media_name, c.type
		ORDER BY profit DESC
	`

	// 执行查询
	if err := s.db.Raw(query, params...).Scan(&results).Error; err != nil {
		return domain.WeeklyDetailEntity{}, fmt.Errorf("查询周报表详情失败: %v", err)
	}

	// 转换为实体
	var details []domain.DetailItemEntity
	profitMediaCount := 0
	lossMediaCount := 0

	for _, item := range results {
		// 计算利润率
		var profitMarginRate float64
		if item.Revenue > 0 {
			profitMarginRate = (item.Profit / item.Revenue) * 100
		}

		// 判断是否盈利
		isProfit := item.Profit >= 0
		if isProfit {
			profitMediaCount++
		} else {
			lossMediaCount++
		}

		detailItem := domain.DetailItemEntity{
			MediaID:          item.MediaID,
			MediaName:        item.MediaName,
			PromotionChannel: item.PromotionChannel,
			IsProfit:         isProfit,
			Orders:           item.Orders,
			Revenue:          item.Revenue,
			Cost:             item.Cost,
			Profit:           item.Profit,
			Clicks:           item.Clicks,
			ROI:              item.ROI,
			ProfitMarginRate: profitMarginRate,
			UserID:           item.UserID,
			UserName:         item.UserName,
		}
		details = append(details, detailItem)
	}

	// 汇总数据
	var totalProfit float64
	var totalOrders int
	for _, item := range details {
		totalProfit += item.Profit
		totalOrders += item.Orders
	}

	summary := domain.DetailSummaryEntity{
		TotalProfit:      totalProfit,
		TotalOrders:      totalOrders,
		ProfitMediaCount: profitMediaCount,
		LossMediaCount:   lossMediaCount,
	}

	return domain.WeeklyDetailEntity{
		YearWeek:  param.YearWeek,
		WeekStart: weekStart,
		WeekEnd:   weekEnd,
		List:      details,
		Summary:   summary,
	}, nil
}

// ExportWeeklyReport 导出周报表数据
func (s *DailyReportService) ExportWeeklyReport(param domain.WeeklyReportParam) ([]byte, string, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.getDataScopeInfo(param.UserID, "report")
	if err != nil {
		return nil, "", fmt.Errorf("获取数据权限失败: %v", err)
	}

	// 验证日期范围
	start, err := time.Parse("2006-01-02", param.StartDate)
	if err != nil {
		return nil, "", fmt.Errorf("开始日期格式错误: %v", err)
	}

	end, err := time.Parse("2006-01-02", param.EndDate)
	if err != nil {
		return nil, "", fmt.Errorf("结束日期格式错误: %v", err)
	}

	if end.Before(start) {
		return nil, "", fmt.Errorf("结束日期不能早于开始日期")
	}

	// 导出数据
	fileData, filename, err := s.exportWeeklyReportData(param, scope, departmentUsers)
	if err != nil {
		return nil, "", fmt.Errorf("导出周报表数据失败: %v", err)
	}

	return fileData, filename, nil
}

// exportWeeklyReportData 导出周报表数据为Excel
func (s *DailyReportService) exportWeeklyReportData(param domain.WeeklyReportParam, scope string, departmentUsers []int64) ([]byte, string, error) {
	// 获取数据
	reportData, err := s.getWeeklyReportData(param, scope, departmentUsers)
	if err != nil {
		return nil, "", err
	}

	// 创建Excel文件
	f := excelize.NewFile()
	sheetName := "周报表"

	// 删除默认的Sheet1，创建新的工作表
	f.DeleteSheet("Sheet1")
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return nil, "", fmt.Errorf("创建工作表失败: %v", err)
	}
	f.SetActiveSheet(index)

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Size:   12,
			Family: "微软雅黑",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建表头样式失败: %v", err)
	}

	// 设置数据样式
	dataStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   11,
			Family: "微软雅黑",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
		NumFmt: 2, // 数字格式，保留两位小数
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建数据样式失败: %v", err)
	}

	// 设置日期样式
	dateStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size:   11,
			Family: "微软雅黑",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
		},
		NumFmt: 14, // 日期格式 yyyy-mm-dd
	})
	if err != nil {
		return nil, "", fmt.Errorf("创建日期样式失败: %v", err)
	}

	// 设置表头
	headers := []string{
		"年周", "周开始", "周结束",
		"支付宝利润", "支付宝订单数", "支付宝佣金收入", "支付宝成本",
		"微信利润", "微信订单数", "微信佣金收入", "微信成本",
		"其他利润", "其他佣金收入", "其他成本",
		"总利润", "总订单数", "总佣金收入", "总成本", "ROI", "利润率(%)",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// 写入数据
	for i, item := range reportData.List {
		row := i + 2 // 从第2行开始写入数据

		// 解析日期
		var weekStartTime, weekEndTime time.Time
		if item.WeekStart != "" {
			weekStartTime, _ = time.Parse("2006-01-02", item.WeekStart)
		}
		if item.WeekEnd != "" {
			weekEndTime, _ = time.Parse("2006-01-02", item.WeekEnd)
		}

		// 写入各列数据
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.YearWeek)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), weekStartTime)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), weekEndTime)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.AlipayProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.AlipayOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.AlipayRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.AlipayCost)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.WechatProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.WechatOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.WechatRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), item.WechatCost)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), item.OtherProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), item.OtherRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), item.OtherCost)
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", row), item.TotalProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("P%d", row), item.TotalOrders)
		f.SetCellValue(sheetName, fmt.Sprintf("Q%d", row), item.TotalRevenue)
		f.SetCellValue(sheetName, fmt.Sprintf("R%d", row), item.TotalCost)
		f.SetCellValue(sheetName, fmt.Sprintf("S%d", row), item.ROI)
		f.SetCellValue(sheetName, fmt.Sprintf("T%d", row), item.ProfitMarginRate)

		// 应用样式
		// 年周列
		f.SetCellStyle(sheetName, fmt.Sprintf("A%d", row), fmt.Sprintf("A%d", row), dataStyle)

		// 日期列
		f.SetCellStyle(sheetName, fmt.Sprintf("B%d", row), fmt.Sprintf("C%d", row), dateStyle)

		// 数据列
		f.SetCellStyle(sheetName, fmt.Sprintf("D%d", row), fmt.Sprintf("T%d", row), dataStyle)
	}

	// 设置列宽
	columnWidths := map[string]float64{
		"A": 12, // 年周
		"B": 15, // 周开始
		"C": 15, // 周结束
		"D": 15, // 支付宝利润
		"E": 12, // 支付宝订单数
		"F": 15, // 支付宝佣金收入
		"G": 15, // 支付宝成本
		"H": 15, // 微信利润
		"I": 12, // 微信订单数
		"J": 15, // 微信佣金收入
		"K": 15, // 微信成本
		"L": 15, // 其他利润
		"M": 15, // 其他佣金收入
		"N": 15, // 其他成本
		"O": 15, // 总利润
		"P": 12, // 总订单数
		"Q": 15, // 总佣金收入
		"R": 15, // 总成本
		"S": 10, // ROI
		"T": 12, // 利润率
	}

	for col, width := range columnWidths {
		f.SetColWidth(sheetName, col, col, width)
	}

	// 冻结首行
	f.SetPanes(sheetName, &excelize.Panes{
		Freeze:      true,
		XSplit:      0,
		YSplit:      1,
		TopLeftCell: "A2",
	})

	// 生成Excel文件内容
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, "", fmt.Errorf("生成Excel文件失败: %v", err)
	}

	// 生成文件名
	filename := fmt.Sprintf("周报表_%s_%s_%s.xlsx",
		param.StartDate,
		param.EndDate,
		time.Now().Format("20060102_150405"))

	return buf.Bytes(), filename, nil
}

// getWeekStartEndDates 获取指定ISO-8601格式年周的开始和结束日期
// 格式：2024-W01 表示2024年第一周
func (s *DailyReportService) getWeekStartEndDates(yearWeek string) (string, string, error) {
	// 解析年周
	t, err := time.Parse("2006-W02", yearWeek)
	if err != nil {
		return "", "", fmt.Errorf("解析年周格式失败: %v", err)
	}

	// 调整到周一（ISO-8601标准周的开始）
	weekday := t.Weekday()
	if weekday == time.Sunday {
		t = t.AddDate(0, 0, -6)
	} else {
		t = t.AddDate(0, 0, -(int(weekday) - 1))
	}

	start := t.Format("2006-01-02")
	end := t.AddDate(0, 0, 6).Format("2006-01-02")

	return start, end, nil
}
