package domain

// 资源位类型常量
const (
	SlotTypeWechatH5     = "wechat_h5"     // 微信H5
	SlotTypeWechatMP     = "wechat_mp"     // 微信小程序
	SlotTypeWechatPlugin = "wechat_plugin" // 微信插件
	SlotTypeAlipayMP     = "alipay_mp"     // 支付宝小程序
	SlotTypeAlipayH5     = "alipay_h5"     // 支付宝H5
	SlotTypeOther        = "other"         // 其他
)

// 审核状态常量
const (
	SlotAuditStatusPending  = "pending"  // 审核中
	SlotAuditStatusApproved = "approved" // 已通过
	SlotAuditStatusRejected = "rejected" // 已拒绝
)

// SlotListParam 获取资源位列表参数
type SlotListParam struct {
	Name        string // 资源位名称
	Type        string // 资源位类型
	AuditStatus string // 审核状态
	MediaID     int    // 媒体ID
	UserID      int    // 用户ID
	Page        int    // 页码
	Size        int    // 每页数量
}

// SlotListResult 获取资源位列表结果
type SlotListResult struct {
	Total int64        // 总数
	List  []SlotEntity // 列表
}

// SlotEntity 资源位实体
type SlotEntity struct {
	ID            int64   // ID
	Code          string  // 资源位编号
	Name          string  // 资源位名称
	Type          string  // 资源位类型
	TypeText      string  // 类型文本
	MediaID       int64   // 媒体ID
	MediaName     string  // 媒体名称
	UserID        int64   // 用户ID
	Description   string  // 描述
	ScreenshotURL string  // 截图URL
	VideoURL      string  // 视频URL
	AuditStatus   string  // 审核状态
	RejectReason  string  // 拒绝原因
	LeakRate      float64 // 漏损率
	DiscountRate  float64 // 折扣率
	Remark        string  // 备注
	CreatedAt     string  // 创建时间
	CreatedBy     string  // 创建人
	UpdatedAt     string  // 更新时间
	UpdatedBy     string  // 更新人
}

// GetTypeText 获取类型文本
func GetTypeText(slotType string) string {
	typeMap := map[string]string{
		SlotTypeWechatH5:     "微信H5",
		SlotTypeWechatMP:     "微信小程序",
		SlotTypeWechatPlugin: "微信插件",
		SlotTypeAlipayMP:     "支付宝小程序",
		SlotTypeAlipayH5:     "支付宝H5",
		SlotTypeOther:        "其他",
	}

	if text, ok := typeMap[slotType]; ok {
		return text
	}
	return slotType
}

// SlotCreateEntity 创建资源位实体
type SlotCreateEntity struct {
	Name          string  // 资源位名称
	Type          string  // 资源位类型
	MediaID       int64   // 媒体ID
	Description   string  // 描述
	ScreenshotURL string  // 截图URL
	VideoURL      string  // 视频URL
	LeakRate      float64 // 漏损率
	DiscountRate  float64 // 折扣率
	Remark        string  // 备注
}

// SlotUpdateEntity 更新资源位实体
type SlotUpdateEntity struct {
	ID            int64   // 资源位ID
	Name          string  // 资源位名称
	Type          string  // 资源位类型
	MediaID       int64   // 媒体ID
	Description   string  // 描述
	ScreenshotURL string  // 截图URL
	VideoURL      string  // 视频URL
	LeakRate      float64 // 漏损率
	DiscountRate  float64 // 折扣率
	Remark        string  // 备注
}

// SlotAuditEntity 审核资源位实体
type SlotAuditEntity struct {
	ID           int64  // 资源位ID
	AuditStatus  string // 审核状态
	AuditRemark  string // 审核备注
	RejectReason string // 拒绝原因
}
