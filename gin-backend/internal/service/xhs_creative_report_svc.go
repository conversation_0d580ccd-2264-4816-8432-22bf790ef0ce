package service

import (
	"context"
	"errors"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/xiaohongshu"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// XHSCreativeReportService 小红书创意报表服务（包含实时数据同步功能）
type XHSCreativeReportService struct {
	db *gorm.DB
}

// NewXHSCreativeReportService 创建小红书创意报表服务
func NewXHSCreativeReportService() *XHSCreativeReportService {
	return &XHSCreativeReportService{
		db: global.DB,
	}
}

// SyncYesterdayReports 同步昨天的创意报表数据
func (s *XHSCreativeReportService) SyncYesterdayReports(ctx context.Context) error {
	yesterday := time.Now().AddDate(0, 0, -1)
	return s.SyncReportsByDate(ctx, yesterday)
}

// SyncReportsByDate 同步指定日期的创意报表数据
func (s *XHSCreativeReportService) SyncReportsByDate(ctx context.Context, date time.Time) error {
	zap.L().Info("开始同步小红书创意报表数据", zap.String("date", date.Format("2006-01-02")))

	// 获取所有已授权的小红书账号
	accounts, err := s.getAuthorizedXHSAccounts(ctx)
	if err != nil {
		return fmt.Errorf("获取已授权账号失败: %w", err)
	}

	if len(accounts) == 0 {
		zap.L().Info("没有找到已授权的小红书账号，跳过同步")
		return nil
	}

	// 创建小红书客户端
	xhsConfig := &xiaohongshu.Config{
		AppId:  config.AppConfig.Xiaohongshu.AppId,
		Secret: config.AppConfig.Xiaohongshu.Secret,
		IsProd: config.AppConfig.Xiaohongshu.IsProd,
	}
	client := xiaohongshu.NewClient(xhsConfig)

	// 为每个账号同步数据
	successCount := 0
	errorCount := 0

	for _, account := range accounts {
		err := s.syncAccountReports(ctx, client, account, date)
		if err != nil {
			zap.L().Error("同步账号报表数据失败",
				zap.Int64("account_id", account.ID),
				zap.String("account_name", account.AccountName),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}
	}

	zap.L().Info("小红书创意报表数据同步完成",
		zap.String("date", date.Format("2006-01-02")),
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))

	return nil
}

// getAuthorizedXHSAccounts 获取已授权的小红书账号
func (s *XHSCreativeReportService) getAuthorizedXHSAccounts(ctx context.Context) ([]model.AdAccounts, error) {
	var accounts []model.AdAccounts

	err := s.db.WithContext(ctx).Where(
		"platform = ? AND authorization_status = ? AND usage_status = ? AND token != '' AND token_expire_time > ?",
		1, // 小红书平台
		2, // 已授权
		1, // 启用
		time.Now(),
	).Find(&accounts).Error

	if err != nil {
		return nil, err
	}

	return accounts, nil
}

// syncAccountReports 同步单个账号的报表数据
func (s *XHSCreativeReportService) syncAccountReports(ctx context.Context, client *xiaohongshu.Client, account model.AdAccounts, date time.Time) error {
	dateStr := date.Format("2006-01-02")

	// 检查是否已经同步过该日期的数据
	exists, err := s.checkReportExists(ctx, account.ID, date)
	if err != nil {
		return fmt.Errorf("检查报表数据是否存在失败: %w", err)
	}

	if exists {
		zap.L().Info("该账号该日期的数据已存在，跳过同步",
			zap.Int64("account_id", account.ID),
			zap.String("date", dateStr))
		return nil
	}

	// 构建报表请求
	advertiserID, err := strconv.ParseInt(account.PlatformAccountId, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err)
	}

	request := xiaohongshu.NewReportBuilder(advertiserID, dateStr, dateStr).
		WithTimeUnit(xiaohongshu.TimeUnitSummary).
		WithPagination(1, 500). // 每页500条
		Build()

	// 分页获取所有数据
	allReports := make([]*model.XHSCreativeReports, 0)
	pageNum := 1

	for {
		request.PageNum = pageNum

		// 调用小红书API
		resp, err := client.GetCreativeReport(account.Token, request)
		if err != nil {
			return fmt.Errorf("调用小红书API失败: %w", err)
		}

		if !resp.Success {
			return fmt.Errorf("小红书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 转换数据格式
		reports, err := s.convertToReportModels(account, resp.Data.DataList, date)
		if err != nil {
			return fmt.Errorf("转换数据格式失败: %w", err)
		}

		allReports = append(allReports, reports...)

		// 检查是否还有更多数据
		if len(resp.Data.DataList) < request.PageSize {
			break
		}

		pageNum++
	}

	// 批量保存到数据库
	if len(allReports) > 0 {
		err = s.batchSaveReports(ctx, allReports)
		if err != nil {
			return fmt.Errorf("批量保存报表数据失败: %w", err)
		}

		zap.L().Info("成功同步账号报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("account_name", account.AccountName),
			zap.String("date", dateStr),
			zap.Int("count", len(allReports)))
	} else {
		zap.L().Info("该账号该日期无报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("date", dateStr))
	}

	return nil
}

// checkReportExists 检查报表数据是否已存在
func (s *XHSCreativeReportService) checkReportExists(ctx context.Context, accountID int64, date time.Time) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{}).
		Where("account_id = ? AND DATE(time) = ?", accountID, date.Format("2006-01-02")).
		Count(&count).Error

	return count > 0, err
}

// convertToReportModels 转换API响应数据为数据库模型
func (s *XHSCreativeReportService) convertToReportModels(account model.AdAccounts, dataList []xiaohongshu.ReportData, date time.Time) ([]*model.XHSCreativeReports, error) {
	reports := make([]*model.XHSCreativeReports, 0, len(dataList))

	for _, data := range dataList {
		// 标题_口令词_内容姓名/投手姓名_日期_时间_智投_10:51_3
		creativityNames := strings.Split(data.CreativityName, "_")
		var (
			pwd           string
			title         string
			contentPeople string
			pitcherName   string
		)

		if len(creativityNames) == 8 {
			pwd = creativityNames[1]
			title = creativityNames[0]
			peoples := strings.Split(creativityNames[2], "/")
			if len(peoples) == 2 {
				contentPeople = peoples[0]
				pitcherName = peoples[1]
			}

		}
		report := &model.XHSCreativeReports{
			Id:            0,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			AccountId:     account.ID,
			AccountName:   account.AccountName,
			Pwd:           pwd,
			Title:         title,
			ContentPeople: contentPeople,
			PitcherName:   pitcherName,
			// 业务字段
			CampaignID:      data.CampaignID,
			CampaignName:    data.CampaignName,
			UnitID:          data.UnitID,
			UnitName:        data.UnitName,
			CreativityID:    data.CreativityID,
			CreativityName:  data.CreativityName,
			CreativityImage: data.CreativityImage,
			NoteID:          data.NoteID,
			Time:            date,
			PageID:          data.PageID,
			ItemID:          data.ItemID,
			LiveRedID:       data.LiveRedID,
			CountryName:     data.CountryName,
			Province:        data.Province,
			City:            data.City,
			NoteUserID:      data.NoteUserID,
		}

		// 转换枚举字段
		if placement, err := s.parseIntField(data.Placement); err == nil {
			report.Placement = int8(placement)
		}
		if optimizeTarget, err := s.parseIntField(data.OptimizeTarget); err == nil {
			report.OptimizeTarget = int8(optimizeTarget)
		}
		if promotionTarget, err := s.parseIntField(data.PromotionTarget); err == nil {
			report.PromotionTarget = int8(promotionTarget)
		}
		if biddingStrategy, err := s.parseIntField(data.BiddingStrategy); err == nil {
			report.BiddingStrategy = int8(biddingStrategy)
		}
		if buildType, err := s.parseIntField(data.BuildType); err == nil {
			report.BuildType = int8(buildType)
		}
		if marketingTarget, err := s.parseIntField(data.MarketingTarget); err == nil {
			report.MarketingTarget = int8(marketingTarget)
		}

		// 转换基础指标
		report.Fee, _ = s.parseFloatField(data.Fee)
		report.Impression, _ = s.parseIntField(data.Impression)
		report.Click, _ = s.parseIntField(data.Click)
		report.CTR, _ = s.parseFloatField(data.CTR)
		report.ACP, _ = s.parseFloatField(data.ACP)
		report.CPM, _ = s.parseFloatField(data.CPM)

		// 转换互动指标
		report.Like, _ = s.parseIntField(data.Like)
		report.Comment, _ = s.parseIntField(data.Comment)
		report.Collect, _ = s.parseIntField(data.Collect)
		report.Follow, _ = s.parseIntField(data.Follow)
		report.Share, _ = s.parseIntField(data.Share)
		report.Interaction, _ = s.parseIntField(data.Interaction)
		report.CPI, _ = s.parseFloatField(data.CPI)
		report.ActionButtonClick, _ = s.parseIntField(data.ActionButtonClick)
		report.ActionButtonCTR, _ = s.parseFloatField(data.ActionButtonCTR)
		report.Screenshot, _ = s.parseIntField(data.Screenshot)
		report.PicSave, _ = s.parseIntField(data.PicSave)
		report.ReservePV, _ = s.parseIntField(data.ReservePV)

		// 转换电商指标
		report.GoodsVisit, _ = s.parseIntField(data.GoodsVisit)
		report.GoodsVisitPrice, _ = s.parseFloatField(data.GoodsVisitPrice)
		report.SellerVisit, _ = s.parseIntField(data.SellerVisit)
		report.SellerVisitPrice, _ = s.parseFloatField(data.SellerVisitPrice)
		report.ShoppingCartAdd, _ = s.parseIntField(data.ShoppingCartAdd)
		report.AddCartPrice, _ = s.parseFloatField(data.AddCartPrice)

		// 转换直播间互动指标
		report.ClkLiveEntryPV, _ = s.parseIntField(data.ClkLiveEntryPV)
		report.ClkLiveEntryPVCost, _ = s.parseFloatField(data.ClkLiveEntryPVCost)
		// 直播间停留时长转换为秒（原始数据可能是分钟）
		if avgViewTime, err := s.parseFloatField(data.ClkLiveAvgViewTime); err == nil {
			report.ClkLiveAvgViewTime = int32(avgViewTime * 60) // 转换为秒
		}
		report.ClkLiveAllFollow, _ = s.parseIntField(data.ClkLiveAllFollow)
		report.ClkLive5sEntryPV, _ = s.parseIntField(data.ClkLive5sEntryPV)
		report.ClkLive5sEntryUVCost, _ = s.parseFloatField(data.ClkLive5sEntryUVCost)
		report.ClkLiveComment, _ = s.parseIntField(data.ClkLiveComment)

		// 转换笔记种草指标
		report.SearchCmtClick, _ = s.parseIntField(data.SearchCmtClick)
		report.SearchCmtClickCVR, _ = s.parseFloatField(data.SearchCmtClickCVR)
		report.SearchCmtAfterRead, _ = s.parseIntField(data.SearchCmtAfterRead)
		report.SearchCmtAfterReadAvg, _ = s.parseFloatField(data.SearchCmtAfterReadAvg)
		report.IUserNum, _ = s.parseIntField(data.IUserNum)
		report.TIUserNum, _ = s.parseIntField(data.TIUserNum)
		report.IUserPrice, _ = s.parseFloatField(data.IUserPrice)
		report.TIUserPrice, _ = s.parseFloatField(data.TIUserPrice)

		// 转换7日转化指标
		report.PresaleOrderNum7D, _ = s.parseIntField(data.PresaleOrderNum7D)
		report.PresaleOrderGMV7D, _ = s.parseFloatField(data.PresaleOrderGMV7D)
		report.GoodsOrder, _ = s.parseIntField(data.GoodsOrder)
		report.GoodsOrderPrice, _ = s.parseFloatField(data.GoodsOrderPrice)
		report.RGMV, _ = s.parseFloatField(data.RGMV)
		report.ROI, _ = s.parseFloatField(data.ROI)
		report.SuccessGoodsOrder, _ = s.parseIntField(data.SuccessGoodsOrder)
		report.ClickOrderCVR, _ = s.parseFloatField(data.ClickOrderCVR)
		report.PurchaseOrderPrice7D, _ = s.parseFloatField(data.PurchaseOrderPrice7D)
		report.PurchaseOrderGMV7D, _ = s.parseFloatField(data.PurchaseOrderGMV7D)
		report.PurchaseOrderROI7D, _ = s.parseFloatField(data.PurchaseOrderROI7D)

		// 转换直播间转化指标
		report.ClkLiveRoomOrderNum, _ = s.parseIntField(data.ClkLiveRoomOrderNum)
		report.LiveAverageOrderCost, _ = s.parseFloatField(data.LiveAverageOrderCost)
		report.ClkLiveRoomRGMV, _ = s.parseFloatField(data.ClkLiveRoomRGMV)
		report.ClkLiveRoomROI, _ = s.parseFloatField(data.ClkLiveRoomROI)

		// 转换销售线索指标
		report.Leads, _ = s.parseIntField(data.Leads)
		report.LeadsCPL, _ = s.parseFloatField(data.LeadsCPL)
		report.LandingPageVisit, _ = s.parseIntField(data.LandingPageVisit)
		report.LeadsButtonImpression, _ = s.parseIntField(data.LeadsButtonImpression)
		report.ValidLeads, _ = s.parseIntField(data.ValidLeads)
		report.ValidLeadsCPL, _ = s.parseFloatField(data.ValidLeadsCPL)
		report.LeadsCVR, _ = s.parseFloatField(data.LeadsCVR)
		report.PhoneCallCnt, _ = s.parseIntField(data.PhoneCallCnt)
		report.PhoneCallSuccCnt, _ = s.parseIntField(data.PhoneCallSuccCnt)
		report.WechatCopyCnt, _ = s.parseIntField(data.WechatCopyCnt)
		report.WechatCopySuccCnt, _ = s.parseIntField(data.WechatCopySuccCnt)
		report.IdentityCertiCnt, _ = s.parseIntField(data.IdentityCertiCnt)
		report.CommodityBuyCnt, _ = s.parseIntField(data.CommodityBuyCnt)

		// 转换私信营销指标
		report.MessageUser, _ = s.parseIntField(data.MessageUser)
		report.Message, _ = s.parseIntField(data.Message)
		report.MessageConsult, _ = s.parseIntField(data.MessageConsult)
		// 平均响应时长转换为秒（原始数据可能是分钟）
		if replyTime, err := s.parseFloatField(data.MessageFstReplyTimeAvg); err == nil {
			report.MessageFstReplyTimeAvg = int32(replyTime * 60) // 转换为秒
		}
		report.InitiativeMessage, _ = s.parseIntField(data.InitiativeMessage)
		report.MessageConsultCPL, _ = s.parseFloatField(data.MessageConsultCPL)
		report.InitiativeMessageCPL, _ = s.parseFloatField(data.InitiativeMessageCPL)
		report.MsgLeadsNum, _ = s.parseIntField(data.MsgLeadsNum)
		report.MsgLeadsCost, _ = s.parseFloatField(data.MsgLeadsCost)

		// 转换行业商品销量指标
		report.ExternalGoodsVisit7, _ = s.parseIntField(data.ExternalGoodsVisit7)
		report.ExternalGoodsVisitPrice7, _ = s.parseFloatField(data.ExternalGoodsVisitPrice7)
		report.ExternalGoodsVisitRate7, _ = s.parseFloatField(data.ExternalGoodsVisitRate7)
		report.ExternalGoodsOrder7, _ = s.parseIntField(data.ExternalGoodsOrder7)
		report.ExternalRGMV7, _ = s.parseFloatField(data.ExternalRGMV7)
		report.ExternalGoodsOrderPrice7, _ = s.parseFloatField(data.ExternalGoodsOrderPrice7)
		report.ExternalGoodsOrderRate7, _ = s.parseFloatField(data.ExternalGoodsOrderRate7)
		report.ExternalROI7, _ = s.parseFloatField(data.ExternalROI7)
		report.ExternalGoodsOrder15, _ = s.parseIntField(data.ExternalGoodsOrder15)
		report.ExternalRGMV15, _ = s.parseFloatField(data.ExternalRGMV15)
		report.ExternalGoodsOrderPrice15, _ = s.parseFloatField(data.ExternalGoodsOrderPrice15)
		report.ExternalGoodsOrderRate15, _ = s.parseFloatField(data.ExternalGoodsOrderRate15)
		report.ExternalROI15, _ = s.parseFloatField(data.ExternalROI15)
		report.ExternalGoodsOrder30, _ = s.parseIntField(data.ExternalGoodsOrder30)
		report.ExternalRGMV30, _ = s.parseFloatField(data.ExternalRGMV30)
		report.ExternalGoodsOrderPrice30, _ = s.parseFloatField(data.ExternalGoodsOrderPrice30)
		report.ExternalGoodsOrderRate30, _ = s.parseFloatField(data.ExternalGoodsOrderRate30)
		report.ExternalROI30, _ = s.parseFloatField(data.ExternalROI30)

		// 转换外链专属指标
		report.ExternalLeads, _ = s.parseIntField(data.ExternalLeads)
		report.ExternalLeadsCPL, _ = s.parseFloatField(data.ExternalLeadsCPL)

		// 转换关键词指标
		report.WordAvgLocation, _ = s.parseFloatField(data.WordAvgLocation)
		report.WordImpressionRankFirst, _ = s.parseIntField(data.WordImpressionRankFirst)
		report.WordImpressionRateFirst, _ = s.parseFloatField(data.WordImpressionRateFirst)
		report.WordImpressionRankThird, _ = s.parseIntField(data.WordImpressionRankThird)
		report.WordImpressionRateThird, _ = s.parseFloatField(data.WordImpressionRateThird)
		report.WordClickRankFirst, _ = s.parseIntField(data.WordClickRankFirst)
		report.WordClickRateFirst, _ = s.parseFloatField(data.WordClickRateFirst)
		report.WordClickRateThird, _ = s.parseFloatField(data.WordClickRateThird)
		report.WordClickRankThird, _ = s.parseIntField(data.WordClickRankThird)
		report.WordImpressionRankAll, _ = s.parseIntField(data.WordImpressionRankAll)
		report.WordImpressionRateAll, _ = s.parseFloatField(data.WordImpressionRateAll)
		report.WordClickRankAll, _ = s.parseIntField(data.WordClickRankAll)
		report.WordClickRateAll, _ = s.parseFloatField(data.WordClickRateAll)

		// 转换APP内转化数据指标
		report.InvokeAppOpenCnt, _ = s.parseIntField(data.InvokeAppOpenCnt)
		report.InvokeAppOpenCost, _ = s.parseFloatField(data.InvokeAppOpenCost)
		report.InvokeAppEnterStoreCnt, _ = s.parseIntField(data.InvokeAppEnterStoreCnt)
		report.InvokeAppEnterStoreCost, _ = s.parseFloatField(data.InvokeAppEnterStoreCost)
		report.InvokeAppEngagementCnt, _ = s.parseIntField(data.InvokeAppEngagementCnt)
		report.InvokeAppEngagementCost, _ = s.parseFloatField(data.InvokeAppEngagementCost)
		report.InvokeAppPaymentCnt, _ = s.parseIntField(data.InvokeAppPaymentCnt)
		report.InvokeAppPaymentCost, _ = s.parseFloatField(data.InvokeAppPaymentCost)
		report.SearchInvokeButtonClickCnt, _ = s.parseIntField(data.SearchInvokeButtonClickCnt)
		report.SearchInvokeButtonClickCost, _ = s.parseFloatField(data.SearchInvokeButtonClickCost)
		report.InvokeAppPaymentROI, _ = s.parseFloatField(data.InvokeAppPaymentROI)
		report.InvokeAppPaymentAmount, _ = s.parseFloatField(data.InvokeAppPaymentAmount)
		report.InvokeAppPaymentUnitPrice, _ = s.parseFloatField(data.InvokeAppPaymentUnitPrice)

		// 转换京东站外店铺行为指标
		report.JDActiveUserNum, _ = s.parseIntField(data.JDActiveUserNum)
		report.JDActiveUserNumCVR, _ = s.parseFloatField(data.JDActiveUserNumCVR)
		report.JDActiveUserNumCPL, _ = s.parseFloatField(data.JDActiveUserNumCPL)

		// 转换应用下载指标
		report.AppDownloadButtonClickCnt, _ = s.parseIntField(data.AppDownloadButtonClickCnt)
		report.AppDownloadButtonClickCTR, _ = s.parseFloatField(data.AppDownloadButtonClickCTR)
		report.AppDownloadButtonClickCost, _ = s.parseFloatField(data.AppDownloadButtonClickCost)
		report.AppActivateCnt, _ = s.parseIntField(data.AppActivateCnt)
		report.AppActivateCost, _ = s.parseFloatField(data.AppActivateCost)
		report.AppActivateCTR, _ = s.parseFloatField(data.AppActivateCTR)
		report.AppRegisterCnt, _ = s.parseIntField(data.AppRegisterCnt)
		report.AppRegisterCost, _ = s.parseFloatField(data.AppRegisterCost)
		report.AppRegisterCTR, _ = s.parseFloatField(data.AppRegisterCTR)
		report.FirstAppPayCnt, _ = s.parseIntField(data.FirstAppPayCnt)
		report.FirstAppPayCost, _ = s.parseFloatField(data.FirstAppPayCost)
		report.FirstAppPayCTR, _ = s.parseFloatField(data.FirstAppPayCTR)
		report.CurrentAppPayCnt, _ = s.parseIntField(data.CurrentAppPayCnt)
		report.CurrentAppPayCost, _ = s.parseFloatField(data.CurrentAppPayCost)
		report.AppKeyActionCnt, _ = s.parseIntField(data.AppKeyActionCnt)
		report.AppKeyActionCost, _ = s.parseFloatField(data.AppKeyActionCost)
		report.AppKeyActionCTR, _ = s.parseFloatField(data.AppKeyActionCTR)
		report.AppPayCnt7D, _ = s.parseIntField(data.AppPayCnt7D)
		report.AppPayCost7D, _ = s.parseFloatField(data.AppPayCost7D)
		report.AppPayAmount, _ = s.parseFloatField(data.AppPayAmount)
		report.AppPayROI, _ = s.parseFloatField(data.AppPayROI)
		report.AppActivateAmount1D, _ = s.parseFloatField(data.AppActivateAmount1D)
		report.AppActivateAmount3D, _ = s.parseFloatField(data.AppActivateAmount3D)
		report.AppActivateAmount7D, _ = s.parseFloatField(data.AppActivateAmount7D)
		report.AppActivateAmount1DROI, _ = s.parseFloatField(data.AppActivateAmount1DROI)
		report.AppActivateAmount3DROI, _ = s.parseFloatField(data.AppActivateAmount3DROI)
		report.AppActivateAmount7DROI, _ = s.parseFloatField(data.AppActivateAmount7DROI)
		report.Retention1DCnt, _ = s.parseIntField(data.Retention1DCnt)
		report.Retention3DCnt, _ = s.parseIntField(data.Retention3DCnt)
		report.Retention7DCnt, _ = s.parseIntField(data.Retention7DCnt)

		// 转换企微营销指标
		report.AddWechatCount, _ = s.parseIntField(data.AddWechatCount)
		report.AddWechatCost, _ = s.parseFloatField(data.AddWechatCost)
		report.AddWechatSucCount, _ = s.parseIntField(data.AddWechatSucCount)
		report.AddWechatSucCost, _ = s.parseFloatField(data.AddWechatSucCost)
		report.WechatTalkCount, _ = s.parseIntField(data.WechatTalkCount)
		report.WechatTalkCost, _ = s.parseFloatField(data.WechatTalkCost)

		// 转换门店营销指标
		report.ShopPoiClickNum, _ = s.parseIntField(data.ShopPoiClickNum)
		report.ShopPoiPagePV, _ = s.parseIntField(data.ShopPoiPagePV)
		report.ShopPoiPageVisitPrice, _ = s.parseFloatField(data.ShopPoiPageVisitPrice)
		report.ShopPoiPageNavigateClick, _ = s.parseIntField(data.ShopPoiPageNavigateClick)

		reports = append(reports, report)
	}

	return reports, nil
}

// parseIntField 解析字符串为int64
func (s *XHSCreativeReportService) parseIntField(value string) (int64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseInt(strings.TrimSpace(value), 10, 64)
}

// parseFloatField 解析字符串为float64
func (s *XHSCreativeReportService) parseFloatField(value string) (float64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseFloat(strings.TrimSpace(value), 64)
}

// batchSaveReports 批量保存报表数据
func (s *XHSCreativeReportService) batchSaveReports(ctx context.Context, reports []*model.XHSCreativeReports) error {
	// 使用事务批量插入
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批1000条
		batchSize := 1000
		for i := 0; i < len(reports); i += batchSize {
			end := i + batchSize
			if end > len(reports) {
				end = len(reports)
			}

			batch := reports[i:end]
			if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetReportList 获取创意报表列表
func (s *XHSCreativeReportService) GetReportList(ctx context.Context, param domain.XHSCreativeReportListParam) (domain.XHSCreativeReportListResult, error) {
	// 参数验证
	if err := domain.ValidateXHSCreativeReportListParam(param); err != nil {
		return domain.XHSCreativeReportListResult{}, err
	}

	// 设置默认聚合类型
	aggregateType := param.AggregateType
	if aggregateType == "" {
		aggregateType = "daily" // 默认为分日数据
	}

	// 根据聚合类型选择不同的查询逻辑
	switch aggregateType {
	case "daily":
		return s.getDailyReportList(ctx, param)
	case "summary":
		return s.getSummaryReportList(ctx, param)
	default:
		return domain.XHSCreativeReportListResult{}, fmt.Errorf("不支持的聚合类型: %s", aggregateType)
	}
}

// getDailyReportList 获取分日报表数据（不聚合，按日期+创意ID维度）
func (s *XHSCreativeReportService) getDailyReportList(ctx context.Context, param domain.XHSCreativeReportListParam) (domain.XHSCreativeReportListResult, error) {
	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addFilterConditions(query, param)

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.XHSCreativeReportListResult{}, fmt.Errorf("获取总数失败: %w", err)
	}

	// 分页查询
	offset := (param.Page - 1) * param.PageSize
	var reports []model.XHSCreativeReports
	err := query.Order("time DESC, creativity_id ASC, id DESC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&reports).Error

	if err != nil {
		return domain.XHSCreativeReportListResult{}, fmt.Errorf("查询分日报表列表失败: %w", err)
	}

	return domain.XHSCreativeReportListResult{
		List:  reports,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// getSummaryReportList 获取汇总报表数据（按创意ID聚合多日数据）
func (s *XHSCreativeReportService) getSummaryReportList(ctx context.Context, param domain.XHSCreativeReportListParam) (domain.XHSCreativeReportListResult, error) {
	// 构建基础查询条件
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addFilterConditions(query, param)

	// 按创意ID分组聚合查询
	var aggregatedReports []model.XHSCreativeReports

	// 构建聚合查询SQL
	selectFields := `
		creativity_id,
		MAX(account_id) as account_id,
		MAX(account_name) as account_name,
		MAX(title) as title,
		MAX(pwd) as pwd,
		MAX(content_people) as content_people,
		MAX(pitcher_name) as pitcher_name,
		MAX(campaign_id) as campaign_id,
		MAX(campaign_name) as campaign_name,
		MAX(unit_id) as unit_id,
		MAX(unit_name) as unit_name,
		MAX(creativity_name) as creativity_name,
		MAX(creativity_image) as creativity_image,
		MAX(note_id) as note_id,
		MAX(placement) as placement,
		MAX(optimize_target) as optimize_target,
		MAX(promotion_target) as promotion_target,
		MAX(bidding_strategy) as bidding_strategy,
		MAX(build_type) as build_type,
		MAX(marketing_target) as marketing_target,
		MIN(time) as time,
		SUM(fee) as fee,
		SUM(impression) as impression,
		SUM(click) as click,
		AVG(ctr) as ctr,
		AVG(acp) as acp,
		AVG(cpm) as cpm,
		SUM(like) as like,
		SUM(comment) as comment,
		SUM(collect) as collect,
		SUM(follow) as follow,
		SUM(share) as share,
		SUM(interaction) as interaction,
		AVG(cpi) as cpi,
		SUM(goods_order) as goods_order,
		SUM(rgmv) as rgmv,
		AVG(roi) as roi,
		SUM(success_goods_order) as success_goods_order,
		AVG(purchase_order_roi_7d) as purchase_order_roi_7d,
		MAX(created_at) as created_at,
		MAX(updated_at) as updated_at
	`

	// 获取聚合后的总数
	var totalCount int64
	countQuery := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})
	s.addFilterConditions(countQuery, param)

	err := countQuery.Select("COUNT(DISTINCT creativity_id)").Scan(&totalCount).Error
	if err != nil {
		return domain.XHSCreativeReportListResult{}, fmt.Errorf("获取汇总数据总数失败: %w", err)
	}

	// 分页查询聚合数据
	offset := (param.Page - 1) * param.PageSize
	err = query.Select(selectFields).
		Group("creativity_id, campaign_id, unit_id, note_id, pitcher_name").
		Order("MIN(time) DESC, creativity_id ASC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&aggregatedReports).Error

	if err != nil {
		return domain.XHSCreativeReportListResult{}, fmt.Errorf("查询汇总报表列表失败: %w", err)
	}

	return domain.XHSCreativeReportListResult{
		List:  aggregatedReports,
		Total: totalCount,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// addFilterConditions 添加通用筛选条件
func (s *XHSCreativeReportService) addFilterConditions(query *gorm.DB, param domain.XHSCreativeReportListParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.CampaignName != "" {
		query.Where("campaign_name LIKE ?", "%"+param.CampaignName+"%")
	}
	if param.UnitName != "" {
		query.Where("unit_name LIKE ?", "%"+param.UnitName+"%")
	}
	if param.Title != "" {
		query.Where("title LIKE ?", "%"+param.Title+"%")
	}
	if param.Pwd != "" {
		query.Where("pwd LIKE ?", "%"+param.Pwd+"%")
	}
	if param.StartDate != nil {
		query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}
	if param.Placement != nil {
		query.Where("placement = ?", *param.Placement)
	}
}

// GetReportStats 获取创意报表统计信息
func (s *XHSCreativeReportService) GetReportStats(ctx context.Context, param domain.XHSCreativeReportStatsParam) (domain.XHSCreativeReportStatsResult, error) {
	// 参数验证
	if err := domain.ValidateXHSCreativeReportStatsParam(param); err != nil {
		return domain.XHSCreativeReportStatsResult{}, err
	}

	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.CampaignName != "" {
		query = query.Where("campaign_name LIKE ?", "%"+param.CampaignName+"%")
	}
	if param.UnitName != "" {
		query = query.Where("unit_name LIKE ?", "%"+param.UnitName+"%")
	}
	if param.Title != "" {
		query = query.Where("title LIKE ?", "%"+param.Title+"%")
	}
	if param.Pwd != "" {
		query = query.Where("pwd LIKE ?", "%"+param.Pwd+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query = query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}
	if param.Placement != nil {
		query = query.Where("placement = ?", *param.Placement)
	}

	// 统计查询
	var result struct {
		TotalReports int64   `gorm:"column:total_reports"`
		TotalFee     float64 `gorm:"column:total_fee"`
		TotalClick   int64   `gorm:"column:total_click"`
		TotalROI     float64 `gorm:"column:total_roi"`
		AvgCTR       float64 `gorm:"column:avg_ctr"`
		AvgCPI       float64 `gorm:"column:avg_cpi"`
	}

	err := query.Select(`
		COUNT(*) as total_reports,
		COALESCE(SUM(fee), 0) as total_fee,
		COALESCE(SUM(click), 0) as total_click,
		COALESCE(AVG(roi), 0) as total_roi,
		COALESCE(AVG(ctr), 0) as avg_ctr,
		COALESCE(AVG(cpi), 0) as avg_cpi
	`).Scan(&result).Error

	if err != nil {
		return domain.XHSCreativeReportStatsResult{}, fmt.Errorf("获取统计信息失败: %w", err)
	}

	// 获取最后更新时间
	var lastUpdateTime time.Time
	err = s.db.WithContext(ctx).Model(&model.XHSCreativeReports{}).
		Select("MAX(updated_at)").
		Scan(&lastUpdateTime).Error

	if err != nil {
		zap.L().Warn("获取最后更新时间失败", zap.Error(err))
		lastUpdateTime = time.Now()
	}

	return domain.XHSCreativeReportStatsResult{
		TotalReports:   result.TotalReports,
		TotalFee:       result.TotalFee,
		TotalClick:     result.TotalClick,
		TotalROI:       result.TotalROI,
		AvgCTR:         result.AvgCTR,
		AvgCPI:         result.AvgCPI,
		LastUpdateTime: lastUpdateTime,
	}, nil
}

// ExportReports 导出创意报表数据
func (s *XHSCreativeReportService) ExportReports(ctx context.Context, param domain.XHSCreativeReportExportParam) (domain.XHSCreativeReportExportResult, error) {
	// 参数验证
	if err := domain.ValidateXHSCreativeReportExportParam(param); err != nil {
		return domain.XHSCreativeReportExportResult{}, err
	}

	// 确定聚合类型
	aggregateType := param.AggregateType
	if aggregateType == "" {
		aggregateType = "daily" // 默认为分日数据
	}

	// 根据聚合类型选择不同的查询逻辑
	var reports []model.XHSCreativeReports
	var err error

	switch aggregateType {
	case "daily":
		reports, err = s.getDailyReportsForExport(ctx, param)
	case "summary":
		reports, err = s.getSummaryReportsForExport(ctx, param)
	default:
		return domain.XHSCreativeReportExportResult{}, fmt.Errorf("不支持的聚合类型: %s", aggregateType)
	}

	if err != nil {
		return domain.XHSCreativeReportExportResult{}, fmt.Errorf("查询导出数据失败: %w", err)
	}

	if len(reports) == 0 {
		return domain.XHSCreativeReportExportResult{}, fmt.Errorf("没有找到符合条件的数据")
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	aggregateTypeName := map[string]string{"daily": "分日", "summary": "汇总"}[aggregateType]
	fileName := fmt.Sprintf("xhs_creative_reports_%s_%s.%s", aggregateTypeName, timestamp, param.Format)

	// 根据格式导出文件
	var fileURL string
	switch param.Format {
	case "xlsx":
		fileURL, err = s.exportToExcel(ctx, reports, fileName)
	case "csv":
		fileURL, err = s.exportToCSV(ctx, reports, fileName)
	default:
		return domain.XHSCreativeReportExportResult{}, fmt.Errorf("不支持的导出格式: %s", param.Format)
	}

	if err != nil {
		return domain.XHSCreativeReportExportResult{}, fmt.Errorf("导出文件失败: %w", err)
	}

	return domain.XHSCreativeReportExportResult{
		FileName: fileName,
		FileURL:  fileURL,
	}, nil
}

// exportToExcel 导出为Excel文件
func (s *XHSCreativeReportService) exportToExcel(ctx context.Context, reports []model.XHSCreativeReports, fileName string) (string, error) {
	// 这里应该实现Excel导出逻辑
	// 由于需要引入Excel库，这里先返回一个模拟的URL
	// 实际实现时需要使用如 github.com/xuri/excelize/v2 等库

	zap.L().Info("导出Excel文件",
		zap.String("fileName", fileName),
		zap.Int("count", len(reports)))

	// 模拟文件URL，实际应该是上传到OSS后的URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	// TODO: 实现实际的Excel导出逻辑
	// 1. 创建Excel文件
	// 2. 写入表头
	// 3. 写入数据
	// 4. 保存文件
	// 5. 上传到OSS
	// 6. 返回下载URL

	return fileURL, nil
}

// exportToCSV 导出为CSV文件
func (s *XHSCreativeReportService) exportToCSV(ctx context.Context, reports []model.XHSCreativeReports, fileName string) (string, error) {
	// 这里应该实现CSV导出逻辑
	// 可以使用标准库的encoding/csv包

	zap.L().Info("导出CSV文件",
		zap.String("fileName", fileName),
		zap.Int("count", len(reports)))

	// 模拟文件URL，实际应该是上传到OSS后的URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	// TODO: 实现实际的CSV导出逻辑
	// 1. 创建CSV文件
	// 2. 写入表头
	// 3. 写入数据
	// 4. 保存文件
	// 5. 上传到OSS
	// 6. 返回下载URL

	return fileURL, nil
}

// GetReportDetail 获取创意报表详情
func (s *XHSCreativeReportService) GetReportDetail(ctx context.Context, id int64) (*model.XHSCreativeReports, error) {
	var report model.XHSCreativeReports
	err := s.db.WithContext(ctx).Where("id = ?", id).First(&report).Error
	if err != nil {
		return nil, err
	}
	return &report, nil
}

// getDailyReportsForExport 获取分日报表数据用于导出（不聚合，按日期+创意ID维度）
func (s *XHSCreativeReportService) getDailyReportsForExport(ctx context.Context, param domain.XHSCreativeReportExportParam) ([]model.XHSCreativeReports, error) {
	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addFilterConditionsForExport(query, param)

	// 查询数据（限制最大导出数量）
	var reports []model.XHSCreativeReports
	err := query.Order("time DESC, creativity_id ASC, id DESC").
		Limit(50000). // 限制最大导出5万条
		Find(&reports).Error

	if err != nil {
		return nil, fmt.Errorf("查询分日导出数据失败: %w", err)
	}

	return reports, nil
}

// getSummaryReportsForExport 获取汇总报表数据用于导出（按创意ID聚合多日数据）
func (s *XHSCreativeReportService) getSummaryReportsForExport(ctx context.Context, param domain.XHSCreativeReportExportParam) ([]model.XHSCreativeReports, error) {
	// 构建基础查询条件
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addFilterConditionsForExport(query, param)

	// 构建聚合查询SQL
	selectFields := `
		creativity_id,
		MAX(account_id) as account_id,
		MAX(account_name) as account_name,
		MAX(title) as title,
		MAX(pwd) as pwd,
		MAX(content_people) as content_people,
		MAX(pitcher_name) as pitcher_name,
		MAX(campaign_id) as campaign_id,
		MAX(campaign_name) as campaign_name,
		MAX(unit_id) as unit_id,
		MAX(unit_name) as unit_name,
		MAX(creativity_name) as creativity_name,
		MAX(creativity_image) as creativity_image,
		MAX(note_id) as note_id,
		MAX(placement) as placement,
		MAX(optimize_target) as optimize_target,
		MAX(promotion_target) as promotion_target,
		MAX(bidding_strategy) as bidding_strategy,
		MAX(build_type) as build_type,
		MAX(marketing_target) as marketing_target,
		MIN(time) as time,
		SUM(fee) as fee,
		SUM(impression) as impression,
		SUM(click) as click,
		AVG(ctr) as ctr,
		AVG(acp) as acp,
		AVG(cpm) as cpm,
		SUM(like) as like,
		SUM(comment) as comment,
		SUM(collect) as collect,
		SUM(follow) as follow,
		SUM(share) as share,
		SUM(interaction) as interaction,
		AVG(cpi) as cpi,
		SUM(goods_order) as goods_order,
		SUM(rgmv) as rgmv,
		AVG(roi) as roi,
		SUM(success_goods_order) as success_goods_order,
		AVG(purchase_order_roi_7d) as purchase_order_roi_7d,
		MAX(created_at) as created_at,
		MAX(updated_at) as updated_at
	`

	// 查询聚合数据（限制最大导出数量）
	var aggregatedReports []model.XHSCreativeReports
	err := query.Select(selectFields).
		Group("creativity_id, campaign_id, unit_id, note_id, pitcher_name").
		Order("MIN(time) DESC, creativity_id ASC").
		Limit(50000). // 限制最大导出5万条
		Find(&aggregatedReports).Error

	if err != nil {
		return nil, fmt.Errorf("查询汇总导出数据失败: %w", err)
	}

	return aggregatedReports, nil
}

// addFilterConditionsForExport 为导出添加通用筛选条件
func (s *XHSCreativeReportService) addFilterConditionsForExport(query *gorm.DB, param domain.XHSCreativeReportExportParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.CampaignName != "" {
		query.Where("campaign_name LIKE ?", "%"+param.CampaignName+"%")
	}
	if param.UnitName != "" {
		query.Where("unit_name LIKE ?", "%"+param.UnitName+"%")
	}
	if param.Title != "" {
		query.Where("title LIKE ?", "%"+param.Title+"%")
	}
	if param.Pwd != "" {
		query.Where("pwd LIKE ?", "%"+param.Pwd+"%")
	}
	if param.StartDate != nil {
		query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}
	if param.Placement != nil {
		query.Where("placement = ?", *param.Placement)
	}
}

// ==================== 实时数据同步功能 ====================

// SyncCurrentHourReports 同步当前小时的实时报表数据
func (s *XHSCreativeReportService) SyncCurrentHourReports(ctx context.Context) error {
	zap.L().Info("开始同步当前小时的小红书实时报表数据")

	// 获取所有有效的小红书账号
	var accounts []model.AdAccounts
	err := s.db.WithContext(ctx).
		Where("platform = ? AND usage_status = ? AND token IS NOT NULL AND token != ''", 1, 1).
		Find(&accounts).Error

	if err != nil {
		return fmt.Errorf("获取小红书账号列表失败: %w", err)
	}

	if len(accounts) == 0 {
		zap.L().Info("没有找到有效的小红书账号，跳过同步")
		return nil
	}

	zap.L().Info("找到小红书账号", zap.Int("count", len(accounts)))

	// 逐个账号同步数据
	successCount := 0
	errorCount := 0

	for _, account := range accounts {
		if err := s.syncAccountRealtimeReports(ctx, account); err != nil {
			zap.L().Error("同步账号实时报表数据失败",
				zap.Int64("account_id", account.ID),
				zap.String("account_name", account.AccountName),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}

		// 每个账号之间稍作休息，避免API调用过于频繁
		time.Sleep(500 * time.Millisecond)
	}

	zap.L().Info("小红书实时报表数据同步完成",
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))

	if errorCount > 0 {
		return fmt.Errorf("部分账号同步失败，成功: %d, 失败: %d", successCount, errorCount)
	}

	return nil
}

// syncAccountRealtimeReports 同步单个账号的实时报表数据
func (s *XHSCreativeReportService) syncAccountRealtimeReports(ctx context.Context, account model.AdAccounts) error {
	zap.L().Info("开始同步账号实时报表数据",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName))

	// 创建小红书客户端
	client := xiaohongshu.NewClient(&xiaohongshu.Config{
		AppId:  config.AppConfig.Xiaohongshu.AppId,
		Secret: config.AppConfig.Xiaohongshu.Secret,
		IsProd: config.AppConfig.Xiaohongshu.IsProd,
	})

	// 获取当前时间，用于实时数据查询
	now := time.Now()
	today := now.Format("2006-01-02")

	// 解析广告主ID
	advertiserID, err := strconv.ParseInt(account.PlatformAccountId, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err)
	}

	// 构建实时报表请求 - 获取今日数据
	request := xiaohongshu.NewRealtimeReportBuilder(advertiserID, today, today).
		WithPagination(1, 100).  // 每页100条
		WithSort("fee", "desc"). // 按消费降序
		Build()

	// 分页获取所有数据
	allReports := make([]model.XHSCreativeReports, 0)
	pageNum := 1
	pageSize := 100

	for {
		// 更新分页参数
		request.PageNum = pageNum
		request.PageSize = pageSize

		// 调用API获取数据
		response, err := client.GetCreativityRealtimeReport(account.Token, request)
		if err != nil {
			return fmt.Errorf("调用小红书实时报表API失败: %w", err)
		}

		if !response.Success {
			return fmt.Errorf("小红书API返回错误: code=%d, msg=%s", response.Code, response.Msg)
		}

		// 转换数据
		reports := s.convertToRealtimeReportModels(response.CreativityDtos, account, now)
		allReports = append(allReports, reports...)

		zap.L().Info("获取实时报表数据",
			zap.Int64("account_id", account.ID),
			zap.Int("page", pageNum),
			zap.Int("count", len(reports)))

		// 检查是否还有更多数据
		if len(response.CreativityDtos) < pageSize {
			break // 最后一页
		}

		pageNum++

		// 避免请求过于频繁
		time.Sleep(200 * time.Millisecond)
	}

	if len(allReports) == 0 {
		zap.L().Info("账号没有实时报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("account_name", account.AccountName))
		return nil
	}

	// 批量保存到数据库并聚合统计数据
	if err := s.batchSaveReportsWithAggregation(ctx, allReports, account, now); err != nil {
		return fmt.Errorf("保存实时报表数据失败: %w", err)
	}

	zap.L().Info("账号实时报表数据同步完成",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName),
		zap.Int("total_count", len(allReports)))

	return nil
}

// convertToRealtimeReportModels 转换API响应数据为数据库模型
func (s *XHSCreativeReportService) convertToRealtimeReportModels(creativityDtos []xiaohongshu.CreativityDTO, account model.AdAccounts, syncTime time.Time) []model.XHSCreativeReports {
	reports := make([]model.XHSCreativeReports, 0, len(creativityDtos))

	for _, data := range creativityDtos {
		report := model.XHSCreativeReports{
			// 账号信息
			AccountId:   account.ID,
			AccountName: account.AccountName,

			// 业务字段
			CampaignID:     strconv.FormatInt(data.BaseCampaign.CampaignID, 10),
			CampaignName:   data.BaseCampaign.CampaignName,
			UnitID:         strconv.FormatInt(data.BaseUnit.UnitID, 10),
			UnitName:       data.BaseUnit.UnitName,
			CreativityID:   strconv.FormatInt(data.BaseCreativity.CreativityID, 10),
			CreativityName: data.BaseCreativity.CreativityName,
			NoteID:         data.BaseCreativity.NoteID,

			// 统计时间
			Time: syncTime,
		}

		// 解析创意名称
		var title, pwd, contentPeople, pitcherName string
		creativityNames := strings.Split(data.BaseCreativity.CreativityName, "_")
		if len(creativityNames) == 8 {
			title = creativityNames[0]
			pwd = creativityNames[1]
			peoples := strings.Split(creativityNames[2], "/")
			if len(peoples) == 2 {
				contentPeople = peoples[0]
				pitcherName = peoples[1]
			}
		}
		report.Title = title
		report.Pwd = pwd
		report.ContentPeople = contentPeople
		report.PitcherName = pitcherName

		// 转换枚举字段
		report.Placement = int8(data.BaseCampaign.Placement)
		report.OptimizeTarget = int8(data.BaseCampaign.OptimizeTarget)
		report.PromotionTarget = int8(data.BaseCampaign.PromotionTarget)
		report.BiddingStrategy = int8(data.BaseCampaign.BiddingStrategy)
		report.BuildType = int8(data.BaseCampaign.BuildType)
		report.MarketingTarget = int8(data.BaseCampaign.MarketingTarget)

		// 转换基础指标
		report.Fee, _ = s.parseFloatField(data.Data.Fee)
		report.Impression, _ = s.parseIntField(data.Data.Impression)
		report.Click, _ = s.parseIntField(data.Data.Click)
		report.CTR, _ = s.parseFloatField(data.Data.Ctr)
		report.ACP, _ = s.parseFloatField(data.Data.Acp)
		report.CPM, _ = s.parseFloatField(data.Data.Cpm)

		// 转换互动指标
		report.Like, _ = s.parseIntField(data.Data.Like)
		report.Comment, _ = s.parseIntField(data.Data.Comment)
		report.Collect, _ = s.parseIntField(data.Data.Collect)
		report.Follow, _ = s.parseIntField(data.Data.Follow)
		report.Share, _ = s.parseIntField(data.Data.Share)
		report.Interaction, _ = s.parseIntField(data.Data.Interaction)
		report.CPI, _ = s.parseFloatField(data.Data.Cpi)

		// 转换电商转化指标
		report.GoodsOrder, _ = s.parseIntField(data.Data.GoodsOrder)
		report.RGMV, _ = s.parseFloatField(data.Data.Rgmv)
		report.ROI, _ = s.parseFloatField(data.Data.Roi)
		report.SuccessGoodsOrder, _ = s.parseIntField(data.Data.SuccessGoodsOrder)
		report.PurchaseOrderROI7D, _ = s.parseFloatField(data.Data.PurchaseOrderRoi7d)

		reports = append(reports, report)
	}

	return reports
}

// batchSaveReportsWithAggregation 批量保存报表数据并聚合统计数据
func (s *XHSCreativeReportService) batchSaveReportsWithAggregation(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time) error {
	if len(reports) == 0 {
		return nil
	}

	// 使用事务确保数据一致性
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 批量保存创意报表数据
		if err := s.batchSaveReportsInTx(ctx, reports, tx); err != nil {
			return fmt.Errorf("保存创意报表数据失败: %w", err)
		}

		// 2. 聚合计划统计数据
		if err := s.aggregatePlanStats(ctx, reports, account, syncTime, tx); err != nil {
			return fmt.Errorf("聚合计划统计数据失败: %w", err)
		}

		// 3. 聚合口令统计数据
		if err := s.aggregatePasswordStats(ctx, reports, account, syncTime, tx); err != nil {
			return fmt.Errorf("聚合口令统计数据失败: %w", err)
		}

		return nil
	})
}

// batchSaveReportsInTx 在事务中批量保存创意报表数据
func (s *XHSCreativeReportService) batchSaveReportsInTx(ctx context.Context, reports []model.XHSCreativeReports, tx *gorm.DB) error {
	// 分批插入，每批1000条
	batchSize := 1000
	for i := 0; i < len(reports); i += batchSize {
		end := i + batchSize
		if end > len(reports) {
			end = len(reports)
		}

		batch := reports[i:end]
		if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
			return err
		}
	}
	return nil
}

// aggregatePlanStats 聚合计划统计数据到ad_plan_stats表
func (s *XHSCreativeReportService) aggregatePlanStats(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time, tx *gorm.DB) error {
	// 按计划ID分组聚合数据
	planStatsMap := make(map[string]*model.AdPlanStats)
	statDate := time.Date(syncTime.Year(), syncTime.Month(), syncTime.Day(), 0, 0, 0, 0, syncTime.Location())

	for _, report := range reports {
		planID := report.CampaignID
		if planID == "" {
			continue
		}

		if _, exists := planStatsMap[planID]; !exists {
			planStatsMap[planID] = &model.AdPlanStats{
				PlanId:    planID,
				PlanName:  report.CampaignName,
				AccountId: account.ID,
				StatDate:  statDate,
				CreatedAt: syncTime,
				UpdatedAt: syncTime,
			}
		}

		stats := planStatsMap[planID]
		stats.Cost += report.Fee
		stats.ActualCost += report.Fee * (1 - account.RebateRate) // 实际消费等于消费金额乘以（1-返点率）
		stats.TotalCost += report.Fee                             // 总消费累加
		stats.Impressions += report.Impression
		stats.Clicks += report.Click

		// 计算点击率（百分比）
		if stats.Impressions > 0 {
			stats.ClickThroughRate = float64(stats.Clicks) / float64(stats.Impressions) * 100
		}
	}

	// 批量保存或更新计划统计数据
	for _, stats := range planStatsMap {
		var existingStats model.AdPlanStats
		err := tx.Where("plan_id = ? AND account_id = ? AND DATE(stat_date) = ?",
			stats.PlanId, stats.AccountId, statDate.Format("2006-01-02")).
			First(&existingStats).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 新增记录
			if err := tx.Create(stats).Error; err != nil {
				return fmt.Errorf("创建计划统计数据失败: %w", err)
			}
		} else if err == nil {
			// 更新现有记录
			existingStats.Cost += stats.Cost
			existingStats.ActualCost += stats.ActualCost
			existingStats.TotalCost += stats.TotalCost
			existingStats.Impressions += stats.Impressions
			existingStats.Clicks += stats.Clicks

			// 重新计算点击率
			if existingStats.Impressions > 0 {
				existingStats.ClickThroughRate = float64(existingStats.Clicks) / float64(existingStats.Impressions) * 100
			}
			existingStats.UpdatedAt = syncTime

			if err := tx.Save(&existingStats).Error; err != nil {
				return fmt.Errorf("更新计划统计数据失败: %w", err)
			}
		} else {
			return fmt.Errorf("查询计划统计数据失败: %w", err)
		}
	}

	zap.L().Info("计划统计数据聚合完成",
		zap.Int64("account_id", account.ID),
		zap.Int("plan_count", len(planStatsMap)),
		zap.String("stat_date", statDate.Format("2006-01-02")))

	return nil
}

// aggregatePasswordStats 聚合口令统计数据到password_stats表
func (s *XHSCreativeReportService) aggregatePasswordStats(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time, tx *gorm.DB) error {
	// 按口令分组聚合数据
	passwordStatsMap := make(map[string]*model.PasswordStats)
	statDate := time.Date(syncTime.Year(), syncTime.Month(), syncTime.Day(), 0, 0, 0, 0, syncTime.Location())

	// 获取所有口令
	var pwds []struct {
		Name string `gorm:"column:name"`
		Pid  string `gorm:"column:pid"`
	}
	var pwdNames []string
	for _, report := range reports {
		if report.Pwd == "" {
			continue
		}
		pwdNames = append(pwdNames, report.Pwd)
	}
	if err := s.db.WithContext(ctx).Table("ad_search_words").Where("name in ?", pwdNames).Select("name, pid").Find(&pwds).Error; err != nil {
		return fmt.Errorf("获取口令失败: %w", err)
	}

	if len(pwds) == 0 {
		return nil
	}

	dateStr := syncTime.Format(time.DateOnly)

	// 提取PID列表
	pids := make([]string, 0, len(pwds))
	pidMap := make(map[string]string)
	for _, pwd := range pwds {
		pids = append(pids, pwd.Pid)
		pidMap[pwd.Name] = pwd.Pid
	}

	// 从 byn_data.elm_ad_zone_reports 表获取UV数据
	type UVStat struct {
		Pid      string  `gorm:"column:pid"`
		Pv       int64   `gorm:"column:pv"`
		Uv       int64   `gorm:"column:uv"`
		ClickPv  int64   `gorm:"column:click_pv"`
		ClickUv  int64   `gorm:"column:click_uv"`
		OrderNum int64   `gorm:"column:order_num"`
		Income   float64 `gorm:"column:income"`
		Settle   float64 `gorm:"column:settle"`
	}

	var uvStats []UVStat
	if err := s.db.Table("byn_data.elm_ad_zone_reports").
		Select("pid, pv, uv, click_pv, click_uv, order_num, income, settle").
		Where("report_date = ?", dateStr).
		Where("pid IN ?", pids).
		Find(&uvStats).Error; err != nil {
		zap.L().Warn("获取UV数据失败", zap.Error(err), zap.String("date", dateStr))
		// 继续处理，不中断整个流程
	}

	// 构建PID到UV数据的映射
	uvStatsMap := make(map[string]UVStat)
	for _, stat := range uvStats {
		uvStatsMap[stat.Pid] = stat
	}

	// 从订单表获取实际订单数据
	type OrderStat struct {
		Pid      string  `gorm:"column:pid"`
		OrderNum int64   `gorm:"column:order_num"`
		Income   float64 `gorm:"column:income"`
		Settle   float64 `gorm:"column:settle"`
	}

	var orderStats []OrderStat
	cpsOrderName := fmt.Sprintf("cps_orders_%s", syncTime.Format("200601"))
	query := `
			SELECT 
				pid, 
				COUNT(1) as order_num, 
				SUM(pre_commission) as income, 
				SUM(commission) as settle 
			FROM warehouse.` + cpsOrderName + ` 
			WHERE supplier_id = 104 
				AND create_time BETWEEN ? AND ? 
				AND pid IN ? 
				AND order_status IN (2,3,4) 
			GROUP BY pid
		`
	if err := s.db.Raw(query,
		dateStr+" 00:00:00",
		dateStr+" 23:59:59",
		pids,
	).Scan(&orderStats).Error; err != nil {
		zap.L().Warn("获取订单数据失败", zap.Error(err), zap.String("date", dateStr))
		// 继续处理，不中断整个流程
	}

	// 构建PID到订单数据的映射
	orderStatsMap := make(map[string]OrderStat)
	for _, stat := range orderStats {
		orderStatsMap[stat.Pid] = stat
	}

	for _, report := range reports {
		pwd := report.Pwd
		if pwd == "" {
			continue
		}

		if _, exists := passwordStatsMap[pwd]; !exists {
			passwordStatsMap[pwd] = &model.PasswordStats{
				Pwd:       pwd,
				AccountId: account.ID,
				StatDate:  statDate,
				CreatedAt: syncTime,
				UpdatedAt: syncTime,
			}
		}

		stats := passwordStatsMap[pwd]
		stats.Consumption += report.Fee
		stats.ActualConsumption += report.Fee * (1 - account.RebateRate) // 实际消费等于消费金额乘以（1-返点率）
		stats.Impressions += report.Impression

		// 聚合订单相关数据（从创意报表中获取）
		pid := pidMap[pwd]
		if stat, ok := orderStatsMap[pid]; ok {
			stats.TotalOrders += stat.OrderNum
			stats.TotalIncome += stat.Income
		}
		//stats.NewOrdersToday += report.GoodsOrder

		if stat, ok := uvStatsMap[pid]; ok {
			stats.ClickCount += stat.ClickPv
			stats.SearchCount += stat.ClickUv
		}

		// 计算点击率（百分比）
		if stats.Impressions > 0 {
			stats.ClickRate = float64(stats.ClickCount) / float64(stats.Impressions) * 100
		}

		// 计算回收率（ROI转换为百分比）
		if stats.Consumption > 0 {
			stats.TotalRecoveryRate = (stats.TotalIncome / stats.Consumption) * 100
		}

		// 计算订单成本
		if stats.NewOrdersToday > 0 {
			stats.OrderCostToday = stats.Consumption / float64(stats.NewOrdersToday)
		}
	}

	// 批量保存或更新口令统计数据
	for _, stats := range passwordStatsMap {
		var existingStats model.PasswordStats
		err := tx.Where("password_name = ? AND account_id = ? AND DATE(stat_date) = ?",
			stats.Pwd, stats.AccountId, statDate.Format("2006-01-02")).
			First(&existingStats).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 新增记录
			if err := tx.Create(stats).Error; err != nil {
				return fmt.Errorf("创建口令统计数据失败: %w", err)
			}
		} else if err == nil {
			// 更新现有记录
			existingStats.Consumption += stats.Consumption
			existingStats.ActualConsumption += stats.ActualConsumption
			existingStats.Impressions += stats.Impressions
			existingStats.ClickCount += stats.ClickCount
			existingStats.NewOrdersToday += stats.NewOrdersToday
			existingStats.TotalOrders += stats.TotalOrders
			existingStats.TotalIncome += stats.TotalIncome

			// 重新计算比率
			if existingStats.Impressions > 0 {
				existingStats.ClickRate = float64(existingStats.ClickCount) / float64(existingStats.Impressions) * 100
			}
			if existingStats.Consumption > 0 {
				existingStats.TotalRecoveryRate = (existingStats.TotalIncome / existingStats.Consumption) * 100
			}
			if existingStats.NewOrdersToday > 0 {
				existingStats.OrderCostToday = existingStats.Consumption / float64(existingStats.NewOrdersToday)
			}
			existingStats.UpdatedAt = syncTime

			if err := tx.Save(&existingStats).Error; err != nil {
				return fmt.Errorf("更新口令统计数据失败: %w", err)
			}
		} else {
			return fmt.Errorf("查询口令统计数据失败: %w", err)
		}
	}

	zap.L().Info("口令统计数据聚合完成",
		zap.Int64("account_id", account.ID),
		zap.Int("password_count", len(passwordStatsMap)),
		zap.String("stat_date", statDate.Format("2006-01-02")))

	return nil
}
