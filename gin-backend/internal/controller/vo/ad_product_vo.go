package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// AdProductListReq 获取投放产品列表请求
type AdProductListReq struct {
	Page   int    `json:"page" form:"page"`           // 页码
	Size   int    `json:"page_size" form:"page_size"` // 每页大小
	Name   string `json:"name" form:"name"`           // 产品名称
	Status string `json:"status" form:"status"`       // 产品状态
}

// AdProductInfoItem 投放产品信息（用于列表显示）
type AdProductInfoItem struct {
	ID          int64  `json:"id"`          // 产品ID
	Code        string `json:"code"`        // 产品编号
	Name        string `json:"name"`        // 产品名称
	Image       string `json:"image"`       // 产品图片
	Description string `json:"description"` // 产品描述
	Status      string `json:"status"`      // 产品状态
	StatusText  string `json:"status_text"` // 状态文本
}

// AdProductListResp 获取投放产品列表响应
type AdProductListResp struct {
	List  []AdProductInfoItem `json:"list"`  // 产品列表
	Total int64               `json:"total"` // 总数
}

// AdProductDetailResp 获取投放产品详情响应
type AdProductDetailResp struct {
	ID          int64     `json:"id"`                   // 产品ID
	Code        string    `json:"code"`                 // 产品编号
	Name        string    `json:"name"`                 // 产品名称
	Image       string    `json:"image"`                // 产品图片
	Description string    `json:"description"`          // 产品描述
	Status      string    `json:"status"`               // 产品状态
	CreatedAt   time.Time `json:"created_at,omitempty"` // 创建时间
	UpdatedAt   time.Time `json:"updated_at,omitempty"` // 更新时间
}

// ToAdProductParam 将VO转换为领域参数
func (r AdProductListReq) ToAdProductParam() domain.AdProductParam {
	page := 1
	pageSize := 10
	if r.Page > 0 {
		page = r.Page
	}
	if r.Size > 0 {
		pageSize = r.Size
	}
	return domain.AdProductParam{
		Page:   page,
		Size:   pageSize,
		Name:   r.Name,
		Status: r.Status,
	}
}

// FromDomainAdProductInfo 从领域模型转换为VO
func FromDomainAdProductInfo(info domain.AdProductInfo) AdProductInfoItem {
	return AdProductInfoItem{
		ID:          info.ID,
		Code:        info.Code,
		Name:        info.Name,
		Image:       info.Image,
		Description: info.Description,
		Status:      info.Status,
		StatusText:  info.StatusText,
	}
}

// FromDomainAdProductListEntity 从领域模型列表转换为VO列表
func FromDomainAdProductListEntity(entity domain.AdProductListEntity) AdProductListResp {
	infoList := make([]AdProductInfoItem, 0, len(entity.List))
	for _, info := range entity.List {
		infoList = append(infoList, FromDomainAdProductInfo(info))
	}

	return AdProductListResp{
		List:  infoList,
		Total: entity.Total,
	}
}

// FromDomainAdProductEntity 从领域实体转换为详情VO
func FromDomainAdProductEntity(entity domain.AdProductEntity) AdProductDetailResp {
	return AdProductDetailResp{
		ID:          entity.ID,
		Code:        entity.Code,
		Name:        entity.Name,
		Image:       entity.Image,
		Description: entity.Description,
		Status:      entity.Status,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}
