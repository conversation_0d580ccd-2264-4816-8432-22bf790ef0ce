package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// PasswordInfo 口令信息
type PasswordInfo struct {
	ID   int64  `json:"id,omitempty"`            // 口令ID
	Name string `json:"name" binding:"required"` // 口令名称
	PID  string `json:"pid" binding:"required"`  // 口令PID
}

// PasswordCreateReq 创建口令组请求
type PasswordCreateReq struct {
	GroupName  string         `json:"groupName" binding:"required" validate:"required"`                   // 口令组名称
	CategoryID int            `json:"categoryId" binding:"required" validate:"required,oneof=55 173 284"` // 口令分类ID
	Passwords  []PasswordInfo `json:"passwords" binding:"required" validate:"required,min=1"`             // 口令列表
}

// ToPasswordEntity 转换为领域实体
func (req PasswordCreateReq) ToPasswordEntity() domain.PasswordEntity {
	passwords := make([]domain.PasswordInfo, len(req.Passwords))
	for i, p := range req.Passwords {
		passwords[i] = domain.PasswordInfo{
			ID:   p.ID,
			Name: p.Name,
			PID:  p.PID,
		}
	}

	return domain.PasswordEntity{
		GroupName:  req.GroupName,
		CategoryID: req.CategoryID,
		Passwords:  passwords,
	}
}

// PasswordListReq 口令组列表请求
type PasswordListReq struct {
	Page       int    `form:"page,default=1" validate:"min=1"`         // 页码
	PageSize   int    `form:"pageSize,default=20" validate:"max=1000"` // 每页数量
	Size       int    `form:"size,default=20" validate:"max=1000"`     // 每页数量
	GroupName  string `form:"groupName"`                               // 口令组名称
	CategoryID int64  `form:"categoryId"`                              // 口令分类ID
}

// ToPasswordQueryParam 转换为查询参数
func (req PasswordListReq) ToPasswordQueryParam() domain.PasswordQueryParam {
	return domain.PasswordQueryParam{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Size:       req.Size,
		GroupName:  req.GroupName,
		CategoryID: req.CategoryID,
	}
}

// PasswordUpdateReq 编辑口令组请求
type PasswordUpdateReq struct {
	GroupName  string         `json:"groupName" binding:"required"`  // 口令组名称
	CategoryID int            `json:"categoryId" binding:"required"` // 口令分类ID
	Passwords  []PasswordInfo `json:"passwords" binding:"required"`  // 口令列表
}

// ToPasswordEntity 转换为领域实体
func (req PasswordUpdateReq) ToPasswordEntity() domain.PasswordEntity {
	passwords := make([]domain.PasswordInfo, len(req.Passwords))
	for i, p := range req.Passwords {
		passwords[i] = domain.PasswordInfo{
			ID:   p.ID,
			Name: p.Name,
			PID:  p.PID,
		}
	}

	return domain.PasswordEntity{
		GroupName:  req.GroupName,
		CategoryID: req.CategoryID,
		Passwords:  passwords,
	}
}

// PasswordOriListReq 原始口令列表请求
type PasswordOriListReq struct {
	Page       int   `form:"page,default=1" validate:"min=1" json:"page"`             // 页码
	PageSize   int   `form:"pageSize,default=20" validate:"max=2000" json:"pageSize"` // 每页数量
	CategoryID []int `form:"categoryId" binding:"required" json:"categoryId"`         // 口令分类ID
}

// ToPasswordOriQueryParam 转换为原始口令查询参数
func (req PasswordOriListReq) ToPasswordOriQueryParam() domain.PasswordOriQueryParam {
	return domain.PasswordOriQueryParam{
		Page:       req.Page,
		PageSize:   req.PageSize,
		CategoryID: req.CategoryID,
	}
}

// PasswordItem 口令项响应
type PasswordItem struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	PID  string `json:"pid"`
}

// PasswordGroupItem 口令组项响应
type PasswordGroupItem struct {
	ID         int            `json:"id"`         // 口令组ID
	GroupName  string         `json:"groupName"`  // 口令组名称
	CategoryID int64          `json:"categoryId"` // 口令分类ID
	CreatedAt  time.Time      `json:"createdAt"`  // 创建时间
	UpdatedAt  time.Time      `json:"updatedAt"`  // 更新时间
	Passwords  []PasswordItem `json:"passwords"`  // 口令列表
}

// PasswordListResp 口令组列表响应
type PasswordListResp struct {
	List  []PasswordGroupItem `json:"list"`  // 口令组列表
	Total int64               `json:"total"` // 总数量
	Page  int                 `json:"page"`  // 页码
}

// FromPasswordResult 从领域结果转换
func FromPasswordResult(result domain.PasswordResult) PasswordListResp {
	items := make([]PasswordGroupItem, len(result.List))
	for i, item := range result.List {
		passwordItems := make([]PasswordItem, len(item.Passwords))
		for j, p := range item.Passwords {
			passwordItems[j] = PasswordItem{
				ID:   p.ID,
				Name: p.Name,
				PID:  p.PID,
			}
		}

		items[i] = PasswordGroupItem{
			ID:         item.ID,
			GroupName:  item.GroupName,
			CategoryID: item.CategoryID,
			CreatedAt:  item.CreatedAt,
			UpdatedAt:  item.UpdatedAt,
			Passwords:  passwordItems,
		}
	}

	return PasswordListResp{
		List:  items,
		Total: result.Total,
		Page:  result.Page,
	}
}

// PasswordOriItem 原始口令项
type PasswordOriItem struct {
	Name string `json:"name"` // 口令名称
	PID  string `json:"pid"`  // 口令PID
}

// PasswordOriListResp 原始口令列表响应
type PasswordOriListResp struct {
	List  []PasswordOriItem `json:"list"`  // 原始口令列表
	Total int               `json:"total"` // 总数量
	Page  int               `json:"page"`  // 页码
}

// FromPasswordOriResult 从领域结果转换
func FromPasswordOriResult(result domain.PasswordOriResult) PasswordOriListResp {
	items := make([]PasswordOriItem, len(result.List))
	for i, item := range result.List {
		items[i] = PasswordOriItem{
			Name: item.Name,
			PID:  item.PID,
		}
	}

	return PasswordOriListResp{
		List:  items,
		Total: result.Total,
		Page:  result.Page,
	}
}

// PasswordCreateResp 创建响应
type PasswordCreateResp struct {
	ID int `json:"id"` // 口令ID
}

// FromPasswordActionResult 从操作结果转换
func FromPasswordActionResult(result domain.PasswordActionResult) PasswordCreateResp {
	return PasswordCreateResp{
		ID: result.ID,
	}
}

// PasswordUpdateResp 更新响应
type PasswordUpdateResp struct {
	ID int `json:"id"` // 口令ID
}

// FromPasswordActionResult 从操作结果转换
func FromPasswordActionResultToUpdateResp(result domain.PasswordActionResult) PasswordUpdateResp {
	return PasswordUpdateResp{
		ID: result.ID,
	}
}

// PasswordDeleteResp 删除响应
type PasswordDeleteResp struct {
	ID int `json:"id"` // 口令ID
}

// FromPasswordActionResult 从操作结果转换
func FromPasswordActionResultToDeleteResp(result domain.PasswordActionResult) PasswordDeleteResp {
	return PasswordDeleteResp{
		ID: result.ID,
	}
}
