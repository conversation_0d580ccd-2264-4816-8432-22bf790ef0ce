package job

import (
	"context"
	"fmt"
	"gin-backend/internal/service"
	"time"

	"go.uber.org/zap"
)

// XHSRealtimeReportSyncJob 小红书实时报表数据同步定时任务
type XHSRealtimeReportSyncJob struct {
	realtimeService *service.XHSRealtimeReportService
}

// NewXHSRealtimeReportSyncJob 创建小红书实时报表数据同步定时任务
func NewXHSRealtimeReportSyncJob() *XHSRealtimeReportSyncJob {
	return &XHSRealtimeReportSyncJob{
		realtimeService: service.NewXHSRealtimeReportService(),
	}
}

// Name 任务名称
func (j *XHSRealtimeReportSyncJob) Name() string {
	return "sync-xhs-realtime-report"
}

// Run 任务执行 - 每小时执行一次
func (j *XHSRealtimeReportSyncJob) Run() error {
	startTime := time.Now()
	zap.L().Info("开始执行小红书实时报表数据同步任务")

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	// 同步当前小时的实时数据
	if err := j.realtimeService.SyncCurrentHourReports(ctx); err != nil {
		zap.L().Error("小红书实时报表数据同步失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("小红书实时报表数据同步任务完成",
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// RunManual 手动执行同步任务（用于测试或手动触发）
func (j *XHSRealtimeReportSyncJob) RunManual() error {
	startTime := time.Now()
	zap.L().Info("手动执行小红书实时报表数据同步任务")

	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancel()

	if err := j.realtimeService.SyncCurrentHourReports(ctx); err != nil {
		zap.L().Error("手动执行小红书实时报表数据同步失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("手动执行小红书实时报表数据同步任务完成",
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// GetJobInfo 获取任务信息
func (j *XHSRealtimeReportSyncJob) GetJobInfo() JobInfo {
	return JobInfo{
		Name:        j.Name(),
		Description: "小红书创意层级实时报表数据同步",
		Schedule:    "每小时执行一次",
		Timeout:     "15分钟",
		Category:    "数据同步",
		DataSource:  "小红书广告API",
		Target:      "xhs_realtime_reports表",
	}
}

// JobInfo 任务信息结构
type JobInfo struct {
	Name        string `json:"name"`        // 任务名称
	Description string `json:"description"` // 任务描述
	Schedule    string `json:"schedule"`    // 执行计划
	Timeout     string `json:"timeout"`     // 超时时间
	Category    string `json:"category"`    // 任务分类
	DataSource  string `json:"data_source"` // 数据源
	Target      string `json:"target"`      // 目标表
}

// ValidateJobConfig 验证任务配置
func (j *XHSRealtimeReportSyncJob) ValidateJobConfig() error {
	// 检查服务是否正常初始化
	if j.realtimeService == nil {
		return fmt.Errorf("实时报表服务未初始化")
	}

	// 可以添加更多配置验证逻辑
	// 例如：检查数据库连接、API配置等

	return nil
}

// GetLastSyncInfo 获取最后同步信息（可用于监控）
func (j *XHSRealtimeReportSyncJob) GetLastSyncInfo(ctx context.Context) (*SyncInfo, error) {
	// 这里可以查询数据库获取最后同步的信息
	// 例如：最后同步时间、同步的数据量等
	
	// 简单实现：返回当前时间作为示例
	return &SyncInfo{
		LastSyncTime: time.Now(),
		Status:       "completed",
		Message:      "最近一次同步成功",
	}, nil
}

// SyncInfo 同步信息结构
type SyncInfo struct {
	LastSyncTime time.Time `json:"last_sync_time"` // 最后同步时间
	Status       string    `json:"status"`         // 同步状态
	Message      string    `json:"message"`        // 同步消息
	RecordCount  int       `json:"record_count"`   // 同步记录数
}

// HealthCheck 健康检查
func (j *XHSRealtimeReportSyncJob) HealthCheck(ctx context.Context) error {
	// 检查服务状态
	if j.realtimeService == nil {
		return fmt.Errorf("实时报表服务未初始化")
	}

	// 可以添加更多健康检查逻辑
	// 例如：检查数据库连接、API可用性等

	return nil
}

// Stop 停止任务（用于优雅关闭）
func (j *XHSRealtimeReportSyncJob) Stop() error {
	zap.L().Info("停止小红书实时报表数据同步任务")
	
	// 这里可以添加清理逻辑
	// 例如：取消正在进行的同步操作、关闭连接等
	
	return nil
}

// GetMetrics 获取任务指标（用于监控）
func (j *XHSRealtimeReportSyncJob) GetMetrics(ctx context.Context) (*JobMetrics, error) {
	// 这里可以返回任务的执行指标
	// 例如：执行次数、成功率、平均执行时间等
	
	return &JobMetrics{
		ExecutionCount:   0,    // 执行次数
		SuccessCount:     0,    // 成功次数
		FailureCount:     0,    // 失败次数
		AvgExecutionTime: 0,    // 平均执行时间（秒）
		LastExecutionTime: time.Time{}, // 最后执行时间
	}, nil
}

// JobMetrics 任务指标结构
type JobMetrics struct {
	ExecutionCount    int       `json:"execution_count"`    // 执行次数
	SuccessCount      int       `json:"success_count"`      // 成功次数
	FailureCount      int       `json:"failure_count"`      // 失败次数
	AvgExecutionTime  float64   `json:"avg_execution_time"` // 平均执行时间（秒）
	LastExecutionTime time.Time `json:"last_execution_time"` // 最后执行时间
}

// SetSchedule 设置调度计划（用于动态调整）
func (j *XHSRealtimeReportSyncJob) SetSchedule(schedule string) error {
	// 这里可以实现动态调整调度计划的逻辑
	zap.L().Info("设置小红书实时报表同步任务调度计划", zap.String("schedule", schedule))
	
	// 验证调度计划格式
	// 例如：验证cron表达式格式
	
	return nil
}

// GetConfig 获取任务配置
func (j *XHSRealtimeReportSyncJob) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"name":         j.Name(),
		"description":  "小红书创意层级实时报表数据同步",
		"schedule":     "0 * * * *", // 每小时执行一次的cron表达式
		"timeout":      "15m",
		"retry_count":  3,
		"retry_delay":  "1m",
		"enabled":      true,
		"data_source":  "xiaohongshu_api",
		"target_table": "xhs_realtime_reports",
	}
}
