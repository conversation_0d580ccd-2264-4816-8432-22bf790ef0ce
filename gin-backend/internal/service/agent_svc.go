package service

import (
	"context"
	"fmt"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// AgentService 代理服务
type AgentService struct {
	db               *gorm.DB
	dataScopeService *DataScopeService
}

// NewAgentService 创建代理服务实例
func NewAgentService() *AgentService {
	return &AgentService{
		db:               global.DB,
		dataScopeService: NewDataScopeService(),
	}
}

// GetList 获取代理列表
func (s *AgentService) GetList(ctx context.Context, userEntity domain.UserEntity, param domain.AgentParam) (domain.AgentListResult, error) {
	// 验证请求参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 || param.PageSize > 100 {
		param.PageSize = 20
	}

	// 验证代理类型
	if param.Type != "" {
		if err := s.validateAgentType(param.Type); err != nil {
			return domain.AgentListResult{}, err
		}
	}

	// 验证审核状态
	if param.AuditStatus != "" {
		if err := s.validateAuditStatus(param.AuditStatus); err != nil {
			return domain.AgentListResult{}, err
		}
	}

	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userEntity, "agent")
	if err != nil {
		return domain.AgentListResult{}, fmt.Errorf("获取数据权限失败: %w", err)
	}

	var agents []model.Agent
	var total int64

	query := s.db.WithContext(ctx).Model(&model.Agent{})

	// 应用搜索条件
	if param.Name != "" {
		query = query.Where("name LIKE ? OR company_name LIKE ?", "%"+param.Name+"%", "%"+param.Name+"%")
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}
	if param.AuditStatus != "" {
		query = query.Where("audit_status = ?", param.AuditStatus)
	}

	// 应用数据权限
	switch dataScope {
	case "all":
		// 不需要额外的条件
	case "dept":
		if len(departmentUserIDs) > 0 {
			query = query.Where("user_id IN ?", departmentUserIDs)
		} else {
			query = query.Where("1 = 0") // 没有权限查看任何数据
		}
	case "self":
		query = query.Where("user_id = ?", userEntity.ID)
	default:
		return domain.AgentListResult{}, fmt.Errorf("未知的数据权限范围: %s", dataScope)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.AgentListResult{}, fmt.Errorf("获取代理总数失败: %w", err)
	}

	// 获取分页数据
	if err := query.Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Order("id DESC").
		Find(&agents).Error; err != nil {
		return domain.AgentListResult{}, fmt.Errorf("获取代理列表失败: %w", err)
	}

	// 转换为响应结构
	list := make([]domain.AgentInfo, len(agents))
	for i, agent := range agents {
		list[i] = s.convertToAgentInfo(agent)
	}

	return domain.AgentListResult{
		List:     list,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

// GetByID 获取代理详情
func (s *AgentService) GetByID(ctx context.Context, userEntity domain.UserEntity, id int64) (domain.AgentInfo, error) {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userEntity, "agent")
	if err != nil {
		return domain.AgentInfo{}, fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 获取代理信息
	var agent model.Agent
	if err := s.db.WithContext(ctx).First(&agent, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.AgentInfo{}, fmt.Errorf("代理不存在")
		}
		return domain.AgentInfo{}, fmt.Errorf("获取代理信息失败: %w", err)
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, id, userEntity, dataScope, departmentUserIDs); err != nil {
		return domain.AgentInfo{}, err
	}

	return s.convertToAgentInfo(agent), nil
}

// Create 创建代理
func (s *AgentService) Create(ctx context.Context, userEntity domain.UserEntity, entity domain.AgentEntity) (int64, error) {
	// 验证代理类型
	if err := s.validateAgentType(entity.Type); err != nil {
		return 0, err
	}

	// 验证平台配置（针对投放类型代理）
	if entity.Type == domain.AgentTypeDelivery {
		if err := s.validatePlatformConfig(entity.PlatformConfig); err != nil {
			return 0, err
		}
	}

	// 构建代理模型
	agent := &model.Agent{
		Name:           entity.Name,
		Type:           entity.Type,
		UserID:         userEntity.ID,
		CompanyName:    entity.CompanyName,
		CompanyAddress: entity.CompanyAddress,
		ContactName:    entity.ContactName,
		ContactPhone:   entity.ContactPhone,
		Remarks:        entity.Remarks,
		CreatedBy:      userEntity.ID,
	}

	// 设置平台配置
	configJSON, err := entity.PlatformConfig.ConvertToJSON()
	if err != nil {
		return 0, fmt.Errorf("序列化平台配置失败: %w", err)
	}
	agent.PlatformConfig = configJSON

	// 创建代理
	if err := s.db.WithContext(ctx).Create(agent).Error; err != nil {
		return 0, fmt.Errorf("创建代理失败: %w", err)
	}

	return agent.ID, nil
}

// Update 更新代理
func (s *AgentService) Update(ctx context.Context, userEntity domain.UserEntity, entity domain.AgentEntity) error {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userEntity, "agent")
	if err != nil {
		return fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, entity.ID, userEntity, dataScope, departmentUserIDs); err != nil {
		return err
	}

	// 验证代理类型
	if err := s.validateAgentType(entity.Type); err != nil {
		return err
	}

	// 验证平台配置（针对投放类型代理）
	if entity.Type == domain.AgentTypeDelivery {
		if err := s.validatePlatformConfig(entity.PlatformConfig); err != nil {
			return err
		}
	}

	// 构建更新数据
	updates := map[string]any{
		"name":            entity.Name,
		"type":            entity.Type,
		"company_name":    entity.CompanyName,
		"company_address": entity.CompanyAddress,
		"contact_name":    entity.ContactName,
		"contact_phone":   entity.ContactPhone,
		"remarks":         entity.Remarks,
		"updated_by":      userEntity.ID,
	}

	// 处理平台配置
	configJSON, err := entity.PlatformConfig.ConvertToJSON()
	if err != nil {
		return fmt.Errorf("序列化平台配置失败: %w", err)
	}
	updates["platform_config"] = configJSON

	// 更新代理
	if err := s.db.WithContext(ctx).Model(&model.Agent{}).Where("id = ?", entity.ID).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新代理失败: %w", err)
	}

	return nil
}

// Delete 删除代理
func (s *AgentService) Delete(ctx context.Context, userEntity domain.UserEntity, id int64) error {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userEntity, "agent")
	if err != nil {
		return fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, id, userEntity, dataScope, departmentUserIDs); err != nil {
		return err
	}

	// 执行软删除
	if err := s.db.WithContext(ctx).Delete(&model.Agent{}, id).Error; err != nil {
		return fmt.Errorf("删除代理失败: %w", err)
	}

	return nil
}

// Audit 审核代理
func (s *AgentService) Audit(ctx context.Context, userEntity domain.UserEntity, entity domain.AgentAuditEntity) error {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userEntity, "agent")
	if err != nil {
		return fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, entity.ID, userEntity, dataScope, departmentUserIDs); err != nil {
		return err
	}

	// 验证审核状态
	if err := s.validateAuditStatus(entity.AuditStatus); err != nil {
		return err
	}

	// 审核逻辑
	updates := map[string]any{
		"audit_status": entity.AuditStatus,
		"updated_by":   userEntity.ID,
	}

	// 如果是拒绝，则需要设置拒绝原因
	if entity.AuditStatus == domain.AuditStatusRejected {
		updates["reject_reason"] = entity.RejectReason
	}

	if err := s.db.WithContext(ctx).Model(&model.Agent{}).Where("id = ?", entity.ID).Updates(updates).Error; err != nil {
		return fmt.Errorf("审核代理失败: %w", err)
	}

	return nil
}

// validatePlatformConfig 验证平台配置
func (s *AgentService) validatePlatformConfig(config domain.PlatformConfig) error {
	// 验证平台类型
	switch config.Platform {
	case "denghuoplus", "xiaohongshu":
		// 合法的平台类型
	default:
		return fmt.Errorf("不支持的平台类型: %s", config.Platform)
	}

	// 针对不同平台的特定验证
	switch config.Platform {
	case "denghuoplus":
		// 验证灯火平台必要的配置项
		accountID, ok := config.Info["account_id"]
		if !ok {
			return fmt.Errorf("缺少灯火平台账号ID")
		}
		if _, ok := accountID.(string); !ok {
			return fmt.Errorf("灯火平台账号ID必须是字符串")
		}

		accessToken, ok := config.Info["access_token"]
		if !ok {
			return fmt.Errorf("缺少灯火平台访问令牌")
		}
		if _, ok := accessToken.(string); !ok {
			return fmt.Errorf("灯火平台访问令牌必须是字符串")
		}

	case "xiaohongshu":
		// 验证小红书平台必要的配置项
		accountID, ok := config.Info["account_id"]
		if !ok {
			return fmt.Errorf("缺少小红书平台账号ID")
		}
		if _, ok := accountID.(string); !ok {
			return fmt.Errorf("小红书平台账号ID必须是字符串")
		}
	}

	return nil
}

// validateAgentType 验证代理类型
func (s *AgentService) validateAgentType(agentType string) error {
	// 验证代理类型
	switch agentType {
	case domain.AgentTypeTraffic, domain.AgentTypeDelivery:
		return nil
	default:
		return fmt.Errorf("不支持的代理类型: %s", agentType)
	}
}

// validateAuditStatus 验证审核状态
func (s *AgentService) validateAuditStatus(status string) error {
	// 验证审核状态
	switch status {
	case domain.AuditStatusPending, domain.AuditStatusApproved, domain.AuditStatusRejected:
		return nil
	default:
		return fmt.Errorf("不支持的审核状态: %s", status)
	}
}

// checkDataPermission 检查数据权限
func (s *AgentService) checkDataPermission(ctx context.Context, id int64, userEntity domain.UserEntity, dataScope string, departmentUserIDs []int64) error {
	var agent model.Agent
	if err := s.db.WithContext(ctx).Select("user_id").First(&agent, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("代理不存在")
		}
		return fmt.Errorf("查询代理所属用户失败: %w", err)
	}

	// 超级管理员角色直接通过权限检查
	if domain.IsAdmin(userEntity.Role) || dataScope == "all" {
		return nil
	}

	// 部门数据权限判断
	if dataScope == "dept" {
		for _, uid := range departmentUserIDs {
			if agent.UserID == uid {
				return nil
			}
		}
	}

	// 个人数据权限判断
	if dataScope == "self" && agent.UserID == userEntity.ID {
		return nil
	}

	return fmt.Errorf("无权限操作该代理")
}

// convertToAgentInfo 将model.Agent转换为domain.AgentInfo
func (s *AgentService) convertToAgentInfo(agent model.Agent) domain.AgentInfo {
	info := domain.AgentInfo{
		ID:                agent.ID,
		Code:              agent.Code,
		Name:              agent.Name,
		Type:              agent.Type,
		UserID:            agent.UserID,
		AuditStatus:       agent.AuditStatus,
		CooperationStatus: agent.CooperationStatus,
		CompanyName:       agent.CompanyName,
		CompanyAddress:    agent.CompanyAddress,
		ContactName:       agent.ContactName,
		ContactPhone:      agent.ContactPhone,
		RejectReason:      agent.RejectReason,
		Remarks:           agent.Remarks,
	}

	// 解析平台配置
	config, _ := domain.GetPlatformConfigFromJSON(agent.PlatformConfig)
	info.PlatformConfig = config
	return info
}
