package xiaohongshu

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// TestNewClient 测试创建客户端
func TestNewClient(t *testing.T) {
	config := &Config{
		AppId:  "test_app_id",
		Secret: "test_secret",
		IsProd: false,
	}

	client := NewClient(config)
	if client == nil {
		t.Fatal("客户端创建失败")
	}

	if client.config.AppId != config.AppId {
		t.<PERSON><PERSON><PERSON>("期望AppId为%s，实际为%s", config.AppId, client.config.AppId)
	}
}

// TestGetAccessToken 测试获取访问令牌
func TestGetAccessToken(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			t.<PERSON><PERSON><PERSON>("期望POST请求，实际为%s", r.Method)
		}

		// 验证Content-Type
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望Content-Type为application/json，实际为%s", r.Header.Get("Content-Type"))
		}

		// 返回模拟响应
		response := TokenResponse{
			Code:    0,
			Success: true,
			Msg:     "成功",
			Data: &TokenData{
				UserId:   "5c8650cb0000000001004367",
				RoleType: 3,
				ApprovalAdvertisers: []Advertiser{
					{
						AdvertiserId:   1234,
						AdvertiserName: "品牌测试账号222",
					},
				},
				RefreshToken:          "5be1789576f45f90ccfbf4ba16ca4a5b",
				AdvertiserId:          1234,
				RefreshTokenExpiresIn: 2591999,
				ApprovalRoleType:      4,
				PlatformType:          1,
				AccessToken:           "0cde2287cd0dedcf472ceb266d0710ba",
				AccessTokenExpiresIn:  86399,
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建测试客户端，直接使用模拟服务器URL
	client := &Client{
		config: &Config{
			AppId:  "3",
			Secret: "1234abc",
			IsProd: false,
		},
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 测试获取访问令牌，直接调用requestToken方法
	request := &AccessTokenRequest{
		AppId:    client.config.AppId,
		Secret:   client.config.Secret,
		AuthCode: "d6a0b18531a2b9599a2c1e2361659c00",
	}

	tokenResp, err := client.requestToken(server.URL, request)
	if err != nil {
		t.Fatalf("获取访问令牌失败: %v", err)
	}

	if !tokenResp.Success {
		t.Errorf("期望成功，实际失败: %s", tokenResp.Msg)
	}

	if tokenResp.Data.AccessToken != "0cde2287cd0dedcf472ceb266d0710ba" {
		t.Errorf("访问令牌不匹配")
	}

	if tokenResp.Data.AdvertiserId != 1234 {
		t.Errorf("期望广告主ID为1234，实际为%d", tokenResp.Data.AdvertiserId)
	}
}

// TestRefreshToken 测试刷新令牌
func TestRefreshToken(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := TokenResponse{
			Code:    0,
			Success: true,
			Msg:     "成功",
			Data: &TokenData{
				UserId:                "5c8650cb0000000001004367",
				AccessToken:           "new_access_token",
				RefreshToken:          "new_refresh_token",
				AccessTokenExpiresIn:  86399,
				RefreshTokenExpiresIn: 2591999,
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建测试客户端
	client := &Client{
		config: &Config{
			AppId:  "1",
			Secret: "xxsf",
			IsProd: false,
		},
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 测试刷新令牌，直接调用requestToken方法
	request := &RefreshTokenRequest{
		AppId:        client.config.AppId,
		Secret:       client.config.Secret,
		RefreshToken: "18419c9324853d3b22a8580eb9b67b86",
	}

	tokenResp, err := client.requestToken(server.URL, request)
	if err != nil {
		t.Fatalf("刷新令牌失败: %v", err)
	}

	if !tokenResp.Success {
		t.Errorf("期望成功，实际失败: %s", tokenResp.Msg)
	}

	if tokenResp.Data.AccessToken != "new_access_token" {
		t.Errorf("新访问令牌不匹配")
	}
}

// TestSetTimeout 测试设置超时时间
func TestSetTimeout(t *testing.T) {
	config := &Config{
		AppId:  "test",
		Secret: "test",
		IsProd: false,
	}
	client := NewClient(config)

	// 设置超时时间
	timeout := 60 * time.Second
	client.SetTimeout(timeout)

	if client.client.Timeout != timeout {
		t.Errorf("期望超时时间为%v，实际为%v", timeout, client.client.Timeout)
	}
}

// TestClone 测试克隆客户端
func TestClone(t *testing.T) {
	config := &Config{
		AppId:  "test",
		Secret: "test",
		IsProd: false,
	}
	client := NewClient(config)
	client.SetTimeout(60 * time.Second)

	// 克隆客户端
	clonedClient := client.Clone()

	if clonedClient.config.AppId != client.config.AppId {
		t.Errorf("克隆的客户端配置不匹配")
	}

	if clonedClient.client.Timeout != client.client.Timeout {
		t.Errorf("克隆的客户端超时时间不匹配")
	}

	// 确保是不同的实例
	if clonedClient == client {
		t.Errorf("克隆的客户端应该是不同的实例")
	}
}
