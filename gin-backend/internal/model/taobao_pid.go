package model

import (
	"time"
)

// TaobaoPid 淘联链接
type TaobaoPid struct {
	ID           int64     `gorm:"primaryKey;autoIncrement"`
	AccountName  string    `gorm:"column:account_name;type:varchar(200)"`    // 联盟账号
	MemberID     int64     `gorm:"column:member_id;index"`                   // 会员ID
	ZoneName     string    `gorm:"column:zone_name;type:varchar(200)"`       // 推广位名称
	PID          string    `gorm:"column:pid;type:varchar(200);uniqueIndex"` // 推广位ID
	IsUsed       int       `gorm:"column:is_used;default:0"`                 // 是否使用: 0-未使用, 1-已使用
	UsedAt       time.Time `gorm:"column:used_at"`                           // 使用时间
	UsedBy       int64     `gorm:"column:used_by"`                           // 使用者ID
	AdSlotPlanID int64     `gorm:"column:ad_slot_plan_id"`                   // 关联计划ID
	AdCreativeID int64     `gorm:"column:ad_creative_id"`                    // 关联创意ID
	ActType      int       `gorm:"column:act_type"`                          // 活动类型: 1-飞猪, 2-福利购
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime"`         // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime"`         // 更新时间
	DeletedAt    time.Time `gorm:"column:deleted_at;index"`                  // 删除时间
}

func (TaobaoPid) TableName() string {
	return "taobao_pids"
}

// 使用状态常量
const (
	PidUnused = 0 // 未使用
	PidUsed   = 1 // 已使用
)

// 活动类型常量
const (
	ActTypeFliggy  = 1 // 飞猪
	ActTypeWelfare = 2 // 福利购
)
