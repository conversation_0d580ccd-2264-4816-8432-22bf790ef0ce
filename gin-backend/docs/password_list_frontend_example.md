# 口令列表前端集成示例

## Vue.js 集成示例

### 1. 口令列表页面组件

```vue
<template>
  <div class="password-list-container">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="账号">
          <el-select v-model="filterForm.account_id" placeholder="全部" clearable>
            <el-option
              v-for="account in accountOptions"
              :key="account.id"
              :label="account.name"
              :value="account.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="口令名称">
          <el-input v-model="filterForm.password_name" placeholder="请输入口令名称"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-statistic title="总口令数" :value="stats.total_passwords" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="总消费" :value="stats.total_consumption" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="总订单数" :value="stats.total_orders" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="总收入" :value="stats.total_income" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="总回收率" :value="stats.total_recovery_rate" suffix="%" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="平均点击率" :value="stats.avg_click_rate" suffix="%" :precision="2" />
        </el-col>
      </el-row>
    </div>

    <!-- 口令列表表格 -->
    <div class="table-section">
      <el-table
        :data="passwordList"
        v-loading="loading"
        stripe
        border>
        <el-table-column prop="password_name" label="口令名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="account_name" label="账号" width="120" />
        <el-table-column prop="consumption" label="消费" width="100" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.consumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="actual_consumption" label="实际消费" width="100" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.actual_consumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="impressions" label="展现量" width="100" align="right" />
        <el-table-column prop="click_count" label="点击量" width="100" align="right" />
        <el-table-column prop="click_rate" label="点击率" width="100" align="right">
          <template #default="scope">
            {{ scope.row.click_rate.toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="search_count" label="搜索人数" width="100" align="right" />
        <el-table-column prop="new_orders_today" label="今日新订单" width="120" align="right" />
        <el-table-column prop="today_order_cost" label="今日订单成本" width="120" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.today_order_cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="cumulative_orders" label="累计订单" width="100" align="right" />
        <el-table-column prop="cumulative_income" label="累计收入" width="120" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.cumulative_income) }}
          </template>
        </el-table-column>
        <el-table-column prop="cumulative_recovery" label="累计回收" width="100" align="right">
          <template #default="scope">
            {{ scope.row.cumulative_recovery.toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="last_update" label="最后更新" width="160" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleViewDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPasswordList, getPasswordStats, exportPasswordList, getAccountList } from '@/api/password'

// 响应式数据
const loading = ref(false)
const passwordList = ref([])
const accountOptions = ref([])
const stats = ref({
  total_passwords: 0,
  total_consumption: 0,
  total_orders: 0,
  total_income: 0,
  total_recovery_rate: 0,
  avg_click_rate: 0
})

// 筛选表单
const filterForm = reactive({
  account_id: '',
  password_name: ''
})

// 日期范围
const dateRange = ref([])

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 计算属性：获取查询参数
const queryParams = computed(() => {
  const params = {
    page: pagination.page,
    page_size: pagination.page_size
  }
  
  if (filterForm.account_id) {
    params.account_id = filterForm.account_id
  }
  
  if (filterForm.password_name) {
    params.password_name = filterForm.password_name
  }
  
  if (dateRange.value && dateRange.value.length === 2) {
    params.start_date = dateRange.value[0]
    params.end_date = dateRange.value[1]
  }
  
  return params
})

// 获取口令列表
const fetchPasswordList = async () => {
  try {
    loading.value = true
    const response = await getPasswordList(queryParams.value)
    passwordList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取口令列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const params = {}
    if (filterForm.account_id) {
      params.account_id = filterForm.account_id
    }
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    const response = await getPasswordStats(params)
    stats.value = response.data
  } catch (error) {
    console.error('获取统计信息失败：', error)
  }
}

// 获取账号列表
const fetchAccountList = async () => {
  try {
    const response = await getAccountList()
    accountOptions.value = response.data
  } catch (error) {
    console.error('获取账号列表失败：', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchPasswordList()
  fetchStats()
}

// 重置
const handleReset = () => {
  filterForm.account_id = ''
  filterForm.password_name = ''
  dateRange.value = []
  pagination.page = 1
  fetchPasswordList()
  fetchStats()
}

// 导出
const handleExport = async () => {
  try {
    const params = {
      format: 'xlsx'
    }
    
    if (filterForm.account_id) {
      params.account_id = filterForm.account_id
    }
    
    if (filterForm.password_name) {
      params.password_name = filterForm.password_name
    }
    
    if (dateRange.value && dateRange.value.length === 2) {
      params.start_date = dateRange.value[0]
      params.end_date = dateRange.value[1]
    }
    
    await exportPasswordList(params)
    ElMessage.success('导出任务已启动，请稍后查看导出结果')
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  }
}

// 查看详情
const handleViewDetail = (row) => {
  // 跳转到详情页面或打开详情弹窗
  console.log('查看详情：', row)
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.page_size = size
  pagination.page = 1
  fetchPasswordList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page
  fetchPasswordList()
}

// 格式化货币
const formatCurrency = (value) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountList()
  fetchPasswordList()
  fetchStats()
})
</script>

<style scoped>
.password-list-container {
  padding: 20px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.table-section {
  background: white;
  border-radius: 4px;
}

.pagination-section {
  padding: 20px;
  text-align: right;
}
</style>
```

### 2. API 服务文件

```javascript
// api/password.js
import request from '@/utils/request'

// 获取口令列表
export function getPasswordList(params) {
  return request({
    url: '/api/v1/passwords',
    method: 'get',
    params
  })
}

// 获取口令统计信息
export function getPasswordStats(params) {
  return request({
    url: '/api/v1/passwords/stats',
    method: 'get',
    params
  })
}

// 导出口令列表
export function exportPasswordList(data) {
  return request({
    url: '/api/v1/passwords/export',
    method: 'post',
    data
  })
}

// 获取口令详情
export function getPasswordDetail(passwordName) {
  return request({
    url: `/api/v1/passwords/${passwordName}`,
    method: 'get'
  })
}

// 获取账号列表
export function getAccountList() {
  return request({
    url: '/api/v1/passwords/accounts',
    method: 'get'
  })
}
```

## React.js 集成示例

### 1. 口令列表组件

```jsx
import React, { useState, useEffect, useCallback } from 'react'
import {
  Table,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Card,
  Statistic,
  Row,
  Col,
  Pagination,
  message
} from 'antd'
import { getPasswordList, getPasswordStats, exportPasswordList, getAccountList } from '../api/password'

const { RangePicker } = DatePicker
const { Option } = Select

const PasswordListPage = () => {
  const [loading, setLoading] = useState(false)
  const [passwordList, setPasswordList] = useState([])
  const [accountOptions, setAccountOptions] = useState([])
  const [stats, setStats] = useState({
    total_passwords: 0,
    total_consumption: 0,
    total_orders: 0,
    total_income: 0,
    total_recovery_rate: 0,
    avg_click_rate: 0
  })
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  
  const [form] = Form.useForm()

  // 表格列定义
  const columns = [
    {
      title: '口令名称',
      dataIndex: 'password_name',
      key: 'password_name',
      ellipsis: true,
      width: 150
    },
    {
      title: '账号',
      dataIndex: 'account_name',
      key: 'account_name',
      width: 120
    },
    {
      title: '消费',
      dataIndex: 'consumption',
      key: 'consumption',
      width: 100,
      align: 'right',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '实际消费',
      dataIndex: 'actual_consumption',
      key: 'actual_consumption',
      width: 100,
      align: 'right',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '展现量',
      dataIndex: 'impressions',
      key: 'impressions',
      width: 100,
      align: 'right'
    },
    {
      title: '点击量',
      dataIndex: 'click_count',
      key: 'click_count',
      width: 100,
      align: 'right'
    },
    {
      title: '点击率',
      dataIndex: 'click_rate',
      key: 'click_rate',
      width: 100,
      align: 'right',
      render: (value) => `${value.toFixed(2)}%`
    },
    {
      title: '搜索人数',
      dataIndex: 'search_count',
      key: 'search_count',
      width: 100,
      align: 'right'
    },
    {
      title: '今日新订单',
      dataIndex: 'new_orders_today',
      key: 'new_orders_today',
      width: 120,
      align: 'right'
    },
    {
      title: '累计订单',
      dataIndex: 'cumulative_orders',
      key: 'cumulative_orders',
      width: 100,
      align: 'right'
    },
    {
      title: '累计收入',
      dataIndex: 'cumulative_income',
      key: 'cumulative_income',
      width: 120,
      align: 'right',
      render: (value) => `¥${value.toFixed(2)}`
    },
    {
      title: '累计回收',
      dataIndex: 'cumulative_recovery',
      key: 'cumulative_recovery',
      width: 100,
      align: 'right',
      render: (value) => `${value.toFixed(2)}%`
    },
    {
      title: '最后更新',
      dataIndex: 'last_update',
      key: 'last_update',
      width: 160
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Button type="link" onClick={() => handleViewDetail(record)}>
          详情
        </Button>
      )
    }
  ]

  // 获取口令列表
  const fetchPasswordList = useCallback(async () => {
    try {
      setLoading(true)
      const values = form.getFieldsValue()
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        ...values
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      const response = await getPasswordList(params)
      setPasswordList(response.data.list)
      setPagination(prev => ({
        ...prev,
        total: response.data.total
      }))
    } catch (error) {
      message.error('获取口令列表失败：' + error.message)
    } finally {
      setLoading(false)
    }
  }, [form, pagination.current, pagination.pageSize])

  // 获取统计信息
  const fetchStats = useCallback(async () => {
    try {
      const values = form.getFieldsValue()
      const params = {}
      
      if (values.account_id) {
        params.account_id = values.account_id
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      const response = await getPasswordStats(params)
      setStats(response.data)
    } catch (error) {
      console.error('获取统计信息失败：', error)
    }
  }, [form])

  // 获取账号列表
  const fetchAccountList = useCallback(async () => {
    try {
      const response = await getAccountList()
      setAccountOptions(response.data)
    } catch (error) {
      console.error('获取账号列表失败：', error)
    }
  }, [])

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPasswordList()
    fetchStats()
  }

  // 重置
  const handleReset = () => {
    form.resetFields()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchPasswordList()
    fetchStats()
  }

  // 导出
  const handleExport = async () => {
    try {
      const values = form.getFieldsValue()
      const params = {
        format: 'xlsx',
        ...values
      }
      
      if (values.dateRange) {
        params.start_date = values.dateRange[0].format('YYYY-MM-DD')
        params.end_date = values.dateRange[1].format('YYYY-MM-DD')
      }
      
      await exportPasswordList(params)
      message.success('导出任务已启动，请稍后查看导出结果')
    } catch (error) {
      message.error('导出失败：' + error.message)
    }
  }

  // 查看详情
  const handleViewDetail = (record) => {
    console.log('查看详情：', record)
  }

  // 分页改变
  const handleTableChange = (page, pageSize) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize
    }))
  }

  useEffect(() => {
    fetchAccountList()
  }, [fetchAccountList])

  useEffect(() => {
    fetchPasswordList()
    fetchStats()
  }, [fetchPasswordList, fetchStats])

  return (
    <div style={{ padding: 24 }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="inline">
          <Form.Item name="account_id" label="账号">
            <Select placeholder="全部" allowClear style={{ width: 120 }}>
              {accountOptions.map(option => (
                <Option key={option.id} value={option.value}>
                  {option.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item name="dateRange" label="时间">
            <RangePicker format="YYYY-MM-DD" />
          </Form.Item>
          
          <Form.Item name="password_name" label="口令名称">
            <Input placeholder="请输入口令名称" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={handleReset}>
              重置
            </Button>
            <Button type="default" style={{ marginLeft: 8 }} onClick={handleExport}>
              导出
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic title="总口令数" value={stats.total_passwords} />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="总消费" value={stats.total_consumption} precision={2} prefix="¥" />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="总订单数" value={stats.total_orders} />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="总收入" value={stats.total_income} precision={2} prefix="¥" />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="总回收率" value={stats.total_recovery_rate} precision={2} suffix="%" />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic title="平均点击率" value={stats.avg_click_rate} precision={2} suffix="%" />
          </Card>
        </Col>
      </Row>

      {/* 口令列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={passwordList}
          loading={loading}
          rowKey="password_name"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange
          }}
          scroll={{ x: 1600 }}
        />
      </Card>
    </div>
  )
}

export default PasswordListPage
```

## 关键实现要点

### 1. 数据查询优化
- 根据时间范围自动选择查询策略（单日/多日）
- 合理设置分页参数，避免一次加载过多数据
- 统计信息与列表数据分离查询，提升用户体验

### 2. 用户体验优化
- 提供实时的统计信息展示
- 支持多维度筛选条件
- 异步导出避免页面阻塞
- 响应式设计适配不同屏幕

### 3. 数据展示优化
- 货币格式化显示
- 百分比格式化显示
- 表格列宽度优化
- 超长文本省略显示

### 4. 性能优化
- 使用 useCallback 避免不必要的重渲染
- 合理的数据缓存策略
- 分页加载减少内存占用
