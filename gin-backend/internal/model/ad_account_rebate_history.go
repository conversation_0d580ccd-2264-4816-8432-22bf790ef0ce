package model

import "time"

// AdAccountRebateHistory 账号返利比例变更记录表
type AdAccountRebateHistory struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`                    // 主键ID
	AccountId     int64     `json:"account_id" gorm:"not null;index"`                     // 账号ID
	OldRate       *float64  `json:"old_rate"`                                             // 原返利比例
	NewRate       float64   `json:"new_rate" gorm:"not null"`                            // 新返利比例
	EffectiveDate time.Time `json:"effective_date" gorm:"not null"`                      // 生效日期
	ChangeType    string    `json:"change_type" gorm:"type:varchar(20);not null"`        // 变更类型：create/update/delete
	ChangeReason  string    `json:"change_reason" gorm:"type:varchar(500)"`              // 变更原因
	OperatorId    *int64    `json:"operator_id" gorm:"index"`                            // 操作人ID
	OperatorName  string    `json:"operator_name" gorm:"type:varchar(100)"`              // 操作人姓名
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`                    // 创建时间
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`                    // 更新时间
}

// TableName 表名
func (m *AdAccountRebateHistory) TableName() string {
	return "ad_account_rebate_history"
}

// ChangeTypeCreate 创建返利比例
const ChangeTypeCreate = "create"

// ChangeTypeUpdate 更新返利比例
const ChangeTypeUpdate = "update"

// ChangeTypeDelete 删除返利比例
const ChangeTypeDelete = "delete"

// GetChangeTypeDescription 获取变更类型描述
func GetChangeTypeDescription(changeType string) string {
	switch changeType {
	case ChangeTypeCreate:
		return "新增返利比例"
	case ChangeTypeUpdate:
		return "修改返利比例"
	case ChangeTypeDelete:
		return "删除返利比例"
	default:
		return "未知操作"
	}
}

// IsValidChangeType 验证变更类型是否有效
func IsValidChangeType(changeType string) bool {
	return changeType == ChangeTypeCreate || 
		   changeType == ChangeTypeUpdate || 
		   changeType == ChangeTypeDelete
}
