package vo

import (
	"mime/multipart"

	"gin-backend/internal/service/domain"
)

// ModelCreateReq 创建模型请求
type ModelCreateReq struct {
	ModelName string `json:"modelName" binding:"required"` // 模型名称
}

// ModelCreateResp 创建模型响应
type ModelCreateResp struct {
	ID uint64 `json:"id"` // 模型ID
}

// ModelUpdateReq 更新模型请求
type ModelUpdateReq struct {
	ID        uint64 `json:"id" binding:"required"`        // 模型ID
	ModelName string `json:"modelName" binding:"required"` // 模型名称
}

// ModelDeleteReq 删除模型请求
type ModelDeleteReq struct {
	ID uint64 `json:"id" binding:"required"` // 模型ID
}

// ModelListReq 模型列表请求
type ModelListReq struct {
	Page      int    `form:"page"`      // 页码
	PageSize  int    `form:"pageSize"`  // 每页数量
	ModelName string `form:"modelName"` // 模型名称
}

// ModelListResp 模型列表响应
type ModelListResp struct {
	Total int64           `json:"total"` // 总数量
	List  []ModelListItem `json:"list"`  // 模型列表
}

// ModelListItem 模型列表项
type ModelListItem struct {
	ID        uint64 `json:"id"`        // 模型ID
	ModelName string `json:"modelName"` // 模型名称
	Creator   string `json:"creator"`   // 创建者
	CreatedAt string `json:"createdAt"` // 创建时间
	UpdatedAt string `json:"updatedAt"` // 更新时间
}

// ModelImportReq 导入模型请求
type ModelImportReq struct {
	ModelName string `form:"modelName" binding:"required"` // 模型名称
}

// ModelDetailResp 模型详情响应
type ModelDetailResp struct {
	ID        uint64            `json:"id"`             // 模型ID
	ModelName string            `json:"modelName"`      // 模型名称
	Creator   string            `json:"creator"`        // 创建者
	CreatedAt string            `json:"createdAt"`      // 创建时间
	UpdatedAt string            `json:"updatedAt"`      // 更新时间
	Data      []ModelDetailData `json:"data,omitempty"` // 模型数据
}

// ModelDetailData 模型详情数据
type ModelDetailData struct {
	Day   int     `json:"day"`   // 日期
	Value float64 `json:"value"` // 值
}

// ToModelParam 将模型列表请求转换为领域参数
func (req ModelListReq) ToModelParam() domain.ModelParam {
	return domain.ModelParam{
		Page:      req.Page,
		PageSize:  req.PageSize,
		ModelName: req.ModelName,
	}
}

// FromModelItem 将领域模型项转换为VO模型列表项
func FromModelItem(item domain.ModelItem) ModelListItem {
	return ModelListItem{
		ID:        item.ID,
		ModelName: item.ModelName,
		Creator:   item.Creator,
		CreatedAt: item.CreatedAt,
		UpdatedAt: item.UpdatedAt,
	}
}

// FromModelListResult 将领域模型列表结果转换为VO响应
func FromModelListResult(result domain.ModelListResult) ModelListResp {
	resp := ModelListResp{
		Total: result.Total,
		List:  make([]ModelListItem, 0, len(result.Items)),
	}

	for _, item := range result.Items {
		resp.List = append(resp.List, FromModelItem(item))
	}

	return resp
}

// FromModelDetailResult 将领域模型详情结果转换为VO响应
func FromModelDetailResult(result domain.ModelDetailResult) ModelDetailResp {
	resp := ModelDetailResp{
		ID:        result.ID,
		ModelName: result.ModelName,
		Creator:   result.Creator,
		CreatedAt: result.CreatedAt,
		UpdatedAt: result.UpdatedAt,
		Data:      make([]ModelDetailData, 0, len(result.Data)),
	}

	for _, item := range result.Data {
		resp.Data = append(resp.Data, ModelDetailData{
			Day:   item.Day,
			Value: item.Value,
		})
	}

	return resp
}

// ToModelImportParam 将导入模型请求转换为领域参数
func (req ModelImportReq) ToModelImportParam(fileHeader *multipart.FileHeader, creatorID uint64) domain.ModelImportParam {
	return domain.ModelImportParam{
		ModelName: req.ModelName,
		File:      fileHeader,
		CreatorID: creatorID,
	}
}
