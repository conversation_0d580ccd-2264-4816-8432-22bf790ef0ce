package service

import (
	"context"
	"gin-backend/internal/service/domain"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestGetAdAccountsHierarchy 测试获取广告账号层级结构
func TestGetAdAccountsHierarchy(t *testing.T) {
	// 这是一个示例测试，实际使用时需要配置测试数据库
	t.Skip("需要配置测试数据库")

	service := NewAdAccountService()
	ctx := context.Background()

	// 测试参数
	param := domain.GetAdAccountsParam{
		Page:     1,
		PageSize: 10,
	}

	// 调用方法
	result, err := service.GetAdAccountsHierarchy(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.GreaterOrEqual(t, result.Total, int64(0))
	assert.Equal(t, param.Page, result.Page)
	assert.Equal(t, param.PageSize, result.Size)

	// 验证层级结构
	for _, hierarchy := range result.List {
		// 验证主账号
		assert.Equal(t, domain.AccountTypeMaster, hierarchy.AccountType)
		assert.NotEmpty(t, hierarchy.AccountName)

		// 验证子账号
		for _, subAccount := range hierarchy.SubAccounts {
			assert.Equal(t, domain.AccountTypeSub, subAccount.AccountType)
			assert.Equal(t, hierarchy.ID, subAccount.ParentId)
			assert.Equal(t, hierarchy.AccountName, subAccount.ParentAccountName)
		}
	}
}

// TestGetAdAccountsHierarchyWithFilters 测试带筛选条件的层级结构查询
func TestGetAdAccountsHierarchyWithFilters(t *testing.T) {
	t.Skip("需要配置测试数据库")

	service := NewAdAccountService()
	ctx := context.Background()

	// 测试带筛选条件的参数
	param := domain.GetAdAccountsParam{
		Page:        1,
		PageSize:    10,
		AccountName: "测试",
		Platform:    domain.PlatformXiaohongshu,
		UsageStatus: domain.UsageStatusEnabled,
	}

	// 调用方法
	result, err := service.GetAdAccountsHierarchy(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证筛选条件生效
	for _, hierarchy := range result.List {
		// 主账号应该满足筛选条件
		assert.Equal(t, domain.PlatformXiaohongshu, hierarchy.Platform)
		assert.Equal(t, domain.UsageStatusEnabled, hierarchy.UsageStatus)

		// 子账号也应该满足筛选条件
		for _, subAccount := range hierarchy.SubAccounts {
			assert.Equal(t, domain.PlatformXiaohongshu, subAccount.Platform)
			assert.Equal(t, domain.UsageStatusEnabled, subAccount.UsageStatus)
		}
	}
}

// TestGetAdAccountsHierarchyPagination 测试分页功能
func TestGetAdAccountsHierarchyPagination(t *testing.T) {
	t.Skip("需要配置测试数据库")

	service := NewAdAccountService()
	ctx := context.Background()

	// 测试第一页
	param1 := domain.GetAdAccountsParam{
		Page:     1,
		PageSize: 2,
	}

	result1, err := service.GetAdAccountsHierarchy(ctx, param1)
	assert.NoError(t, err)
	assert.LessOrEqual(t, len(result1.List), 2)

	// 测试第二页
	param2 := domain.GetAdAccountsParam{
		Page:     2,
		PageSize: 2,
	}

	result2, err := service.GetAdAccountsHierarchy(ctx, param2)
	assert.NoError(t, err)

	// 验证分页结果不重复
	if len(result1.List) > 0 && len(result2.List) > 0 {
		assert.NotEqual(t, result1.List[0].ID, result2.List[0].ID)
	}
}

// BenchmarkGetAdAccountsHierarchy 性能测试
func BenchmarkGetAdAccountsHierarchy(b *testing.B) {
	b.Skip("需要配置测试数据库")

	service := NewAdAccountService()
	ctx := context.Background()

	param := domain.GetAdAccountsParam{
		Page:     1,
		PageSize: 20,
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.GetAdAccountsHierarchy(ctx, param)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// MockAdAccountHierarchyData 模拟层级结构数据
func MockAdAccountHierarchyData() domain.GetAdAccountsHierarchyResult {
	now := time.Now()

	// 模拟主账号
	masterAccount := domain.AdAccountEntity{
		ID:                      1,
		AccountType:             domain.AccountTypeMaster,
		AccountTypeName:         "主账号",
		ParentId:                0,
		Platform:                domain.PlatformXiaohongshu,
		PlatformName:            "小红书",
		AccountName:             "营销树长大",
		PlatformAccountId:       "***********",
		AuthorizationStatus:     domain.AuthStatusAuthorized,
		AuthorizationStatusName: "已授权",
		Token:                   "access_token_xxx",
		TokenExpireTime:         now.Add(24 * time.Hour),
		UsageStatus:             domain.UsageStatusEnabled,
		UsageStatusName:         "启用",
		AccountBalance:          10000.00,
		Owner:                   "张三",
		LastSync:                now,
		CreatedAt:               now.Add(-30 * 24 * time.Hour),
		UpdatedAt:               now,
	}

	// 模拟子账号
	subAccounts := []domain.AdAccountEntity{
		{
			ID:                      2,
			AccountType:             domain.AccountTypeSub,
			AccountTypeName:         "子账号",
			ParentId:                1,
			ParentAccountName:       "营销树长大",
			Platform:                domain.PlatformXiaohongshu,
			PlatformName:            "小红书",
			AccountName:             "营销树长大-子账号1",
			PlatformAccountId:       "***********",
			AuthorizationStatus:     domain.AuthStatusInherited,
			AuthorizationStatusName: "继承授权",
			Token:                   "",
			TokenExpireTime:         time.Time{},
			UsageStatus:             domain.UsageStatusEnabled,
			UsageStatusName:         "启用",
			AccountBalance:          5000.00,
			Owner:                   "李四",
			LastSync:                now,
			CreatedAt:               now.Add(-15 * 24 * time.Hour),
			UpdatedAt:               now,
		},
		{
			ID:                      3,
			AccountType:             domain.AccountTypeSub,
			AccountTypeName:         "子账号",
			ParentId:                1,
			ParentAccountName:       "营销树长大",
			Platform:                domain.PlatformXiaohongshu,
			PlatformName:            "小红书",
			AccountName:             "营销树长大-子账号2",
			PlatformAccountId:       "***********",
			AuthorizationStatus:     domain.AuthStatusInherited,
			AuthorizationStatusName: "继承授权",
			Token:                   "",
			TokenExpireTime:         time.Time{},
			UsageStatus:             domain.UsageStatusEnabled,
			UsageStatusName:         "启用",
			AccountBalance:          3000.00,
			Owner:                   "王五",
			LastSync:                now,
			CreatedAt:               now.Add(-10 * 24 * time.Hour),
			UpdatedAt:               now,
		},
	}

	// 构建层级结构
	hierarchy := domain.AdAccountHierarchyEntity{
		AdAccountEntity: masterAccount,
		SubAccounts:     subAccounts,
	}

	return domain.GetAdAccountsHierarchyResult{
		List:  []domain.AdAccountHierarchyEntity{hierarchy},
		Total: 1,
		Page:  1,
		Size:  20,
	}
}

// TestMockDataStructure 测试模拟数据结构
func TestMockDataStructure(t *testing.T) {
	mockData := MockAdAccountHierarchyData()

	// 验证基本结构
	assert.Equal(t, int64(1), mockData.Total)
	assert.Equal(t, 1, mockData.Page)
	assert.Equal(t, 20, mockData.Size)
	assert.Len(t, mockData.List, 1)

	// 验证层级结构
	hierarchy := mockData.List[0]
	assert.Equal(t, domain.AccountTypeMaster, hierarchy.AccountType)
	assert.Equal(t, "营销树长大", hierarchy.AccountName)
	assert.Len(t, hierarchy.SubAccounts, 2)

	// 验证子账号
	for i, subAccount := range hierarchy.SubAccounts {
		assert.Equal(t, domain.AccountTypeSub, subAccount.AccountType)
		assert.Equal(t, hierarchy.ID, subAccount.ParentId)
		assert.Equal(t, hierarchy.AccountName, subAccount.ParentAccountName)
		assert.Contains(t, subAccount.AccountName, "子账号")
		
		if i == 0 {
			assert.Equal(t, "营销树长大-子账号1", subAccount.AccountName)
			assert.Equal(t, "李四", subAccount.Owner)
		} else {
			assert.Equal(t, "营销树长大-子账号2", subAccount.AccountName)
			assert.Equal(t, "王五", subAccount.Owner)
		}
	}
}
