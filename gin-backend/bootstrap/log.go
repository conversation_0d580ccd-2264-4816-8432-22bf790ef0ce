package bootstrap

import (
	"strings"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func getZapLoggerAndLevel() (*zap.Logger, string) {
	type Cfg struct {
		Level      string `mapstructure:"level"`
		Filename   string `mapstructure:"filename"`
		MaxSize    int    `mapstructure:"max_size"`
		MaxBackups int    `mapstructure:"max_backups"`
		MaxAge     int    `mapstructure:"max_age"`
	}
	var c Cfg
	err := viper.UnmarshalKey("log", &c)
	if err != nil {
		panic(err)
	}

	lumberjackLogger := &lumberjack.Logger{
		Filename:   c.Filename,
		MaxSize:    c.MaxSize,
		MaxBackups: c.MaxBackups,
		MaxAge:     c.Max<PERSON>ge,
		Compress:   true,
	}

	level := zapcore.DebugLevel
	switch strings.ToLower(c.Level) {
	case "error":
		level = zapcore.ErrorLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	}

	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig()),
		zapcore.AddSync(lumberjackLogger),
		level,
	)

	l := zap.New(core, zap.AddCaller())

	return l, c.Level
}

func RegisterLogger() {
	zapLogger, _ := getZapLoggerAndLevel()
	// 为了方便 ginx.wrapper.go 中日志的打印，不然从参数中传递 logger.Logger 太麻烦
	zap.ReplaceGlobals(zapLogger)
}
