package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AdPlanListService 计划列表服务
type AdPlanListService struct {
	db *gorm.DB
}

// NewAdPlanListService 创建计划列表服务
func NewAdPlanListService() *AdPlanListService {
	return &AdPlanListService{
		db: global.DB,
	}
}

// GetPlanList 获取计划列表
func (s *AdPlanListService) GetPlanList(ctx context.Context, param domain.AdPlanListParam) (domain.AdPlanListResult, error) {
	// 参数验证
	if err := domain.ValidateAdPlanListParam(param); err != nil {
		return domain.AdPlanListResult{}, err
	}

	// 判断是否为单日查询
	isSingleDay := s.isSingleDayQuery(param.StartDate, param.EndDate)

	var plans []domain.AdPlanListItem
	var total int64
	var err error

	if isSingleDay {
		// 单日查询：直接从ad_plan_stats表查询
		plans, total, err = s.getPlanListFromStats(ctx, param)
	} else {
		// 多日查询：从创意报表聚合数据
		plans, total, err = s.getPlanListFromReports(ctx, param)
	}

	if err != nil {
		return domain.AdPlanListResult{}, err
	}

	return domain.AdPlanListResult{
		List:  plans,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// isSingleDayQuery 判断是否为单日查询
func (s *AdPlanListService) isSingleDayQuery(startDate, endDate *time.Time) bool {
	if startDate == nil || endDate == nil {
		return false
	}
	return startDate.Format("2006-01-02") == endDate.Format("2006-01-02")
}

// getPlanListFromStats 从统计表获取计划列表（单日查询）
func (s *AdPlanListService) getPlanListFromStats(ctx context.Context, param domain.AdPlanListParam) ([]domain.AdPlanListItem, int64, error) {
	query := s.db.WithContext(ctx).Model(&model.AdPlanStats{})

	// 添加筛选条件
	s.addPlanFilterConditions(query, param)

	// 如果有日期条件，添加日期筛选
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计计划总数失败: %w", err)
	}

	// 分页查询
	var stats []model.AdPlanStats
	offset := (param.Page - 1) * param.PageSize
	err := query.Order("cost DESC, plan_id ASC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&stats).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询计划统计数据失败: %w", err)
	}

	// 转换为列表项
	plans := make([]domain.AdPlanListItem, 0, len(stats))
	for _, stat := range stats {
		plans = append(plans, domain.AdPlanListItem{
			PlanID:      stat.PlanId,
			PlanName:    stat.PlanName,
			AccountName: "",
			Consumption: stat.Cost,
			ActualCost:  stat.ActualCost,
			Impressions: stat.Impressions,
			Clicks:      stat.Clicks,
			ClickRate:   stat.ClickThroughRate,
			LastUpdate:  stat.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return plans, total, nil
}

// getPlanListFromReports 从创意报表聚合计划列表（多日查询）
func (s *AdPlanListService) getPlanListFromReports(ctx context.Context, param domain.AdPlanListParam) ([]domain.AdPlanListItem, int64, error) {
	// 构建聚合查询
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addReportFilterConditions(query, param)

	// 构建聚合查询SQL
	selectFields := `
		campaign_id as plan_id,
		MAX(campaign_name) as plan_name,
		MAX(account_name) as account_name,
		SUM(fee) as consumption,
		SUM(fee) as actual_cost,
		SUM(impression) as impressions,
		SUM(click) as clicks,
		CASE 
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0 
		END as click_rate,
		MAX(updated_at) as last_update
	`

	// 先统计总数（按计划ID分组后的数量）
	var totalCount int64
	countQuery := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})
	s.addReportFilterConditions(countQuery, param)

	err := countQuery.Select("COUNT(DISTINCT campaign_id)").Row().Scan(&totalCount)
	if err != nil {
		return nil, 0, fmt.Errorf("统计计划总数失败: %w", err)
	}

	// 分页查询聚合数据
	offset := (param.Page - 1) * param.PageSize

	type PlanAggregateResult struct {
		PlanID      string    `gorm:"column:plan_id"`
		PlanName    string    `gorm:"column:plan_name"`
		AccountName string    `gorm:"column:account_name"`
		Consumption float64   `gorm:"column:consumption"`
		ActualCost  float64   `gorm:"column:actual_cost"`
		Impressions int64     `gorm:"column:impressions"`
		Clicks      int64     `gorm:"column:clicks"`
		ClickRate   float64   `gorm:"column:click_rate"`
		LastUpdate  time.Time `gorm:"column:last_update"`
	}

	var results []PlanAggregateResult
	err = query.Select(selectFields).
		Group("campaign_id").
		Order("consumption DESC, plan_id ASC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&results).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询计划聚合数据失败: %w", err)
	}

	// 转换为列表项
	plans := make([]domain.AdPlanListItem, 0, len(results))
	for _, result := range results {
		plans = append(plans, domain.AdPlanListItem{
			PlanID:      result.PlanID,
			PlanName:    result.PlanName,
			AccountName: result.AccountName,
			Consumption: result.Consumption,
			ActualCost:  result.ActualCost,
			Impressions: result.Impressions,
			Clicks:      result.Clicks,
			ClickRate:   result.ClickRate,
			LastUpdate:  result.LastUpdate.Format("2006-01-02 15:04:05"),
		})
	}

	return plans, totalCount, nil
}

// addPlanFilterConditions 添加计划筛选条件（用于统计表查询）
func (s *AdPlanListService) addPlanFilterConditions(query *gorm.DB, param domain.AdPlanListParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query.Where("account LIKE ?", "%"+param.AccountName+"%")
	}
	if param.PlanName != "" {
		query.Where("plan_name LIKE ?", "%"+param.PlanName+"%")
	}
}

// addReportFilterConditions 添加报表筛选条件（用于创意报表查询）
func (s *AdPlanListService) addReportFilterConditions(query *gorm.DB, param domain.AdPlanListParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.PlanName != "" {
		query.Where("campaign_name LIKE ?", "%"+param.PlanName+"%")
	}
	if param.StartDate != nil {
		query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}
}

// GetPlanStats 获取计划统计信息
func (s *AdPlanListService) GetPlanStats(ctx context.Context, param domain.AdPlanStatsParam) (domain.AdPlanStatsResult, error) {
	// 参数验证
	if err := domain.ValidateAdPlanStatsParam(param); err != nil {
		return domain.AdPlanStatsResult{}, err
	}

	// 判断查询来源
	isSingleDay := s.isSingleDayQuery(param.StartDate, param.EndDate)

	if isSingleDay {
		return s.getPlanStatsFromStats(ctx, param)
	} else {
		return s.getPlanStatsFromReports(ctx, param)
	}
}

// getPlanStatsFromStats 从统计表获取计划统计
func (s *AdPlanListService) getPlanStatsFromStats(ctx context.Context, param domain.AdPlanStatsParam) (domain.AdPlanStatsResult, error) {
	query := s.db.WithContext(ctx).Model(&model.AdPlanStats{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account LIKE ?", "%"+param.AccountName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	type StatsResult struct {
		TotalPlans       int64     `gorm:"column:total_plans"`
		TotalCost        float64   `gorm:"column:total_cost"`
		TotalClicks      int64     `gorm:"column:total_clicks"`
		TotalImpressions int64     `gorm:"column:total_impressions"`
		AvgClickRate     float64   `gorm:"column:avg_click_rate"`
		LastUpdateTime   time.Time `gorm:"column:last_update_time"`
	}

	var result StatsResult
	err := query.Select(`
		COUNT(*) as total_plans,
		SUM(cost) as total_cost,
		SUM(clicks) as total_clicks,
		SUM(impressions) as total_impressions,
		CASE 
			WHEN SUM(impressions) > 0 THEN (SUM(clicks) * 100.0 / SUM(impressions))
			ELSE 0 
		END as avg_click_rate,
		MAX(updated_at) as last_update_time
	`).Row().Scan(
		&result.TotalPlans,
		&result.TotalCost,
		&result.TotalClicks,
		&result.TotalImpressions,
		&result.AvgClickRate,
		&result.LastUpdateTime,
	)

	if err != nil {
		return domain.AdPlanStatsResult{}, fmt.Errorf("查询计划统计失败: %w", err)
	}

	return domain.AdPlanStatsResult{
		TotalPlans:       result.TotalPlans,
		TotalCost:        result.TotalCost,
		TotalClicks:      result.TotalClicks,
		TotalImpressions: result.TotalImpressions,
		AvgClickRate:     result.AvgClickRate,
		LastUpdateTime:   result.LastUpdateTime,
	}, nil
}

// ExportPlanList 导出计划列表
func (s *AdPlanListService) ExportPlanList(ctx context.Context, param domain.AdPlanExportParam) (domain.AdPlanExportResult, error) {
	// 参数验证
	if err := domain.ValidateAdPlanExportParam(param); err != nil {
		return domain.AdPlanExportResult{}, err
	}

	// 判断是否为单日查询
	isSingleDay := s.isSingleDayQuery(param.StartDate, param.EndDate)

	var plans []domain.AdPlanListItem
	var err error

	if isSingleDay {
		// 单日查询：从统计表获取数据
		plans, err = s.getExportDataFromStats(ctx, param)
	} else {
		// 多日查询：从创意报表聚合数据
		plans, err = s.getExportDataFromReports(ctx, param)
	}

	if err != nil {
		return domain.AdPlanExportResult{}, fmt.Errorf("查询导出数据失败: %w", err)
	}

	if len(plans) == 0 {
		return domain.AdPlanExportResult{}, fmt.Errorf("没有找到符合条件的数据")
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	timeRange := domain.GetTimeRangeDescription(param.StartDate, param.EndDate)
	fileName := fmt.Sprintf("ad_plan_list_%s_%s.%s", timeRange, timestamp, param.Format)

	// 根据格式导出文件
	var fileURL string
	switch param.Format {
	case "xlsx":
		fileURL, err = s.exportPlanListToExcel(ctx, plans, fileName, timeRange)
	case "csv":
		fileURL, err = s.exportPlanListToCSV(ctx, plans, fileName, timeRange)
	default:
		return domain.AdPlanExportResult{}, fmt.Errorf("不支持的导出格式: %s", param.Format)
	}

	if err != nil {
		return domain.AdPlanExportResult{}, fmt.Errorf("导出文件失败: %w", err)
	}

	return domain.AdPlanExportResult{
		FileName: fileName,
		FileURL:  fileURL,
	}, nil
}

// getExportDataFromStats 从统计表获取导出数据
func (s *AdPlanListService) getExportDataFromStats(ctx context.Context, param domain.AdPlanExportParam) ([]domain.AdPlanListItem, error) {
	query := s.db.WithContext(ctx).Model(&model.AdPlanStats{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.PlanName != "" {
		query = query.Where("plan_name LIKE ?", "%"+param.PlanName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	// 查询数据（限制最大导出数量）
	var stats []model.AdPlanStats
	err := query.Order("cost DESC, plan_id ASC").
		Limit(10000). // 限制最大导出1万条
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询计划统计数据失败: %w", err)
	}

	// 转换为列表项
	plans := make([]domain.AdPlanListItem, 0, len(stats))
	for _, stat := range stats {
		plans = append(plans, domain.AdPlanListItem{
			PlanID:      stat.PlanId,
			PlanName:    stat.PlanName,
			AccountName: "",
			Consumption: stat.Cost,
			ActualCost:  stat.ActualCost,
			Impressions: stat.Impressions,
			Clicks:      stat.Clicks,
			ClickRate:   stat.ClickThroughRate,
			LastUpdate:  stat.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return plans, nil
}

// getExportDataFromReports 从创意报表聚合导出数据
func (s *AdPlanListService) getExportDataFromReports(ctx context.Context, param domain.AdPlanExportParam) ([]domain.AdPlanListItem, error) {
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.PlanName != "" {
		query = query.Where("campaign_name LIKE ?", "%"+param.PlanName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query = query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}

	// 构建聚合查询SQL
	selectFields := `
		campaign_id as plan_id,
		MAX(campaign_name) as plan_name,
		MAX(account_name) as account_name,
		SUM(fee) as consumption,
		SUM(fee) as actual_cost,
		SUM(impression) as impressions,
		SUM(click) as clicks,
		CASE
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0
		END as click_rate,
		MAX(updated_at) as last_update
	`

	type PlanAggregateResult struct {
		PlanID      string    `gorm:"column:plan_id"`
		PlanName    string    `gorm:"column:plan_name"`
		AccountName string    `gorm:"column:account_name"`
		Consumption float64   `gorm:"column:consumption"`
		ActualCost  float64   `gorm:"column:actual_cost"`
		Impressions int64     `gorm:"column:impressions"`
		Clicks      int64     `gorm:"column:clicks"`
		ClickRate   float64   `gorm:"column:click_rate"`
		LastUpdate  time.Time `gorm:"column:last_update"`
	}

	var results []PlanAggregateResult
	err := query.Select(selectFields).
		Group("campaign_id").
		Order("consumption DESC, plan_id ASC").
		Limit(10000). // 限制最大导出1万条
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("查询计划聚合数据失败: %w", err)
	}

	// 转换为列表项
	plans := make([]domain.AdPlanListItem, 0, len(results))
	for _, result := range results {
		plans = append(plans, domain.AdPlanListItem{
			PlanID:      result.PlanID,
			PlanName:    result.PlanName,
			AccountName: result.AccountName,
			Consumption: result.Consumption,
			ActualCost:  result.ActualCost,
			Impressions: result.Impressions,
			Clicks:      result.Clicks,
			ClickRate:   result.ClickRate,
			LastUpdate:  result.LastUpdate.Format("2006-01-02 15:04:05"),
		})
	}

	return plans, nil
}

// exportPlanListToExcel 导出计划列表到Excel
func (s *AdPlanListService) exportPlanListToExcel(ctx context.Context, plans []domain.AdPlanListItem, fileName, timeRange string) (string, error) {
	// 这里可以实现Excel导出逻辑
	// 暂时返回一个模拟的文件URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	zap.L().Info("导出计划列表到Excel",
		zap.String("file_name", fileName),
		zap.String("time_range", timeRange),
		zap.Int("count", len(plans)))

	// TODO: 实现实际的Excel导出逻辑
	// 可以使用 github.com/xuri/excelize/v2 库

	return fileURL, nil
}

// exportPlanListToCSV 导出计划列表到CSV
func (s *AdPlanListService) exportPlanListToCSV(ctx context.Context, plans []domain.AdPlanListItem, fileName, timeRange string) (string, error) {
	// 这里可以实现CSV导出逻辑
	// 暂时返回一个模拟的文件URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	zap.L().Info("导出计划列表到CSV",
		zap.String("file_name", fileName),
		zap.String("time_range", timeRange),
		zap.Int("count", len(plans)))

	// TODO: 实现实际的CSV导出逻辑
	// 可以使用标准库的 encoding/csv

	return fileURL, nil
}

// getPlanStatsFromReports 从创意报表获取计划统计
func (s *AdPlanListService) getPlanStatsFromReports(ctx context.Context, param domain.AdPlanStatsParam) (domain.AdPlanStatsResult, error) {
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query = query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}

	type StatsResult struct {
		TotalPlans       int64     `gorm:"column:total_plans"`
		TotalCost        float64   `gorm:"column:total_cost"`
		TotalClicks      int64     `gorm:"column:total_clicks"`
		TotalImpressions int64     `gorm:"column:total_impressions"`
		AvgClickRate     float64   `gorm:"column:avg_click_rate"`
		LastUpdateTime   time.Time `gorm:"column:last_update_time"`
	}

	var result StatsResult
	err := query.Select(`
		COUNT(DISTINCT campaign_id) as total_plans,
		SUM(fee) as total_cost,
		SUM(click) as total_clicks,
		SUM(impression) as total_impressions,
		CASE 
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0 
		END as avg_click_rate,
		MAX(updated_at) as last_update_time
	`).Row().Scan(
		&result.TotalPlans,
		&result.TotalCost,
		&result.TotalClicks,
		&result.TotalImpressions,
		&result.AvgClickRate,
		&result.LastUpdateTime,
	)

	if err != nil {
		return domain.AdPlanStatsResult{}, fmt.Errorf("查询计划统计失败: %w", err)
	}

	return domain.AdPlanStatsResult{
		TotalPlans:       result.TotalPlans,
		TotalCost:        result.TotalCost,
		TotalClicks:      result.TotalClicks,
		TotalImpressions: result.TotalImpressions,
		AvgClickRate:     result.AvgClickRate,
		LastUpdateTime:   result.LastUpdateTime,
	}, nil
}
