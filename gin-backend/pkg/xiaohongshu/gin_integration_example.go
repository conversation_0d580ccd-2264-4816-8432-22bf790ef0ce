package xiaohongshu

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// XHSService 小红书服务
type XHSService struct {
	client *Client
}

// NewXHSService 创建小红书服务
func NewXHSService(appId, secret string, isProd bool) *XHSService {
	config := &Config{
		AppId:  appId,
		Secret: secret,
		IsProd: isProd,
	}

	return &XHSService{
		client: NewClient(config),
	}
}

// GetAccessTokenRequest 获取访问令牌请求参数
type GetAccessTokenRequest struct {
	AuthCode string `json:"auth_code" binding:"required"`
}

// RefreshTokenRequest 刷新令牌请求参数
type RefreshTokenRequestBody struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// ReportRequestBody 报表请求参数
type ReportRequestBody struct {
	AccessToken  string   `json:"access_token" binding:"required"`  // 访问令牌
	AdvertiserID int64    `json:"advertiser_id" binding:"required"` // 广告主ID
	StartDate    string   `json:"start_date" binding:"required"`    // 开始日期
	EndDate      string   `json:"end_date" binding:"required"`      // 结束日期
	TimeUnit     string   `json:"time_unit,omitempty"`              // 时间维度
	PageNum      int      `json:"page_num,omitempty"`               // 页数
	PageSize     int      `json:"page_size,omitempty"`              // 页大小
	SortColumn   string   `json:"sort_column,omitempty"`            // 排序字段
	Sort         string   `json:"sort,omitempty"`                   // 排序方式
	SplitColumns []string `json:"split_columns,omitempty"`          // 细分条件
	Filters      []Filter `json:"filters,omitempty"`                // 过滤条件
	DataCaliber  int      `json:"data_caliber,omitempty"`           // 数据归因类型
}

// RealtimeReportRequestBody 实时报表请求参数
type RealtimeReportRequestBody struct {
	AccessToken               string `json:"access_token" binding:"required"`        // 访问令牌
	AdvertiserID              int64  `json:"advertiser_id" binding:"required"`       // 广告主ID
	StartDate                 string `json:"start_date" binding:"required"`          // 开始日期
	EndDate                   string `json:"end_date" binding:"required"`            // 结束日期
	PageNum                   int    `json:"page_num,omitempty"`                     // 页数
	PageSize                  int    `json:"page_size,omitempty"`                    // 页大小
	SortColumn                string `json:"sort_column,omitempty"`                  // 排序字段
	Sort                      string `json:"sort,omitempty"`                         // 排序方式
	PlacementList             []int  `json:"placement_list,omitempty"`               // 广告类型
	CreativityFilterState     int    `json:"creativity_filter_state,omitempty"`      // 创意状态过滤
	CreativityCreateBeginTime string `json:"creativity_create_begin_time,omitempty"` // 创意创建时间范围开始
	CreativityCreateEndTime   string `json:"creativity_create_end_time,omitempty"`   // 创意创建时间范围结束
	ConversionType            int    `json:"conversion_type,omitempty"`              // 创意类型
	ProgrammaticList          []int  `json:"programmatic_list,omitempty"`            // 创意组合方式
	CreativityAuditState      int    `json:"creativity_audit_state,omitempty"`       // 创意审核状态
	Name                      string `json:"name,omitempty"`                         // 搜索创意名称
	ID                        int    `json:"id,omitempty"`                           // 搜索创意ID
	DataCaliber               int    `json:"data_caliber,omitempty"`                 // 数据指标归因时间类型
	NeedHourlyData            bool   `json:"need_hourly_data,omitempty"`             // 是否拉取小时数据
}

// TokenResponseData 令牌响应数据
type TokenResponseData struct {
	AccessToken           string       `json:"access_token"`
	RefreshToken          string       `json:"refresh_token"`
	AccessTokenExpiresIn  int64        `json:"access_token_expires_in"`
	RefreshTokenExpiresIn int64        `json:"refresh_token_expires_in"`
	UserId                string       `json:"user_id"`
	AdvertiserId          int64        `json:"advertiser_id"`
	ApprovalAdvertisers   []Advertiser `json:"approval_advertisers"`
}

// APIResponse 统一API响应格式
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// GetAccessTokenHandler 获取访问令牌的HTTP处理器
func (s *XHSService) GetAccessTokenHandler(c *gin.Context) {
	var req GetAccessTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 调用小红书API获取访问令牌
	tokenResp, err := s.client.GetAccessToken(req.AuthCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "获取访问令牌失败: " + err.Error(),
		})
		return
	}

	if !tokenResp.Success {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    tokenResp.Code,
			Message: tokenResp.Msg,
		})
		return
	}

	// 转换响应数据
	responseData := TokenResponseData{
		AccessToken:           tokenResp.Data.AccessToken,
		RefreshToken:          tokenResp.Data.RefreshToken,
		AccessTokenExpiresIn:  tokenResp.Data.AccessTokenExpiresIn,
		RefreshTokenExpiresIn: tokenResp.Data.RefreshTokenExpiresIn,
		UserId:                tokenResp.Data.UserId,
		AdvertiserId:          tokenResp.Data.AdvertiserId,
		ApprovalAdvertisers:   tokenResp.Data.ApprovalAdvertisers,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "成功",
		Data:    responseData,
	})
}

// RefreshTokenHandler 刷新访问令牌的HTTP处理器
func (s *XHSService) RefreshTokenHandler(c *gin.Context) {
	var req RefreshTokenRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 调用小红书API刷新令牌
	tokenResp, err := s.client.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "刷新访问令牌失败: " + err.Error(),
		})
		return
	}

	if !tokenResp.Success {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    tokenResp.Code,
			Message: tokenResp.Msg,
		})
		return
	}

	// 转换响应数据
	responseData := TokenResponseData{
		AccessToken:           tokenResp.Data.AccessToken,
		RefreshToken:          tokenResp.Data.RefreshToken,
		AccessTokenExpiresIn:  tokenResp.Data.AccessTokenExpiresIn,
		RefreshTokenExpiresIn: tokenResp.Data.RefreshTokenExpiresIn,
		UserId:                tokenResp.Data.UserId,
		AdvertiserId:          tokenResp.Data.AdvertiserId,
		ApprovalAdvertisers:   tokenResp.Data.ApprovalAdvertisers,
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "成功",
		Data:    responseData,
	})
}

// GetCreativeReportHandler 获取创意报表的HTTP处理器
func (s *XHSService) GetCreativeReportHandler(c *gin.Context) {
	var req ReportRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 构建报表请求
	reportRequest := &ReportRequest{
		AdvertiserID: req.AdvertiserID,
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		TimeUnit:     req.TimeUnit,
		PageNum:      req.PageNum,
		PageSize:     req.PageSize,
		SortColumn:   req.SortColumn,
		Sort:         req.Sort,
		SplitColumns: req.SplitColumns,
		Filters:      req.Filters,
		DataCaliber:  req.DataCaliber,
	}

	// 设置默认值
	if reportRequest.PageNum == 0 {
		reportRequest.PageNum = 1
	}
	if reportRequest.PageSize == 0 {
		reportRequest.PageSize = 20
	}

	// 调用小红书API获取报表
	reportResp, err := s.client.GetCreativeReport(req.AccessToken, reportRequest)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "获取创意报表失败: " + err.Error(),
		})
		return
	}

	if !reportResp.Success {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    reportResp.Code,
			Message: reportResp.Msg,
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "成功",
		Data:    reportResp.Data,
	})
}

// GetCreativityRealtimeReportHandler 获取创意实时报表的HTTP处理器
func (s *XHSService) GetCreativityRealtimeReportHandler(c *gin.Context) {
	var req RealtimeReportRequestBody
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 构建实时报表请求
	realtimeRequest := &CreativityRealtimeReportRequest{
		AdvertiserID:              req.AdvertiserID,
		StartDate:                 req.StartDate,
		EndDate:                   req.EndDate,
		PageNum:                   req.PageNum,
		PageSize:                  req.PageSize,
		SortColumn:                req.SortColumn,
		Sort:                      req.Sort,
		PlacementList:             req.PlacementList,
		CreativityFilterState:     req.CreativityFilterState,
		CreativityCreateBeginTime: req.CreativityCreateBeginTime,
		CreativityCreateEndTime:   req.CreativityCreateEndTime,
		ConversionType:            req.ConversionType,
		ProgrammaticList:          req.ProgrammaticList,
		CreativityAuditState:      req.CreativityAuditState,
		Name:                      req.Name,
		ID:                        req.ID,
		DataCaliber:               req.DataCaliber,
		NeedHourlyData:            req.NeedHourlyData,
	}

	// 设置默认值
	if realtimeRequest.PageNum == 0 {
		realtimeRequest.PageNum = 1
	}
	if realtimeRequest.PageSize == 0 {
		realtimeRequest.PageSize = 20
	}

	// 调用小红书API获取实时报表
	realtimeResp, err := s.client.GetCreativityRealtimeReport(req.AccessToken, realtimeRequest)
	if err != nil {
		c.JSON(http.StatusInternalServerError, APIResponse{
			Code:    500,
			Message: "获取创意实时报表失败: " + err.Error(),
		})
		return
	}

	if !realtimeResp.Success {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    realtimeResp.Code,
			Message: realtimeResp.Msg,
		})
		return
	}

	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "成功",
		Data:    realtimeResp,
	})
}

// RegisterRoutes 注册路由
func (s *XHSService) RegisterRoutes(router *gin.Engine) {
	xhsGroup := router.Group("/api/xiaohongshu")
	{
		// OAuth相关
		xhsGroup.POST("/oauth/access_token", s.GetAccessTokenHandler)
		xhsGroup.POST("/oauth/refresh_token", s.RefreshTokenHandler)

		// 报表相关
		xhsGroup.POST("/report/creative", s.GetCreativeReportHandler)
		xhsGroup.POST("/report/realtime/creativity", s.GetCreativityRealtimeReportHandler)
	}
}

// ExampleGinIntegration Gin集成示例
func ExampleGinIntegration() {
	// 创建Gin引擎
	r := gin.Default()

	// 创建小红书服务
	xhsService := NewXHSService("your_app_id", "your_secret", false)

	// 注册路由
	xhsService.RegisterRoutes(r)

	// 启动服务器
	r.Run(":8080")
}

// 使用示例的curl命令：
//
// 1. 获取访问令牌：
// curl -X POST http://localhost:8080/api/xiaohongshu/oauth/access_token \
//   -H "Content-Type: application/json" \
//   -d '{"auth_code": "d6a0b18531a2b9599a2c1e2361659c00"}'
//
// 2. 刷新访问令牌：
// curl -X POST http://localhost:8080/api/xiaohongshu/oauth/refresh_token \
//   -H "Content-Type: application/json" \
//   -d '{"refresh_token": "5be1789576f45f90ccfbf4ba16ca4a5b"}'
//
// 3. 获取创意报表：
// curl -X POST http://localhost:8080/api/xiaohongshu/report/creative \
//   -H "Content-Type: application/json" \
//   -d '{
//     "access_token": "your_access_token",
//     "advertiser_id": **********,
//     "start_date": "2024-01-01",
//     "end_date": "2024-01-07",
//     "time_unit": "DAY",
//     "page_num": 1,
//     "page_size": 20
//   }'
//
// 4. 获取创意实时报表：
// curl -X POST http://localhost:8080/api/xiaohongshu/report/realtime/creativity \
//   -H "Content-Type: application/json" \
//   -d '{
//     "access_token": "your_access_token",
//     "advertiser_id": **********,
//     "start_date": "2024-01-01",
//     "end_date": "2024-01-01",
//     "page_num": 1,
//     "page_size": 20,
//     "need_hourly_data": true
//   }'

// HealthCheckHandler 健康检查处理器
func (s *XHSService) HealthCheckHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "小红书API服务正常",
		Data: map[string]interface{}{
			"service": "xiaohongshu",
			"status":  "healthy",
		},
	})
}

// GetConfigHandler 获取配置信息处理器（不包含敏感信息）
func (s *XHSService) GetConfigHandler(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    0,
		Message: "成功",
		Data: map[string]interface{}{
			"app_id":   s.client.config.AppId,
			"is_prod":  s.client.config.IsProd,
			"base_url": s.client.config.GetBaseUrl(),
		},
	})
}

// RegisterExtendedRoutes 注册扩展路由
func (s *XHSService) RegisterExtendedRoutes(router *gin.Engine) {
	xhsGroup := router.Group("/api/xiaohongshu")
	{
		// OAuth相关
		xhsGroup.POST("/oauth/access_token", s.GetAccessTokenHandler)
		xhsGroup.POST("/oauth/refresh_token", s.RefreshTokenHandler)

		// 报表相关
		xhsGroup.POST("/report/creative", s.GetCreativeReportHandler)
		xhsGroup.POST("/report/realtime/creativity", s.GetCreativityRealtimeReportHandler)

		// 服务状态
		xhsGroup.GET("/health", s.HealthCheckHandler)
		xhsGroup.GET("/config", s.GetConfigHandler)
	}
}
