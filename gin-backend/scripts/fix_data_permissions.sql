-- 修复数据权限配置
-- 添加缺失的 data:all 权限

-- 检查并添加缺失的字段（如果permissions表没有type和data_scope字段）
SET @type_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'type');
SET @data_scope_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'data_scope');

-- 添加type字段（如果不存在）
SET @sql = IF(@type_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `type` varchar(20) DEFAULT ''function'' COMMENT ''权限类型：function-功能权限，data-数据权限'' AFTER `module`',
    'SELECT ''type字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加data_scope字段（如果不存在）
SET @sql = IF(@data_scope_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `data_scope` varchar(20) DEFAULT NULL COMMENT ''数据权限范围：all-全部，dept-部门，self-个人'' AFTER `type`',
    'SELECT ''data_scope字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 1. 添加缺失的全部数据权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('代理全部数据', 'agent:data:all', 'agent', 'data', '查看全部范围的代理数据', 'all', 1, NOW(), NOW()),
('媒体全部数据', 'media:data:all', 'media', 'data', '查看全部范围的媒体数据', 'all', 1, NOW(), NOW()),
('资源位全部数据', 'slot:data:all', 'slot', 'data', '查看全部范围的资源位数据', 'all', 1, NOW(), NOW()),
('投放计划全部数据', 'plan:data:all', 'plan', 'data', '查看全部范围的投放计划数据', 'all', 1, NOW(), NOW()),
('投放全部数据', 'delivery:data:all', 'delivery', 'data', '查看全部范围的投放数据', 'all', 1, NOW(), NOW()),
('报表全部数据', 'report:data:all', 'report', 'data', '查看全部范围的报表数据', 'all', 1, NOW(), NOW()),
('创意全部数据', 'creative:data:all', 'creative', 'data', '查看全部范围的创意数据', 'all', 1, NOW(), NOW()),
('仪表盘全部数据', 'dashboard:data:all', 'dashboard', 'data', '查看全部范围的仪表盘数据', 'all', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    type = VALUES(type),
    description = VALUES(description),
    data_scope = VALUES(data_scope),
    updated_at = NOW();

-- 2. 为超级管理员分配所有新的数据权限
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'super_admin'),
    p.id,
    NOW()
FROM permissions p
WHERE p.code LIKE '%:data:all'
AND p.status = 1
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = (SELECT id FROM roles WHERE code = 'super_admin')
    AND rp.permission_id = p.id
);

-- 3. 为管理层分配所有新的数据权限
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'manager'),
    p.id,
    NOW()
FROM permissions p
WHERE p.code LIKE '%:data:all'
AND p.status = 1
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = (SELECT id FROM roles WHERE code = 'manager')
    AND rp.permission_id = p.id
);

-- 4. 更新现有数据权限的type字段（如果为空）
UPDATE permissions 
SET type = 'data' 
WHERE code LIKE '%:data:%' AND (type IS NULL OR type = '');

-- 5. 更新现有功能权限的type字段（如果为空）
UPDATE permissions 
SET type = 'function' 
WHERE code NOT LIKE '%:data:%' AND (type IS NULL OR type = '');

-- 6. 验证权限添加结果
SELECT '新增数据权限:' as '信息';
SELECT code, name, type, data_scope FROM permissions WHERE code LIKE '%:data:all' ORDER BY module, code;

-- 7. 验证角色权限分配
SELECT '超级管理员数据权限:' as '信息';
SELECT p.code, p.name, p.type, p.data_scope
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN roles r ON rp.role_id = r.id
WHERE r.code = 'super_admin' AND p.code LIKE '%:data:%'
ORDER BY p.module, p.code;

SELECT '管理层数据权限:' as '信息';
SELECT p.code, p.name, p.type, p.data_scope
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN roles r ON rp.role_id = r.id
WHERE r.code = 'manager' AND p.code LIKE '%:data:%'
ORDER BY p.module, p.code; 