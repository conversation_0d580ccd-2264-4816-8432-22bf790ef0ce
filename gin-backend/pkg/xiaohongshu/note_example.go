package xiaohongshu

import (
	"fmt"
	"log"
)

// ExampleGetNoteList 获取笔记列表的基本示例
func ExampleGetNoteList() {
	// 1. 创建配置
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true, // 生产环境
	}

	// 2. 创建客户端
	client := NewClient(config)

	// 3. 创建请求参数
	request := &NoteListRequest{
		AdvertiserID:    123456,        // 广告主ID
		NoteType:        2,             // 合作笔记
		Page:            1,             // 第一页
		PageSize:        20,            // 每页20条
		BaseOnly:        false,         // 获取完整信息
		OrderField:      "create_time", // 按创建时间排序
		OrderType:       "desc",        // 降序排列
		NoteContentType: 1,             // 图文笔记
	}

	// 4. 验证请求参数
	if err := ValidateNoteListRequest(request); err != nil {
		log.Fatalf("请求参数验证失败: %v", err)
	}

	// 5. 调用API
	accessToken := "your_access_token"
	response, err := client.GetNoteList(accessToken, request)
	if err != nil {
		log.Fatalf("获取笔记列表失败: %v", err)
	}

	// 6. 处理响应
	fmt.Printf("总笔记数: %d\n", response.Data.Total)
	for i, note := range response.Data.Notes {
		fmt.Printf("\n笔记 #%d:\n", i+1)
		fmt.Printf("  ID: %s\n", note.NoteID)
		fmt.Printf("  标题: %s\n", note.Title)
		fmt.Printf("  作者: %s\n", note.Author)
		fmt.Printf("  阅读数: %d\n", note.ReadCount)
		fmt.Printf("  互动数: %d\n", note.InteractCount)
		fmt.Printf("  是否优质: %s\n", map[int]string{0: "否", 1: "是"}[note.HighQuality])
	}
}

// ExampleGetNoteListWithHelper 使用辅助工具获取笔记列表
func ExampleGetNoteListWithHelper() {
	// 1. 创建配置和客户端
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true,
	}
	client := NewClient(config)

	// 2. 创建辅助工具
	helper := NewNoteListHelper(client)

	accessToken := "your_access_token"
	advertiserID := int64(123456)

	// 3. 获取我的笔记
	fmt.Println("=== 我的笔记 ===")
	myNotes, err := helper.GetMyNotes(accessToken, advertiserID, 1, 10)
	if err != nil {
		log.Printf("获取我的笔记失败: %v", err)
	} else {
		printNotes(myNotes.Data.Notes)
	}

	// 4. 获取合作笔记
	fmt.Println("\n=== 合作笔记 ===")
	cooperationNotes, err := helper.GetCooperationNotes(accessToken, advertiserID, 1, 10)
	if err != nil {
		log.Printf("获取合作笔记失败: %v", err)
	} else {
		printNotes(cooperationNotes.Data.Notes)
	}

	// 5. 获取员工笔记
	fmt.Println("\n=== 员工笔记 ===")
	staffNotes, err := helper.GetStaffNotes(accessToken, advertiserID, 1, 10)
	if err != nil {
		log.Printf("获取员工笔记失败: %v", err)
	} else {
		printNotes(staffNotes.Data.Notes)
	}

	// 6. 搜索笔记
	fmt.Println("\n=== 搜索结果 ===")
	searchResults, err := helper.SearchNotesByKeyword(accessToken, advertiserID, 2, "美妆", 1, 5)
	if err != nil {
		log.Printf("搜索笔记失败: %v", err)
	} else {
		printNotes(searchResults.Data.Notes)
	}
}

// ExampleGetHighQualityNotes 获取优质笔记示例
func ExampleGetHighQualityNotes() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true,
	}
	client := NewClient(config)
	helper := NewNoteListHelper(client)

	accessToken := "your_access_token"
	advertiserID := int64(123456)

	// 获取优质笔记
	highQualityNotes, err := helper.GetHighQualityNotes(accessToken, advertiserID, 2, 1, 20)
	if err != nil {
		log.Fatalf("获取优质笔记失败: %v", err)
	}

	fmt.Printf("找到 %d 条优质笔记:\n", highQualityNotes.Data.Total)
	for _, note := range highQualityNotes.Data.Notes {
		fmt.Printf("- %s (阅读: %d, 互动: %d)\n", note.Title, note.ReadCount, note.InteractCount)
	}
}

// ExampleGetAllNotes 获取所有笔记示例（自动分页）
func ExampleGetAllNotes() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true,
	}
	client := NewClient(config)
	helper := NewNoteListHelper(client)

	accessToken := "your_access_token"
	advertiserID := int64(123456)

	// 获取所有合作笔记
	fmt.Println("正在获取所有合作笔记...")
	allNotes, err := helper.GetAllNotes(accessToken, advertiserID, 2)
	if err != nil {
		log.Fatalf("获取所有笔记失败: %v", err)
	}

	fmt.Printf("总共获取到 %d 条笔记\n", len(allNotes))

	// 统计分析
	var totalRead, totalInteract int
	var highQualityCount int
	contentTypeCount := make(map[int]int)

	for _, note := range allNotes {
		totalRead += note.ReadCount
		totalInteract += note.InteractCount
		if note.HighQuality == 1 {
			highQualityCount++
		}
		contentTypeCount[note.NoteContentType]++
	}

	fmt.Printf("\n=== 统计分析 ===\n")
	fmt.Printf("总阅读数: %d\n", totalRead)
	fmt.Printf("总互动数: %d\n", totalInteract)
	fmt.Printf("优质笔记数: %d (%.1f%%)\n", highQualityCount, float64(highQualityCount)/float64(len(allNotes))*100)
	fmt.Printf("图文笔记: %d 条\n", contentTypeCount[1])
	fmt.Printf("视频笔记: %d 条\n", contentTypeCount[2])

	if len(allNotes) > 0 {
		fmt.Printf("平均阅读数: %.1f\n", float64(totalRead)/float64(len(allNotes)))
		fmt.Printf("平均互动数: %.1f\n", float64(totalInteract)/float64(len(allNotes)))
	}
}

// ExampleFilterNotesByContentType 按内容类型筛选笔记
func ExampleFilterNotesByContentType() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true,
	}
	client := NewClient(config)
	helper := NewNoteListHelper(client)

	accessToken := "your_access_token"
	advertiserID := int64(123456)

	// 获取图文笔记
	fmt.Println("=== 图文笔记 ===")
	imageNotes, err := helper.GetNotesByContentType(accessToken, advertiserID, 2, 1, 1, 10)
	if err != nil {
		log.Printf("获取图文笔记失败: %v", err)
	} else {
		printNotes(imageNotes.Data.Notes)
	}

	// 获取视频笔记
	fmt.Println("\n=== 视频笔记 ===")
	videoNotes, err := helper.GetNotesByContentType(accessToken, advertiserID, 2, 2, 1, 10)
	if err != nil {
		log.Printf("获取视频笔记失败: %v", err)
	} else {
		printNotes(videoNotes.Data.Notes)
	}
}

// printNotes 打印笔记列表的辅助函数
func printNotes(notes []BaseNoteItem) {
	for i, note := range notes {
		fmt.Printf("%d. %s\n", i+1, note.Title)
		fmt.Printf("   作者: %s | 阅读: %d | 互动: %d | 类型: %s\n",
			note.Author,
			note.ReadCount,
			note.InteractCount,
			map[int]string{1: "图文", 2: "视频"}[note.NoteContentType])
		if note.HighQuality == 1 {
			fmt.Printf("   ⭐ 优质笔记\n")
		}
		if note.HighPotential == 1 {
			fmt.Printf("   🚀 高潜笔记\n")
		}
		fmt.Println()
	}
}

// ExampleCompleteWorkflow 完整的工作流程示例
func ExampleCompleteWorkflow() {
	// 1. 初始化
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: true,
	}
	client := NewClient(config)
	helper := NewNoteListHelper(client)

	accessToken := "your_access_token"
	advertiserID := int64(123456)

	// 2. 获取默认笔记列表
	fmt.Println("=== 获取默认笔记列表 ===")
	defaultNotes, err := helper.GetNoteListWithDefaults(accessToken, advertiserID, 2)
	if err != nil {
		log.Fatalf("获取默认笔记列表失败: %v", err)
	}
	fmt.Printf("获取到 %d 条笔记\n", len(defaultNotes.Data.Notes))

	// 3. 分页获取更多笔记
	fmt.Println("\n=== 分页获取笔记 ===")
	for page := 1; page <= 3; page++ {
		pageNotes, err := helper.GetNoteListWithPagination(accessToken, advertiserID, 2, page, 5)
		if err != nil {
			log.Printf("获取第%d页失败: %v", page, err)
			continue
		}
		fmt.Printf("第%d页: %d 条笔记\n", page, len(pageNotes.Data.Notes))
		if len(pageNotes.Data.Notes) == 0 {
			break
		}
	}

	// 4. 搜索特定笔记
	fmt.Println("\n=== 搜索笔记 ===")
	searchResults, err := helper.SearchNotesByKeyword(accessToken, advertiserID, 2, "护肤", 1, 10)
	if err != nil {
		log.Printf("搜索失败: %v", err)
	} else {
		fmt.Printf("搜索到 %d 条相关笔记\n", len(searchResults.Data.Notes))
	}

	fmt.Println("\n工作流程完成!")
}
