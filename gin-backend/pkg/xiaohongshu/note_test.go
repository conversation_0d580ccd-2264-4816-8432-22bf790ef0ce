package xiaohongshu

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestClient_GetNoteList(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			t.<PERSON><PERSON><PERSON>("期望POST请求，得到 %s", r.Method)
		}

		// 验证请求头
		if r.Header.Get("Content-Type") != "application/json" {
			t.<PERSON><PERSON>rf("期望Content-Type为application/json，得到 %s", r.Header.Get("Content-Type"))
		}

		if r.Header.Get("Access-Token") != "test-token" {
			t.<PERSON><PERSON>("期望Access-Token为test-token，得到 %s", r.Header.Get("Access-Token"))
		}

		// 模拟成功响应
		response := NoteListResponse{
			Code:    0,
			Msg:     "success",
			Success: true,
			Data: struct {
				Total int            `json:"total"`
				Notes []BaseNoteItem `json:"notes"`
			}{
				Total: 2,
				Notes: []BaseNoteItem{
					{
						NoteID:          "note123",
						Title:           "测试笔记1",
						Author:          "测试作者1",
						CreateTime:      1640995200000,
						NoteContentType: 1,
						ReadCount:       1000,
						InteractCount:   50,
						HighQuality:     1,
					},
					{
						NoteID:          "note456",
						Title:           "测试笔记2",
						Author:          "测试作者2",
						CreateTime:      1640995300000,
						NoteContentType: 2,
						ReadCount:       2000,
						InteractCount:   100,
						HighQuality:     0,
					},
				},
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建客户端
	config := &Config{
		AppId:  "test-app-id",
		Secret: "test-secret",
		IsProd: false,
	}
	client := NewClient(config)

	// 创建请求
	request := &NoteListRequest{
		AdvertiserID: 123456,
		NoteType:     2,
		Page:         1,
		PageSize:     20,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}

	// 调用API
	response, err := client.requestNoteList(server.URL, "test-token", request)

	// 验证结果
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}

	if response.Code != 0 {
		t.Errorf("期望返回码为0，得到 %d", response.Code)
	}

	if !response.Success {
		t.Errorf("期望Success为true，得到 %v", response.Success)
	}

	if response.Data.Total != 2 {
		t.Errorf("期望Total为2，得到 %d", response.Data.Total)
	}

	if len(response.Data.Notes) != 2 {
		t.Errorf("期望笔记数量为2，得到 %d", len(response.Data.Notes))
	}

	// 验证第一个笔记
	note1 := response.Data.Notes[0]
	if note1.NoteID != "note123" {
		t.Errorf("期望第一个笔记ID为note123，得到 %s", note1.NoteID)
	}

	if note1.Title != "测试笔记1" {
		t.Errorf("期望第一个笔记标题为'测试笔记1'，得到 %s", note1.Title)
	}

	if note1.HighQuality != 1 {
		t.Errorf("期望第一个笔记为优质笔记，得到 %d", note1.HighQuality)
	}
}

func TestClient_GetNoteList_Error(t *testing.T) {
	// 创建模拟服务器返回错误
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := NoteListResponse{
			Code:    1001,
			Msg:     "参数错误",
			Success: false,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建客户端
	config := &Config{
		AppId:  "test-app-id",
		Secret: "test-secret",
		IsProd: false,
	}
	client := NewClient(config)

	// 创建请求
	request := &NoteListRequest{
		AdvertiserID: 123456,
		NoteType:     2,
		BaseOnly:     false,
	}

	// 调用API
	response, err := client.requestNoteList(server.URL, "test-token", request)

	// 验证错误
	if err == nil {
		t.Fatalf("期望返回错误，但没有错误")
	}

	if response.Code != 1001 {
		t.Errorf("期望返回码为1001，得到 %d", response.Code)
	}

	if response.Success {
		t.Errorf("期望Success为false，得到 %v", response.Success)
	}
}

func TestValidateNoteListRequest(t *testing.T) {
	tests := []struct {
		name    string
		request *NoteListRequest
		wantErr bool
	}{
		{
			name: "有效请求",
			request: &NoteListRequest{
				AdvertiserID: 123456,
				NoteType:     2,
				Page:         1,
				PageSize:     20,
				BaseOnly:     false,
			},
			wantErr: false,
		},
		{
			name: "无效广告主ID",
			request: &NoteListRequest{
				AdvertiserID: 0,
				NoteType:     2,
				BaseOnly:     false,
			},
			wantErr: true,
		},
		{
			name: "无效笔记类型",
			request: &NoteListRequest{
				AdvertiserID: 123456,
				NoteType:     99,
				BaseOnly:     false,
			},
			wantErr: true,
		},
		{
			name: "无效页面大小",
			request: &NoteListRequest{
				AdvertiserID: 123456,
				NoteType:     2,
				PageSize:     200,
				BaseOnly:     false,
			},
			wantErr: true,
		},
		{
			name: "无效内容类型",
			request: &NoteListRequest{
				AdvertiserID:    123456,
				NoteType:        2,
				NoteContentType: 5,
				BaseOnly:        false,
			},
			wantErr: true,
		},
		{
			name: "无效排序字段",
			request: &NoteListRequest{
				AdvertiserID: 123456,
				NoteType:     2,
				OrderField:   "invalid_field",
				BaseOnly:     false,
			},
			wantErr: true,
		},
		{
			name: "无效排序方式",
			request: &NoteListRequest{
				AdvertiserID: 123456,
				NoteType:     2,
				OrderType:    "invalid_order",
				BaseOnly:     false,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateNoteListRequest(tt.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateNoteListRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNoteListHelper_GetMyNotes(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 解析请求体来验证笔记类型
		var request NoteListRequest
		json.NewDecoder(r.Body).Decode(&request)

		// 验证笔记类型是否为"我的笔记"
		if request.NoteType != 1 {
			t.Errorf("期望笔记类型为1(我的笔记)，得到 %d", request.NoteType)
		}

		response := NoteListResponse{
			Code:    0,
			Msg:     "success",
			Success: true,
			Data: struct {
				Total int            `json:"total"`
				Notes []BaseNoteItem `json:"notes"`
			}{
				Total: 1,
				Notes: []BaseNoteItem{
					{
						NoteID:    "my_note_123",
						Title:     "我的笔记",
						Author:    "我",
						ReadCount: 500,
					},
				},
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建客户端
	config := &Config{
		AppId:  "test-app-id",
		Secret: "test-secret",
		IsProd: false,
	}
	client := NewClient(config)

	// 调用辅助方法，使用模拟服务器URL
	response, err := client.requestNoteList(server.URL, "test-token", &NoteListRequest{
		AdvertiserID: 123456,
		NoteType:     1,
		Page:         1,
		PageSize:     20,
		BaseOnly:     false,
	})

	// 验证结果
	if err != nil {
		t.Fatalf("请求失败: %v", err)
	}

	if len(response.Data.Notes) != 1 {
		t.Errorf("期望笔记数量为1，得到 %d", len(response.Data.Notes))
	}

	if response.Data.Notes[0].Title != "我的笔记" {
		t.Errorf("期望笔记标题为'我的笔记'，得到 %s", response.Data.Notes[0].Title)
	}
}
