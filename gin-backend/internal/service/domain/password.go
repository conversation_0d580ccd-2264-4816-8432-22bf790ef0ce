package domain

import "time"

// PasswordInfo 口令信息实体
type PasswordInfo struct {
	// ID 口令ID
	ID int64
	// Name 口令名称
	Name string
	// PID 口令唯一标识符
	PID string
}

// PasswordEntity 创建/更新口令组实体
type PasswordEntity struct {
	// GroupName 口令组名称
	GroupName string
	// CategoryID 分类ID(55:小红书,173:抖音,284:闪购)
	CategoryID int
	// Passwords 口令列表
	Passwords []PasswordInfo
}

// PasswordQueryParam 口令组查询参数
type PasswordQueryParam struct {
	// Page 页码
	Page int
	// PageSize 每页数量
	PageSize int
	// Size 兼容参数(历史原因保留)
	Size int
	// GroupName 组名(支持模糊搜索)
	GroupName string
	// CategoryID 分类ID
	CategoryID int64
}

// PasswordOriQueryParam 原始口令查询参数
type PasswordOriQueryParam struct {
	// Page 页码
	Page int
	// PageSize 每页数量
	PageSize int
	// CategoryID 分类ID数组
	CategoryID []int
}

// PasswordItem 口令项
type PasswordItem struct {
	// ID 口令ID
	ID int64
	// Name 口令名称
	Name string
	// PID 口令唯一标识符
	PID string
}

// PasswordGroupItem 口令组项
type PasswordGroupItem struct {
	// ID 口令组ID
	ID int
	// GroupName 口令组名称
	GroupName string
	// CategoryID 分类ID
	CategoryID int64
	// CreatedAt 创建时间
	CreatedAt time.Time
	// UpdatedAt 更新时间
	UpdatedAt time.Time
	// Passwords 口令列表
	Passwords []PasswordItem
}

// PasswordResult 口令组列表结果
type PasswordResult struct {
	// List 口令组列表
	List []PasswordGroupItem
	// Total 总数量
	Total int64
	// Page 当前页码
	Page int
}

// PasswordOriItem 原始口令项
type PasswordOriItem struct {
	// Name 口令名称
	Name string
	// PID 口令唯一标识符
	PID string
}

// PasswordOriResult 原始口令列表结果
type PasswordOriResult struct {
	// List 原始口令列表
	List []PasswordOriItem
	// Total 总数量
	Total int
	// Page 当前页码
	Page int
}

// PasswordActionResult 操作结果(创建/更新/删除)
type PasswordActionResult struct {
	// ID 操作的口令组ID
	ID int
}
