package router

import (
	"gin-backend/internal/controller"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"

	"github.com/gin-gonic/gin"
)

// Setup 设置所有路由
func Setup(r *gin.Engine) {
	// 添加中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())
	r.Use(middleware.Recovery())

	// API路由组
	api := r.Group("/api/v1")

	// 设置认证路由
	setupAuthRoutes(api)

	// 设置代理管理路由
	setupAgentRoutes(api)

	// 设置媒体管理路由
	setupMediaRoutes(api)

	// 设置资源位管理路由
	setupSlotRoutes(api)

	// 设置投放计划路由
	setupPlanRoutes(api)

	// 设置投放产品路由
	setupAdProductRoutes(api)

	// 设置淘联链接路由
	setupTaobaoPidRoutes(api)

	// 设置创意管理路由
	setupCreativeRoutes(api)

	// 设置口令管理路由
	setupPasswordRoutes(api)

	// 设置费用管理路由
	setupCostRoutes(api)

	// 设置模型管理路由
	setupModelRoutes(api)

	// 设置仪表盘路由
	setupDashboardRoutes(api)

	// 设置平台计划管理路由（投放管理下的计划管理）
	setupPlatformPlanRoutes(r)

	// 设置广告组管理路由（投放管理下的广告组管理）
	setupPlatformGroupRoutes(r)

	// 设置广告创意管理路由（投放管理下的广告创意管理）
	setupPlatformCreativeRoutes(r)

	// 设置上传管理路由
	setupUploadRoutes(api)

	// 设置用户管理路由
	setupUserRoutes(api)

	// 设置部门管理路由
	setupDepartmentRoutes(api)

	// 设置角色权限管理路由
	setupRoleRoutes(api)

	// 设置工作模式管理路由
	setupWorkModeRoutes(api)

	// 设置日报表路由
	setupDailyReportRoutes(r, controller.NewDailyReportController())

	// 设置广告插件路由
	setupAdPluginRoutes(r)

	// 设置广告账号管理路由
	setupAdAccountRoutes(api)

	// 设置小红书创意报表路由
	SetupXHSCreativeReportRoutes(api)

	// 设置计划列表路由
	SetupAdPlanListRoutes(api)

	// 设置小红书授权路由
	SetupXHSAuthRoutes(api)
}

// setupAuthRoutes 设置认证相关路由
func setupAuthRoutes(api *gin.RouterGroup) {
	authController := controller.NewAuthController()

	// 认证路由组
	auth := api.Group("/auth")
	{
		// 登录（无需认证）
		auth.POST("/login", authController.Login)

		// 需要认证的路由
		authRequired := auth.Group("")
		authRequired.Use(middleware.JWTAuth())
		{
			authRequired.GET("/info", authController.GetUserInfo)                // 获取用户信息
			authRequired.POST("/change-password", authController.ChangePassword) // 修改密码
			authRequired.POST("/logout", authController.Logout)                  // 登出
		}
	}
}

// setupAgentRoutes 设置代理管理相关路由
func setupAgentRoutes(api *gin.RouterGroup) {
	agentController := controller.NewAgentController()

	// 代理管理路由组（需要认证）
	agents := api.Group("/agents")
	agents.Use(middleware.JWTAuth())
	{
		// 兼容原有前端API调用格式
		agents.GET("/list", agentController.GetList)     // 获取代理列表
		agents.POST("/create", agentController.Create)   // 创建代理
		agents.GET("/detail", agentController.GetDetail) // 获取代理详情 (使用查询参数 ?id=123)
		agents.PUT("/update", agentController.Update)    // 更新代理 (ID在请求体中)
		agents.DELETE("/delete", agentController.Delete) // 删除代理 (使用查询参数 ?id=123)
		agents.PUT("/audit", agentController.Audit)      // 审核代理 (ID在请求体中)
	}
}

// setupMediaRoutes 设置媒体管理相关路由
func setupMediaRoutes(api *gin.RouterGroup) {
	// 创建依赖服务
	mediaController := controller.NewMediaController()

	// 媒体管理路由组（需要认证）
	media := api.Group("/media")
	media.Use(middleware.JWTAuth())
	{
		// 兼容原有前端API调用格式
		media.GET("/list", mediaController.GetList)     // 获取媒体列表
		media.POST("/create", mediaController.Create)   // 创建媒体
		media.GET("/detail", mediaController.GetDetail) // 获取媒体详情 (使用查询参数 ?id=123)
		media.PUT("/update", mediaController.Update)    // 更新媒体 (ID在请求体中)
		media.DELETE("/delete", mediaController.Delete) // 删除媒体 (使用查询参数 ?id=123)
		media.PUT("/audit", mediaController.Audit)      // 审核媒体 (ID在请求体中)

		// 媒介用户列表接口（用于下拉框）
		userController := controller.NewUserController()
		media.GET("/users", userController.GetMediaUsers) // 获取媒介用户列表
	}
}

// setupSlotRoutes 设置资源位管理相关路由
func setupSlotRoutes(api *gin.RouterGroup) {
	// 创建依赖服务
	permissionService := service.NewPermissionService()
	roleService := service.NewRoleService()
	slotController := controller.NewSlotController(permissionService, roleService)

	// 资源位管理路由组（需要认证）
	slots := api.Group("/slots")
	slots.Use(middleware.JWTAuth())
	{
		// 兼容原有前端API调用格式
		slots.GET("/list", slotController.GetSlotList)     // 获取资源位列表
		slots.POST("/create", slotController.CreateSlot)   // 创建资源位
		slots.PUT("/update", slotController.UpdateSlot)    // 更新资源位 (ID在请求体中)
		slots.DELETE("/delete", slotController.DeleteSlot) // 删除资源位 (使用查询参数 ?id=123)
		slots.PUT("/audit", slotController.AuditSlot)      // 审核资源位 (ID在请求体中)
	}
}

// setupPlanRoutes 设置投放计划相关路由
func setupPlanRoutes(api *gin.RouterGroup) {
	// 初始化服务
	planController := controller.NewPlanController()

	// 广告位计划管理（与前端API路径匹配）
	adSlotPlans := api.Group("/ad-slot-plans")
	adSlotPlans.Use(middleware.JWTAuth())
	{
		adSlotPlans.GET("/list", planController.ListAdSlotPlans)                           // 获取广告位计划列表
		adSlotPlans.POST("/create", planController.CreateAdSlotPlan)                       // 创建广告位计划
		adSlotPlans.GET("/get", planController.GetAdSlotPlan)                              // 获取广告位计划详情 (使用查询参数 ?id=123)
		adSlotPlans.POST("/update", planController.UpdateAdSlotPlan)                       // 更新广告位计划 (ID在请求体中)
		adSlotPlans.POST("/delete", planController.DeleteAdSlotPlan)                       // 删除广告位计划 (使用查询参数 ?id=123)
		adSlotPlans.POST("/approve", planController.ApproveAdSlotPlan)                     // 审核通过 (使用查询参数 ?id=123)
		adSlotPlans.POST("/reject", planController.RejectAdSlotPlan)                       // 审核拒绝 (ID在请求体中)
		adSlotPlans.POST("/update-delivery-mode", planController.UpdateDeliveryMode)       // 更新投放策略 (ID在请求体中)
		adSlotPlans.POST("/generate-promotion-link", planController.GeneratePromotionLink) // 生成推广链接 (ID在请求体中)
		adSlotPlans.POST("/update-promotion-link", planController.UpdatePromotionLink)     // 更新推广链接 (ID在请求体中)
		adSlotPlans.POST("/generate-short-url", planController.GenerateShortURL)           // 生成短链接 (ID在请求体中)
		adSlotPlans.POST("/update-short-url", planController.UpdateShortURL)               // 更新短链接 (ID在请求体中)
		adSlotPlans.POST("/update-merge-links", planController.UpdateMergeLinks)           // 更新融合链接 (ID在请求体中)
		adSlotPlans.GET("/platform_object_data", planController.GetPlatformObjectData)     // 获取平台对象数据
	}

}

// setupPlatformPlanRoutes 设置平台计划管理相关路由（投放管理下的计划管理）
func setupPlatformPlanRoutes(r *gin.Engine) {
	platformPlanController := controller.NewPlatformPlanController()

	// 平台计划管理（投放管理下的计划管理）
	platformPlanController.RegisterRoutes(r)
}

// setupPlatformGroupRoutes 设置广告组管理相关路由（投放管理下的广告组管理）
func setupPlatformGroupRoutes(r *gin.Engine) {
	platformGroupController := controller.NewPlatformGroupController()

	// 广告组管理（投放管理下的广告组管理）
	platformGroupController.RegisterRoutes(r)
}

// setupPlatformCreativeRoutes 设置广告创意管理相关路由（投放管理下的广告创意管理）
func setupPlatformCreativeRoutes(r *gin.Engine) {
	platformCreativeController := controller.NewPlatformCreativeController()

	// 广告创意管理（投放管理下的广告创意管理）
	platformCreativeController.RegisterRoutes(r)
}

// setupAdProductRoutes 设置投放产品相关路由
func setupAdProductRoutes(api *gin.RouterGroup) {
	adProductController := controller.NewAdProductController()

	// 投放产品管理（需要认证）
	adProducts := api.Group("/ad-products")
	adProducts.Use(middleware.JWTAuth())
	{
		adProducts.GET("/list", adProductController.GetList) // 获取投放产品列表
	}
}

// setupTaobaoPidRoutes 设置淘联链接相关路由
func setupTaobaoPidRoutes(api *gin.RouterGroup) {
	taobaoPidController := controller.NewTaobaoPidController()

	// 淘联链接管理（需要认证）
	taobaoPids := api.Group("/taobao-pids")
	taobaoPids.Use(middleware.JWTAuth())
	{
		// 兼容原有前端API调用格式
		taobaoPids.GET("/list", taobaoPidController.List)        // 获取淘联链接列表
		taobaoPids.POST("/create", taobaoPidController.Create)   // 创建淘联链接
		taobaoPids.GET("/detail", taobaoPidController.GetByID)   // 获取淘联链接详情 (使用查询参数 ?id=123)
		taobaoPids.PUT("/update", taobaoPidController.Update)    // 更新淘联链接 (ID在请求体中)
		taobaoPids.DELETE("/delete", taobaoPidController.Delete) // 删除淘联链接 (使用查询参数 ?id=123)
	}
}

// setupCreativeRoutes 设置创意管理相关路由
func setupCreativeRoutes(api *gin.RouterGroup) {
	creativeController := controller.NewCreativeController()

	// 创意管理（需要认证）
	creatives := api.Group("/ad-creatives")
	creatives.Use(middleware.JWTAuth())
	{
		// 兼容原有前端API调用格式
		creatives.GET("/list", creativeController.List)      // 获取创意列表
		creatives.POST("/create", creativeController.Create) // 创建创意
		creatives.GET("/detail", creativeController.Detail)  // 获取创意详情 (使用查询参数 ?id=123)
		creatives.PUT("/update", creativeController.Update)  // 更新创意 (ID在请求体中)
	}
}

// setupPasswordRoutes 设置口令管理相关路由
func setupPasswordRoutes(api *gin.RouterGroup) {
	passwordController := controller.NewPasswordController()

	// 口令管理路由组（需要认证）
	passwords := api.Group("/password")
	passwords.Use(middleware.JWTAuth())
	{
		passwords.POST("/create", passwordController.Create)  // 创建口令组及口令
		passwords.GET("/list", passwordController.List)       // 获取口令组列表
		passwords.GET("/oriList", passwordController.OriList) // 获取原始口令列表
		passwords.PUT("/:id", passwordController.Update)      // 编辑口令组及口令
		passwords.DELETE("/:id", passwordController.Delete)   // 删除口令组
	}
}

// setupCostRoutes 设置费用管理相关路由
func setupCostRoutes(api *gin.RouterGroup) {
	// 初始化服务
	costController := controller.NewCostController()
	// 初始化推广报表控制器
	promotionReportController := controller.NewPromotionReportController()

	// 费用管理路由组（需要认证）
	costs := api.Group("/cost")
	costs.Use(middleware.JWTAuth())
	{
		// 新版API接口
		costs.POST("", costController.Create)       // 创建费用记录
		costs.PUT("", costController.Update)        // 更新费用记录
		costs.DELETE("/:id", costController.Delete) // 删除费用记录
		costs.GET("", costController.List)          // 获取费用记录列表
		costs.GET("/:id", costController.GetByID)   // 根据ID获取费用记录
		costs.POST("/audit", costController.Audit)  // 审核费用记录

	}

	// 兼容原项目的API路径
	delivery := api.Group("/delivery")
	delivery.Use(middleware.JWTAuth())
	{
		reports := delivery.Group("/reports")
		{
			cost := reports.Group("/cost")
			{
				cost.POST("/update", promotionReportController.UpdateCost) // 兼容原项目的更新费用接口
				cost.GET("/list", promotionReportController.GetCostList)   // 兼容原项目的获取费用列表接口
				cost.POST("/import", promotionReportController.ImportCost)
				cost.GET("/tasks", promotionReportController.GetTaskList)
			}
		}
		// 推广报表路由组（需要认证）
		reports.GET("/list", promotionReportController.GetModelReport) // 获取推广报表列表
	}

}

// setupModelRoutes 设置模型管理相关路由
func setupModelRoutes(api *gin.RouterGroup) {
	// 初始化服务
	modelController := controller.NewModelController()

	// 兼容GoFrame原有的API路径结构
	delivery := api.Group("/delivery")
	delivery.Use(middleware.JWTAuth())
	{
		models := delivery.Group("/models")
		{
			models.GET("/list", modelController.List)      // 获取模型列表
			models.POST("/create", modelController.Create) // 创建模型
			models.POST("/update", modelController.Update) // 更新模型
			models.POST("/delete", modelController.Delete) // 删除模型
			models.POST("/import", modelController.Import) // 导入模型
			models.GET("/:id", modelController.GetDetail)  // 获取模型详情
		}
	}
}

// setupDashboardRoutes 设置仪表盘相关路由
func setupDashboardRoutes(api *gin.RouterGroup) {
	dashboardController := controller.NewDashboardController()

	// 仪表盘路由组（需要认证）
	dashboard := api.Group("/dashboard")
	dashboard.Use(middleware.JWTAuth())
	{
		dashboard.GET("/metrics", dashboardController.GetMetrics)                      // 获取仪表盘指标数据
		dashboard.GET("/filter-options", dashboardController.GetFilterOptions)         // 获取筛选选项数据
		dashboard.GET("/products", dashboardController.GetProducts)                    // 获取产品列表
		dashboard.GET("/trend", dashboardController.GetTrendData)                      // 获取趋势数据
		dashboard.GET("/region", dashboardController.GetRegionData)                    // 获取地域分析数据
		dashboard.GET("/order-type", dashboardController.GetOrderTypeData)             // 获取订单类型分析数据
		dashboard.GET("/plan-stats", dashboardController.GetPlanStats)                 // 获取分计划统计数据
		dashboard.GET("/media-list", dashboardController.GetMediaList)                 // 获取媒体列表
		dashboard.GET("/plans", dashboardController.GetPlans)                          // 获取投放计划列表
		dashboard.GET("/dynamic-filter", dashboardController.GetDynamicFilterOptions)  // 获取动态筛选选项
		dashboard.GET("/debug-permission", dashboardController.GetUserPermissionDebug) // 调试用户权限（临时）
	}
}

// setupUserRoutes 设置用户管理相关路由
func setupUserRoutes(api *gin.RouterGroup) {
	userController := controller.NewUserController()

	// 用户管理路由组（需要认证）
	users := api.Group("/users")
	users.Use(middleware.JWTAuth())
	{
		users.GET("", userController.GetUsers)                          // 获取用户列表
		users.POST("", userController.CreateUser)                       // 创建用户
		users.PUT("/:id", userController.UpdateUser)                    // 更新用户
		users.DELETE("/:id", userController.DeleteUser)                 // 删除用户
		users.POST("/:id/inactive", userController.SetUserInactive)     // 标记用户离职
		users.POST("/:id/lock", userController.LockUser)                // 锁定用户
		users.POST("/:id/unlock", userController.UnlockUser)            // 解锁用户
		users.POST("/:id/password/reset", userController.ResetPassword) // 重置密码
		users.GET("/options", userController.GetUserOptions)            // 获取用户选项
	}
}

// setupDepartmentRoutes 设置部门管理相关路由
func setupDepartmentRoutes(api *gin.RouterGroup) {
	departmentController := controller.NewDepartmentController()

	// 部门管理路由组（需要认证）
	departments := api.Group("/departments")
	departments.Use(middleware.JWTAuth())
	{
		departments.GET("", departmentController.GetDepartments)                     // 获取部门列表
		departments.POST("", departmentController.CreateDepartment)                  // 创建部门
		departments.GET("/tree", departmentController.GetDepartmentTree)             // 获取部门树形结构
		departments.GET("/options", departmentController.GetDepartmentOptions)       // 获取部门选项
		departments.GET("/statistics", departmentController.GetDepartmentStatistics) // 获取部门统计信息
		departments.GET("/:id", departmentController.GetDepartmentByID)              // 获取部门详情
		departments.PUT("/:id", departmentController.UpdateDepartment)               // 更新部门
		departments.DELETE("/:id", departmentController.DeleteDepartment)            // 删除部门
	}
}

// setupWorkModeRoutes 设置工作模式管理相关路由
func setupWorkModeRoutes(api *gin.RouterGroup) {
	workModeController := controller.NewWorkModeController()

	// 工作模式管理路由组（需要认证）
	workModes := api.Group("/work-modes")
	workModes.Use(middleware.JWTAuth())
	{
		workModes.GET("", workModeController.GetUserModes)              // 获取用户可用的工作模式
		workModes.POST("/switch", workModeController.SwitchWorkMode)    // 切换工作模式
		workModes.GET("/check", workModeController.CheckModePermission) // 检查指定模式的权限
	}
}

// setupRoleRoutes 设置角色权限管理相关路由
func setupRoleRoutes(api *gin.RouterGroup) {
	roleController := controller.NewRoleController()

	// 角色管理路由组（需要认证）
	roles := api.Group("/roles")
	roles.Use(middleware.JWTAuth())
	{
		roles.GET("", roleController.GetRoles)               // 获取角色列表
		roles.POST("", roleController.CreateRole)            // 创建角色
		roles.GET("/options", roleController.GetRoleOptions) // 获取角色选项
		roles.GET("/:id", roleController.GetRoleByID)        // 获取角色详情
		roles.PUT("/:id", roleController.UpdateRole)         // 更新角色
		roles.DELETE("/:id", roleController.DeleteRole)      // 删除角色
	}

	// 权限管理路由组（需要认证）
	permissions := api.Group("/permissions")
	permissions.Use(middleware.JWTAuth())
	{
		permissions.GET("", roleController.GetPermissions)               // 获取权限列表
		permissions.POST("", roleController.CreatePermission)            // 创建权限
		permissions.GET("/modules", roleController.GetPermissionModules) // 获取权限模块
		permissions.GET("/:id", roleController.GetPermissionByID)        // 获取权限详情
		permissions.PUT("/:id", roleController.UpdatePermission)         // 更新权限
		permissions.DELETE("/:id", roleController.DeletePermission)      // 删除权限
	}

	// 角色权限关联管理路由组（需要认证）
	rolePermissions := api.Group("/role-permissions")
	rolePermissions.Use(middleware.JWTAuth())
	{
		rolePermissions.GET("", roleController.GetRolePermissions)        // 获取角色权限
		rolePermissions.POST("/assign", roleController.AssignPermissions) // 分配权限给角色
		rolePermissions.POST("/revoke", roleController.RevokePermissions) // 撤销角色权限
	}

	// 用户权限管理路由组（需要认证）
	userPermissions := api.Group("/user-permissions")
	userPermissions.Use(middleware.JWTAuth())
	{
		userPermissions.GET("", roleController.GetUserPermissions)        // 获取用户权限列表
		userPermissions.GET("/check", roleController.CheckUserPermission) // 检查用户权限
	}

	// 权限日志管理路由组（需要认证）
	permissionLogs := api.Group("/permission-logs")
	permissionLogs.Use(middleware.JWTAuth())
	{
		permissionLogs.GET("", roleController.GetPermissionLogs) // 获取权限操作日志
	}
}

// setupDailyReportRoutes 设置日报表相关路由
func setupDailyReportRoutes(r *gin.Engine, dailyReportController *controller.DailyReportController) {
	reportAPI := r.Group("/api/v1/reports")
	reportAPI.Use(middleware.JWTAuth())
	{
		// 日报表相关接口
		reportAPI.GET("/daily-report", dailyReportController.GetDailyReport)
		reportAPI.GET("/daily-detail", dailyReportController.GetDailyDetail)
		reportAPI.GET("/daily-report/export", dailyReportController.ExportDailyReport)

		// 周报表相关接口
		reportAPI.GET("/weekly-report", dailyReportController.GetWeeklyReport)
		reportAPI.GET("/weekly-detail", dailyReportController.GetWeeklyDetail)
		reportAPI.GET("/weekly-report/export", dailyReportController.ExportWeeklyReport)
	}
}

// setupAdPluginRoutes 设置广告插件相关路由
func setupAdPluginRoutes(r *gin.Engine) {
	adPluginController := controller.NewAdPluginController()
	// 小程序插件获取广告链接
	r.POST("/api/v1/get-adlink", adPluginController.GetAdLink)
	r.POST("/api/v1/get-adpage", adPluginController.GetAdPage)
}

// setupAdAccountRoutes 设置广告账号管理相关路由
func setupAdAccountRoutes(api *gin.RouterGroup) {
	adAccountController := controller.NewAdAccountController()

	// 广告账号管理路由组（需要认证）
	adAccounts := api.Group("/ad-accounts")
	adAccounts.Use(middleware.JWTAuth())
	{
		adAccounts.GET("", adAccountController.GetAdAccounts)               // 获取广告账号列表
		adAccounts.POST("", adAccountController.CreateAdAccount)            // 创建广告账号
		adAccounts.GET("/options", adAccountController.GetAdAccountOptions) // 获取广告账号选项
		adAccounts.GET("/:id", adAccountController.GetAdAccountByID)        // 获取广告账号详情
		adAccounts.PUT("/:id", adAccountController.UpdateAdAccount)         // 更新广告账号
		adAccounts.DELETE("/:id", adAccountController.DeleteAdAccount)      // 删除广告账号

		// 便捷接口
		adAccounts.POST("/master", adAccountController.CreateMasterAccount) // 创建主账号
		adAccounts.POST("/sub", adAccountController.CreateSubAccount)       // 创建子账号

		// 小红书授权接口
		adAccounts.POST("/xiaohongshu/auth", adAccountController.XiaohongshuAuth)                  // 小红书账号授权
		adAccounts.POST("/xiaohongshu/refresh-token", adAccountController.XiaohongshuRefreshToken) // 小红书刷新令牌
	}
}

// setupUploadRoutes 设置上传管理相关路由
func setupUploadRoutes(api *gin.RouterGroup) {
	uploadController := controller.NewUploadController()

	// 上传管理路由组（需要认证）
	upload := api.Group("")
	upload.Use(middleware.JWTAuth())
	{
		upload.POST("/upload", uploadController.Upload)            // 通用文件上传
		upload.POST("/upload/image", uploadController.UploadImage) // 图片上传
	}
}
