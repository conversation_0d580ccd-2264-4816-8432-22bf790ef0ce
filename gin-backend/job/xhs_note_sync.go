package job

import (
	"context"
	"fmt"
	"gin-backend/internal/service"
	"time"

	"go.uber.org/zap"
)

// XHSNoteSyncJob 小红书笔记同步定时任务
type XHSNoteSyncJob struct {
	noteSyncService *service.XHSNoteSyncService
}

// NewXHSNoteSyncJob 创建小红书笔记同步定时任务
func NewXHSNoteSyncJob() *XHSNoteSyncJob {
	return &XHSNoteSyncJob{
		noteSyncService: service.NewXHSNoteSyncService(),
	}
}

// Name 任务名称
func (j *XHSNoteSyncJob) Name() string {
	return "sync-xhs-notes"
}

// Run 任务执行 - 每20分钟执行一次
func (j *XHSNoteSyncJob) Run() error {
	startTime := time.Now()
	zap.L().Info("开始执行小红书笔记同步任务")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	// 同步所有账号的笔记
	if err := j.noteSyncService.SyncAllNotes(ctx); err != nil {
		zap.L().Error("小红书笔记同步失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("小红书笔记同步任务完成",
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// RunManual 手动执行同步任务（用于测试或手动触发）
func (j *XHSNoteSyncJob) RunManual() error {
	startTime := time.Now()
	zap.L().Info("手动执行小红书笔记同步任务")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	if err := j.noteSyncService.SyncAllNotes(ctx); err != nil {
		zap.L().Error("手动执行小红书笔记同步失败",
			zap.Error(err),
			zap.Duration("duration", time.Since(startTime)))
		return err
	}

	zap.L().Info("手动执行小红书笔记同步任务完成",
		zap.Duration("duration", time.Since(startTime)))
	return nil
}

// GetJobInfo 获取任务信息
func (j *XHSNoteSyncJob) GetJobInfo() JobInfo {
	return JobInfo{
		Name:        j.Name(),
		Description: "小红书笔记列表数据同步",
		Schedule:    "每20分钟执行一次",
		Timeout:     "10分钟",
		Category:    "数据同步",
		DataSource:  "小红书笔记API",
		Target:      "xhs_notes表",
	}
}

// ValidateJobConfig 验证任务配置
func (j *XHSNoteSyncJob) ValidateJobConfig() error {
	// 检查服务是否正常初始化
	if j.noteSyncService == nil {
		return fmt.Errorf("笔记同步服务未初始化")
	}

	// 可以添加更多配置验证逻辑
	// 例如：检查数据库连接、API配置等

	return nil
}

// GetLastSyncInfo 获取最后同步信息（可用于监控）
func (j *XHSNoteSyncJob) GetLastSyncInfo(ctx context.Context) (*SyncInfo, error) {
	// 这里可以查询数据库获取最后同步的信息
	// 例如：最后同步时间、同步的数据量等
	
	// 简单实现：返回当前时间作为示例
	return &SyncInfo{
		LastSyncTime: time.Now(),
		Status:       "completed",
		Message:      "最近一次同步成功",
	}, nil
}

// HealthCheck 健康检查
func (j *XHSNoteSyncJob) HealthCheck(ctx context.Context) error {
	// 检查服务状态
	if j.noteSyncService == nil {
		return fmt.Errorf("笔记同步服务未初始化")
	}

	// 可以添加更多健康检查逻辑
	// 例如：检查数据库连接、API可用性等

	return nil
}

// Stop 停止任务（用于优雅关闭）
func (j *XHSNoteSyncJob) Stop() error {
	zap.L().Info("停止小红书笔记同步任务")
	
	// 这里可以添加清理逻辑
	// 例如：取消正在进行的同步操作、关闭连接等
	
	return nil
}

// GetMetrics 获取任务指标（用于监控）
func (j *XHSNoteSyncJob) GetMetrics(ctx context.Context) (*JobMetrics, error) {
	// 这里可以返回任务的执行指标
	// 例如：执行次数、成功率、平均执行时间等
	
	return &JobMetrics{
		ExecutionCount:   0,    // 执行次数
		SuccessCount:     0,    // 成功次数
		FailureCount:     0,    // 失败次数
		AvgExecutionTime: 0,    // 平均执行时间（秒）
		LastExecutionTime: time.Time{}, // 最后执行时间
	}, nil
}

// SetSchedule 设置调度计划（用于动态调整）
func (j *XHSNoteSyncJob) SetSchedule(schedule string) error {
	// 这里可以实现动态调整调度计划的逻辑
	zap.L().Info("设置小红书笔记同步任务调度计划", zap.String("schedule", schedule))
	
	// 验证调度计划格式
	// 例如：验证cron表达式格式
	
	return nil
}

// GetConfig 获取任务配置
func (j *XHSNoteSyncJob) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"name":         j.Name(),
		"description":  "小红书笔记列表数据同步",
		"schedule":     "*/20 * * * *", // 每20分钟执行一次的cron表达式
		"timeout":      "10m",
		"retry_count":  3,
		"retry_delay":  "1m",
		"enabled":      true,
		"data_source":  "xiaohongshu_note_api",
		"target_table": "xhs_notes",
		"note_types": []map[string]interface{}{
			{"type": 1, "name": "我的笔记"},
			{"type": 2, "name": "合作笔记"},
			{"type": 4, "name": "主理人笔记"},
			{"type": 6, "name": "员工笔记"},
			{"type": 11, "name": "授权笔记"},
		},
	}
}

// SyncSpecificNoteType 同步指定类型的笔记（扩展功能）
func (j *XHSNoteSyncJob) SyncSpecificNoteType(ctx context.Context, noteType int) error {
	startTime := time.Now()
	zap.L().Info("开始同步指定类型的小红书笔记",
		zap.Int("note_type", noteType))

	// 这里可以实现同步指定类型笔记的逻辑
	// 目前先返回成功，后续可以扩展

	zap.L().Info("指定类型笔记同步完成",
		zap.Int("note_type", noteType),
		zap.Duration("duration", time.Since(startTime)))
	
	return nil
}

// GetSyncStatistics 获取同步统计信息
func (j *XHSNoteSyncJob) GetSyncStatistics(ctx context.Context) (*SyncStatistics, error) {
	// 这里可以查询数据库获取同步统计信息
	// 例如：各类型笔记数量、最近同步时间等
	
	return &SyncStatistics{
		TotalNotes:     0,
		MyNotes:        0,
		CooperateNotes: 0,
		ManagerNotes:   0,
		StaffNotes:     0,
		AuthorizedNotes: 0,
		LastSyncTime:   time.Now(),
	}, nil
}

// SyncStatistics 同步统计信息结构
type SyncStatistics struct {
	TotalNotes      int       `json:"total_notes"`      // 总笔记数
	MyNotes         int       `json:"my_notes"`         // 我的笔记数
	CooperateNotes  int       `json:"cooperate_notes"`  // 合作笔记数
	ManagerNotes    int       `json:"manager_notes"`    // 主理人笔记数
	StaffNotes      int       `json:"staff_notes"`      // 员工笔记数
	AuthorizedNotes int       `json:"authorized_notes"` // 授权笔记数
	LastSyncTime    time.Time `json:"last_sync_time"`   // 最后同步时间
}
