package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// AdSlotPlan 广告位投放计划
type AdSlotPlan struct {
	ID               int64         `gorm:"primaryKey;autoIncrement"`                             // 主键ID
	Code             string        `gorm:"column:code;type:varchar(100);uniqueIndex"`            // 计划编码
	Type             string        `gorm:"column:type;type:varchar(20);default:normal"`          // 计划类型
	AdProductID      int64         `gorm:"column:ad_product_id;index"`                           // 广告产品ID
	AdCreativeID     int64         `gorm:"column:ad_creative_id;index"`                          // 广告创意ID
	AdSlotID         int64         `gorm:"column:ad_slot_id;index"`                              // 广告位ID
	MediaID          int64         `gorm:"column:media_id;index"`                                // 媒体ID
	UserID           int64         `gorm:"column:user_id;index"`                                 // 用户ID
	AuditStatus      string        `gorm:"column:audit_status;type:varchar(20);default:pending"` // 审核状态
	DeliveryStatus   string        `gorm:"column:delivery_status;type:varchar(20);default:init"` // 投放状态
	DeliveryMode     int           `gorm:"column:delivery_mode;default:1"`                       // 投放模式
	StartDate        time.Time     `gorm:"column:start_date"`                                    // 开始日期
	EndDate          time.Time     `gorm:"column:end_date"`                                      // 结束日期
	IsEndDateEnabled bool          `gorm:"column:is_end_date_enabled;default:false"`             // 是否启用结束日期
	PromotionLink    string        `gorm:"column:promotion_link;type:text"`                      // 推广链接
	PromotionQrcode  string        `gorm:"column:promotion_qrcode;type:text"`                    // 推广二维码
	ShortURL         string        `gorm:"column:short_url;type:varchar(500)"`                   // 短链接
	RejectReason     string        `gorm:"column:reject_reason;type:text"`                       // 拒绝原因
	Remark           string        `gorm:"column:remark;type:text"`                              // 备注
	MergeLinks       MergeLinkJSON `gorm:"column:merge_links;type:json"`                         // 融合链接
	MaterialType     int8          `gorm:"column:material_type;default:1"`                       // 素材类型
	CreatedAt        time.Time     `gorm:"column:created_at;autoCreateTime"`                     // 创建时间
	UpdatedAt        time.Time     `gorm:"column:updated_at;autoUpdateTime"`                     // 更新时间

	// 关联模型（暂时注释，等相关模型创建后再启用）
	AdProduct *AdProduct `gorm:"foreignKey:AdProductID"` // 广告产品
	// AdCreative *AdCreative `gorm:"foreignKey:AdCreativeID" json:"ad_creative,omitempty"`
	AdSlot         *Slot            `gorm:"foreignKey:AdSlotID"` // 广告位
	Media          *Media           `gorm:"foreignKey:MediaID"`  // 媒体
	User           *User            `gorm:"foreignKey:UserID"`   // 用户
	PromotionZones []*PromotionZone `gorm:"foreignKey:PlanID"`   // 推广区
}

func (AdSlotPlan) TableName() string {
	return "ad_slot_plans"
}

// PlanRelateRel 计划关联关系
type PlanRelateRel struct {
	ID         int64     `gorm:"primaryKey;autoIncrement"`         // 主键ID
	PlanID     int64     `gorm:"column:plan_id;index"`             // 计划ID
	RelateType int8      `gorm:"column:relate_type"`               // 关联类型
	RelateID   int64     `gorm:"column:relate_id;index"`           // 关联ID
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime"` // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;autoUpdateTime"` // 更新时间
}

func (PlanRelateRel) TableName() string {
	return "plan_relate_rels"
}

// AdSlotPlanOperationLog 投放计划操作日志
type AdSlotPlanOperationLog struct {
	ID              int64           `gorm:"primaryKey;autoIncrement"`          // ID
	PlanID          int64           `gorm:"column:plan_id;index"`              // 计划ID
	UserID          int64           `gorm:"column:user_id;index"`              // 操作用户ID
	Action          string          `gorm:"column:action;type:varchar(20)"`    // 操作类型
	OperationDesc   string          `gorm:"column:operation_desc;type:text"`   // 操作描述
	OperationParams json.RawMessage `gorm:"column:operation_params;type:json"` // 操作参数
	OldStatus       int             `gorm:"column:old_status"`                 // 原状态
	NewStatus       int             `gorm:"column:new_status"`                 // 新状态
	Remark          string          `gorm:"column:remark;type:text"`           // 备注
	CreatedAt       time.Time       `gorm:"column:created_at;autoCreateTime"`  // 创建时间
	UpdatedAt       time.Time       `gorm:"column:updated_at;autoUpdateTime"`  // 更新时间
}

// TableName 表名
func (a *AdSlotPlanOperationLog) TableName() string {
	return "ad_slot_plan_operation_logs"
}

// MergeLinkJSON 自定义JSON类型用于存储融合链接信息
type MergeLinkJSON map[string]interface{}

// Value 实现 driver.Valuer 接口
func (m MergeLinkJSON) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan 实现 sql.Scanner 接口
func (m *MergeLinkJSON) Scan(value interface{}) error {
	if value == nil {
		*m = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, m)
	case string:
		return json.Unmarshal([]byte(v), m)
	default:
		return nil
	}
}
