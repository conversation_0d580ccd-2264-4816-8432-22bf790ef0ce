package service

import (
	"context"
	"encoding/json"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskService 任务服务
type TaskService struct {
	db *gorm.DB
}

// NewTaskService 创建任务服务实例
func NewTaskService() *TaskService {
	return &TaskService{
		db: global.DB,
	}
}

// GetTaskList 获取任务列表
func (s *TaskService) GetTaskList(ctx context.Context, param domain.TaskListParam) (domain.TaskListResult, error) {
	var tasks []model.Task
	var result domain.TaskListResult
	result.List = make([]domain.TaskEntity, 0)
	result.Page = param.Page
	result.PageSize = param.PageSize

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.Task{}).Where("task_type = ?", 1) // 1=费用导入任务

	// 如果指定了状态，则添加状态过滤条件
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return result, err
	}
	result.Total = total

	// 如果请求了分页
	if param.Page > 0 && param.PageSize > 0 {
		offset := (param.Page - 1) * param.PageSize
		query = query.Offset(offset).Limit(param.PageSize)
	} else {
		// 默认限制10条
		query = query.Limit(10)
	}

	// 执行查询
	err := query.Order("created_at DESC").Find(&tasks).Error
	if err != nil {
		return result, err
	}

	// 转换为响应格式
	for _, task := range tasks {
		result.List = append(result.List, domain.TaskEntity{
			ID:        task.ID,
			Name:      task.Name,
			Status:    task.Status,
			CreatedAt: task.CreatedAt,
		})
	}

	return result, nil
}

// ProcessTasks 处理导入任务
// 定期在后台协程中调用此方法处理待处理任务
func (s *TaskService) ProcessTasks(ctx context.Context) error {
	// 查询待处理的任务
	var tasks []model.Task
	err := s.db.WithContext(ctx).
		Where("task_type = ? AND status = ?", 1, "pending"). // 费用导入场景，待处理状态
		Order("id ASC").
		Find(&tasks).Error
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		return nil
	}

	// 处理每个任务
	for _, task := range tasks {
		// 更新任务状态为处理中
		s.db.Model(&model.Task{}).
			Where("id = ?", task.ID).
			Update("status", "processing")

		// 解析任务参数
		var groupInfos []map[string]any
		err = json.Unmarshal([]byte(task.Params), &groupInfos)
		if err != nil {
			zap.L().Error("解析任务参数失败", zap.Error(err))
			s.UpdateTaskStatus(ctx, uint64(task.ID), "failed", err.Error())
			continue
		}

		// 开启事务处理数据
		err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			// 处理每个组的数据
			for _, info := range groupInfos {
				groupID := int64(info["group_id"].(float64))
				startDate := info["start_date"].(string)
				endDate := info["end_date"].(string)

				// 调用更新订单数据的方法
				// 注意：这里需要调用正确的方法来更新订单数据
				if err := NewPromotionReportService().UpdateOrderDataBatch(ctx, tx, groupID, startDate, endDate); err != nil {
					return err
				}
			}
			return nil
		})

		if err != nil {
			zap.L().Error("处理任务失败", zap.Error(err))
			s.UpdateTaskStatus(ctx, uint64(task.ID), "failed", err.Error())
			continue
		}

		// 更新任务状态为完成
		s.UpdateTaskStatus(ctx, uint64(task.ID), "completed", "")
	}

	return nil
}

// UpdateTaskStatus 更新任务状态
func (s *TaskService) UpdateTaskStatus(ctx context.Context, taskID uint64, status string, errorMsg string) error {
	updates := map[string]any{
		"status": status,
	}

	if errorMsg != "" {
		updates["error_msg"] = errorMsg
	}

	return s.db.WithContext(ctx).Model(&model.Task{}).
		Where("id = ?", taskID).
		Updates(updates).Error
}
