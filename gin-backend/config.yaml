server:
  port: ":8080"
  mode: "debug"  # debug, release, test

log:
  level: "info"
  filename: "log/app.log"
  max_size: 1024
  max_age: 7
  max_backups: 7

database:
  host: "rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com"
  port: 3306
  user: "adpro"
  password: "Ddkj2024"
  db_name: "adpro_test"
  charset: "utf8mb4"

redis:
  host: " 127.0.0.1"
  port: 6379
  password: ""
  db: 0

jwt:
  secret: "your-secret-key"
  expire: 86400  # 24小时

oss:
  endpoint: "oss-cn-hangzhou.aliyuncs.com"
  access_key_id: "LTAI5tHVcLrTDZSbGwJwokvT"
  access_key_secret: "******************************"
  bucket_name: "rebate-robot"
  url_prefix: "https://img.dac6.cn"

xiaohongshu:
  app_id: "1234"
  secret: "your-xiaohongshu-secret"
  is_prod: false
  redirect_uri: "http://www.baidu.com"

