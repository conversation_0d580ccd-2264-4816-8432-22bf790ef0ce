package domain

import "time"

// AdProductParam 查询产品的参数
type AdProductParam struct {
	Page   int    // 页码
	Size   int    // 每页大小
	Name   string // 产品名称
	Status string // 产品状态
}

// AdProductEntity 投放产品实体
type AdProductEntity struct {
	ID          int64     // 产品ID
	Code        string    // 产品编号
	Name        string    // 产品名称
	Image       string    // 产品图片
	Description string    // 产品描述
	Status      string    // 产品状态
	CreatedBy   int64     // 创建人ID
	UpdatedBy   int64     // 更新人ID
	CreatedAt   time.Time // 创建时间
	UpdatedAt   time.Time // 更新时间
}

// AdProductInfo 投放产品简要信息
type AdProductInfo struct {
	ID          int64  // 产品ID
	Code        string // 产品编号
	Name        string // 产品名称
	Image       string // 产品图片
	Description string // 产品描述
	Status      string // 产品状态
	StatusText  string // 状态文本
}

// AdProductListEntity 产品列表结果
type AdProductListEntity struct {
	List     []AdProductInfo // 产品列表
	Total    int64           // 总数
	Page     int             // 当前页
	PageSize int             // 每页大小
}

// 产品状态常量
const (
	ProductStatusActive   = "active"   // 启用
	ProductStatusInactive = "inactive" // 停用
)

// ProductStatusMap 状态映射
var ProductStatusMap = map[string]string{
	ProductStatusActive:   "启用",
	ProductStatusInactive: "停用",
}
