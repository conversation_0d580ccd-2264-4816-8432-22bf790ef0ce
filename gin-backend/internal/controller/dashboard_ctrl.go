package controller

import (
	"net/http"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// DashboardController 仪表盘控制器
type DashboardController struct {
	dashboardService *service.DashboardService
}

// NewDashboardController 创建仪表盘控制器实例
func NewDashboardController() *DashboardController {
	return &DashboardController{
		dashboardService: service.NewDashboardService(),
	}
}

// GetMetrics 获取仪表盘指标数据
func (c *DashboardController) GetMetrics(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardMetricsReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetMetrics(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取指标数据失败: "+err.Error())
		return
	}

	// 5. 将Domain转换为VO
	voResp := vo.FromDomainDashboardMetrics(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetFilterOptions 获取仪表盘筛选项数据
func (c *DashboardController) GetFilterOptions(ctx *gin.Context) {
	// 1. 验证用户登录状态
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	// 2. 调用服务层获取筛选选项
	result, err := c.dashboardService.GetFilterOptions()
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取筛选选项失败: "+err.Error())
		return
	}

	// 3. 转换Domain为VO
	voResp := vo.FromDomainFilterOptions(result)

	// 4. 返回成功响应
	response.Success(ctx, voResp)
}

// GetProducts 获取产品列表
func (c *DashboardController) GetProducts(ctx *gin.Context) {
	// 1. 验证用户登录状态
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	// 2. 调用服务层获取产品列表
	result, err := c.dashboardService.GetProducts()
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取产品列表失败: "+err.Error())
		return
	}

	// 3. 转换Domain为VO
	voResp := vo.FromDomainProducts(result)

	// 4. 返回成功响应
	response.Success(ctx, voResp)
}

// GetTrendData 获取趋势数据
func (c *DashboardController) GetTrendData(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardTrendReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetTrendData(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取趋势数据失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainTrend(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetPlanStats 获取分计划统计数据
func (c *DashboardController) GetPlanStats(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardPlanStatsReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetPlanStats(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取分计划统计失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainPlanStats(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetMediaList 获取媒体列表
func (c *DashboardController) GetMediaList(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardMediaListReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetMediaList(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取媒体列表失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainMediaList(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetPlans 获取投放计划列表
func (c *DashboardController) GetPlans(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardPlansReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetPlans(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取计划列表失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainPlans(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetRegionData 获取地域分析数据
func (c *DashboardController) GetRegionData(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardRegionReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetRegionData(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取地域分析失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainRegion(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetOrderTypeData 获取订单类型分析数据
func (c *DashboardController) GetOrderTypeData(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardOrderTypeReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetOrderTypeData(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取订单类型分析失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainOrderType(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetDynamicFilterOptions 获取动态筛选选项
func (c *DashboardController) GetDynamicFilterOptions(ctx *gin.Context) {
	// 1. 绑定查询参数
	var voReq vo.DashboardDynamicFilterReq
	if err := ctx.ShouldBindQuery(&voReq); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 2. 获取用户信息
	userInfo := middleware.GetCurrentUser(ctx)
	if userInfo.ID == 0 {
		response.Error(ctx, http.StatusUnauthorized, "用户未登录")
		return
	}

	userID := int(userInfo.ID)
	userRole := userInfo.Role

	// 3. 转换VO到Domain
	domainParam := voReq.ToDomain()

	// 4. 调用服务层处理业务逻辑
	result, err := c.dashboardService.GetDynamicFilterOptions(domainParam, userID, userRole)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取动态筛选选项失败: "+err.Error())
		return
	}

	// 5. 转换Domain为VO
	voResp := vo.FromDomainDynamicFilter(result)

	// 6. 返回成功响应
	response.Success(ctx, voResp)
}

// GetUserPermissionDebug 获取用户权限调试信息
func (c *DashboardController) GetUserPermissionDebug(ctx *gin.Context) {
	// 1. 获取用户ID参数
	userIDStr := ctx.Query("user_id")
	if userIDStr == "" {
		response.Error(ctx, http.StatusBadRequest, "缺少用户ID参数")
		return
	}

	// 2. 验证当前用户是否有管理员权限
	currentUser := middleware.GetCurrentUser(ctx)
	if currentUser.Role != 1 { // 假设1是管理员角色
		response.Error(ctx, http.StatusForbidden, "没有权限执行此操作")
		return
	}

	// 3. 调用服务层获取权限调试信息
	info, users, err := c.dashboardService.GetUserPermissionDebugInfo(uint64(currentUser.ID))
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取权限调试信息失败: "+err.Error())
		return
	}

	// 4. 返回成功响应
	response.Success(ctx, gin.H{
		"scope":           info,
		"departmentUsers": users,
	})
}

// RegisterRoutes 注册路由
func (c *DashboardController) RegisterRoutes(router *gin.RouterGroup) {
	dashboard := router.Group("/dashboard")
	{
		dashboard.GET("/metrics", c.GetMetrics)                     // 获取仪表盘指标
		dashboard.GET("/filter-options", c.GetFilterOptions)        // 获取仪表盘筛选选项
		dashboard.GET("/products", c.GetProducts)                   // 获取产品列表
		dashboard.GET("/trend", c.GetTrendData)                     // 获取趋势数据
		dashboard.GET("/plan-stats", c.GetPlanStats)                // 获取分计划统计
		dashboard.GET("/media-list", c.GetMediaList)                // 获取媒体列表
		dashboard.GET("/plans", c.GetPlans)                         // 获取计划列表
		dashboard.GET("/region", c.GetRegionData)                   // 获取地域分析数据
		dashboard.GET("/order-type", c.GetOrderTypeData)            // 获取订单类型分析数据
		dashboard.GET("/filter", c.GetDynamicFilterOptions)         // 获取动态筛选选项
		dashboard.GET("/user-permission", c.GetUserPermissionDebug) // 获取用户权限调试信息（仅供开发调试使用）
	}
}
