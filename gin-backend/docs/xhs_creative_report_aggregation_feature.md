# 小红书创意报表聚合功能详解

## 功能概述

小红书创意报表API现在支持两种不同的数据聚合方式：
- **分日数据（daily）**：按日期+创意ID维度展示，每个创意每天一条记录
- **汇总数据（summary）**：按创意ID维度聚合多日数据，每个创意只有一条汇总记录

## 聚合类型对比

### 1. 分日数据（daily）

#### 特点
- **维度**：日期 + 创意ID
- **记录数**：每个创意每天一条记录
- **用途**：查看创意的日常表现趋势，分析每日数据变化

#### 数据示例
```json
{
  "list": [
    {
      "id": 1,
      "creativity_id": "creative_001",
      "time": "2024-01-15T00:00:00Z",
      "fee": 100.50,
      "impression": 10000,
      "click": 500
    },
    {
      "id": 2,
      "creativity_id": "creative_001", 
      "time": "2024-01-14T00:00:00Z",
      "fee": 95.30,
      "impression": 9500,
      "click": 480
    },
    {
      "id": 3,
      "creativity_id": "creative_002",
      "time": "2024-01-15T00:00:00Z", 
      "fee": 80.20,
      "impression": 8000,
      "click": 400
    }
  ]
}
```

#### 使用场景
- 分析创意的日常表现趋势
- 查看特定日期的创意数据
- 进行时间序列分析
- 监控创意的日常波动

### 2. 汇总数据（summary）

#### 特点
- **维度**：创意ID
- **记录数**：每个创意只有一条汇总记录
- **用途**：查看创意的整体表现，进行创意间的对比分析

#### 聚合规则
- **文本字段**：取最新值（MAX）
- **数值指标**：
  - 累计类指标：SUM（消费、展现、点击、互动等）
  - 比率类指标：AVG（CTR、CPI、ROI等）
- **时间字段**：取最早时间（MIN）

#### 数据示例
```json
{
  "list": [
    {
      "creativity_id": "creative_001",
      "time": "2024-01-14T00:00:00Z",
      "fee": 195.80,
      "impression": 19500,
      "click": 980,
      "ctr": 5.03,
      "roi": 12.5
    },
    {
      "creativity_id": "creative_002",
      "time": "2024-01-15T00:00:00Z",
      "fee": 80.20,
      "impression": 8000,
      "click": 400,
      "ctr": 5.0,
      "roi": 10.2
    }
  ]
}
```

#### 使用场景
- 创意整体表现对比
- 创意效果排行榜
- 投放效果总结报告
- 创意优化决策支持

## API使用方法

### 1. 获取分日数据

```bash
# 默认为分日数据
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?start_date=2024-01-01&end_date=2024-01-31&page=1&page_size=20"

# 明确指定分日数据
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?aggregate_type=daily&start_date=2024-01-01&end_date=2024-01-31&page=1&page_size=20"
```

### 2. 获取汇总数据

```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?aggregate_type=summary&start_date=2024-01-01&end_date=2024-01-31&page=1&page_size=20"
```

### 3. 参数说明

| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|--------|
| aggregate_type | string | 否 | 聚合类型：daily 或 summary | daily |

## 技术实现

### 1. 分日数据查询

```sql
SELECT * FROM xhs_creative_reports 
WHERE [筛选条件]
ORDER BY time DESC, creativity_id ASC, id DESC
LIMIT offset, page_size
```

### 2. 汇总数据查询

```sql
SELECT 
    creativity_id,
    MAX(account_name) as account_name,
    MAX(title) as title,
    MIN(time) as time,
    SUM(fee) as fee,
    SUM(impression) as impression,
    SUM(click) as click,
    AVG(ctr) as ctr,
    AVG(roi) as roi,
    ...
FROM xhs_creative_reports 
WHERE [筛选条件]
GROUP BY creativity_id
ORDER BY MIN(time) DESC, creativity_id ASC
LIMIT offset, page_size
```

### 3. 聚合字段规则

#### 文本字段（取最新值）
```sql
MAX(account_name) as account_name,
MAX(campaign_name) as campaign_name,
MAX(unit_name) as unit_name,
MAX(title) as title,
MAX(pwd) as pwd,
MAX(content_people) as content_people,
MAX(pitcher_name) as pitcher_name
```

#### 累计指标（求和）
```sql
SUM(fee) as fee,
SUM(impression) as impression,
SUM(click) as click,
SUM(like) as like,
SUM(comment) as comment,
SUM(collect) as collect,
SUM(follow) as follow,
SUM(share) as share,
SUM(interaction) as interaction,
SUM(goods_order) as goods_order,
SUM(rgmv) as rgmv,
SUM(success_goods_order) as success_goods_order
```

#### 比率指标（平均值）
```sql
AVG(ctr) as ctr,
AVG(acp) as acp,
AVG(cpm) as cpm,
AVG(cpi) as cpi,
AVG(roi) as roi,
AVG(purchase_order_roi_7d) as purchase_order_roi_7d
```

#### 时间字段（最早时间）
```sql
MIN(time) as time
```

## 性能考虑

### 1. 索引建议

#### 分日数据查询
```sql
-- 复合索引：筛选 + 排序
CREATE INDEX idx_daily_query ON xhs_creative_reports 
(account_id, time DESC, creativity_id, id DESC);

-- 日期范围索引
CREATE INDEX idx_time_range ON xhs_creative_reports (time);
```

#### 汇总数据查询
```sql
-- 分组聚合索引
CREATE INDEX idx_summary_query ON xhs_creative_reports 
(creativity_id, account_id, time);

-- 筛选条件索引
CREATE INDEX idx_filter_conditions ON xhs_creative_reports 
(account_id, placement, time);
```

### 2. 查询优化

#### 分日数据
- 利用时间范围索引快速定位数据
- 使用复合索引避免文件排序
- 合理设置分页大小

#### 汇总数据
- GROUP BY 操作需要足够的内存
- 考虑对大数据量进行分批处理
- 可以考虑预计算汇总表

## 业务应用场景

### 1. 日常监控（分日数据）
- **用途**：监控创意的日常表现
- **频率**：每日查看
- **关注指标**：消费、展现、点击、转化
- **分析维度**：时间趋势、异常检测

### 2. 效果分析（汇总数据）
- **用途**：分析创意的整体效果
- **频率**：周报、月报
- **关注指标**：总ROI、平均CTR、总转化
- **分析维度**：创意对比、效果排名

### 3. 优化决策（两种结合）
- **分日数据**：识别表现异常的时间点
- **汇总数据**：确定整体表现优秀的创意
- **决策支持**：暂停低效创意、加大优质创意投放

## 注意事项

### 1. 数据一致性
- 分日数据的总和应该等于汇总数据（累计指标）
- 比率指标在汇总时使用平均值，可能与实际计算略有差异

### 2. 性能影响
- 汇总查询比分日查询更消耗资源
- 大时间范围的汇总查询需要更多时间
- 建议合理设置查询时间范围

### 3. 业务理解
- 汇总数据中的时间字段表示该创意的最早投放时间
- 比率指标的平均值可能不等于总和的比率
- 文本字段取最新值，可能不是最具代表性的值

## 扩展功能

### 1. 自定义聚合维度
未来可以支持更多聚合维度：
- 按计划聚合
- 按单元聚合
- 按账号聚合
- 按时间段聚合（周、月）

### 2. 聚合规则配置
可以允许用户自定义聚合规则：
- 选择需要聚合的字段
- 指定聚合函数（SUM、AVG、MAX、MIN）
- 设置权重计算

### 3. 预计算优化
对于频繁查询的汇总数据，可以考虑：
- 定时预计算汇总表
- 增量更新机制
- 缓存热点数据

这个聚合功能为小红书创意报表提供了灵活的数据查看方式，满足不同业务场景的分析需求。
