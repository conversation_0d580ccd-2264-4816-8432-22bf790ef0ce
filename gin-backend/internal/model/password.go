package model

import (
	"time"
)

// Password 口令表实体
type Password struct {
	ID        uint      `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	PID       string    `gorm:"column:pid;not null;size:100;comment:口令ID"`
	Name      string    `gorm:"column:name;not null;size:255;comment:口令名称"`
	GroupID   uint      `gorm:"column:group_id;not null;comment:所属组ID"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime;comment:更新时间"`

	// 关联关系
	Group *PasswordGroup `gorm:"foreignKey:GroupID;references:ID"`
}

// TableName 指定表名
func (Password) TableName() string {
	return "passwords"
}

// PasswordGroup 口令组表实体
type PasswordGroup struct {
	ID         uint      `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Name       string    `gorm:"column:name;not null;size:100;comment:组名称"`
	CategoryID int       `gorm:"column:category_id;not null;comment:分类ID(55:小红书,173:抖音,284:闪购)"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt  time.Time `gorm:"column:updated_at;autoUpdateTime;comment:更新时间"`

	// 关联关系
	Passwords []Password `gorm:"foreignKey:GroupID;references:ID"`
}

// TableName 指定表名
func (PasswordGroup) TableName() string {
	return "password_groups"
}
