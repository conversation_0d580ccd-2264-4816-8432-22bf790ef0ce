package domain

import (
	"errors"
	"fmt"
	"strings"
	"time"
)

// CreativeReportImportRow Excel导入行数据
type CreativeReportImportRow struct {
	NoteID        string    `json:"note_id"`        // 笔记ID
	ContentStaff  string    `json:"content_staff"`  // 内容人员
	PlanID        string    `json:"plan_id"`        // 计划ID
	Operator      string    `json:"operator"`       // 投手
	Password      string    `json:"password"`       // 口令词
	StartDate     time.Time `json:"start_date"`     // 开始日期
	EndDate       time.Time `json:"end_date"`       // 结束日期
	RowNumber     int       `json:"row_number"`     // 行号（用于错误定位）
}

// Validate 验证导入行数据
func (r CreativeReportImportRow) Validate() error {
	// 笔记ID和计划ID至少要有一个
	if strings.TrimSpace(r.NoteID) == "" && strings.TrimSpace(r.PlanID) == "" {
		return fmt.Errorf("第%d行：笔记ID和计划ID至少要填写一个", r.RowNumber)
	}

	// 开始日期和结束日期必填
	if r.StartDate.IsZero() {
		return fmt.Errorf("第%d行：开始日期不能为空", r.RowNumber)
	}
	if r.EndDate.IsZero() {
		return fmt.Errorf("第%d行：结束日期不能为空", r.RowNumber)
	}

	// 开始日期不能晚于结束日期
	if r.StartDate.After(r.EndDate) {
		return fmt.Errorf("第%d行：开始日期不能晚于结束日期", r.RowNumber)
	}

	// 时间范围不能超过90天
	if r.EndDate.Sub(r.StartDate) > 90*24*time.Hour {
		return fmt.Errorf("第%d行：时间范围不能超过90天", r.RowNumber)
	}

	// 如果有笔记ID，内容人员必填
	if strings.TrimSpace(r.NoteID) != "" && strings.TrimSpace(r.ContentStaff) == "" {
		return fmt.Errorf("第%d行：有笔记ID时，内容人员不能为空", r.RowNumber)
	}

	// 如果有计划ID，投手必填
	if strings.TrimSpace(r.PlanID) != "" && strings.TrimSpace(r.Operator) == "" {
		return fmt.Errorf("第%d行：有计划ID时，投手不能为空", r.RowNumber)
	}

	return nil
}

// CreativeReportImportParam 创意报表导入参数
type CreativeReportImportParam struct {
	FilePath string `json:"file_path"` // Excel文件路径
}

// Validate 验证导入参数
func (p CreativeReportImportParam) Validate() error {
	if strings.TrimSpace(p.FilePath) == "" {
		return errors.New("文件路径不能为空")
	}
	return nil
}

// CreativeReportImportResult 创意报表导入结果
type CreativeReportImportResult struct {
	TotalRows       int                           `json:"total_rows"`       // 总行数
	SuccessRows     int                           `json:"success_rows"`     // 成功行数
	FailedRows      int                           `json:"failed_rows"`      // 失败行数
	UpdatedRecords  int                           `json:"updated_records"`  // 更新记录数
	Errors          []CreativeReportImportError   `json:"errors"`           // 错误列表
	Summary         CreativeReportImportSummary   `json:"summary"`          // 导入摘要
}

// CreativeReportImportError 导入错误信息
type CreativeReportImportError struct {
	RowNumber int    `json:"row_number"` // 行号
	Error     string `json:"error"`      // 错误信息
}

// CreativeReportImportSummary 导入摘要
type CreativeReportImportSummary struct {
	NoteUpdates     int `json:"note_updates"`     // 笔记更新数量
	PlanUpdates     int `json:"plan_updates"`     // 计划更新数量
	ContentStaffSet int `json:"content_staff_set"` // 设置内容人员数量
	OperatorSet     int `json:"operator_set"`     // 设置投手数量
	PasswordSet     int `json:"password_set"`     // 设置口令词数量
}

// CreativeReportUpdateInfo 创意报表更新信息
type CreativeReportUpdateInfo struct {
	ID           int64     `json:"id"`            // 记录ID
	NoteID       string    `json:"note_id"`       // 笔记ID
	PlanID       string    `json:"plan_id"`       // 计划ID
	ContentStaff string    `json:"content_staff"` // 内容人员（新值）
	Operator     string    `json:"operator"`      // 投手（新值）
	Password     string    `json:"password"`      // 口令词（新值）
	UpdateType   string    `json:"update_type"`   // 更新类型：note/plan
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
}

// ExcelColumnMapping Excel列映射
type ExcelColumnMapping struct {
	NoteIDColumn      int // 笔记ID列索引
	ContentStaffColumn int // 内容人员列索引
	PlanIDColumn      int // 计划ID列索引
	OperatorColumn    int // 投手列索引
	PasswordColumn    int // 口令词列索引
	StartDateColumn   int // 开始日期列索引
	EndDateColumn     int // 结束日期列索引
}

// GetDefaultColumnMapping 获取默认列映射
func GetDefaultColumnMapping() ExcelColumnMapping {
	return ExcelColumnMapping{
		NoteIDColumn:       0, // A列：笔记ID
		ContentStaffColumn: 1, // B列：内容人员
		PlanIDColumn:       2, // C列：计划ID
		OperatorColumn:     3, // D列：投手
		PasswordColumn:     4, // E列：口令词
		StartDateColumn:    5, // F列：开始日期
		EndDateColumn:      6, // G列：结束日期
	}
}

// ValidateCreativeReportImportParam 验证创意报表导入参数
func ValidateCreativeReportImportParam(param CreativeReportImportParam) error {
	return param.Validate()
}

// ValidateCreativeReportImportRow 验证创意报表导入行数据
func ValidateCreativeReportImportRow(row CreativeReportImportRow) error {
	return row.Validate()
}

// IsValidNoteID 验证笔记ID格式
func IsValidNoteID(noteID string) bool {
	noteID = strings.TrimSpace(noteID)
	if noteID == "" {
		return false
	}
	// 笔记ID通常是字母数字组合，长度在10-30之间
	if len(noteID) < 10 || len(noteID) > 30 {
		return false
	}
	return true
}

// IsValidPlanID 验证计划ID格式
func IsValidPlanID(planID string) bool {
	planID = strings.TrimSpace(planID)
	if planID == "" {
		return false
	}
	// 计划ID通常是数字或字母数字组合
	if len(planID) < 5 || len(planID) > 50 {
		return false
	}
	return true
}

// FormatDateRange 格式化日期范围
func FormatDateRange(startDate, endDate time.Time) string {
	return fmt.Sprintf("%s 至 %s", 
		startDate.Format("2006-01-02"), 
		endDate.Format("2006-01-02"))
}

// GetUpdateTypeDescription 获取更新类型描述
func GetUpdateTypeDescription(updateType string) string {
	switch updateType {
	case "note":
		return "按笔记ID更新"
	case "plan":
		return "按计划ID更新"
	default:
		return "未知更新类型"
	}
}

// CreativeReportImportPreview 导入预览
type CreativeReportImportPreview struct {
	TotalRows    int                       `json:"total_rows"`    // 总行数
	ValidRows    int                       `json:"valid_rows"`    // 有效行数
	InvalidRows  int                       `json:"invalid_rows"`  // 无效行数
	SampleData   []CreativeReportImportRow `json:"sample_data"`   // 示例数据（前10行）
	Errors       []CreativeReportImportError `json:"errors"`      // 错误列表
}

// GetImportPreview 获取导入预览
func GetImportPreview(rows []CreativeReportImportRow) CreativeReportImportPreview {
	preview := CreativeReportImportPreview{
		TotalRows:   len(rows),
		ValidRows:   0,
		InvalidRows: 0,
		SampleData:  make([]CreativeReportImportRow, 0),
		Errors:      make([]CreativeReportImportError, 0),
	}

	// 验证每一行并统计
	for _, row := range rows {
		if err := row.Validate(); err != nil {
			preview.InvalidRows++
			preview.Errors = append(preview.Errors, CreativeReportImportError{
				RowNumber: row.RowNumber,
				Error:     err.Error(),
			})
		} else {
			preview.ValidRows++
		}

		// 添加示例数据（前10行）
		if len(preview.SampleData) < 10 {
			preview.SampleData = append(preview.SampleData, row)
		}
	}

	return preview
}
