package xiaohongshu

// AccessTokenRequest 获取访问令牌请求
type AccessTokenRequest struct {
	AppId    string `json:"app_id"`    // 应用ID
	Secret   string `json:"secret"`    // 应用密钥
	AuthCode string `json:"auth_code"` // 授权码
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	AppId        string `json:"app_id"`        // 应用ID
	Secret       string `json:"secret"`        // 应用密钥
	RefreshToken string `json:"refresh_token"` // 刷新令牌
}

// Advertiser 广告主信息
type Advertiser struct {
	AdvertiserId   int64  `json:"advertiser_id"`   // 广告主ID
	AdvertiserName string `json:"advertiser_name"` // 广告主名称
}

// TokenData 令牌数据
type TokenData struct {
	UserId                string       `json:"user_id"`                  // 用户ID
	RoleType              int          `json:"role_type"`                // 角色类型
	ApprovalAdvertisers   []Advertiser `json:"approval_advertisers"`     // 授权的广告主列表
	RefreshToken          string       `json:"refresh_token"`            // 刷新令牌
	AdvertiserId          int64        `json:"advertiser_id"`            // 广告主ID
	RefreshTokenExpiresIn int64        `json:"refresh_token_expires_in"` // 刷新令牌过期时间(秒)
	ApprovalRoleType      int          `json:"approval_role_type"`       // 授权角色类型
	PlatformType          int          `json:"platform_type"`            // 平台类型
	AccessToken           string       `json:"access_token"`             // 访问令牌
	AccessTokenExpiresIn  int64        `json:"access_token_expires_in"`  // 访问令牌过期时间(秒)
}

// TokenResponse 令牌响应
type TokenResponse struct {
	Code    int        `json:"code"`    // 状态码
	Success bool       `json:"success"` // 是否成功
	Msg     string     `json:"msg"`     // 消息
	Data    *TokenData `json:"data"`    // 数据
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Code    int    `json:"code"`    // 错误码
	Success bool   `json:"success"` // 是否成功
	Msg     string `json:"msg"`     // 错误消息
}

// ==================== 笔记列表相关类型 ====================

// NoteListRequest 获取笔记列表请求参数
type NoteListRequest struct {
	AdvertiserID    int64  `json:"advertiser_id"`               // 必填 - 广告主ID
	NoteType        int    `json:"note_type"`                   // 必填 - 笔记类型 (1:我的笔记, 2:合作笔记, 4:主理人笔记, 6:员工笔记, 11:授权笔记)
	Keyword         string `json:"keyword,omitempty"`           // 可选 - 搜索关键词 (笔记ID或名称)
	OrderField      string `json:"order_field,omitempty"`       // 可选 - 排序字段 (read_count, interact_count, read_rate, interact_rate, create_time)
	OrderType       string `json:"order_type,omitempty"`        // 可选 - 排序方式 (asc/desc, 默认desc)
	NoteContentType int    `json:"note_content_type,omitempty"` // 可选 - 笔记内容类型 (1:图文, 2:视频)
	PlacementType   int    `json:"placement_type,omitempty"`    // 可选 - 推广场景 (1:信息流, 2:搜索, 3:开屏, 4:全站智投, 7:视频内流)
	SpuID           string `json:"spu_id,omitempty"`            // 可选 - SPU ID (白名单支持)
	FilterTaobao    int    `json:"filter_taobao,omitempty"`     // 可选 - 是否只展示小红星笔记 (0:所有, 1:仅小红星)
	MarketTarget    int    `json:"market_target,omitempty"`     // 可选 - 营销诉求 (4:产品种草, 9:客资收集, 16:应用换端)
	SpuType         int    `json:"spu_type,omitempty"`          // 可选 - SPU类型 (1:spu, 2:非标品, 3:品牌推广)(白名单支持)
	Page            int    `json:"page,omitempty"`              // 可选 - 页码 (默认1)
	PageSize        int    `json:"page_size,omitempty"`         // 可选 - 每页数量 (默认20, 最大100)
	BaseOnly        bool   `json:"base_only"`                   // 必填 - 是否只拉取基本信息 (true:仅基本信息, false:完整信息)
}

// NoteListResponse 获取笔记列表响应结构
type NoteListResponse struct {
	Code    int    `json:"code"`    // 返回码
	Msg     string `json:"msg"`     // 返回信息
	Success bool   `json:"success"` // 请求是否成功
	Data    struct {
		Total int            `json:"total"` // 笔记总数
		Notes []BaseNoteItem `json:"notes"` // 笔记列表
	} `json:"data"`
}

// BaseNoteItem 笔记基本信息结构
type BaseNoteItem struct {
	NoteID                 string         `json:"note_id"`                  // 笔记ID
	Image                  string         `json:"image"`                    // 笔记封面图
	Desc                   string         `json:"desc"`                     // 笔记内容
	CreateTime             int64          `json:"create_time"`              // 创建时间(毫秒时间戳)
	Author                 string         `json:"author"`                   // 作者名称
	AuthorImage            string         `json:"author_image"`             // 作者头像
	Status                 int            `json:"status"`                   // 笔记状态 (0:正常, 1:不匹配, 2:非法, 3:删除等)
	NoteContentType        int            `json:"note_content_type"`        // 笔记类型 (1:图文, 2:视频)
	CooperateState         bool           `json:"cooperate_state"`          // 是否合作笔记
	Title                  string         `json:"title"`                    // 笔记标题
	ImageList              []string       `json:"image_list"`               // 封面图片列表
	CooperateComponentType int            `json:"cooperate_component_type"` // 合作组件类型 (1-6, 0表示无意义)
	CrowdCreationNote      bool           `json:"crowd_creation_note"`      // 是否为共创笔记
	ItemID                 string         `json:"item_id"`                  // 商品ID (base_only=false时返回)
	ReadCount              int            `json:"read_count"`               // 阅读数
	ReadRate               string         `json:"read_rate"`                // 阅读率
	InteractCount          int            `json:"interact_count"`           // 互动数
	InteractRate           string         `json:"interact_rate"`            // 互动率
	HighQuality            int            `json:"high_quality"`             // 是否优质笔记 (0:否, 1:是)
	HighPotential          int            `json:"high_potential"`           // 是否高潜笔记 (0:否, 1:是)
	OutsideShopVisit       int            `json:"outside_shop_visit"`       // 站外进店量
	OutsideShopVisitRate   string         `json:"outside_shop_visitRate"`   // 站外进店率
	ItemIDs                []string       `json:"item_ids"`                 // 绑定的商品ID集合
	NoteMultiSpuInfo       []NoteMultiSpu `json:"note_multi_spu_info"`      // 多SPU绑定信息
	Taxonomy1              string         `json:"taxonomy1"`                // 一级类目
	Taxonomy2              string         `json:"taxonomy2"`                // 二级类目
	Taxonomy3              string         `json:"taxonomy3"`                // 三级类目
	IsHitStrategy          int            `json:"is_hit_strategy"`          // 是否命中策略 (0:未命中, 1:命中)
	HitStrategyContent     string         `json:"hit_strategy_content"`     // 命中策略内容
	WinHorseNote           bool           `json:"win_horse_note"`           // 是否为优胜笔记
	StaffTag               string         `json:"staff_tag"`                // 员工标签 (仅员工笔记)
	StaffArea              string         `json:"staff_area"`               // 地域 (仅员工笔记)
	NoteURL                string         `json:"note_url"`                 // 笔记链接 (有效期两个月)
	HasShopCard            bool           `json:"has_shop_card"`            // 是否为商品笔记
}

// NoteMultiSpu 笔记绑定的多SPU信息
type NoteMultiSpu struct {
	BindID                 int64  `json:"bind_id"`                       // 绑定ID
	SpuID                  string `json:"spu_id"`                        // SPU ID
	SpuName                string `json:"spu_name"`                      // SPU名称
	ExceedModifyLimit30Day bool   `json:"exceed_modify_limit_in_30_day"` // 30天内修改超过限制
	ExceedModifyLimitToday bool   `json:"exceed_modify_limit_today"`     // 当天修改超过限制
	BindByCurAccount       bool   `json:"bind_by_cur_account"`           // 当前账户绑定
	BindAuditStatus        int    `json:"bind_audit_status"`             // 绑定审核状态 (0:未绑定,1:申请中,2:通过,3:拒绝,4:等待)
	BindAuditReason        string `json:"bind_audit_reason"`             // 审核拒绝原因
	SpuType                int    `json:"spu_type"`                      // SPU类型 (1:标品,2:非标品,3:品牌,4:系列)
	SpuSubName             string `json:"spu_sub_name"`                  // 非标/品牌名称
	SeriesID               string `json:"series_id"`                     // 系列ID
}

// ==================== 创意层级离线报表相关类型 ====================

// ReportRequest 报表请求参数
type ReportRequest struct {
	// 必填参数
	AdvertiserID int64  `json:"advertiser_id"` // 广告主ID
	StartDate    string `json:"start_date"`    // 开始时间，格式 yyyy-MM-dd
	EndDate      string `json:"end_date"`      // 结束时间，格式 yyyy-MM-dd

	// 可选参数
	TimeUnit        string   `json:"time_unit,omitempty"`        // 时间维度: "DAY"(分天), "HOUR"(分时), "SUMMARY"(汇总)
	MarketingTarget []int    `json:"marketing_target,omitempty"` // 营销目标过滤
	BiddingStrategy []int    `json:"bidding_strategy,omitempty"` // 出价方式过滤
	OptimizeTarget  []int    `json:"optimize_target,omitempty"`  // 推广目标过滤
	Placement       []int    `json:"placement,omitempty"`        // 广告类型过滤
	PromotionTarget []int    `json:"promotion_target,omitempty"` // 推广标的类型过滤
	Programmatic    []int    `json:"programmatic,omitempty"`     // 创意组合方式过滤
	BuildType       []int    `json:"build_type,omitempty"`       // 搭建方式过滤
	DeliveryMode    []int    `json:"delivery_mode,omitempty"`    // 投放模式过滤
	SplitColumns    []string `json:"split_columns,omitempty"`    // 细分条件
	SortColumn      string   `json:"sort_column,omitempty"`      // 排序字段
	Sort            string   `json:"sort,omitempty"`             // 排序方式: "asc"(升序), "desc"(降序)
	PageNum         int      `json:"page_num,omitempty"`         // 页数，默认1
	PageSize        int      `json:"page_size,omitempty"`        // 页大小，默认20，最大500
	DataCaliber     int      `json:"data_caliber,omitempty"`     // 数据指标归因时间类型: 0-点击时间, 1-转化时间
	Filters         []Filter `json:"filters,omitempty"`          // 过滤条件
	NoteUserID      string   `json:"note_user_id,omitempty"`     // 笔记作者ID
}

// Filter 过滤条件
type Filter struct {
	Column   string   `json:"column"`   // 筛选列名: creativityId, fee, impression等
	Operator string   `json:"operator"` // 操作符: ">", "<", "in"
	Values   []string `json:"values"`   // 筛选值
}

// ReportResponse 报表API响应
type ReportResponse struct {
	Data struct {
		DataList        []ReportData `json:"data_list"`        // 报表数据列表
		AggregationData ReportData   `json:"aggregation_data"` // 汇总数据
		TotalCount      int64        `json:"total_count"`      // 总记录数
	} `json:"data"`
	RequestID string `json:"request_id"` // 请求ID
	Code      int    `json:"code"`       // 返回码
	Success   bool   `json:"success"`    // 是否成功
	Msg       string `json:"msg"`        // 返回信息
}
