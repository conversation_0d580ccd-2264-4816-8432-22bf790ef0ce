package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"net/http"

	"github.com/gin-gonic/gin"
)

// UploadController 上传控制器
type UploadController struct {
	ossService *service.OSSService
}

// NewUploadController 创建上传控制器
func NewUploadController() *UploadController {
	return &UploadController{
		ossService: service.NewOSSService(),
	}
}

// Upload 上传文件
// @Summary 上传文件
// @Description 上传文件到OSS
// @Tags 上传管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "文件"
// @Success 200 {object} response.Response{data=vo.UploadResp}
// @Router /api/v1/upload [post]
func (c *UploadController) Upload(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}

	// 上传文件到OSS
	url, err := c.ossService.UploadFile(file)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "上传文件失败: "+err.Error())
		return
	}

	// 返回文件URL
	response.Success(ctx, vo.UploadResp{
		URL: url,
	})
}

// UploadImage 上传图片
// @Summary 上传图片
// @Description 上传图片到OSS
// @Tags 上传管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "图片文件"
// @Success 200 {object} response.Response{data=vo.UploadImageResp}
// @Router /api/v1/upload/image [post]
func (c *UploadController) UploadImage(ctx *gin.Context) {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "获取上传文件失败: "+err.Error())
		return
	}

	// 上传图片到OSS
	url, err := c.ossService.UploadImage(file)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "上传图片失败: "+err.Error())
		return
	}

	// 返回图片URL
	response.Success(ctx, vo.UploadImageResp{
		URL: url,
	})
}
