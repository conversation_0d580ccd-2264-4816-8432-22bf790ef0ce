package model

import (
	"time"

	"gorm.io/gorm"
)

// AdSlotPlanDailyStats 广告位计划每日统计数据
type AdSlotPlanDailyStats struct {
	gorm.Model
	Date      time.Time `json:"date" gorm:"index;comment:日期"`
	UserID    uint      `json:"user_id" gorm:"index;comment:用户ID"`
	MediaID   uint      `json:"media_id" gorm:"index;comment:媒体ID"`
	PlanID    uint      `json:"plan_id" gorm:"index;comment:计划ID"`
	ProductID uint      `json:"product_id" gorm:"index;comment:产品ID"`
	AdSlotID  uint      `json:"ad_slot_id" gorm:"index;comment:广告位ID"`

	// 业务数据统计
	BdOrders        int     `json:"bd_orders" gorm:"default:0;comment:订单数量"`
	BdRevenue       float64 `json:"bd_revenue" gorm:"type:decimal(10,2);default:0.00;comment:预估佣金/收入"`
	BdSettleRevenue float64 `json:"bd_settle_revenue" gorm:"type:decimal(10,2);default:0.00;comment:结算佣金/收入"`
	Cost            float64 `json:"cost" gorm:"type:decimal(10,2);default:0.00;comment:成本"`
	BdProfit        float64 `json:"bd_profit" gorm:"type:decimal(10,2);default:0.00;comment:预估利润"`
	BdSettleProfit  float64 `json:"bd_settle_profit" gorm:"type:decimal(10,2);default:0.00;comment:结算利润"`

	// 流量数据统计
	ElmClickPV int `json:"elm_click_pv" gorm:"default:0;comment:页面访问次数"`
	ElmClickUV int `json:"elm_click_uv" gorm:"default:0;comment:页面访问人数"`
}

// TableName 指定表名
func (AdSlotPlanDailyStats) TableName() string {
	return "ad_slot_plan_daily_stats"
}
