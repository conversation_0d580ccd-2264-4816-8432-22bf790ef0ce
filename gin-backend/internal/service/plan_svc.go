package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/promotion"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PlanService struct {
	db               *gorm.DB
	dataScopeService *DataScopeService
	promotionClient  *promotion.Client
}

func NewPlanService() *PlanService {
	return &PlanService{
		db:               global.DB,
		dataScopeService: NewDataScopeService(),
		promotionClient:  promotion.NewClient(nil),
	}
}

// ========== 广告位计划相关方法 ==========

// CreateAdSlotPlan 创建广告位计划
func (s *PlanService) CreateAdSlotPlan(ctx context.Context, planEntity domain.AdSlotPlanEntity, userInfo domain.UserEntity) (domain.AdSlotPlanEntity, error) {
	// 生成唯一编号
	planCode := fmt.Sprintf("ASP%s%d", time.Now().Format("20060102"), rand.Intn(10000))

	// 创建模型
	plan := model.AdSlotPlan{
		Code:         planCode,
		Type:         planEntity.Type,
		AdSlotID:     planEntity.AdSlotID,
		UserID:       planEntity.UserID,
		StartDate:    planEntity.StartDate,
		MaterialType: int8(planEntity.MaterialType),
	}

	if !planEntity.EndDate.IsZero() {
		plan.EndDate = planEntity.EndDate
		plan.IsEndDateEnabled = true
	}

	// 审核状态初始值
	plan.AuditStatus = "pending"
	// 投放状态初始值
	plan.DeliveryStatus = "init"

	// 开启事务
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return domain.AdSlotPlanEntity{}, fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建计划
	if err := tx.Create(&plan).Error; err != nil {
		tx.Rollback()
		return domain.AdSlotPlanEntity{}, fmt.Errorf("创建计划失败: %v", err)
	}

	// 创建关联关系
	if len(planEntity.Rels) > 0 {
		for _, rel := range planEntity.Rels {
			planRel := model.PlanRelateRel{
				PlanID: int64(plan.ID),
				RelateType: func() int8 {
					relTypeInt, _ := strconv.Atoi(rel.RelateType)
					return int8(relTypeInt)
				}(),
				RelateID: rel.RelateID,
			}
			if err := tx.Create(&planRel).Error; err != nil {
				tx.Rollback()
				return domain.AdSlotPlanEntity{}, fmt.Errorf("创建关联关系失败: %v", err)
			}
		}
	}

	// 记录操作日志
	opLog := model.AdSlotPlanOperationLog{
		PlanID:        plan.ID,
		UserID:        userInfo.ID,
		Action:        "create",
		OperationDesc: "创建计划",
	}
	if err := tx.Create(&opLog).Error; err != nil {
		// 仅记录日志错误，不回滚事务
		zap.L().Error("记录操作日志失败", zap.Error(err))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return domain.AdSlotPlanEntity{}, fmt.Errorf("提交事务失败: %v", err)
	}

	// 创建成功，返回实体
	return domain.AdSlotPlanEntity{
		ID:           int64(plan.ID),
		Code:         plan.Code,
		Type:         plan.Type,
		UserID:       int64(plan.UserID),
		AdSlotID:     int64(plan.AdSlotID),
		StartDate:    plan.StartDate,
		EndDate:      plan.EndDate,
		MaterialType: int(plan.MaterialType),
	}, nil
}

// GetAdSlotPlan 获取广告位计划详情
func (s *PlanService) GetAdSlotPlan(ctx context.Context, id uint64, userInfo domain.UserEntity) (domain.AdSlotPlanEntity, error) {
	// 获取数据权限范围
	dataScope, _, err := s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "plan")
	if err != nil {
		return domain.AdSlotPlanEntity{}, fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 检查数据权限
	var plan model.AdSlotPlan
	query := s.db.WithContext(ctx)

	switch dataScope {
	case "all":
		// 全部数据权限，不添加过滤条件
	case "dept":
		// 部门数据权限
		query = query.Where("user_id IN (SELECT id FROM users WHERE department = ?)", userInfo.Department)
	default:
		// 个人数据权限
		query = query.Where("user_id = ?", userInfo.ID)
	}

	if err := query.Preload("User").First(&plan, id).Error; err != nil {
		return domain.AdSlotPlanEntity{}, fmt.Errorf("计划不存在: %v", err)
	}

	// 查询关联关系
	var rels []*model.PlanRelateRel
	if err := s.db.WithContext(ctx).Where("plan_id = ?", id).Find(&rels).Error; err != nil {
		return domain.AdSlotPlanEntity{}, fmt.Errorf("查询关联关系失败: %v", err)
	}

	// 将model转换为domain
	var planUserInfo domain.UserEntity
	if plan.User != nil {
		planUserInfo = domain.UserEntity{
			ID:       int64(plan.User.ID),
			RealName: plan.User.RealName,
		}
	}

	// 审核状态转换
	var auditStatus int
	switch plan.AuditStatus {
	case "pending":
		auditStatus = 1
	case "approved":
		auditStatus = 2
	case "rejected":
		auditStatus = 3
	}

	// 投放状态转换
	var deliveryStatus int
	switch plan.DeliveryStatus {
	case "init":
		deliveryStatus = 1
	case "configuring":
		deliveryStatus = 2
	case "wait":
		deliveryStatus = 3
	case "running":
		deliveryStatus = 4
	case "stopped":
		deliveryStatus = 5
	case "stopped_auto":
		deliveryStatus = 6
	case "completed":
		deliveryStatus = 7
	}

	endDate := plan.EndDate

	// 转换关联关系
	planRels := make([]domain.PlanRelateEntity, 0, len(rels))
	for _, rel := range rels {
		planRels = append(planRels, domain.PlanRelateEntity{
			ID:         int64(rel.ID),
			PlanID:     rel.PlanID,
			RelateType: strconv.Itoa(int(rel.RelateType)),
			RelateID:   rel.RelateID,
		})
	}

	return domain.AdSlotPlanEntity{
		ID:             int64(plan.ID),
		Code:           plan.Code,
		UserID:         int64(plan.UserID),
		AdSlotID:       int64(plan.AdSlotID),
		StartDate:      plan.StartDate,
		EndDate:        endDate,
		Type:           plan.Type,
		MaterialType:   int(plan.MaterialType),
		AuditStatus:    auditStatus,
		DeliveryStatus: deliveryStatus,
		User:           planUserInfo,
		Rels:           planRels,
	}, nil
}

// UpdateAdSlotPlan 更新广告位计划
func (s *PlanService) UpdateAdSlotPlan(ctx context.Context, planEntity domain.AdSlotPlanEntity, userInfo domain.UserEntity) error {
	// 使用事务
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先查询计划是否存在
	var plan model.AdSlotPlan
	if err := tx.First(&plan, planEntity.ID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("计划不存在: %v", err)
	}

	// 只允许更新素材类型和关联关系
	plan.MaterialType = int8(planEntity.MaterialType)

	// 更新计划
	if err := tx.Save(&plan).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新计划失败: %v", err)
	}

	// 更新关联关系：先删除所有现有关联，再添加新的关联
	if len(planEntity.Rels) > 0 {
		// 删除现有关联
		if err := tx.Where("plan_id = ?", planEntity.ID).Delete(&model.PlanRelateRel{}).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("删除关联关系失败: %v", err)
		}

		// 添加新的关联
		for _, rel := range planEntity.Rels {
			planRel := model.PlanRelateRel{
				PlanID: planEntity.ID,
				RelateType: func() int8 {
					relTypeInt, _ := strconv.Atoi(rel.RelateType)
					return int8(relTypeInt)
				}(),
				RelateID: rel.RelateID,
			}
			if err := tx.Create(&planRel).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("创建关联关系失败: %v", err)
			}
		}
	}

	// 记录操作日志
	opLog := model.AdSlotPlanOperationLog{
		PlanID:        planEntity.ID,
		UserID:        userInfo.ID,
		Action:        "update",
		OperationDesc: "更新计划",
	}
	if err := tx.Create(&opLog).Error; err != nil {
		// 仅记录日志错误，不回滚事务
		zap.L().Error("记录操作日志失败", zap.Error(err))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// DeleteAdSlotPlan 删除广告位计划
func (s *PlanService) DeleteAdSlotPlan(ctx context.Context, id int64, userInfo domain.UserEntity) error {
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 先查询计划是否存在
	var plan model.AdSlotPlan
	if err := tx.First(&plan, id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("计划不存在: %v", err)
	}

	// 删除关联关系
	if err := tx.Where("plan_id = ?", id).Delete(&model.PlanRelateRel{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除关联关系失败: %v", err)
	}

	// 删除计划
	if err := tx.Delete(&plan).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除计划失败: %v", err)
	}

	// 记录操作日志
	opLog := model.AdSlotPlanOperationLog{
		PlanID:        id,
		UserID:        userInfo.ID,
		Action:        "delete",
		OperationDesc: "删除计划",
	}
	if err := tx.Create(&opLog).Error; err != nil {
		// 仅记录日志错误，不回滚事务
		zap.L().Error("记录操作日志失败", zap.Error(err))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// ListAdSlotPlans 获取广告位计划列表
func (s *PlanService) ListAdSlotPlans(ctx context.Context, param domain.AdSlotPlanQueryParam, userInfo domain.UserEntity) ([]domain.AdSlotPlanEntity, int64, error) {
	// 构建查询条件
	query := s.db.WithContext(ctx).Model(&model.AdSlotPlan{})

	// 应用数据权限
	dataScope, _, err := s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "plan")
	if err != nil {
		return nil, 0, fmt.Errorf("获取数据权限失败: %w", err)
	}

	switch dataScope {
	case "all":
		// 全部数据权限，不添加过滤条件
	case "dept":
		// 部门数据权限
		query = query.Where("user_id IN (SELECT id FROM users WHERE department = ?)", userInfo.Department)
	default:
		// 个人数据权限
		query = query.Where("user_id = ?", userInfo.ID)
	}

	// 应用查询参数
	if param.Code != "" {
		query = query.Where("code LIKE ?", "%"+param.Code+"%")
	}
	if param.MediaID > 0 {
		query = query.Where("media_id = ?", param.MediaID)
	}
	if param.AdSlotID > 0 {
		query = query.Where("ad_slot_id = ?", param.AdSlotID)
	}
	if param.AuditStatus != "" {
		query = query.Where("audit_status = ?", param.AuditStatus)
	}
	if param.DeliveryStatus != "" {
		query = query.Where("delivery_status = ?", param.DeliveryStatus)
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询总数失败: %v", err)
	}

	// 分页查询
	offset := (param.Page - 1) * param.Size
	var plans []model.AdSlotPlan
	if err := query.Preload("User").Offset(offset).Limit(param.Size).Find(&plans).Error; err != nil {
		return nil, 0, fmt.Errorf("查询列表失败: %v", err)
	}

	// 查询所有关联的计划ID
	planIDs := make([]int64, 0, len(plans))
	for _, plan := range plans {
		planIDs = append(planIDs, int64(plan.ID))
	}

	// 查询关联关系
	var allRels []*model.PlanRelateRel
	if len(planIDs) > 0 {
		if err := s.db.WithContext(ctx).Where("plan_id IN ?", planIDs).Find(&allRels).Error; err != nil {
			return nil, 0, fmt.Errorf("查询关联关系失败: %v", err)
		}
	}

	// 构建计划ID到关联关系的映射
	relMap := make(map[int64][]domain.PlanRelateEntity)
	for _, rel := range allRels {
		planID := rel.PlanID
		if _, ok := relMap[planID]; !ok {
			relMap[planID] = make([]domain.PlanRelateEntity, 0)
		}

		// 进行类型转换
		domainRel := domain.PlanRelateEntity{
			ID:         int64(rel.ID),
			PlanID:     rel.PlanID,
			RelateType: strconv.Itoa(int(rel.RelateType)),
			RelateID:   rel.RelateID,
		}

		relMap[planID] = append(relMap[planID], domainRel)
	}

	// 转换为domain实体
	result := make([]domain.AdSlotPlanEntity, 0, len(plans))
	for _, plan := range plans {
		var userInfo domain.UserEntity
		if plan.User != nil {
			userInfo = domain.UserEntity{
				ID:       int64(plan.User.ID),
				RealName: plan.User.RealName,
			}
		}

		// 审核状态转换
		var auditStatus int
		switch plan.AuditStatus {
		case "pending":
			auditStatus = 1
		case "approved":
			auditStatus = 2
		case "rejected":
			auditStatus = 3
		}

		// 投放状态转换
		var deliveryStatus int
		switch plan.DeliveryStatus {
		case "init":
			deliveryStatus = 1
		case "configuring":
			deliveryStatus = 2
		case "wait":
			deliveryStatus = 3
		case "running":
			deliveryStatus = 4
		case "stopped":
			deliveryStatus = 5
		case "stopped_auto":
			deliveryStatus = 6
		case "completed":
			deliveryStatus = 7
		}

		endDate := plan.EndDate

		// 创建计划实体
		planEntity := domain.AdSlotPlanEntity{
			ID:             int64(plan.ID),
			Code:           plan.Code,
			UserID:         int64(plan.UserID),
			AdSlotID:       int64(plan.AdSlotID),
			StartDate:      plan.StartDate,
			EndDate:        endDate,
			Type:           plan.Type,
			MaterialType:   int(plan.MaterialType),
			AuditStatus:    auditStatus,
			DeliveryStatus: deliveryStatus,
			User:           userInfo,
		}

		// 添加关联关系
		if rels, ok := relMap[plan.ID]; ok {
			planEntity.Rels = rels
		}

		result = append(result, planEntity)
	}

	return result, total, nil
}

// ApproveAdSlotPlan 审核通过广告位计划
func (s *PlanService) ApproveAdSlotPlan(ctx context.Context, id int64, userInfo domain.UserEntity) error {
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询计划
	var plan model.AdSlotPlan
	if err := tx.First(&plan, id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("计划不存在: %v", err)
	}

	// 只有待审核状态的计划才能审核通过
	if plan.AuditStatus != "pending" {
		tx.Rollback()
		return fmt.Errorf("只有待审核状态的计划才能审核通过")
	}

	// 更新状态
	plan.AuditStatus = "approved"
	plan.DeliveryStatus = "configuring"
	if err := tx.Save(&plan).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("审核通过计划失败: %v", err)
	}

	// 记录操作日志
	opLog := model.AdSlotPlanOperationLog{
		PlanID:        id,
		UserID:        userInfo.ID,
		Action:        "approve",
		OperationDesc: "审核通过计划",
	}
	if err := tx.Create(&opLog).Error; err != nil {
		// 仅记录日志错误，不回滚事务
		zap.L().Error("记录操作日志失败", zap.Error(err))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// RejectAdSlotPlan 审核拒绝广告位计划
func (s *PlanService) RejectAdSlotPlan(ctx context.Context, id int64, reason string, userInfo domain.UserEntity) error {
	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询计划
	var plan model.AdSlotPlan
	if err := tx.First(&plan, id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("计划不存在: %v", err)
	}

	// 只有待审核状态的计划才能审核拒绝
	if plan.AuditStatus != "pending" {
		tx.Rollback()
		return fmt.Errorf("只有待审核状态的计划才能审核拒绝")
	}

	// 更新状态
	plan.AuditStatus = "rejected"
	plan.RejectReason = reason
	if err := tx.Save(&plan).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("审核拒绝计划失败: %v", err)
	}

	// 记录操作日志
	opLog := model.AdSlotPlanOperationLog{
		PlanID:        id,
		UserID:        userInfo.ID,
		Action:        "reject",
		OperationDesc: fmt.Sprintf("审核拒绝计划，原因：%s", reason),
	}
	if err := tx.Create(&opLog).Error; err != nil {
		// 仅记录日志错误，不回滚事务
		zap.L().Error("记录操作日志失败", zap.Error(err))
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	return nil
}

// UpdateDeliveryMode 更新投放模式
func (s *PlanService) UpdateDeliveryMode(ctx context.Context, id int64, mode int) error {
	if err := s.db.WithContext(ctx).Model(&model.AdSlotPlan{}).Where("id = ?", id).Update("delivery_mode", mode).Error; err != nil {
		return fmt.Errorf("更新投放模式失败: %v", err)
	}
	return nil
}

// GeneratePromotionLink 生成推广链接
func (s *PlanService) GeneratePromotionLink(ctx context.Context, id int64, productID int64) (domain.PromotionLinkEntity, error) {
	var plan model.AdSlotPlan
	if err := s.db.WithContext(ctx).First(&plan, id).Error; err != nil {
		return domain.PromotionLinkEntity{}, fmt.Errorf("计划不存在: %v", err)
	}

	// 调用SDK生成推广链接
	result, err := s.promotionClient.GeneratePromotionLink(uint64(id), uint64(productID))
	if err != nil {
		return domain.PromotionLinkEntity{}, err
	}

	// 更新计划的推广链接
	if err := s.db.WithContext(ctx).Model(&plan).Update("promotion_link", result.PromotionLink).Error; err != nil {
		return domain.PromotionLinkEntity{}, fmt.Errorf("更新推广链接失败: %v", err)
	}

	return domain.PromotionLinkEntity{
		PromotionLink:   result.PromotionLink,
		PromotionQrcode: result.PromotionQrcode,
	}, nil
}

// UpdatePromotionLink 更新推广链接
func (s *PlanService) UpdatePromotionLink(ctx context.Context, id int64, link string) error {
	if err := s.db.WithContext(ctx).Model(&model.AdSlotPlan{}).Where("id = ?", id).Update("promotion_link", link).Error; err != nil {
		return fmt.Errorf("更新推广链接失败: %v", err)
	}
	return nil
}

// GenerateShortURL 生成短链接
func (s *PlanService) GenerateShortURL(ctx context.Context, id int64) (string, error) {
	var plan model.AdSlotPlan
	if err := s.db.WithContext(ctx).First(&plan, id).Error; err != nil {
		return "", fmt.Errorf("计划不存在: %v", err)
	}

	if plan.PromotionLink == "" {
		return "", fmt.Errorf("推广链接不存在")
	}

	// 调用SDK生成短链接
	shortURL, err := s.promotionClient.GenerateShortURL(plan.PromotionLink)
	if err != nil {
		return "", err
	}

	// 更新计划的短链接
	if err := s.db.WithContext(ctx).Model(&plan).Update("short_url", shortURL).Error; err != nil {
		return "", fmt.Errorf("更新短链接失败: %v", err)
	}

	return shortURL, nil
}

// UpdateShortURL 更新短链接
func (s *PlanService) UpdateShortURL(ctx context.Context, id int64, url string) error {
	if err := s.db.WithContext(ctx).Model(&model.AdSlotPlan{}).Where("id = ?", id).Update("short_url", url).Error; err != nil {
		return fmt.Errorf("更新短链接失败: %v", err)
	}
	return nil
}

// UpdateMergeLinks 更新合并链接
func (s *PlanService) UpdateMergeLinks(ctx context.Context, id int64, mergeLinks model.MergeLinkJSON) error {
	if err := s.db.WithContext(ctx).Model(&model.AdSlotPlan{}).Where("id = ?", id).Update("merge_links", mergeLinks).Error; err != nil {
		return fmt.Errorf("更新合并链接失败: %v", err)
	}
	return nil
}

// GetMergeLinks 获取合并链接
func (s *PlanService) GetMergeLinks(ctx context.Context, code string) (model.MergeLinkJSON, error) {
	var plan model.AdSlotPlan
	if err := s.db.WithContext(ctx).Where("code = ?", code).First(&plan).Error; err != nil {
		return nil, fmt.Errorf("计划不存在: %v", err)
	}
	return plan.MergeLinks, nil
}

// SendSettleProfitDingTalk 发送结算佣金钉钉通知
func (s *PlanService) SendSettleProfitDingTalk(ctx context.Context, planIds []uint64) error {
	zap.L().Info("开始发送结算佣金钉钉通知", zap.Uint64s("plan_ids", planIds))

	// 查询计划信息
	var plans []model.AdSlotPlan
	if err := s.db.WithContext(ctx).Where("id IN (?)", planIds).Find(&plans).Error; err != nil {
		zap.L().Error("获取计划信息失败", zap.Error(err))
		return err
	}

	// 查询结算佣金信息
	type PlanProfit struct {
		PlanID      int64   `gorm:"column:plan_id"`
		TotalProfit float64 `gorm:"column:total_profit"`
	}
	var profits []PlanProfit

	// 获取当前日期
	currentDate := time.Now().Format("2006-01-02")

	query := `
		SELECT plan_id, SUM(bd_settle_profit) as total_profit 
		FROM ad_slot_plan_daily_stats
		WHERE plan_id IN (?)
		AND date = ?
		GROUP BY plan_id
	`

	if err := s.db.WithContext(ctx).Raw(query, planIds, currentDate).Scan(&profits).Error; err != nil {
		zap.L().Error("获取结算佣金信息失败", zap.Error(err))
		return err
	}

	// 构建计划ID到结算佣金的映射
	profitMap := make(map[int64]float64)
	for _, profit := range profits {
		profitMap[profit.PlanID] = profit.TotalProfit
	}

	// 构建通知消息
	var messageBuilder strings.Builder
	for _, plan := range plans {
		profit := profitMap[plan.ID]
		messageBuilder.WriteString(fmt.Sprintf("%s，结算佣金：%.2f元\n", plan.Code, profit))
	}

	// 构建钉钉请求，正式
	webhook := "https://oapi.dingtalk.com/robot/send?access_token=7667a0baac31e4dc7f4573deb494cf4e415ef52d847ca3365f76357453f90a1b"
	secret := "SEC8d658796a2f21c8cb184e89f5df3918d72baab2bdd59247ee8ab5c4ffdf8d9c5"
	// 构建钉钉请求，测试
	// webhook := "https://oapi.dingtalk.com/robot/send?access_token=8fe94c3cba9ca877b17a151c0b4255db39aedd5616c8920984a255f9b195b34f"
	// secret := "SEC23ee56c51b0ad86d7778c3608948b28bb47f6e367e5e3923983fc20eb290a069"

	// 计算签名
	timestamp := time.Now().UnixMilli()
	// 按照文档要求，签名原始字符串是"timestamp\nsecret"，注意是时间戳+换行符+密钥
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, secret)

	// 使用 HmacSHA256 算法计算签名
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	sign := url.QueryEscape(base64.StdEncoding.EncodeToString(mac.Sum(nil)))

	// 构建请求URL
	webhookURL := fmt.Sprintf("%s&timestamp=%d&sign=%s", webhook, timestamp, sign)

	// 构建请求体
	reqBody := map[string]interface{}{
		"msgtype": "text",
		"text": map[string]string{
			"content": messageBuilder.String(),
		},
	}

	// 转换为JSON
	reqBodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		zap.L().Error("构建请求体失败", zap.Error(err))
		return err
	}

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, webhookURL, strings.NewReader(string(reqBodyBytes)))
	if err != nil {
		zap.L().Error("创建请求失败", zap.Error(err))
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		zap.L().Error("发送钉钉通知失败", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	// 解析响应
	var result struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		zap.L().Error("解析钉钉响应失败", zap.Error(err))
		return err
	}

	if result.Errcode != 0 {
		return fmt.Errorf("钉钉通知发送失败: %s", result.Errmsg)
	}

	zap.L().Info("结算佣金钉钉通知发送成功", zap.Uint64s("plan_ids", planIds))
	return nil
}

// GeneratePwdDailyReport 生成口令日报
func (s *PlanService) GeneratePwdDailyReport(ctx context.Context, start, end time.Time) error {
	zap.L().Info("开始生成口令日报", zap.Time("start", start), zap.Time("end", end))

	// 获取所有口令
	var pwds []struct {
		ID  uint64 `gorm:"column:id"`
		Pid string `gorm:"column:pid"`
	}

	if err := s.db.WithContext(ctx).Table("ad_search_words").Select("id, pid").Find(&pwds).Error; err != nil {
		zap.L().Error("获取口令失败", zap.Error(err))
		return fmt.Errorf("获取口令失败: %w", err)
	}

	if len(pwds) == 0 {
		zap.L().Info("没有找到口令，跳过生成日报")
		return nil
	}

	// 提取PID列表
	pids := make([]string, 0, len(pwds))
	pidMap := make(map[string]uint64)
	for _, pwd := range pwds {
		pids = append(pids, pwd.Pid)
		pidMap[pwd.Pid] = pwd.ID
	}

	// 处理每一天的数据
	for day := start; !day.After(end); day = day.Add(24 * time.Hour) {
		dateStr := day.Format("2006-01-02")

		// 从 byn_data.elm_ad_zone_reports 表获取UV数据
		type UVStat struct {
			Pid      string  `gorm:"column:pid"`
			Pv       int64   `gorm:"column:pv"`
			Uv       int64   `gorm:"column:uv"`
			ClickPv  int64   `gorm:"column:click_pv"`
			ClickUv  int64   `gorm:"column:click_uv"`
			OrderNum int64   `gorm:"column:order_num"`
			Income   float64 `gorm:"column:income"`
			Settle   float64 `gorm:"column:settle"`
		}

		var uvStats []UVStat
		if err := s.db.Table("byn_data.elm_ad_zone_reports").
			Select("pid, pv, uv, click_pv, click_uv, order_num, income, settle").
			Where("report_date = ?", dateStr).
			Where("pid IN ?", pids).
			Find(&uvStats).Error; err != nil {
			zap.L().Warn("获取UV数据失败", zap.Error(err), zap.String("date", dateStr))
			// 继续处理，不中断整个流程
		}

		// 构建PID到UV数据的映射
		uvStatsMap := make(map[string]UVStat)
		for _, stat := range uvStats {
			uvStatsMap[stat.Pid] = stat
		}

		// 从订单表获取实际订单数据
		type OrderStat struct {
			Pid      string  `gorm:"column:pid"`
			OrderNum int64   `gorm:"column:order_num"`
			Income   float64 `gorm:"column:income"`
			Settle   float64 `gorm:"column:settle"`
		}

		var orderStats []OrderStat
		cpsOrderName := fmt.Sprintf("cps_orders_%s", day.Format("200601"))
		query := `
			SELECT 
				pid, 
				COUNT(1) as order_num, 
				SUM(pre_commission) as income, 
				SUM(commission) as settle 
			FROM warehouse.` + cpsOrderName + ` 
			WHERE supplier_id = 104 
				AND create_time BETWEEN ? AND ? 
				AND pid IN ? 
				AND order_status IN (2,3,4) 
			GROUP BY pid
		`

		if err := s.db.Raw(query,
			dateStr+" 00:00:00",
			dateStr+" 23:59:59",
			pids,
		).Scan(&orderStats).Error; err != nil {
			zap.L().Warn("获取订单数据失败", zap.Error(err), zap.String("date", dateStr))
			// 继续处理，不中断整个流程
		}

		// 构建PID到订单数据的映射
		orderStatsMap := make(map[string]OrderStat)
		for _, stat := range orderStats {
			orderStatsMap[stat.Pid] = stat
		}

		// 整合数据并保存/更新到 ad_search_word_daily_stats 表
		for _, pid := range pids {
			uvStat, hasUV := uvStatsMap[pid]
			orderStat, hasOrder := orderStatsMap[pid]

			// 如果既没有UV数据也没有订单数据，则创建一个空记录
			if !hasUV && !hasOrder {
				continue
			}

			// 检查记录是否已存在
			var count int64
			if err := s.db.WithContext(ctx).
				Table("ad_search_word_daily_stats").
				Where("pid = ? AND date = ?", pid, dateStr).
				Count(&count).Error; err != nil {
				zap.L().Error("检查记录是否存在失败", zap.Error(err), zap.String("pid", pid), zap.String("date", dateStr))
				continue
			}

			// 准备数据
			data := map[string]interface{}{
				"pwd_id": pidMap[pid],
				"pid":    pid,
				"date":   dateStr,
			}

			if hasUV {
				data["pv"] = uvStat.Pv
				data["uv"] = uvStat.Uv
				data["click_pv"] = uvStat.ClickPv
				data["click_uv"] = uvStat.ClickUv
				data["order_num"] = uvStat.OrderNum
				data["income"] = uvStat.Income
				data["settle"] = uvStat.Settle
			}

			if hasOrder {
				data["actual_order_num"] = orderStat.OrderNum
				data["actual_income"] = orderStat.Income
				data["actual_settle"] = orderStat.Settle
			}

			// 保存或更新数据
			if count > 0 {
				// 更新现有记录
				if err := s.db.WithContext(ctx).
					Table("ad_search_word_daily_stats").
					Where("pid = ? AND date = ?", pid, dateStr).
					Updates(data).Error; err != nil {
					zap.L().Error("更新口令日报数据失败", zap.Error(err), zap.String("pid", pid), zap.String("date", dateStr))
				}
			} else {
				// 创建新记录
				if err := s.db.WithContext(ctx).
					Table("ad_search_word_daily_stats").
					Create(data).Error; err != nil {
					zap.L().Error("创建口令日报数据失败", zap.Error(err), zap.String("pid", pid), zap.String("date", dateStr))
				}
			}
		}
	}

	zap.L().Info("口令日报生成完成", zap.Time("start", start), zap.Time("end", end))
	return nil
}
