package model

import (
	"time"
)

// User 用户模型 - 对应数据库users表
type User struct {
	ID        int64     `gorm:"primarykey;column:id"` // 用户ID
	CreatedAt time.Time `gorm:"column:created_at"`    // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at"`    // 更新时间

	Name                string    `gorm:"column:name;size:255"`                               // 用户名
	RealName            string    `gorm:"column:real_name;size:255"`                          // 真实姓名
	Email               string    `gorm:"column:email;uniqueIndex;size:255"`                  // 邮箱
	Phone               string    `gorm:"column:phone;size:20"`                               // 手机号码
	EmailVerifiedAt     time.Time `gorm:"column:email_verified_at"`                           // 邮箱验证时间
	Password            string    `gorm:"column:password;size:255"`                           // 密码
	Role                int       `gorm:"column:role;default:1"`                              // 角色 1-媒介 2-运营 3-管理层 4-投手 5-财务 6-技术 7-产品 8-BI
	RoleID              int64     `gorm:"column:role_id"`                                     // 新角色系统ID
	DefaultWorkMode     string    `gorm:"column:default_work_mode;size:20;default:'traffic'"` // 默认工作模式：traffic-流量采买，ad-广告投放
	Department          string    `gorm:"column:department;size:100"`                         // 部门名称
	Position            string    `gorm:"column:position;size:100"`                           // 职位
	SupervisorID        int64     `gorm:"column:supervisor_id"`                               // 直属上级ID
	Status              int       `gorm:"column:status;default:1"`                            // 状态：1在职 2离职
	IsLocked            bool      `gorm:"column:is_locked;default:0"`                         // 是否锁定：0-正常，1-锁定
	LockReason          string    `gorm:"column:lock_reason;size:255"`                        // 锁定原因
	LastLoginAt         time.Time `gorm:"column:last_login_at"`                               // 最近登录时间
	LastLoginIP         string    `gorm:"column:last_login_ip;size:45"`                       // 最近登录IP
	RememberToken       string    `gorm:"column:remember_token;size:100"`                     // 记住密码令牌
	ForceChangePassword int       `gorm:"column:force_change_password;default:0"`             // 强制修改密码
	PasswordChangedAt   time.Time `gorm:"column:password_changed_at"`                         // 密码修改时间
	DingUserID          string    `gorm:"column:ding_user_id;size:255"`                       // 钉钉用户ID
	DingDeptID          string    `gorm:"column:ding_dept_id;size:255"`                       // 钉钉部门ID
	Remark              string    `gorm:"column:remark;type:text"`                            // 备注信息
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}
