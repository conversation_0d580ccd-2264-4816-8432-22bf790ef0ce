package model

import "time"

// PasswordStats  口令数据统计表
type PasswordStats struct {
	ID                int64     `json:"id"`                  //  自增主键id
	PasswordName      string    `json:"password_name"`       //  口令名称
	Consumption       float64   `json:"consumption"`         //  消费金额(元)
	ActualConsumption float64   `json:"actual_consumption"`  //  实际消费金额(元)
	Impressions       int64     `json:"impressions"`         //  展现量
	ClickRate         float64   `json:"click_rate"`          //  点击率(%)
	ClickCount        int64     `json:"click_count"`         //  点击量(整数形式)
	SearchCount       int64     `json:"search_count"`        //  搜索人数
	NewOrdersToday    int64     `json:"new_orders_today"`    //  今日新订单数
	OrderCostToday    float64   `json:"order_cost_today"`    //  今日订单成本(元)
	TotalOrders       int64     `json:"total_orders"`        //  累计订单数
	TotalIncome       float64   `json:"total_income"`        //  累计收入(元)
	TotalRecoveryRate float64   `json:"total_recovery_rate"` //  累计回收率(%)
	StatDate          time.Time `json:"stat_date"`           //  统计日期
	CreatedAt         time.Time `json:"created_at"`          //  创建时间
	UpdatedAt         time.Time `json:"updated_at"`          //  更新时间
	AccountId         int64     `json:"account_id"`          //  账号id
}

func (PasswordStats) TableName() string {
	return "password_stats"
}
