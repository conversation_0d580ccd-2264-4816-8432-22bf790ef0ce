package vo

import (
	"time"

	"gin-backend/internal/service/domain"
)

// PlatformGroupResp 广告组VO实体
type PlatformGroupResp struct {
	ID           int64     `json:"id"`
	PlatformType string    `json:"platform_type"`
	AgentID      int64     `json:"agent_id"`
	MediaID      int64     `json:"media_id"`
	DataID       string    `json:"data_id"`
	PlanID       int64     `json:"plan_id"`
	Name         string    `json:"name"`
	Remark       string    `json:"remark"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// PlatformGroupListReq 广告组列表请求
type PlatformGroupListReq struct {
	Page             int    `json:"page" form:"page"`
	PageSize         int    `json:"pageSize" form:"pageSize"`
	Size             int    `json:"size" form:"size"` // 兼容size参数
	AgentID          int64  `json:"agentId" form:"agentId"`
	MediaID          int64  `json:"mediaId" form:"mediaId"`
	MarketTargetName string `json:"marketTargetName" form:"marketTargetName"`
	PlanKeyword      string `json:"planKeyword" form:"planKeyword"`
	Keyword          string `json:"keyword" form:"keyword"`
	WithPlan         bool   `json:"withPlan" form:"withPlan"`
	PlatformType     string `json:"platformType" form:"platformType"`
}

// PlatformGroupCreateReq 创建广告组请求
type PlatformGroupCreateReq struct {
	Name         string `json:"name" binding:"required"`
	PlanID       int64  `json:"plan_id" binding:"required"`
	PlatformType string `json:"platform_type" binding:"required"`
	AgentID      int64  `json:"agent_id"`
	MediaID      int64  `json:"media_id"`
	DataID       string `json:"data_id"`
	Remark       string `json:"remark"`
}

// PlatformGroupUpdateReq 更新广告组请求
type PlatformGroupUpdateReq struct {
	ID           int64  `json:"id" binding:"required"`
	Name         string `json:"name"`
	Remark       string `json:"remark"`
	PlatformType string `json:"platform_type"`
}

// PlatformGroupDeleteReq 删除广告组请求
type PlatformGroupDeleteReq struct {
	ID int64 `json:"id" form:"id" binding:"required"`
}

// PlatformGroupItem 广告组列表项
type PlatformGroupItem struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	DataID       string `json:"data_id"`
	PlatformType string `json:"platform_type"`
	PlanName     string `json:"plan_name"`
	MediaName    string `json:"media_name"`
	AgentName    string `json:"agent_name"`
	Remark       string `json:"remark"`
	CreatedAt    string `json:"created_at"`
	UpdatedAt    string `json:"updated_at"`
}

// PlatformGroupListResp 广告组列表响应
type PlatformGroupListResp struct {
	List  []PlatformGroupItem `json:"list"`
	Total int64               `json:"total"`
}

// PlatformGroupCreateResp 创建广告组响应
type PlatformGroupCreateResp struct {
	ID int64 `json:"id"`
}

// PlatformGroupUpdateResp 更新广告组响应
type PlatformGroupUpdateResp struct {
	ID int64 `json:"id"`
}

// PlatformGroupDeleteResp 删除广告组响应
type PlatformGroupDeleteResp struct {
	ID int64 `json:"id"`
}

// ToQueryParam 将请求转换为查询参数
func (req PlatformGroupListReq) ToQueryParam() domain.PlatformGroupQueryParam {
	pageSize := req.PageSize
	if pageSize == 0 && req.Size > 0 {
		pageSize = req.Size
	}
	return domain.PlatformGroupQueryParam{
		Page:             req.Page,
		PageSize:         pageSize,
		AgentID:          req.AgentID,
		MediaID:          req.MediaID,
		MarketTargetName: req.MarketTargetName,
		PlanKeyword:      req.PlanKeyword,
		Keyword:          req.Keyword,
		WithPlan:         req.WithPlan,
		PlatformType:     req.PlatformType,
	}
}

// ToCreateEntity 将请求转换为创建实体
func (req PlatformGroupCreateReq) ToCreateEntity() domain.PlatformGroupCreateEntity {
	return domain.PlatformGroupCreateEntity{
		Name:         req.Name,
		PlanID:       req.PlanID,
		PlatformType: req.PlatformType,
		AgentID:      req.AgentID,
		MediaID:      req.MediaID,
		DataID:       req.DataID,
		Remark:       req.Remark,
	}
}

// ToUpdateEntity 将请求转换为更新实体
func (req PlatformGroupUpdateReq) ToUpdateEntity() domain.PlatformGroupUpdateEntity {
	return domain.PlatformGroupUpdateEntity{
		ID:           req.ID,
		Name:         req.Name,
		Remark:       req.Remark,
		PlatformType: req.PlatformType,
	}
}

// ToDeleteEntity 将请求转换为删除实体
func (req PlatformGroupDeleteReq) ToDeleteEntity() domain.PlatformGroupDeleteEntity {
	return domain.PlatformGroupDeleteEntity{
		ID: req.ID,
	}
}

// FromEntity 从实体转换为响应
func (resp *PlatformGroupResp) FromEntity(entity domain.PlatformGroupEntity) {
	resp.ID = entity.ID
	resp.PlatformType = entity.PlatformType
	resp.AgentID = entity.AgentID
	resp.MediaID = entity.MediaID
	resp.DataID = entity.DataID
	resp.PlanID = entity.PlanID
	resp.Name = entity.Name
	resp.Remark = entity.Remark
	resp.CreatedAt = entity.CreatedAt
	resp.UpdatedAt = entity.UpdatedAt
}

// FromPageEntity 从分页实体转换为分页响应
func (resp *PlatformGroupListResp) FromPageEntity(entity domain.PlatformGroupPageEntity) {
	resp.Total = entity.Total
	resp.List = make([]PlatformGroupItem, 0, len(entity.List))
	for _, item := range entity.List {
		resp.List = append(resp.List, PlatformGroupItem{
			ID:           item.ID,
			Name:         item.Name,
			DataID:       item.DataID,
			PlatformType: item.PlatformType,
			PlanName:     item.PlanName,
			MediaName:    item.MediaName,
			AgentName:    item.AgentName,
			Remark:       item.Remark,
			CreatedAt:    item.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:    item.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
}

// FromIDEntity 从ID实体转换为响应
func (resp *PlatformGroupCreateResp) FromIDEntity(entity domain.PlatformGroupIDEntity) {
	resp.ID = entity.ID
}

// FromIDEntity 从ID实体转换为响应
func (resp *PlatformGroupUpdateResp) FromIDEntity(entity domain.PlatformGroupIDEntity) {
	resp.ID = entity.ID
}

// FromIDEntity 从ID实体转换为响应
func (resp *PlatformGroupDeleteResp) FromIDEntity(entity domain.PlatformGroupIDEntity) {
	resp.ID = entity.ID
}
