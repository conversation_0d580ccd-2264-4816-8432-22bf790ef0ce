package bootstrap

import (
	"gin-backend/internal/config"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
)

type App struct {
	Engine *gin.Engine
	Job    *cron.Cron
}

// InitApp 初始化应用程序
func InitApp(configPath string) (*App, func(), error) {
	// 加载配置
	if err := config.LoadConfig(configPath); err != nil {
		return nil, nil, err
	}

	// 初始化日志
	RegisterLogger()

	// 初始化数据库
	dbClose, err := InitDB()
	if err != nil {
		return nil, nil, err
	}

	// 初始化Redis
	InitRedis()

	// 初始化web引擎
	web := CreateWeb()

	// 初始化定时任务
	job := CreateJob()

	// 创建清理函数
	cleanup := func() {
		if err := dbClose(); err != nil {
			log.Printf("关闭数据库连接失败: %v", err)
		}
	}

	// 返回初始化后的应用和清理函数
	return &App{
		Engine: web,
		Job:    job,
	}, cleanup, nil
}
