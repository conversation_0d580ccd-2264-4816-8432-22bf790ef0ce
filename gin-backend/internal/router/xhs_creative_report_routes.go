package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupXHSCreativeReportRoutes 设置小红书创意报表路由
func SetupXHSCreativeReportRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewXHSCreativeReportController()
	syncCtrl := controller.NewXHSReportSyncController()

	// 小红书创意报表相关路由
	xhsReports := router.Group("/xhs-creative-reports")
	{
		// 获取报表列表
		xhsReports.GET("", ctrl.GetReportList)

		// 获取报表统计信息
		xhsReports.GET("/stats", ctrl.GetReportStats)

		// 导出报表数据
		xhsReports.GET("/export", ctrl.ExportReports)

		// 获取报表详情
		xhsReports.GET("/:id", ctrl.GetReportDetail)
	}

	// 小红书报表同步相关路由
	xhsSync := router.Group("/xhs-reports/sync")
	{
		// 同步昨天的数据
		xhsSync.POST("/yesterday", syncCtrl.SyncYesterday)

		// 同步指定日期的数据
		xhsSync.POST("/date", syncCtrl.SyncSpecificDate)

		// 同步日期范围的数据
		xhsSync.POST("/range", syncCtrl.SyncDateRange)

		// 同步当前小时的实时数据
		xhsSync.POST("/realtime", syncCtrl.SyncRealtime)

		// 同步笔记列表数据
		xhsSync.POST("/notes", syncCtrl.SyncNotes)

		// 获取同步状态
		xhsSync.GET("/status", syncCtrl.GetSyncStatus)
	}
}

// RegisterXHSCreativeReportRoutes 注册小红书创意报表路由到主路由器
func RegisterXHSCreativeReportRoutes(r *gin.Engine) {
	// API v1 路由组
	v1 := r.Group("/api/v1")

	// 设置小红书创意报表路由
	SetupXHSCreativeReportRoutes(v1)
}
