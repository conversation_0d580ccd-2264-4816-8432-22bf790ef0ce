package job

import (
	"context"
	"gin-backend/internal/service"

	"go.uber.org/zap"
)

// PlanProfitNoticeJob 计划利润通知定时任务
type PlanProfitNoticeJob struct {
	planService *service.PlanService
}

// NewPlanProfitNoticeJob 创建计划利润通知定时任务
func NewPlanProfitNoticeJob() *PlanProfitNoticeJob {
	return &PlanProfitNoticeJob{
		planService: service.NewPlanService(),
	}
}

// Name 任务名称
func (j *PlanProfitNoticeJob) Name() string {
	return "plan-profit-notice"
}

// Run 任务执行
func (j *PlanProfitNoticeJob) Run() error {
	ctx := context.Background()

	// 固定的计划ID列表
	planIds := []uint64{
		192, 457, 199, 463, 491, 532, 531, 762, 761, 760, 793, 794, 830, 829,
	}

	if err := j.planService.SendSettleProfitDingTalk(ctx, planIds); err != nil {
		zap.L().Error("执行计划利润通知定时任务失败", zap.Error(err))
		return err
	}

	zap.L().Info("执行计划利润通知定时任务成功")
	return nil
}
