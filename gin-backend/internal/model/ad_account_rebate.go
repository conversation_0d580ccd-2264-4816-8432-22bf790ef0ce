package model

import "time"

// AdAccountRebate  投放账号比例表
type AdAccountRebate struct {
	ID            int64     `json:"id"`             //  主键id
	AccountId     int64     `json:"account_id"`     //  账号id
	Rate          float64   `json:"rate"`           //  返利比例
	EffectiveDate time.Time `json:"effective_date"` //  生效日期
	CreatedAt     time.Time `json:"created_at"`     //  创建时间
	UpdatedAt     time.Time `json:"updated_at"`     //  更新时间
}

func (m *AdAccountRebate) TableName() string {
	return "ad_account_rebate"
}
