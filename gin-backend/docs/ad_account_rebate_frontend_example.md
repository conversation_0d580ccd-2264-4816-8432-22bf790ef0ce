# 账号返利比例管理前端集成示例

## Vue.js 集成示例

### 1. 账号返利比例管理页面

```vue
<template>
  <div class="account-rebate-container">
    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-statistic title="总账号数" :value="stats.total_accounts" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="有效账号数" :value="stats.active_accounts" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="平均返利比例" :value="stats.average_rate" suffix="%" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="最高返利比例" :value="stats.max_rate" suffix="%" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="最低返利比例" :value="stats.min_rate" suffix="%" :precision="2" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="最近30天变更" :value="stats.recent_changes" />
        </el-col>
      </el-row>
    </div>

    <!-- 操作区域 -->
    <div class="action-section">
      <el-button type="primary" @click="showChangeDialog">变更返利比例</el-button>
      <el-button @click="showHistoryDialog">查看变更记录</el-button>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="账号名称">
          <el-input v-model="filterForm.account_name" placeholder="请输入账号名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 返利比例列表 -->
    <div class="table-section">
      <el-table :data="rebateList" v-loading="loading" stripe border>
        <el-table-column prop="account_name" label="账号名称" min-width="150" />
        <el-table-column prop="rate" label="返利比例" width="120" align="right">
          <template #default="scope">
            {{ scope.row.rate.toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column prop="effective_date" label="生效日期" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column prop="updated_at" label="更新时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleChange(scope.row)">修改</el-button>
            <el-button type="text" @click="handleViewHistory(scope.row)">历史</el-button>
            <el-button type="text" danger @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>

    <!-- 变更返利比例对话框 -->
    <el-dialog v-model="changeDialogVisible" title="变更返利比例" width="500px">
      <el-form :model="changeForm" :rules="changeRules" ref="changeFormRef" label-width="120px">
        <el-form-item label="账号" prop="account_id">
          <el-select v-model="changeForm.account_id" placeholder="请选择账号" filterable>
            <el-option
              v-for="account in accountOptions"
              :key="account.id"
              :label="account.name"
              :value="account.id">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="新返利比例" prop="new_rate">
          <el-input-number
            v-model="changeForm.new_rate"
            :min="0"
            :max="100"
            :precision="2"
            placeholder="请输入返利比例">
          </el-input-number>
          <span style="margin-left: 10px;">%</span>
        </el-form-item>
        
        <el-form-item label="生效日期" prop="effective_date">
          <el-date-picker
            v-model="changeForm.effective_date"
            type="date"
            placeholder="请选择生效日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="变更原因" prop="change_reason">
          <el-input
            v-model="changeForm.change_reason"
            type="textarea"
            :rows="3"
            placeholder="请输入变更原因"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
        
        <el-form-item label="操作人" prop="operator_name">
          <el-input v-model="changeForm.operator_name" placeholder="请输入操作人姓名" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="changeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleChangeConfirm" :loading="changeLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 变更记录对话框 -->
    <el-dialog v-model="historyDialogVisible" title="变更记录" width="1200px">
      <div class="history-filter">
        <el-form :model="historyFilter" inline>
          <el-form-item label="变更类型">
            <el-select v-model="historyFilter.change_type" placeholder="全部" clearable>
              <el-option label="新增返利比例" value="create" />
              <el-option label="修改返利比例" value="update" />
              <el-option label="删除返利比例" value="delete" />
            </el-select>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input v-model="historyFilter.operator_name" placeholder="请输入操作人" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchHistory">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table :data="historyList" v-loading="historyLoading" stripe>
        <el-table-column prop="account_name" label="账号名称" width="150" />
        <el-table-column prop="rate_change" label="比例变化" width="200" />
        <el-table-column prop="effective_date" label="生效日期" width="120" />
        <el-table-column prop="change_type_desc" label="变更类型" width="120" />
        <el-table-column prop="change_reason" label="变更原因" min-width="200" show-overflow-tooltip />
        <el-table-column prop="operator_name" label="操作人" width="100" />
        <el-table-column prop="created_at" label="变更时间" width="160" />
      </el-table>
      
      <div class="history-pagination">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.page_size"
          :total="historyPagination.total"
          layout="total, prev, pager, next"
          @current-change="fetchHistory">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getAccountRebateList,
  getAccountRebateStats,
  getAccountRebateHistory,
  changeAccountRebate,
  deleteAccountRebate,
  getAccountList
} from '@/api/account-rebate'

// 响应式数据
const loading = ref(false)
const changeLoading = ref(false)
const historyLoading = ref(false)
const rebateList = ref([])
const historyList = ref([])
const accountOptions = ref([])

// 统计信息
const stats = ref({
  total_accounts: 0,
  active_accounts: 0,
  average_rate: 0,
  max_rate: 0,
  min_rate: 0,
  recent_changes: 0
})

// 筛选表单
const filterForm = reactive({
  account_name: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 变更对话框
const changeDialogVisible = ref(false)
const changeForm = reactive({
  account_id: null,
  new_rate: null,
  effective_date: '',
  change_reason: '',
  operator_name: ''
})

const changeRules = {
  account_id: [{ required: true, message: '请选择账号', trigger: 'change' }],
  new_rate: [{ required: true, message: '请输入返利比例', trigger: 'blur' }],
  effective_date: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  operator_name: [{ required: true, message: '请输入操作人姓名', trigger: 'blur' }]
}

// 历史记录对话框
const historyDialogVisible = ref(false)
const historyFilter = reactive({
  change_type: '',
  operator_name: ''
})

const historyPagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 获取返利比例列表
const fetchRebateList = async () => {
  try {
    loading.value = true
    const params = {
      account_name: filterForm.account_name,
      page: pagination.page,
      page_size: pagination.page_size
    }
    
    const response = await getAccountRebateList(params)
    rebateList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取返利比例列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStats = async () => {
  try {
    const response = await getAccountRebateStats()
    stats.value = response.data
  } catch (error) {
    console.error('获取统计信息失败：', error)
  }
}

// 获取账号列表
const fetchAccountList = async () => {
  try {
    const response = await getAccountList()
    accountOptions.value = response.data
  } catch (error) {
    console.error('获取账号列表失败：', error)
  }
}

// 获取变更记录
const fetchHistory = async () => {
  try {
    historyLoading.value = true
    const params = {
      ...historyFilter,
      page: historyPagination.page,
      page_size: historyPagination.page_size
    }
    
    const response = await getAccountRebateHistory(params)
    historyList.value = response.data.list
    historyPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取变更记录失败：' + error.message)
  } finally {
    historyLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchRebateList()
}

// 重置
const handleReset = () => {
  filterForm.account_name = ''
  pagination.page = 1
  fetchRebateList()
}

// 显示变更对话框
const showChangeDialog = () => {
  resetChangeForm()
  changeDialogVisible.value = true
}

// 显示历史记录对话框
const showHistoryDialog = () => {
  historyDialogVisible.value = true
  fetchHistory()
}

// 修改返利比例
const handleChange = (row) => {
  resetChangeForm()
  changeForm.account_id = row.account_id
  changeForm.new_rate = row.rate
  changeDialogVisible.value = true
}

// 查看历史记录
const handleViewHistory = (row) => {
  historyFilter.account_id = row.account_id
  showHistoryDialog()
}

// 删除返利比例
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除账号"${row.account_name}"的返利比例吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteAccountRebate(row.account_id, {
      operator_name: '当前用户',
      reason: '手动删除'
    })
    
    ElMessage.success('删除成功')
    fetchRebateList()
    fetchStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 确认变更
const handleChangeConfirm = async () => {
  try {
    await changeFormRef.value.validate()
    changeLoading.value = true
    
    await changeAccountRebate(changeForm)
    
    ElMessage.success('变更成功')
    changeDialogVisible.value = false
    fetchRebateList()
    fetchStats()
  } catch (error) {
    ElMessage.error('变更失败：' + error.message)
  } finally {
    changeLoading.value = false
  }
}

// 重置变更表单
const resetChangeForm = () => {
  Object.assign(changeForm, {
    account_id: null,
    new_rate: null,
    effective_date: '',
    change_reason: '',
    operator_name: ''
  })
}

// 禁用日期（不能选择今天之前的日期）
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.page_size = size
  pagination.page = 1
  fetchRebateList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchRebateList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountList()
  fetchRebateList()
  fetchStats()
})
</script>

<style scoped>
.account-rebate-container {
  padding: 20px;
}

.stats-section {
  margin-bottom: 20px;
}

.action-section {
  margin-bottom: 20px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-section {
  background: white;
  border-radius: 4px;
}

.pagination-section {
  padding: 20px;
  text-align: right;
}

.history-filter {
  margin-bottom: 20px;
}

.history-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
```

### 2. API 服务文件

```javascript
// api/account-rebate.js
import request from '@/utils/request'

// 获取账号返利比例列表
export function getAccountRebateList(params) {
  return request({
    url: '/api/v1/account-rebates',
    method: 'get',
    params
  })
}

// 获取统计信息
export function getAccountRebateStats() {
  return request({
    url: '/api/v1/account-rebates/stats',
    method: 'get'
  })
}

// 获取变更记录
export function getAccountRebateHistory(params) {
  return request({
    url: '/api/v1/account-rebates/history',
    method: 'get',
    params
  })
}

// 变更返利比例
export function changeAccountRebate(data) {
  return request({
    url: '/api/v1/account-rebates/change',
    method: 'post',
    data
  })
}

// 删除返利比例
export function deleteAccountRebate(accountId, data) {
  return request({
    url: `/api/v1/account-rebates/${accountId}`,
    method: 'delete',
    data
  })
}

// 获取账号当前返利比例
export function getAccountCurrentRebate(accountId) {
  return request({
    url: `/api/v1/account-rebates/${accountId}/current`,
    method: 'get'
  })
}

// 获取变更类型列表
export function getChangeTypes() {
  return request({
    url: '/api/v1/account-rebates/change-types',
    method: 'get'
  })
}

// 获取账号列表（用于下拉选择）
export function getAccountList() {
  return request({
    url: '/api/v1/accounts',
    method: 'get'
  })
}
```

## 关键实现要点

### 1. 数据展示优化
- 统计信息卡片展示关键指标
- 返利比例格式化显示（保留2位小数）
- 变更记录显示比例变化描述

### 2. 用户交互优化
- 变更对话框支持新增和修改
- 生效日期限制（不能选择过去的日期）
- 删除操作需要确认

### 3. 数据验证
- 返利比例范围验证（0-100%）
- 必填字段验证
- 字符长度限制

### 4. 历史记录管理
- 支持按变更类型、操作人筛选
- 显示详细的变更信息
- 分页加载历史记录

这个前端示例提供了完整的账号返利比例管理界面，包括列表展示、变更操作、历史记录查看等功能，可以直接集成到现有的管理系统中。
