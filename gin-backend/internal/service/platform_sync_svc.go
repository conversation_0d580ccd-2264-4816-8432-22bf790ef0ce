package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/alipay"
	"gin-backend/pkg/alipay/report"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PlatformSyncService 平台数据同步服务
type PlatformSyncService struct {
	db *gorm.DB
}

// NewPlatformSyncService 创建平台数据同步服务
func NewPlatformSyncService() *PlatformSyncService {
	return &PlatformSyncService{
		db: global.DB,
	}
}

// SyncPlatformData 同步平台数据
func (s *PlatformSyncService) SyncPlatformData(ctx context.Context) error {
	// 1. 查询媒体数据
	var mediaList []model.Media
	if err := s.getMediaList(ctx, &mediaList); err != nil {
		zap.L().Error("获取媒体列表失败", zap.Error(err))
		return err
	}

	// 2. 遍历媒体数据
	for _, media := range mediaList {
		// 只处理灯火平台
		if media.PlatformConfig == "" || !strings.Contains(media.PlatformConfig, "denghuoplus") {
			continue
		}

		// 解析平台配置
		platformConfig, err := media.GetPlatformConfig()
		if err != nil {
			zap.L().Error("解析平台配置失败", zap.Error(err), zap.Int64("mediaId", media.ID))
			continue
		}

		if platformConfig.Platform != "denghuoplus" {
			continue
		}

		// 3. 获取代理数据
		var agent model.Agent
		agentID := media.AdAgentID

		if agentID == 0 {
			zap.L().Info("媒体未关联代理", zap.Int64("mediaId", media.ID))
			continue
		}

		if err := s.db.WithContext(ctx).Where("id = ?", agentID).First(&agent).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				continue
			}
			zap.L().Error("获取代理数据失败", zap.Error(err), zap.Int64("mediaId", media.ID))
			continue
		}

		// 检查代理状态和类型
		if agent.AuditStatus != "approved" {
			zap.L().Info("代理审核状态不是已通过", zap.Int64("mediaId", media.ID), zap.Int64("agentId", agent.ID))
			continue
		}
		if agent.Type != "delivery" {
			zap.L().Info("代理类型不是投放", zap.Int64("mediaId", media.ID), zap.Int64("agentId", agent.ID))
			continue
		}

		// 解析代理平台配置
		agentPlatformConfig, err := domain.GetPlatformConfigFromJSON(agent.PlatformConfig)
		if err != nil || agentPlatformConfig.Platform != "denghuoplus" {
			zap.L().Info("代理平台配置不是灯火", zap.Int64("mediaId", media.ID), zap.Int64("agentId", agent.ID))
			continue
		}

		now := time.Now()
		startDate := now.AddDate(0, 0, -6) // 6天前
		endDate := now

		// 4. 调用灯火接口获取数据
		err = s.syncDenghuoPlusData(ctx, media, agent, startDate, endDate)
		if err != nil {
			zap.L().Error("同步灯火数据失败", zap.Error(err), zap.Int64("mediaId", media.ID), zap.Int64("agentId", agent.ID))
			continue
		}
	}

	return nil
}

// getMediaList 获取媒体列表
func (s *PlatformSyncService) getMediaList(ctx context.Context, mediaList *[]model.Media) error {
	return s.db.WithContext(ctx).Where(map[string]interface{}{
		"cooperation_type":   "dh",
		"audit_status":       "approved",
		"cooperation_status": "active",
	}).Find(mediaList).Error
}

// syncDenghuoPlusData 同步灯火数据
func (s *PlatformSyncService) syncDenghuoPlusData(
	ctx context.Context,
	media model.Media,
	agent model.Agent,
	startDate time.Time,
	endDate time.Time,
) error {
	// 定义需要查询的维度
	adLevels := []string{"PLAN", "GROUP", "CREATIVE"}

	// 解析平台配置
	platformConfig, err := media.GetPlatformConfig()
	if err != nil {
		return fmt.Errorf("解析平台配置失败: %w", err)
	}

	// 遍历每个维度进行数据同步
	for _, level := range adLevels {
		pageSize := 100
		currentPage := 1
		totalCount := 0

		// 获取已存在的数据ID集合
		existingIds, err := s.getExistingIds(ctx, level)
		if err != nil {
			zap.L().Error("获取已存在的数据失败", zap.Error(err), zap.String("level", level))
			continue
		}

		for {
			resp := &report.AdReportQueryResponse{}

			if strings.Contains(media.Name, "邦道") {
				// 获取merchantMark
				principalTag := ""
				if platformConfig.Info != nil {
					infoMap, ok := platformConfig.Info.(map[string]interface{})
					if ok {
						if mark, ok := infoMap["merchant_mark"].(string); ok && mark != "" {
							principalTag = mark
						}
					}
				}

				if principalTag == "" {
					principalTag = "7952308912734f3598b6fc48fd6def94"
				}

				params := map[string]interface{}{
					"ad_level":      level,
					"query_type":    "DETAIL",
					"principal_tag": principalTag,
					"page_size":     pageSize,
					"current":       currentPage,
					"start_date":    startDate.Format("20060102"),
					"end_date":      endDate.Format("20060102"),
				}

				jsonParams, err := json.Marshal(params)
				if err != nil {
					zap.L().Error("序列化参数失败", zap.Error(err))
					continue
				}

				httpResp, err := http.Post(
					"https://test-napi.bangdao-tech.com/api/authority/admin/xdelm/data/query",
					"application/json",
					bytes.NewReader(jsonParams),
				)
				if err != nil {
					zap.L().Error("调用邦道获取数据失败", zap.Error(err))
					continue
				}
				defer httpResp.Body.Close()

				body, err := io.ReadAll(httpResp.Body)
				if err != nil {
					zap.L().Error("读取邦道返回的响应失败", zap.Error(err))
					continue
				}

				type Result struct {
					Code    string `json:"code"`
					Msg     string `json:"msg"`
					SubCode any    `json:"subCode"`
					SubMsg  any    `json:"subMsg"`
					Data    string `json:"body"`
				}

				var result Result
				if err = json.Unmarshal(body, &result); err != nil {
					zap.L().Error("反序列化邦道返回的响应失败", zap.Error(err))
					continue
				}

				if result.Code != "10000" {
					zap.L().Error("请求失败业务错误",
						zap.String("msg", result.Msg),
						zap.String("response", string(body)),
					)
					continue
				}

				// 解析data字段
				if err = json.Unmarshal([]byte(result.Data), resp); err != nil {
					zap.L().Error("解析邦道数据失败", zap.Error(err))
					continue
				}
			} else {
				// 处理其他平台的请求
				req := report.NewAdReportQueryRequest(startDate, endDate)

				// 从agent平台配置中获取token和pid
				agentPlatformConfig, err := domain.GetPlatformConfigFromJSON(agent.PlatformConfig)
				if err != nil {
					zap.L().Error("解析代理平台配置失败", zap.Error(err))
					continue
				}

				infoMap := agentPlatformConfig.Info

				token, ok := infoMap["token"].(string)
				if !ok {
					zap.L().Error("代理平台配置中缺少token")
					continue
				}

				pid, ok := infoMap["pid"].(string)
				if !ok {
					zap.L().Error("代理平台配置中缺少pid")
					continue
				}

				req.BizToken = token
				req.AlipayPid = pid
				req.AdLevel = level
				req.QueryType = "DETAIL"

				// 从media平台配置中获取merchantMark
				mediaPlatformConfig, err := domain.GetPlatformConfigFromJSON(media.PlatformConfig)
				if err != nil {
					zap.L().Error("解析媒体平台配置失败", zap.Error(err))
					continue
				}

				mediaInfoMap := mediaPlatformConfig.Info

				merchantMark, _ := mediaInfoMap["merchant_mark"].(string)
				req.PrincipalTag = merchantMark

				req.PageSize = pageSize
				req.Current = currentPage

				// 获取appId、privateKey和publicKey
				appId, _ := mediaInfoMap["app_id"].(string)
				privateKey, _ := mediaInfoMap["private_key"].(string)
				publicKey, _ := mediaInfoMap["public_key"].(string)

				// 调用接口获取数据
				config := &alipay.Config{
					AppId:      appId,
					PrivateKey: privateKey,
					PublicKey:  publicKey,
					IsProd:     true,
				}
				client := alipay.NewClient(config)
				err = client.Execute(req.GetMethod(), req, resp)
				if err != nil {
					zap.L().Error("获取维度数据失败", zap.Error(err), zap.String("level", level))
					break
				}
			}

			// 处理响应数据
			items := resp.Response.Items
			total := resp.Response.Total
			if total > 0 {
				totalCount = total
			}

			zap.L().Info("获取灯火数据成功",
				zap.String("level", level),
				zap.Int("page", currentPage),
				zap.Int("total", totalCount),
				zap.Int("size", len(items)))

			// 3. 处理数据
			for _, item := range items {
				var belongId int64
				var err error

				// 检查基础数据是否已存在
				if _, exists := existingIds[item.DataID]; !exists {
					// 只有不存在时才插入基础数据
					switch level {
					case "PLAN":
						belongId, err = s.upsertPlan(ctx, media.ID, agent.ID, &item)
					case "GROUP":
						belongId, err = s.upsertGroup(ctx, media.ID, agent.ID, &item)
					case "CREATIVE":
						belongId, err = s.upsertCreative(ctx, media.ID, agent.ID, &item)
					}

					if err != nil {
						zap.L().Error("保存数据失败", zap.Error(err), zap.String("level", level))
						continue
					}
				} else {
					// 如果基础数据已存在，直接获取ID
					belongId = s.getExistingId(ctx, level, item.DataID)
				}

				if belongId == 0 {
					zap.L().Error("获取数据ID失败", zap.String("dataId", item.DataID), zap.String("level", level))
					continue
				}

				// 保存报表数据(每次都更新)
				err = s.upsertReport(ctx, belongId, strings.ToLower(level), &item)
				if err != nil {
					zap.L().Error("保存报表数据失败", zap.Error(err), zap.String("level", level))
					continue
				}

				// 保存转化数据(每次都更新)
				err = s.upsertConversions(ctx, belongId, strings.ToLower(level), &item)
				if err != nil {
					zap.L().Error("保存转化数据失败", zap.Error(err), zap.String("level", level))
					continue
				}

				// 将处理过的ID添加到已存在集合
				existingIds[item.DataID] = struct{}{}
			}

			// 判断是否还有下一页
			if len(items) < pageSize || currentPage*pageSize >= totalCount {
				zap.L().Info("完成同步维度数据", zap.String("level", level), zap.Int("totalCount", totalCount))
				break
			}

			// 下一页
			currentPage++
		}
	}

	return nil
}

// getExistingIds 获取已存在的数据ID集合
func (s *PlatformSyncService) getExistingIds(ctx context.Context, level string) (map[string]struct{}, error) {
	result := make(map[string]struct{})
	var rows []struct {
		DataID string `gorm:"column:data_id"`
	}

	var tableName string
	switch level {
	case "PLAN":
		tableName = "platform_plans"
	case "GROUP":
		tableName = "platform_groups"
	case "CREATIVE":
		tableName = "platform_creatives"
	default:
		return result, fmt.Errorf("未知的数据维度: %s", level)
	}

	err := s.db.WithContext(ctx).
		Table(tableName).
		Select("data_id").
		Where("platform_type = ?", "denghuoplus").
		Find(&rows).Error

	if err != nil {
		return nil, err
	}

	for _, row := range rows {
		result[row.DataID] = struct{}{}
	}

	return result, nil
}

// getExistingId 获取已存在数据的ID
func (s *PlatformSyncService) getExistingId(ctx context.Context, level string, dataId string) int64 {
	var id int64

	var tableName string
	switch level {
	case "PLAN":
		tableName = "platform_plans"
	case "GROUP":
		tableName = "platform_groups"
	case "CREATIVE":
		tableName = "platform_creatives"
	default:
		return 0
	}

	row := s.db.WithContext(ctx).
		Table(tableName).
		Select("id").
		Where("platform_type = ? AND data_id = ?", "denghuoplus", dataId).
		Row()

	if row.Scan(&id) != nil {
		return 0
	}

	return id
}

// upsertPlan 更新或插入计划数据
func (s *PlatformSyncService) upsertPlan(ctx context.Context, mediaId, agentId int64, item *report.Item) (int64, error) {
	data := map[string]interface{}{
		"agent_id":           agentId,
		"media_id":           mediaId,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.PlanName,
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
		"updated_at":         time.Now(),
	}

	var id int64
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 尝试查找现有记录
		var count int64
		if err := tx.Table("platform_plans").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			// 更新现有记录
			if err := tx.Table("platform_plans").
				Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
				Updates(data).Error; err != nil {
				return err
			}
		} else {
			// 创建新记录
			data["created_at"] = time.Now()
			if err := tx.Table("platform_plans").Create(data).Error; err != nil {
				return err
			}
		}

		// 获取ID
		var result struct {
			ID int64 `gorm:"column:id"`
		}
		if err := tx.Table("platform_plans").
			Select("id").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Scan(&result).Error; err != nil {
			return err
		}
		id = result.ID
		return nil
	})

	return id, err
}

// upsertGroup 更新或插入广告组数据
func (s *PlatformSyncService) upsertGroup(ctx context.Context, mediaId, agentId int64, item *report.Item) (int64, error) {
	// 先查询计划表获取真实的plan_id
	var planId int64
	err := s.db.WithContext(ctx).
		Table("platform_plans").
		Select("id").
		Where("platform_type = ? AND data_id = ?", "denghuoplus", item.PlanID).
		Scan(&planId).Error

	if err != nil {
		return 0, err
	}

	// 如果找不到对应的计划，则跳过
	if planId == 0 {
		zap.L().Info("找不到对应的计划数据", zap.String("planId", item.PlanID))
		return 0, nil
	}

	data := map[string]interface{}{
		"agent_id":           agentId,
		"media_id":           mediaId,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.GroupName,
		"plan_id":            planId, // 使用查询到的真实plan_id
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
		"updated_at":         time.Now(),
	}

	var id int64
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 尝试查找现有记录
		var count int64
		if err := tx.Table("platform_groups").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			// 更新现有记录
			if err := tx.Table("platform_groups").
				Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
				Updates(data).Error; err != nil {
				return err
			}
		} else {
			// 创建新记录
			data["created_at"] = time.Now()
			if err := tx.Table("platform_groups").Create(data).Error; err != nil {
				return err
			}
		}

		// 获取ID
		var result struct {
			ID int64 `gorm:"column:id"`
		}
		if err := tx.Table("platform_groups").
			Select("id").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Scan(&result).Error; err != nil {
			return err
		}
		id = result.ID
		return nil
	})

	return id, err
}

// upsertCreative 更新或插入广告创意数据
func (s *PlatformSyncService) upsertCreative(ctx context.Context, mediaId, agentId int64, item *report.Item) (int64, error) {
	// 先查询计划表获取真实的plan_id
	var planId int64
	err := s.db.WithContext(ctx).
		Table("platform_plans").
		Select("id").
		Where("platform_type = ? AND data_id = ?", "denghuoplus", item.PlanID).
		Scan(&planId).Error

	if err != nil {
		return 0, err
	}

	// 如果找不到对应的计划，则跳过
	if planId == 0 {
		zap.L().Info("找不到对应的计划数据", zap.String("planId", item.PlanID))
		return 0, nil
	}

	// 查询广告组表获取真实的group_id
	var groupId int64
	err = s.db.WithContext(ctx).
		Table("platform_groups").
		Select("id").
		Where("platform_type = ? AND data_id = ?", "denghuoplus", item.GroupID).
		Scan(&groupId).Error

	if err != nil {
		return 0, err
	}

	// 如果找不到对应的广告组，则跳过
	if groupId == 0 {
		zap.L().Info("找不到对应的广告组数据", zap.String("groupId", item.GroupID))
		return 0, nil
	}

	data := map[string]interface{}{
		"agent_id":           agentId,
		"media_id":           mediaId,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.CreativeName,
		"plan_id":            planId,  // 使用查询到的真实plan_id
		"group_id":           groupId, // 使用查询到的真实group_id
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
		"updated_at":         time.Now(),
	}

	var id int64
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 尝试查找现有记录
		var count int64
		if err := tx.Table("platform_creatives").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			// 更新现有记录
			if err := tx.Table("platform_creatives").
				Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
				Updates(data).Error; err != nil {
				return err
			}
		} else {
			// 创建新记录
			data["created_at"] = time.Now()
			if err := tx.Table("platform_creatives").Create(data).Error; err != nil {
				return err
			}
		}

		// 获取ID
		var result struct {
			ID int64 `gorm:"column:id"`
		}
		if err := tx.Table("platform_creatives").
			Select("id").
			Where("platform_type = ? AND data_id = ?", "denghuoplus", item.DataID).
			Scan(&result).Error; err != nil {
			return err
		}
		id = result.ID
		return nil
	})

	return id, err
}

// upsertReport 更新或插入报表数据
func (s *PlatformSyncService) upsertReport(ctx context.Context, belongId int64, belongType string, item *report.Item) error {
	data := map[string]interface{}{
		"belong_id":   belongId,
		"belong_type": belongType,
		"biz_date":    item.BizDate,
		"impression":  item.Impression,
		"click":       item.Click,
		"cost":        item.Cost,
		"updated_at":  time.Now(),
	}

	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查记录是否存在
		var count int64
		if err := tx.Table("platform_reports").
			Where("belong_id = ? AND belong_type = ? AND biz_date = ?", belongId, belongType, item.BizDate).
			Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			// 更新记录
			return tx.Table("platform_reports").
				Where("belong_id = ? AND belong_type = ? AND biz_date = ?", belongId, belongType, item.BizDate).
				Updates(data).Error
		} else {
			// 创建记录
			data["created_at"] = time.Now()
			return tx.Table("platform_reports").Create(data).Error
		}
	})
}

// upsertConversions 更新或插入转化数据
func (s *PlatformSyncService) upsertConversions(ctx context.Context, belongId int64, belongType string, item *report.Item) error {
	if len(item.ConversionDataList) == 0 {
		return nil
	}

	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, conv := range item.ConversionDataList {
			data := map[string]interface{}{
				"belong_id":            belongId,
				"belong_type":          belongType,
				"plan_id":              belongId, // 如果belongType是plan，则belongId就是planId
				"biz_date":             item.BizDate,
				"conversion_type":      conv.ConversionType,
				"conversion_type_name": conv.ConversionTypeName,
				"conversion_result":    conv.ConversionResult,
				"updated_at":           time.Now(),
			}

			// 检查记录是否存在
			var count int64
			if err := tx.Table("platform_conversions").
				Where("belong_id = ? AND belong_type = ? AND biz_date = ? AND conversion_type = ?",
					belongId, belongType, item.BizDate, conv.ConversionType).
				Count(&count).Error; err != nil {
				return err
			}

			if count > 0 {
				// 更新记录
				if err := tx.Table("platform_conversions").
					Where("belong_id = ? AND belong_type = ? AND biz_date = ? AND conversion_type = ?",
						belongId, belongType, item.BizDate, conv.ConversionType).
					Updates(data).Error; err != nil {
					return err
				}
			} else {
				// 创建记录
				data["created_at"] = time.Now()
				if err := tx.Table("platform_conversions").Create(data).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}
