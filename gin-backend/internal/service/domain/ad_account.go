package domain

import "time"

// 广告账号相关常量定义
const (
	// 账号类型
	AccountTypeMaster = 1 // 主账号
	AccountTypeSub    = 2 // 子账号

	// 平台类型
	PlatformXiaohongshu = 1 // 小红书
	PlatformDengHuo     = 2 // 灯火

	// 授权状态
	AuthStatusUnauthorized = 0 // 未授权
	AuthStatusAuthorized   = 1 // 已授权
	AuthStatusExpired      = 2 // 已过期
	AuthStatusInherited    = 3 // 继承授权

	// 使用状态
	UsageStatusEnabled  = 1 // 启用
	UsageStatusDisabled = 2 // 禁用
)

// GetAdAccountsParam 获取广告账号列表参数
type GetAdAccountsParam struct {
	Page                int
	PageSize            int
	AccountName         string // 账号名称/ID联合查询
	Platform            int64  // 平台筛选
	AuthorizationStatus int8   // 授权状态筛选
	UsageStatus         int8   // 使用状态筛选
	AccountType         int8   // 账号类型筛选
	ParentId            int64  // 父账号ID筛选
}

// GetAdAccountsResult 获取广告账号列表结果
type GetAdAccountsResult struct {
	List  []AdAccountEntity
	Total int64
	Page  int
	Size  int
}

// GetAdAccountsHierarchyResult 获取广告账号层级结构结果
type GetAdAccountsHierarchyResult struct {
	List  []AdAccountHierarchyEntity
	Total int64
	Page  int
	Size  int
}

// AdAccountHierarchyEntity 广告账号层级实体（主账号下挂子账号）
type AdAccountHierarchyEntity struct {
	AdAccountEntity                   // 嵌入主账号信息
	SubAccounts     []AdAccountEntity `json:"sub_accounts"` // 子账号列表
}

// AdAccountEntity 广告账号实体
type AdAccountEntity struct {
	ID                      int64
	AccountType             int8
	AccountTypeName         string
	ParentId                int64
	ParentAccountName       string
	Platform                int64
	PlatformName            string
	AccountName             string
	PlatformAccountId       string
	AuthorizationStatus     int8
	AuthorizationStatusName string
	Token                   string
	TokenExpireTime         time.Time
	UsageStatus             int8
	UsageStatusName         string
	AccountBalance          float64
	Owner                   string
	LastSync                time.Time
	CreatedAt               time.Time
	UpdatedAt               time.Time
	AccountEmail            string  //  账号邮箱
	MainCompany             string  // 账号主体名称
	MerchantMark            string  //  商户标记
	AccountAppId            string  //  账号appid
	PublicKey               string  //  公钥
	PrivateKey              string  //  私钥
	AdAgentId               int64   //  代理id
	UserId                  int64   //  投放运营id
	Remark                  string  //  备注
	MediaId                 int64   // 媒体ID
	AdSlotId                int64   // 广告位ID
	UserName                string  // 用户名
	AdAgentName             string  // 代理商名称
	AdSlotName              string  // 广告位名称
	MediaName               string  // 媒体名称
	RebateRate              float64 // 返利比例
}

// CreateAdAccountParam 创建广告账号参数
type CreateAdAccountParam struct {
	AccountType       int8
	ParentId          int64
	Platform          int64
	AccountName       string
	PlatformAccountId string
	Token             string
	TokenExpireTime   time.Time
	AccountBalance    float64
	RebateRate        float64
	Owner             string
}

// CreateAdAccountResult 创建广告账号结果
type CreateAdAccountResult struct {
	ID int64
}

// UpdateAdAccountParam 更新广告账号参数
type UpdateAdAccountParam struct {
	ID                int64
	AccountName       string
	PlatformAccountId string
	Token             string
	TokenExpireTime   time.Time
	UsageStatus       int8
	AccountBalance    float64
	RebateRate        float64
	Owner             string
}

// GetAdAccountOptionsResult 获取广告账号选项结果
type GetAdAccountOptionsResult struct {
	Platforms      []PlatformOption
	AccountTypes   []AccountTypeOption
	ParentAccounts []ParentAccountOption
}

// PlatformOption 平台选项
type PlatformOption struct {
	Value int64
	Label string
}

// AccountTypeOption 账号类型选项
type AccountTypeOption struct {
	Value int8
	Label string
}

// ParentAccountOption 父账号选项
type ParentAccountOption struct {
	Value int64
	Label string
}

// GetAccountTypeName 获取账号类型名称
func GetAccountTypeName(accountType int8) string {
	switch accountType {
	case AccountTypeMaster:
		return "主账号"
	case AccountTypeSub:
		return "子账号"
	default:
		return "未知"
	}
}

// GetPlatformName 获取平台名称
func GetPlatformName(platform int64) string {
	switch platform {
	case PlatformXiaohongshu:
		return "小红书"
	default:
		return "未知平台"
	}
}

// GetAuthorizationStatusName 获取授权状态名称
func GetAuthorizationStatusName(status int8) string {
	switch status {
	case AuthStatusUnauthorized:
		return "未授权"
	case AuthStatusAuthorized:
		return "已授权"
	case AuthStatusExpired:
		return "已过期"
	case AuthStatusInherited:
		return "继承授权"
	default:
		return "未知状态"
	}
}

// GetUsageStatusName 获取使用状态名称
func GetUsageStatusName(status int8) string {
	switch status {
	case UsageStatusEnabled:
		return "启用"
	case UsageStatusDisabled:
		return "禁用"
	default:
		return "未知状态"
	}
}

// IsTokenExpired 检查token是否过期
func (e *AdAccountEntity) IsTokenExpired() bool {
	if e.TokenExpireTime.IsZero() {
		return false // 如果没有设置过期时间，认为不过期
	}
	return time.Now().After(e.TokenExpireTime)
}

// CanCreateSubAccount 检查是否可以创建子账号
func (e *AdAccountEntity) CanCreateSubAccount() bool {
	return e.AccountType == AccountTypeMaster && e.UsageStatus == UsageStatusEnabled
}

// IsActive 检查账号是否活跃
func (e *AdAccountEntity) IsActive() bool {
	return e.UsageStatus == UsageStatusEnabled &&
		(e.AuthorizationStatus == AuthStatusAuthorized || e.AuthorizationStatus == AuthStatusInherited)
}

// GetEffectiveAuthStatus 获取有效的授权状态（考虑token过期）
func (e *AdAccountEntity) GetEffectiveAuthStatus() int8 {
	if e.AuthorizationStatus == AuthStatusAuthorized && e.IsTokenExpired() {
		return AuthStatusExpired
	}
	return e.AuthorizationStatus
}

// ValidateCreateParam 验证创建参数
func ValidateCreateAdAccountParam(param CreateAdAccountParam) error {
	if param.AccountType != AccountTypeMaster && param.AccountType != AccountTypeSub {
		return NewValidationError("账号类型必须是主账号或子账号")
	}

	if param.AccountType == AccountTypeSub && param.ParentId <= 0 {
		return NewValidationError("子账号必须指定父账号ID")
	}

	if param.Platform <= 0 {
		return NewValidationError("必须指定有效的平台")
	}

	if param.AccountName == "" {
		return NewValidationError("账号名称不能为空")
	}

	if param.PlatformAccountId == "" {
		return NewValidationError("平台账号ID不能为空")
	}

	if param.Owner == "" {
		return NewValidationError("归属人员不能为空")
	}

	return nil
}

// ValidateUpdateParam 验证更新参数
func ValidateUpdateAdAccountParam(param UpdateAdAccountParam) error {
	if param.ID <= 0 {
		return NewValidationError("账号ID必须大于0")
	}

	if param.AccountName == "" {
		return NewValidationError("账号名称不能为空")
	}

	if param.PlatformAccountId == "" {
		return NewValidationError("平台账号ID不能为空")
	}

	if param.UsageStatus != UsageStatusEnabled && param.UsageStatus != UsageStatusDisabled {
		return NewValidationError("使用状态必须是启用或禁用")
	}

	if param.Owner == "" {
		return NewValidationError("归属人员不能为空")
	}

	return nil
}

// ValidationError 验证错误
type ValidationError struct {
	Message string
}

func (e ValidationError) Error() string {
	return e.Message
}

// NewValidationError 创建验证错误
func NewValidationError(message string) error {
	return ValidationError{Message: message}
}

// ==================== 小红书授权相关类型 ====================

// XiaohongshuAuthParam 小红书授权参数
type XiaohongshuAuthParam struct {
	AccountID int64  `json:"account_id"` // 广告账号ID
	AuthCode  string `json:"auth_code"`  // 授权码
}

// XiaohongshuAuthResult 小红书授权结果
type XiaohongshuAuthResult struct {
	AccessToken           string `json:"access_token"`             // 访问令牌
	RefreshToken          string `json:"refresh_token"`            // 刷新令牌
	AccessTokenExpiresIn  int64  `json:"access_token_expires_in"`  // 访问令牌过期时间(秒)
	RefreshTokenExpiresIn int64  `json:"refresh_token_expires_in"` // 刷新令牌过期时间(秒)
	AdvertiserID          int64  `json:"advertiser_id"`            // 广告主ID
	AdvertiserName        string `json:"advertiser_name"`          // 广告主名称
}

// XiaohongshuRefreshTokenParam 小红书刷新令牌参数
type XiaohongshuRefreshTokenParam struct {
	AccountID int64 `json:"account_id"` // 广告账号ID
}

// XiaohongshuRefreshTokenResult 小红书刷新令牌结果
type XiaohongshuRefreshTokenResult struct {
	AccessToken           string `json:"access_token"`             // 新的访问令牌
	RefreshToken          string `json:"refresh_token"`            // 新的刷新令牌
	AccessTokenExpiresIn  int64  `json:"access_token_expires_in"`  // 访问令牌过期时间(秒)
	RefreshTokenExpiresIn int64  `json:"refresh_token_expires_in"` // 刷新令牌过期时间(秒)
}

// ValidateXiaohongshuAuthParam 验证小红书授权参数
func ValidateXiaohongshuAuthParam(param XiaohongshuAuthParam) error {
	if param.AccountID <= 0 {
		return NewValidationError("广告账号ID必须大于0")
	}

	if param.AuthCode == "" {
		return NewValidationError("授权码不能为空")
	}

	return nil
}

// ValidateXiaohongshuRefreshTokenParam 验证小红书刷新令牌参数
func ValidateXiaohongshuRefreshTokenParam(param XiaohongshuRefreshTokenParam) error {
	if param.AccountID <= 0 {
		return NewValidationError("广告账号ID必须大于0")
	}

	return nil
}

type CreateAdAccountByDengHuoParam struct {
	AccountType         int8      //  账号类型: 1-master主账号 2-sub子账号
	ParentId            int64     //  父账号id（子账号时使用）
	Platform            int64     //  所属平台 1-小红书
	AccountName         string    //  账号名称
	PlatformAccountId   string    //  平台账号id（如xhs_account_id）
	AuthorizationStatus int8      //  授权状态：0未授权 1-已授权 2-已过期 3-继承授权
	Token               string    //  token
	RefreshToken        string    //  新的刷新令牌
	TokenExpireTime     time.Time //  token过期时间
	UsageStatus         int8      //  使用状态：1-启用 2-禁用
	AccountBalance      float64   //  账户余额（最多百亿级）
	RebateRate          float64   // 返利比例
	Owner               string    //  归属人员
	LastSync            time.Time //  最后同步时间
	CreatedAt           time.Time //  创建时间
	UpdatedAt           time.Time //  更新时间
	AccountEmail        string    //  账号邮箱
	MainCompany         string    // 账号主体名称
	MerchantMark        string    //  商户标记
	AccountAppId        string    //  账号appid
	PublicKey           string    //  公钥
	PrivateKey          string    //  私钥
	AdAgentId           int64     //  代理id
	UserId              int64     //  投放运营id
	Remark              string    //  备注
	MediaId             int64     // 媒体ID
	AdSlotId            int64     // 广告位ID
}

type UpdateAdAccountByDengHuoParam struct {
	ID int64 `json:"id"`
	CreateAdAccountByDengHuoParam
}
