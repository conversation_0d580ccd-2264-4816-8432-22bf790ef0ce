-- ========================================
-- 周报表权限增量更新脚本
-- 添加周报表相关权限到现有权限系统
-- 执行时间：$(date '+%Y-%m-%d %H:%M:%S')
-- ========================================

-- 显示当前数据库
SELECT DATABASE() as '当前数据库';

-- 检查权限表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ permissions表存在'
        ELSE '✗ permissions表不存在，请先创建权限系统表'
    END as '表检查结果'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'permissions';

-- 检查role_permissions表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ role_permissions表存在'
        ELSE '✗ role_permissions表不存在，请先创建权限系统表'
    END as '表检查结果'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'role_permissions';

-- ========================================
-- 检查是否已经存在周报表权限
-- ========================================
SELECT '=== 检查现有周报表权限 ===' as '信息';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('⚠️  已存在 ', COUNT(*), ' 个周报表权限，将跳过重复插入')
        ELSE '✓ 未发现周报表权限，可以安全添加'
    END as '权限检查结果'
FROM permissions 
WHERE code LIKE 'report:weekly:%';

-- 显示现有周报表权限（如果有）
SELECT 
    id, name, code, description
FROM permissions 
WHERE code LIKE 'report:weekly:%'
ORDER BY id;

-- ========================================
-- 添加周报表权限（如果不存在）
-- ========================================
SELECT '=== 开始添加周报表权限 ===' as '信息';

-- 使用INSERT IGNORE防止重复插入
INSERT IGNORE INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('周报表查看', 'report:weekly:view', 'report', 'function', '查看周报表页面', NULL, 1, NOW(), NOW()),
('周报表数据查询', 'report:weekly:query', 'report', 'function', '查询周报表数据', NULL, 1, NOW(), NOW()),
('周报表详情查看', 'report:weekly:detail', 'report', 'function', '查看周报表详情数据', NULL, 1, NOW(), NOW()),
('周报表数据导出', 'report:weekly:export', 'report', 'function', '导出周报表数据', NULL, 1, NOW(), NOW()),
('周报表全部数据', 'report:weekly:data:all', 'report', 'data', '查看全部范围的周报表数据', 'all', 1, NOW(), NOW()),
('周报表部门数据', 'report:weekly:data:dept', 'report', 'data', '查看部门范围的周报表数据', 'dept', 1, NOW(), NOW()),
('周报表个人数据', 'report:weekly:data:self', 'report', 'data', '查看个人范围的周报表数据', 'self', 1, NOW(), NOW());

-- 显示插入结果
SELECT '=== 周报表权限添加完成 ===' as '信息';
SELECT 
    id, name, code, description
FROM permissions 
WHERE code LIKE 'report:weekly:%'
ORDER BY id;

-- ========================================
-- 为现有角色分配周报表权限
-- ========================================
SELECT '=== 开始分配角色权限 ===' as '信息';

-- 获取角色信息
SELECT '当前系统角色:' as '信息';
SELECT id, name, code FROM roles WHERE status = 1 ORDER BY sort_order;

-- 为超级管理员分配所有周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'super_admin' 
AND r.status = 1
AND p.code LIKE 'report:weekly:%'
AND p.status = 1;

-- 为管理层分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'manager'
AND r.status = 1
AND p.code LIKE 'report:weekly:%'
AND p.status = 1;

-- 为运营人员分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'operator'
AND r.status = 1
AND p.code IN ('report:weekly:view', 'report:weekly:query', 'report:weekly:detail', 'report:weekly:export', 'report:weekly:data:dept', 'report:weekly:data:self')
AND p.status = 1;

-- 为投手分配周报表权限（仅个人数据）
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'advertiser'
AND r.status = 1
AND p.code IN ('report:weekly:view', 'report:weekly:query', 'report:weekly:detail', 'report:weekly:data:self')
AND p.status = 1;

-- 为财务人员分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'finance'
AND r.status = 1
AND p.code LIKE 'report:weekly:%'
AND p.status = 1;

-- 为BI分析师分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'bi_analyst'
AND r.status = 1
AND p.code LIKE 'report:weekly:%'
AND p.status = 1;

-- 为媒介分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'media_specialist'
AND r.status = 1
AND p.code IN ('report:weekly:view', 'report:weekly:query', 'report:weekly:detail', 'report:weekly:data:dept', 'report:weekly:data:self')
AND p.status = 1;

-- 为产品经理分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'product'
AND r.status = 1
AND p.code LIKE 'report:weekly:%'
AND p.status = 1;

-- 为技术人员分配周报表权限
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'tech'
AND r.status = 1
AND p.code IN ('report:weekly:view', 'report:weekly:query', 'report:weekly:detail', 'report:weekly:export')
AND p.status = 1;

-- ========================================
-- 显示最终结果
-- ========================================
SELECT '=== 权限分配完成 ===' as '信息';

-- 显示各角色的周报表权限数量
SELECT 
    r.name as '角色名称',
    r.code as '角色编码',
    COUNT(rp.permission_id) as '周报表权限数量'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.code LIKE 'report:weekly:%'
WHERE r.status = 1
GROUP BY r.id, r.name, r.code
ORDER BY r.sort_order;

-- 显示具体的权限分配情况
SELECT 
    r.name as '角色',
    p.name as '权限名称',
    p.code as '权限编码',
    p.data_scope as '数据范围'
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.status = 1 
AND p.code LIKE 'report:weekly:%'
ORDER BY r.sort_order, p.code;

SELECT '✅ 周报表权限增量更新完成！' as '完成信息';
SELECT CONCAT('📊 共添加了 ', COUNT(*), ' 个周报表权限') as '统计信息'
FROM permissions 
WHERE code LIKE 'report:weekly:%'; 