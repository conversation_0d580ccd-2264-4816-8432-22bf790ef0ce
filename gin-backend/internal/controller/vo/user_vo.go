package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// GetUsersReq 获取用户列表请求
type GetUsersReq struct {
	Page       int    `form:"page" json:"page"`
	PageSize   int    `form:"page_size" json:"page_size"`
	Name       string `form:"name" json:"name"`
	Role       int    `form:"role" json:"role"`
	Status     int    `form:"status" json:"status"`
	Department string `form:"department" json:"department"`
	IsLocked   int    `form:"is_locked" json:"is_locked"`
}

// ToParam 转换为领域层参数
func (req GetUsersReq) ToParam() domain.GetUsersParam {
	return domain.GetUsersParam{
		Page:       req.Page,
		PageSize:   req.PageSize,
		Name:       req.Name,
		Role:       req.Role,
		Status:     req.Status,
		Department: req.Department,
		IsLocked:   req.IsLocked,
	}
}

// GetUsersResp 获取用户列表响应
type GetUsersResp struct {
	List  []UserDetailItem `json:"list"`
	Total int64            `json:"total"`
	Page  int              `json:"page"`
	Size  int              `json:"size"`
}

// FromResult 从领域层结果转换
func GetUsersRespFromResult(result domain.GetUsersResult) GetUsersResp {
	items := make([]UserDetailItem, len(result.List))
	for i, entity := range result.List {
		items[i] = UserDetailItemFromEntity(entity)
	}
	return GetUsersResp{
		List:  items,
		Total: result.Total,
		Page:  result.Page,
		Size:  result.Size,
	}
}

// UserDetailItem 用户详细信息项
type UserDetailItem struct {
	ID             int64     `json:"id"`
	Name           string    `json:"name"`
	RealName       string    `json:"real_name"`
	Email          string    `json:"email"`
	Phone          string    `json:"phone"`
	Department     string    `json:"department"`
	DepartmentName string    `json:"department_name"` // 部门名称
	Position       string    `json:"position"`
	SupervisorID   int64     `json:"supervisor_id"`
	SupervisorName string    `json:"supervisor_name"`
	Role           int       `json:"role"`
	RoleID         int64     `json:"role_id"` // 新角色系统ID
	RoleName       string    `json:"role_name"`
	Status         int       `json:"status"`
	IsLocked       bool      `json:"is_locked"`
	LockReason     string    `json:"lock_reason"`
	Remark         string    `json:"remark"`
	LastLoginAt    time.Time `json:"last_login_at"`
	LastLoginIP    string    `json:"last_login_ip"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// UserDetailItemFromEntity 从领域实体转换为VO项
func UserDetailItemFromEntity(entity domain.UserEntity) UserDetailItem {
	return UserDetailItem{
		ID:             entity.ID,
		Name:           entity.Name,
		RealName:       entity.RealName,
		Email:          entity.Email,
		Phone:          entity.Phone,
		Department:     entity.Department,
		DepartmentName: entity.DepartmentName,
		Position:       entity.Position,
		SupervisorID:   entity.SupervisorID,
		SupervisorName: entity.SupervisorName,
		Role:           entity.Role,
		RoleID:         entity.RoleID,
		RoleName:       entity.RoleName,
		Status:         entity.Status,
		IsLocked:       entity.IsLocked,
		LockReason:     entity.LockReason,
		Remark:         entity.Remark,
		LastLoginAt:    entity.LastLoginAt,
		LastLoginIP:    entity.LastLoginIP,
		CreatedAt:      entity.CreatedAt,
		UpdatedAt:      entity.UpdatedAt,
	}
}

// CreateUserReq 创建用户请求
type CreateUserReq struct {
	Name         string `json:"name" binding:"required,min=3,max=50"`
	RealName     string `json:"real_name" binding:"required,min=2,max=20"`
	Email        string `json:"email" binding:"required,email"`
	Phone        string `json:"phone"`
	Department   string `json:"department"`
	Position     string `json:"position"`
	SupervisorID int64  `json:"supervisor_id"`
	Role         int    `json:"role,omitempty"`             // 兼容旧角色系统
	RoleID       int64  `json:"role_id" binding:"required"` // 新角色系统ID
	Password     string `json:"password" binding:"required,min=6,max=30"`
	Remark       string `json:"remark"`
}

// ToParam 转换为领域层参数
func (req CreateUserReq) ToParam() domain.UserCreateParam {
	return domain.UserCreateParam{
		Name:         req.Name,
		RealName:     req.RealName,
		Email:        req.Email,
		Phone:        req.Phone,
		Department:   req.Department,
		Position:     req.Position,
		SupervisorID: req.SupervisorID,
		Role:         req.Role,
		RoleID:       req.RoleID,
		Password:     req.Password,
		Remark:       req.Remark,
	}
}

// CreateUserResp 创建用户响应
type CreateUserResp struct {
	ID int64 `json:"id"`
}

// FromResult 从领域层结果转换
func CreateUserRespFromResult(result domain.CreateUserResult) CreateUserResp {
	return CreateUserResp{
		ID: result.ID,
	}
}

// UpdateUserReq 更新用户请求
type UpdateUserReq struct {
	ID           int64  `json:"id" binding:"required"`
	Name         string `json:"name" binding:"required,min=3,max=50"`
	RealName     string `json:"real_name" binding:"required,min=2,max=20"`
	Email        string `json:"email" binding:"required,email"`
	Phone        string `json:"phone"`
	Department   string `json:"department"`
	Position     string `json:"position"`
	SupervisorID int64  `json:"supervisor_id"`
	Role         int    `json:"role,omitempty"`             // 兼容旧角色系统
	RoleID       int64  `json:"role_id" binding:"required"` // 新角色系统ID
	Status       int    `json:"status" binding:"required,oneof=1 2"`
	Remark       string `json:"remark"`
}

// ToParam 转换为领域层参数
func (req UpdateUserReq) ToParam() domain.UserUpdateParam {
	return domain.UserUpdateParam{
		ID:           req.ID,
		Name:         req.Name,
		RealName:     req.RealName,
		Email:        req.Email,
		Phone:        req.Phone,
		Department:   req.Department,
		Position:     req.Position,
		SupervisorID: req.SupervisorID,
		Role:         req.Role,
		RoleID:       req.RoleID,
		Status:       req.Status,
		Remark:       req.Remark,
	}
}

// LockUserReq 锁定用户请求
type LockUserReq struct {
	LockReason string `json:"lock_reason" binding:"required,min=1,max=200"`
}

// ToParam 转换为领域层参数
func (req LockUserReq) ToParam() domain.LockUserParam {
	return domain.LockUserParam{
		LockReason: req.LockReason,
	}
}

// ResetPasswordReq 重置密码请求
type ResetPasswordReq struct {
	NewPassword string `json:"new_password" binding:"required,min=6,max=30"`
}

// ToParam 转换为领域层参数
func (req ResetPasswordReq) ToParam() domain.ResetPasswordParam {
	return domain.ResetPasswordParam{
		NewPassword: req.NewPassword,
	}
}

// GetUserOptionsResp 获取用户选项响应
type GetUserOptionsResp struct {
	Roles       []UserRoleOptionItem   `json:"roles"`
	Departments []DepartmentOptionItem `json:"departments"`
	Supervisors []UserOptionItem       `json:"supervisors"`
}

// FromResult 从领域层结果转换
func GetUserOptionsRespFromResult(result domain.GetUserOptionsResult) GetUserOptionsResp {
	roles := make([]UserRoleOptionItem, len(result.Roles))
	for i, role := range result.Roles {
		roles[i] = UserRoleOptionItem{
			Value: role.Value,
			Label: role.Label,
		}
	}

	departments := make([]DepartmentOptionItem, len(result.Departments))
	for i, dept := range result.Departments {
		departments[i] = DepartmentOptionItem{
			Value: dept.Code,
			Label: dept.Name,
		}
	}

	supervisors := make([]UserOptionItem, len(result.Supervisors))
	for i, supervisor := range result.Supervisors {
		supervisors[i] = UserOptionItem{
			Value: supervisor.Value,
			Label: supervisor.Label,
		}
	}

	return GetUserOptionsResp{
		Roles:       roles,
		Departments: departments,
		Supervisors: supervisors,
	}
}

// UserRoleOptionItem 用户角色选项项
type UserRoleOptionItem struct {
	Value int    `json:"value"`
	Label string `json:"label"`
}

// DepartmentOptionItem 部门选项项
type DepartmentOptionItem struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// UserOptionItem 用户选项项
type UserOptionItem struct {
	Value int64  `json:"value"`
	Label string `json:"label"`
}

// MediaUserItem 媒体用户信息项
type MediaUserItem struct {
	ID       int64  `json:"id"`
	Username string `json:"username"` // 对应name字段
	RealName string `json:"realName"` // 对应real_name字段
}

// FromEntity 从领域实体转换
func MediaUserItemFromEntity(entity domain.MediaUserEntity) MediaUserItem {
	return MediaUserItem{
		ID:       entity.ID,
		Username: entity.Username,
		RealName: entity.RealName,
	}
}

// MediaUserListResp 媒体用户列表响应
type MediaUserListResp struct {
	List []MediaUserItem `json:"list"`
}

// FromResult 从领域层结果转换
func MediaUserListRespFromResult(result domain.MediaUserListResult) MediaUserListResp {
	items := make([]MediaUserItem, len(result.List))
	for i, entity := range result.List {
		items[i] = MediaUserItemFromEntity(entity)
	}
	return MediaUserListResp{
		List: items,
	}
}
