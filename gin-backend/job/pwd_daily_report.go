package job

import (
	"context"
	"gin-backend/internal/service"
	"time"

	"go.uber.org/zap"
)

// PwdDailyReportJob 口令日报生成定时任务
type PwdDailyReportJob struct {
	planService *service.PlanService
}

// NewPwdDailyReportJob 创建口令日报生成定时任务
func NewPwdDailyReportJob() *PwdDailyReportJob {
	return &PwdDailyReportJob{
		planService: service.NewPlanService(),
	}
}

// Name 任务名称
func (j *PwdDailyReportJob) Name() string {
	return "generate-pwd-daily-report"
}

// Run 任务执行
func (j *PwdDailyReportJob) Run() error {
	ctx := context.Background()

	// 生成近3天的口令日报
	endTime := time.Now()
	startTime := endTime.Add(-3 * 24 * time.Hour)

	if err := j.planService.GeneratePwdDailyReport(ctx, startTime, endTime); err != nil {
		zap.L().Error("生成口令日报定时任务失败", zap.Error(err))
		return err
	}

	zap.L().Info("生成口令日报定时任务成功")
	return nil
}
