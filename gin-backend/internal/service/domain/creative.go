package domain

import "time"

// HotArea 热区结构
type HotArea struct {
	ID        int    // 热区ID
	Width     int    // 宽度
	Height    int    // 高度
	Unit      string // 单位，默认是px
	X         int    // X坐标
	Y         int    // Y坐标
	EventType int    // 事件类型：1-确定 2-取消
}

// CreativeEntity 创意实体
type CreativeEntity struct {
	ID              int64     // 主键ID
	Name            string    // 创意名称
	ImageURL        string    // 素材图片地址
	ImageArea       string    // 图片区域
	BackgroundColor string    // 背景颜色
	HotAreasList    []HotArea // 热区列表
	CreatedAt       time.Time // 创建时间
	UpdatedAt       time.Time // 更新时间
}

// CreativeListParam 获取创意列表参数
type CreativeListParam struct {
	Page int    // 页码
	Size int    // 每页数量
	Name string // 创意名称，支持模糊查询
}

// CreativeListResult 获取创意列表结果
type CreativeListResult struct {
	List  []CreativeEntity // 列表数据
	Total int64            // 总数
	Page  int              // 页码
	Size  int              // 每页数量
}

// CreativeCreateEntity 创建创意实体
type CreativeCreateEntity struct {
	Name            string    // 创意名称
	ImageURL        string    // 素材图片地址
	ImageArea       string    // 图片区域
	BackgroundColor string    // 背景颜色
	HotAreasList    []HotArea // 热区列表
}

// CreativeUpdateEntity 更新创意实体
type CreativeUpdateEntity struct {
	ID              int64     // 创意ID
	Name            string    // 创意名称
	ImageURL        string    // 素材图片地址
	ImageArea       string    // 图片区域
	BackgroundColor string    // 背景颜色
	HotAreasList    []HotArea // 热区列表
}
