# 小红书授权API文档

## 功能概述

小红书授权API提供了完整的OAuth2授权流程，包括生成授权链接、处理授权回调、获取访问令牌等功能。支持自定义授权范围和状态参数，确保授权过程的安全性和灵活性。

## 授权流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Backend as 后端服务
    participant X<PERSON> as 小红书授权服务器

    User->>Frontend: 点击授权按钮
    Frontend->>Backend: GET /api/v1/xhs/auth/url
    Backend->>Frontend: 返回授权链接
    Frontend->>User: 跳转到授权链接
    User->>XHS: 用户授权
    XHS->>Backend: 回调 /api/v1/xhs/auth/callback?code=xxx&state=xxx
    Backend->>XHS: 使用code获取access_token
    XHS->>Backend: 返回access_token
    Backend->>Frontend: 返回授权结果
```

## API接口列表

### 1. 获取授权链接（GET方式）

**接口地址**：`GET /api/v1/xhs/auth/url`

**功能描述**：获取小红书OAuth授权链接，使用默认参数

**请求参数**：无

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "auth_url": "https://ad-market.xiaohongshu.com/auth?appId=your-app-id&scope=[%22report_service%22,%22ad_query%22,%22ad_manage%22,%22account_manage%22]&redirectUri=http://www.baidu.com&state=xhs_auth_state_abcd",
    "app_id": "your-app-id",
    "redirect_uri": "http://www.baidu.com",
    "scopes": ["report_service", "ad_query", "ad_manage", "account_manage"],
    "state": "xhs_auth_state_abcd"
  }
}
```

### 2. 获取授权链接（POST方式）

**接口地址**：`POST /api/v1/xhs/auth/url`

**功能描述**：获取小红书OAuth授权链接，支持自定义参数

**请求参数**：
```json
{
  "scopes": ["report_service", "ad_query"],
  "state": "custom_state_123"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scopes | array | 否 | 自定义授权范围，不传则使用默认范围 |
| state | string | 否 | 自定义状态参数，不传则自动生成 |

**支持的授权范围**：
- `report_service`：报表服务权限
- `ad_query`：广告查询权限
- `ad_manage`：广告管理权限
- `account_manage`：账户管理权限

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "auth_url": "https://ad-market.xiaohongshu.com/auth?appId=your-app-id&scope=[%22report_service%22,%22ad_query%22]&redirectUri=http://www.baidu.com&state=custom_state_123",
    "app_id": "your-app-id",
    "redirect_uri": "http://www.baidu.com",
    "scopes": ["report_service", "ad_query"],
    "state": "custom_state_123"
  }
}
```

### 3. 处理授权回调

**接口地址**：`GET /api/v1/xhs/auth/callback`

**功能描述**：处理小红书OAuth授权回调

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 否 | 授权码（授权成功时返回） |
| state | string | 否 | 状态参数 |
| error | string | 否 | 错误信息（授权失败时返回） |

**响应示例（授权成功）**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "授权回调处理成功",
    "code": "auth_code_123456",
    "state": "xhs_auth_state_abcd",
    "token": {
      "access_token": "mock_access_token_auth_code_123456",
      "token_type": "Bearer",
      "expires_in": 7200,
      "scope": "[\"report_service\",\"ad_query\",\"ad_manage\",\"account_manage\"]"
    }
  }
}
```

**响应示例（授权失败）**：
```json
{
  "code": 400,
  "message": "授权回调处理失败: 授权失败: access_denied",
  "data": null
}
```

### 4. 获取配置信息

**接口地址**：`GET /api/v1/xhs/auth/config`

**功能描述**：获取小红书授权相关的配置信息（脱敏）

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "config": {
      "app_id": "your-app-id",
      "redirect_uri": "http://www.baidu.com",
      "is_prod": false,
      "secret": "***"
    },
    "scopes": [
      {
        "scope": "report_service",
        "name": "报表服务",
        "description": "获取广告报表数据的权限"
      },
      {
        "scope": "ad_query",
        "name": "广告查询",
        "description": "查询广告信息的权限"
      },
      {
        "scope": "ad_manage",
        "name": "广告管理",
        "description": "管理广告的权限"
      },
      {
        "scope": "account_manage",
        "name": "账户管理",
        "description": "管理广告账户的权限"
      }
    ]
  }
}
```

### 5. 验证配置

**接口地址**：`GET /api/v1/xhs/auth/validate`

**功能描述**：验证小红书授权相关配置是否正确

**响应示例（配置正确）**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "配置验证成功",
    "valid": true
  }
}
```

**响应示例（配置错误）**：
```json
{
  "code": 500,
  "message": "配置验证失败: 小红书应用ID未配置",
  "data": null
}
```

### 6. 获取支持的授权范围

**接口地址**：`GET /api/v1/xhs/auth/scopes`

**功能描述**：获取小红书支持的所有授权范围列表

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "scope": "report_service",
      "name": "报表服务",
      "description": "获取广告报表数据的权限"
    },
    {
      "scope": "ad_query",
      "name": "广告查询",
      "description": "查询广告信息的权限"
    },
    {
      "scope": "ad_manage",
      "name": "广告管理",
      "description": "管理广告的权限"
    },
    {
      "scope": "account_manage",
      "name": "账户管理",
      "description": "管理广告账户的权限"
    }
  ]
}
```

## 配置说明

### 配置文件（config.yaml）

```yaml
xiaohongshu:
  app_id: "your-xiaohongshu-app-id"        # 小红书应用ID
  secret: "your-xiaohongshu-secret"        # 小红书应用密钥
  is_prod: false                           # 是否生产环境
  redirect_uri: "http://www.baidu.com"     # 授权回调地址
```

### 配置项说明

| 配置项 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_id | string | 是 | 小红书开放平台分配的应用ID |
| secret | string | 是 | 小红书开放平台分配的应用密钥 |
| is_prod | bool | 否 | 是否生产环境，默认false |
| redirect_uri | string | 是 | 授权成功后的回调地址 |

## 使用示例

### 1. 前端获取授权链接

```javascript
// 获取默认授权链接
const getAuthUrl = async () => {
  try {
    const response = await fetch('/api/v1/xhs/auth/url', {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer your_token'
      }
    })
    const data = await response.json()
    
    if (data.code === 200) {
      // 跳转到授权页面
      window.location.href = data.data.auth_url
    }
  } catch (error) {
    console.error('获取授权链接失败:', error)
  }
}

// 获取自定义授权链接
const getCustomAuthUrl = async () => {
  try {
    const response = await fetch('/api/v1/xhs/auth/url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your_token'
      },
      body: JSON.stringify({
        scopes: ['report_service', 'ad_query'],
        state: 'custom_state_123'
      })
    })
    const data = await response.json()
    
    if (data.code === 200) {
      window.location.href = data.data.auth_url
    }
  } catch (error) {
    console.error('获取授权链接失败:', error)
  }
}
```

### 2. 处理授权回调

```javascript
// 在回调页面处理授权结果
const handleAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')
  const error = urlParams.get('error')
  
  if (error) {
    console.error('授权失败:', error)
    return
  }
  
  if (code) {
    console.log('授权成功，授权码:', code)
    console.log('状态参数:', state)
    // 可以将授权码发送给后端进行后续处理
  }
}
```

### 3. 验证配置

```bash
# 验证小红书授权配置
curl -X GET "http://localhost:8080/api/v1/xhs/auth/validate" \
  -H "Authorization: Bearer your_token"

# 获取配置信息
curl -X GET "http://localhost:8080/api/v1/xhs/auth/config" \
  -H "Authorization: Bearer your_token"
```

## 安全注意事项

1. **状态参数验证**：使用state参数防止CSRF攻击
2. **HTTPS传输**：生产环境必须使用HTTPS
3. **授权码有效期**：授权码通常有效期很短，需要及时使用
4. **访问令牌存储**：安全存储访问令牌，避免泄露
5. **权限最小化**：只申请必要的授权范围

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误或授权失败 |
| 500 | 服务器内部错误或配置错误 |

## 常见问题

### 1. 配置验证失败
- 检查config.yaml中的xiaohongshu配置项
- 确保app_id、secret、redirect_uri都已正确配置

### 2. 授权链接无法访问
- 检查小红书开放平台应用状态
- 确认redirect_uri与平台配置一致

### 3. 回调处理失败
- 检查state参数是否匹配
- 确认授权码是否有效且未过期

### 4. 获取访问令牌失败
- 检查应用密钥是否正确
- 确认授权码是否已被使用过
