package domain

import (
	"mime/multipart"
	"time"
)

// 审核状态常量
const (
	CostAuditStatusPending  = "pending"  // 待审核
	CostAuditStatusApproved = "approved" // 已通过
	CostAuditStatusRejected = "rejected" // 已拒绝
)

// CostEntity 费用记录实体
type CostEntity struct {
	ID            int64     // 费用记录ID
	PlanID        int64     // 投放计划ID
	GroupName     string    // 口令组名称
	PlanCode      string    // 计划编码
	MediaID       int64     // 媒体ID
	MediaName     string    // 媒体名称
	AdSlotID      int64     // 广告位ID
	AdSlotName    string    // 广告位名称
	AdProductID   int64     // 产品ID
	AdProductName string    // 产品名称
	ProductName   string    // 产品名称(兼容旧字段)
	UserID        int64     // 媒介ID
	UserName      string    // 媒介名称
	Date          time.Time // 统计日期
	Cost          float64   // 费用金额
	Clicks        int       // 点击数
	Remark        string    // 备注
	AuditStatus   string    // 审核状态
	AuditorID     int64     // 审核人ID
	AuditBy       int64     // 审核人ID(兼容旧字段)
	AuditAt       time.Time // 审核时间
	AuditorName   string    // 审核人名称
	AuditTime     time.Time // 审核时间(兼容旧字段)
	AuditRemark   string    // 审核备注
	RejectReason  string    // 拒绝原因
	CreatorID     int64     // 创建人ID
	CreatorName   string    // 创建人名称
	UpdaterID     int64     // 更新人ID
	UpdaterName   string    // 更新人名称
	CreatedAt     time.Time // 创建时间
	UpdatedAt     time.Time // 更新时间
	CategoryID    int64     // 分类ID
	CategoryName  string    // 分类名称
}

// CostCreateEntity 创建费用记录实体
type CostCreateEntity struct {
	PlanID      int64   // 投放计划ID
	MediaID     int64   // 媒体ID
	AdSlotID    int64   // 广告位ID
	AdProductID int64   // 产品ID
	UserID      int64   // 媒介ID
	Date        string  // 日期(格式: YYYY-MM-DD)
	Cost        float64 // 费用金额
	Clicks      int     // 点击数
	Remark      string  // 备注
}

// CostUpdateEntity 更新费用记录实体
type CostUpdateEntity struct {
	ID          int64   // 费用记录ID
	PlanID      int64   // 投放计划ID
	MediaID     int64   // 媒体ID
	AdSlotID    int64   // 广告位ID
	AdProductID int64   // 产品ID
	UserID      int64   // 媒介ID
	Date        string  // 日期(格式: YYYY-MM-DD)
	Cost        float64 // 费用金额
	Clicks      int     // 点击数
	Remark      string  // 备注
}

// CostListParam 费用记录列表查询参数
type CostListParam struct {
	Page        int    // 页码
	PageSize    int    // 每页数量
	PlanID      int64  // 投放计划ID
	GroupID     int64  // 口令组ID
	MediaID     int64  // 媒体ID
	AdSlotID    int64  // 广告位ID
	AdProductID int64  // 产品ID
	UserID      int64  // 媒介ID
	StartDate   string // 开始日期(格式: YYYY-MM-DD)
	EndDate     string // 结束日期(格式: YYYY-MM-DD)
	AuditStatus string // 审核状态
	CategoryID  int64  // 分类ID
}

// CostListResult 费用记录列表查询结果
type CostListResult struct {
	Total    int64        // 总记录数
	Page     int          // 当前页码
	PageSize int          // 每页数量
	List     []CostEntity // 费用记录列表
}

// CostAuditEntity 费用审核实体
type CostAuditEntity struct {
	ID           int64  // 费用记录ID
	AuditStatus  string // 审核状态
	RejectReason string // 拒绝原因
}

// CostImportParam 费用导入参数
type CostImportParam struct {
	CategoryID int64                 // 分类ID
	File       *multipart.FileHeader // 上传的Excel文件
	UserID     int64                 // 操作用户ID
}
