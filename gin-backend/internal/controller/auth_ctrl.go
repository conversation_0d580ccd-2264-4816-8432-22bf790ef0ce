package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService *service.AuthService
}

// NewAuthController 创建认证控制器实例
func NewAuthController() *AuthController {
	return &AuthController{
		authService: service.NewAuthService(),
	}
}

// Login 用户登录
func (ctrl *AuthController) Login(c *gin.Context) {
	var req vo.LoginReq

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数格式错误："+err.Error())
		return
	}

	// 将VO转换为domain实体
	loginEntity := req.ToDomainLoginEntity()

	// 调用服务层处理登录
	profileEntity, err := ctrl.authService.Login(c.Request.Context(), loginEntity)
	if err != nil {
		response.Error(c, response.CodeError, err.Error())
		return
	}

	// 使用vo的方法直接创建响应对象
	loginResp := vo.NewLoginResp(profileEntity)

	response.Success(c, loginResp)
}

// GetUserInfo 获取用户信息
func (ctrl *AuthController) GetUserInfo(c *gin.Context) {
	// 从中间件中获取用户ID
	userIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未登录")
		return
	}

	userID, ok := userIDStr.(uint64)
	if !ok {
		response.ServerError(c, "用户ID格式错误")
		return
	}

	// 获取用户信息
	profileEntity, err := ctrl.authService.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		response.Error(c, response.CodeError, err.Error())
		return
	}

	// 将domain实体转换为VO响应对象
	userProfile := vo.FromDomainUserProfile(profileEntity)

	response.Success(c, userProfile)
}

// ChangePassword 修改密码
func (ctrl *AuthController) ChangePassword(c *gin.Context) {
	var req vo.ChangePasswordReq

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数格式错误："+err.Error())
		return
	}

	// 从中间件中获取用户ID
	userIDStr, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未登录")
		return
	}

	userID, ok := userIDStr.(int64)
	if !ok {
		response.ServerError(c, "用户ID格式错误")
		return
	}

	// 使用vo层的转换方法构造domain实体
	changePasswordEntity := req.ToDomainChangePasswordEntity(userID)

	// 修改密码
	err := ctrl.authService.ChangePassword(c.Request.Context(), changePasswordEntity)
	if err != nil {
		response.Error(c, response.CodeError, err.Error())
		return
	}

	response.Success(c, vo.ChangePasswordResp{
		Message: "密码修改成功",
	})
}

// Logout 用户登出
func (ctrl *AuthController) Logout(c *gin.Context) {
	// 对于JWT token，登出通常在前端处理（删除token）
	// 这里可以记录登出日志或者将token加入黑名单
	response.Success(c, gin.H{
		"message": "登出成功",
	})
}

// GetUserIDFromContext 从上下文中获取用户ID的辅助函数
func GetUserIDFromContext(c *gin.Context) (uint64, error) {
	userIDStr, exists := c.Get("user_id")
	if !exists {
		return 0, gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic, Meta: "用户未登录"}
	}

	if userID, ok := userIDStr.(uint64); ok {
		return userID, nil
	}

	// 尝试字符串转换
	if userIDStr, ok := userIDStr.(string); ok {
		if userID, err := strconv.ParseUint(userIDStr, 10, 64); err == nil {
			return userID, nil
		}
	}

	return 0, gin.Error{Err: gin.Error{}, Type: gin.ErrorTypePublic, Meta: "用户ID格式错误"}
}
