package model

import (
	"gorm.io/gorm"
)

// AdSlots 广告位表
type AdSlots struct {
	gorm.Model
	Name        string `json:"name" gorm:"size:100;not null;comment:广告位名称"`
	Type        string `json:"type" gorm:"size:50;index;comment:广告位类型;default:''"`
	Description string `json:"description" gorm:"size:255;comment:广告位描述"`
	Status      int    `json:"status" gorm:"default:1;comment:状态 1-启用 0-禁用"`
	MediaID     uint   `json:"media_id" gorm:"index;comment:媒体ID"`
	AppID       string `json:"app_id" gorm:"size:50;comment:应用ID"`
	SlotCode    string `json:"slot_code" gorm:"size:100;unique;comment:广告位代码"`
	CreatedBy   uint   `json:"created_by" gorm:"comment:创建人ID"`
	UpdatedBy   uint   `json:"updated_by" gorm:"comment:更新人ID"`
}

// TableName 指定表名
func (AdSlots) TableName() string {
	return "ad_slots"
}
