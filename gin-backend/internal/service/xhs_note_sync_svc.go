package service

import (
	"context"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/pkg/xiaohongshu"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// XHSNoteSyncService 小红书笔记同步服务
type XHSNoteSyncService struct {
	db *gorm.DB
}

// NewXHSNoteSyncService 创建小红书笔记同步服务
func NewXHSNoteSyncService() *XHSNoteSyncService {
	return &XHSNoteSyncService{
		db: global.DB,
	}
}

// SyncAllNotes 同步所有账号的笔记列表
func (s *XHSNoteSyncService) SyncAllNotes(ctx context.Context) error {
	zap.L().Info("开始同步小红书笔记列表")

	// 获取所有有效的小红书账号
	var accounts []model.AdAccounts
	err := s.db.WithContext(ctx).
		Where("platform = ? AND usage_status = ? AND token IS NOT NULL AND token != ''", 1, 1).
		Find(&accounts).Error

	if err != nil {
		return fmt.Errorf("获取小红书账号列表失败: %w", err)
	}

	if len(accounts) == 0 {
		zap.L().Info("没有找到有效的小红书账号，跳过同步")
		return nil
	}

	zap.L().Info("找到小红书账号", zap.Int("count", len(accounts)))

	// 逐个账号同步笔记
	successCount := 0
	errorCount := 0

	for _, account := range accounts {
		if err := s.syncAccountNotes(ctx, account); err != nil {
			zap.L().Error("同步账号笔记失败",
				zap.Int64("account_id", account.ID),
				zap.String("account_name", account.AccountName),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}

		// 每个账号之间稍作休息，避免API调用过于频繁
		time.Sleep(1 * time.Second)
	}

	zap.L().Info("小红书笔记同步完成",
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))

	if errorCount > 0 {
		return fmt.Errorf("部分账号同步失败，成功: %d, 失败: %d", successCount, errorCount)
	}

	return nil
}

// syncAccountNotes 同步单个账号的笔记
func (s *XHSNoteSyncService) syncAccountNotes(ctx context.Context, account model.AdAccounts) error {
	zap.L().Info("开始同步账号笔记",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName))

	// 创建小红书客户端
	client := xiaohongshu.NewClient(&xiaohongshu.Config{
		AppId:  config.AppConfig.Xiaohongshu.AppId,
		Secret: config.AppConfig.Xiaohongshu.Secret,
		IsProd: config.AppConfig.Xiaohongshu.IsProd,
	})

	// 解析广告主ID
	advertiserID, err := strconv.ParseInt(account.PlatformAccountId, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err)
	}

	// 创建笔记列表辅助工具
	helper := xiaohongshu.NewNoteListHelper(client)

	// 同步不同类型的笔记
	noteTypes := []struct {
		Type int
		Name string
	}{
		{1, "我的笔记"},
		{2, "合作笔记"},
		{4, "主理人笔记"},
		{6, "员工笔记"},
		{11, "授权笔记"},
	}

	allNotes := make([]model.XhsNotes, 0)

	for _, noteType := range noteTypes {
		notes, err := s.syncNotesByType(ctx, helper, account.Token, advertiserID, noteType.Type, noteType.Name)
		if err != nil {
			zap.L().Error("同步笔记类型失败",
				zap.Int64("account_id", account.ID),
				zap.String("note_type", noteType.Name),
				zap.Error(err))
			continue
		}
		allNotes = append(allNotes, notes...)

		// 不同类型之间稍作休息
		time.Sleep(500 * time.Millisecond)
	}

	if len(allNotes) == 0 {
		zap.L().Info("账号没有笔记数据",
			zap.Int64("account_id", account.ID),
			zap.String("account_name", account.AccountName))
		return nil
	}

	// 批量保存到数据库
	if err := s.batchSaveNotes(ctx, allNotes); err != nil {
		return fmt.Errorf("保存笔记数据失败: %w", err)
	}

	zap.L().Info("账号笔记同步完成",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName),
		zap.Int("total_count", len(allNotes)))

	return nil
}

// syncNotesByType 同步指定类型的笔记
func (s *XHSNoteSyncService) syncNotesByType(ctx context.Context, helper *xiaohongshu.NoteListHelper, accessToken string, advertiserID int64, noteType int, typeName string) ([]model.XhsNotes, error) {
	zap.L().Info("开始同步笔记类型",
		zap.Int64("advertiser_id", advertiserID),
		zap.String("note_type", typeName))

	var allNotes []model.XhsNotes
	page := 1
	pageSize := 100 // 使用最大页面大小

	for {
		// 分页获取笔记
		response, err := helper.GetNoteListWithPagination(accessToken, advertiserID, noteType, page, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页笔记失败: %w", page, err)
		}

		if !response.Success {
			return nil, fmt.Errorf("API返回错误: code=%d, msg=%s", response.Code, response.Msg)
		}

		// 转换数据
		notes := s.convertToNoteModels(response.Data.Notes)
		allNotes = append(allNotes, notes...)

		zap.L().Info("获取笔记数据",
			zap.Int64("advertiser_id", advertiserID),
			zap.String("note_type", typeName),
			zap.Int("page", page),
			zap.Int("count", len(notes)))

		// 检查是否还有更多数据
		if len(response.Data.Notes) < pageSize {
			break // 最后一页
		}

		page++

		// 避免请求过于频繁
		time.Sleep(200 * time.Millisecond)
	}

	zap.L().Info("笔记类型同步完成",
		zap.Int64("advertiser_id", advertiserID),
		zap.String("note_type", typeName),
		zap.Int("total_count", len(allNotes)))

	return allNotes, nil
}

// convertToNoteModels 转换API响应数据为数据库模型
func (s *XHSNoteSyncService) convertToNoteModels(apiNotes []xiaohongshu.BaseNoteItem) []model.XhsNotes {
	notes := make([]model.XhsNotes, 0, len(apiNotes))

	for _, apiNote := range apiNotes {
		note := model.XhsNotes{
			CreatedAt:              time.Now(),
			UpdatedAt:              time.Now(),
			NoteID:                 apiNote.NoteID,
			Image:                  apiNote.Image,
			Desc:                   apiNote.Desc,
			CreateTime:             apiNote.CreateTime,
			Author:                 apiNote.Author,
			AuthorImage:            apiNote.AuthorImage,
			Status:                 apiNote.Status,
			NoteContentType:        apiNote.NoteContentType,
			CooperateState:         apiNote.CooperateState,
			Title:                  apiNote.Title,
			ImageList:              model.NoteImageList(apiNote.ImageList),
			CooperateComponentType: apiNote.CooperateComponentType,
			CrowdCreationNote:      apiNote.CrowdCreationNote,
			ItemID:                 apiNote.ItemID,
			ReadCount:              apiNote.ReadCount,
			ReadRate:               apiNote.ReadRate,
			InteractCount:          apiNote.InteractCount,
			InteractRate:           apiNote.InteractRate,
			HighQuality:            apiNote.HighQuality,
			HighPotential:          apiNote.HighPotential,
			OutsideShopVisit:       apiNote.OutsideShopVisit,
			OutsideShopVisitRate:   apiNote.OutsideShopVisitRate,
			Taxonomy1:              apiNote.Taxonomy1,
			Taxonomy2:              apiNote.Taxonomy2,
			Taxonomy3:              apiNote.Taxonomy3,
			IsHitStrategy:          apiNote.IsHitStrategy,
			HitStrategyContent:     apiNote.HitStrategyContent,
			WinHorseNote:           apiNote.WinHorseNote,
			StaffTag:               apiNote.StaffTag,
			StaffArea:              apiNote.StaffArea,
			NoteURL:                apiNote.NoteURL,
			HasShopCard:            apiNote.HasShopCard,
		}

		notes = append(notes, note)
	}

	return notes
}

// batchSaveNotes 批量保存笔记数据
func (s *XHSNoteSyncService) batchSaveNotes(ctx context.Context, notes []model.XhsNotes) error {
	if len(notes) == 0 {
		return nil
	}

	// 使用事务批量插入或更新
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 分批处理，每批500条
		batchSize := 500
		for i := 0; i < len(notes); i += batchSize {
			end := i + batchSize
			if end > len(notes) {
				end = len(notes)
			}

			batch := notes[i:end]

			// 使用ON DUPLICATE KEY UPDATE语义，如果笔记已存在则更新
			for _, note := range batch {
				if err := tx.Where("note_id = ?", note.NoteID).
					Assign(note).
					FirstOrCreate(&note).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}
