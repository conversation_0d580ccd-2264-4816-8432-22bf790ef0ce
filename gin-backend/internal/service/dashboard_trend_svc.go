package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// dashboardTrendService 仪表盘趋势服务
type dashboardTrendService struct {
	dashboard *DashboardService
	db        *gorm.DB
}

// newDashboardTrendService 创建仪表盘趋势服务实例
func newDashboardTrendService(d *DashboardService) *dashboardTrendService {
	return &dashboardTrendService{
		dashboard: d,
		db:        d.db,
	}
}

// GetTrendData 获取趋势数据
func (s *dashboardTrendService) GetTrendData(req domain.DashboardTrendParam, userID int, userRole int) (domain.TrendEntity, error) {
	// 记录开始执行
	fmt.Printf("获取趋势数据开始: metric=%s, startDate=%s, endDate=%s, userId=%v, mediaId=%v, planId=%v, productId=%v, type=%v, category=%s\n",
		req.Metric, req.StartDate, req.EndDate, req.UserID, req.MediaID, req.PlanID, req.ProductID, req.Type, req.Category)

	// 设置默认日期（如果未指定）
	if req.StartDate == "" || req.EndDate == "" {
		now := time.Now()
		req.EndDate = now.Format("2006-01-02")
		req.StartDate = now.AddDate(0, 0, -6).Format("2006-01-02")
	}

	// 如果metric为空，设置默认值
	if req.Metric == "" {
		req.Metric = "estimated_profit"
	}

	// 转换参数类型 - 这里很关键，对齐两种实现的参数处理方式
	reqFormatted := domain.DashboardTrendParam{
		UserID:    req.UserID,
		MediaID:   req.MediaID,
		PlanID:    req.PlanID,
		ProductID: req.ProductID,
		Metric:    req.Metric,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Type:      req.Type,
		Category:  req.Category,
	}

	// 获取权限信息
	scope, departmentUsers, err := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	if err != nil {
		return domain.TrendEntity{}, err
	}

	// 检查是否为单日数据
	startDate, _ := time.Parse("2006-01-02", req.StartDate)
	endDate, _ := time.Parse("2006-01-02", req.EndDate)
	isSameDay := startDate.Equal(endDate)

	// 获取当前周期数据
	var currentData []float64
	var timePoints []string

	if isSameDay {
		// 单日数据，使用优化的分时查询方法 - 直接查询原始表
		fmt.Printf("使用单日分时查询方法\n")
		currentData, timePoints, err = s.queryHourlyDataDirectly(
			req.StartDate,
			req.Metric,
			userID,
			scope,
			departmentUsers,
			reqFormatted)
	} else {
		// 多日数据，使用日统计数据
		var currentTrend TrendData
		currentTrend, err = s.getTrendByDateRange(reqFormatted, userID, userRole, scope, departmentUsers, req.StartDate, req.EndDate)
		if err == nil {
			timePoints = currentTrend.TimePoints
			currentData = currentTrend.Values
		}
	}

	if err != nil {
		return domain.TrendEntity{}, err
	}

	// 计算上一个周期的日期
	var prevStartDate, prevEndDate string
	if isSameDay {
		// 上一天
		prevDate := startDate.AddDate(0, 0, -1)
		prevStartDate = prevDate.Format("2006-01-02")
		prevEndDate = prevDate.Format("2006-01-02")
	} else {
		// 上一个同等长度的周期
		duration := endDate.Sub(startDate)
		days := int(duration.Hours() / 24)
		prevStartDate = startDate.AddDate(0, 0, -days-1).Format("2006-01-02")
		prevEndDate = endDate.AddDate(0, 0, -days-1).Format("2006-01-02")
	}

	// 获取上一个周期的趋势数据
	var prevData []float64
	if isSameDay {
		// 同样使用直接查询
		prevReqFormatted := reqFormatted
		prevReqFormatted.StartDate = prevStartDate
		prevReqFormatted.EndDate = prevEndDate

		prevData, _, err = s.queryHourlyDataDirectly(
			prevStartDate,
			req.Metric,
			userID,
			scope,
			departmentUsers,
			prevReqFormatted)

		if err != nil || sum(prevData) == 0 {
			prevData = make([]float64, len(timePoints))
		}
	} else {
		var prevTrend TrendData
		prevTrend, _ = s.getTrendByDateRange(reqFormatted, userID, userRole, scope, departmentUsers, prevStartDate, prevEndDate)
		prevData = prevTrend.Values
		// 如果长度不匹配，需要调整
		if len(prevData) != len(timePoints) {
			prevData = make([]float64, len(timePoints))
		}
	}

	// 计算同比周期日期 (上周同期)
	var samePeriodStartDate, samePeriodEndDate string
	if isSameDay {
		// 上周同一天
		samePeriodDate := startDate.AddDate(0, 0, -7)
		samePeriodStartDate = samePeriodDate.Format("2006-01-02")
		samePeriodEndDate = samePeriodDate.Format("2006-01-02")
	} else {
		// 上周同期
		samePeriodStartDate = startDate.AddDate(0, 0, -7).Format("2006-01-02")
		samePeriodEndDate = endDate.AddDate(0, 0, -7).Format("2006-01-02")
	}

	// 获取同比周期的趋势数据
	var samePeriodData []float64
	if isSameDay {
		// 同样使用直接查询
		samePeriodReqFormatted := reqFormatted
		samePeriodReqFormatted.StartDate = samePeriodStartDate
		samePeriodReqFormatted.EndDate = samePeriodEndDate

		samePeriodData, _, err = s.queryHourlyDataDirectly(
			samePeriodStartDate,
			req.Metric,
			userID,
			scope,
			departmentUsers,
			samePeriodReqFormatted)

		if err != nil || sum(samePeriodData) == 0 {
			samePeriodData = make([]float64, len(timePoints))
		}
	} else {
		var samePeriodTrend TrendData
		samePeriodTrend, _ = s.getTrendByDateRange(reqFormatted, userID, userRole, scope, departmentUsers, samePeriodStartDate, samePeriodEndDate)
		samePeriodData = samePeriodTrend.Values
		// 如果长度不匹配，需要调整
		if len(samePeriodData) != len(timePoints) {
			samePeriodData = make([]float64, len(timePoints))
		}
	}

	// 构造标签
	var labels struct {
		Current    string
		Previous   string
		SamePeriod string
	}

	if isSameDay {
		// 单天分时数据的标签
		labels.Current = fmt.Sprintf("%s (当日)", req.StartDate)
		labels.Previous = fmt.Sprintf("%s (前一日)", prevStartDate)
		labels.SamePeriod = fmt.Sprintf("%s (上周同期)", samePeriodStartDate)
	} else {
		// 多天数据的标签
		labels.Current = fmt.Sprintf("%s至%s", req.StartDate, req.EndDate)
		labels.Previous = fmt.Sprintf("%s至%s", prevStartDate, prevEndDate)
		labels.SamePeriod = fmt.Sprintf("%s至%s", samePeriodStartDate, samePeriodEndDate)
	}

	// 构造返回结果
	result := domain.TrendEntity{
		TimePoints: timePoints,
		Current:    currentData,
		Previous:   prevData,
		SamePeriod: samePeriodData,
		Labels:     labels,
	}

	// 记录执行结果概要
	fmt.Printf("获取趋势数据完成: timePoints=%d, currentSum=%f, previousSum=%f, samePeriodSum=%f\n",
		len(timePoints), sum(currentData), sum(prevData), sum(samePeriodData))

	return result, nil
}

// sum 计算数组和
func sum(arr []float64) float64 {
	var total float64
	for _, v := range arr {
		total += v
	}
	return total
}

// getHourlyDataForSingleDay 获取单天的分时数据（优化实现，与TrendService保持一致）
func (s *dashboardTrendService) getHourlyDataForSingleDay(req domain.DashboardTrendParam, userID int, scope string, departmentUsers []uint64) ([]float64, []string, error) {
	fmt.Printf("开始获取分时数据: date=%s, metric=%s\n", req.StartDate, req.Metric)

	// 获取过滤后的计划ID列表
	planIds, err := s.getFilteredPlanIds(req, userID, scope, departmentUsers)
	if err != nil {
		return nil, nil, err
	}

	// 生成24小时的时间点
	timePoints := make([]string, 24)
	for i := 0; i < 24; i++ {
		timePoints[i] = fmt.Sprintf("%02d:00", i)
	}

	// 如果没有找到符合条件的计划，返回空数据
	if len(planIds) == 0 {
		emptyData := make([]float64, 24)
		return emptyData, timePoints, nil
	}

	// 确定查询的表名
	tableName := getOrderTableName(req.StartDate)

	// 根据指标确定查询字段
	var fieldName string
	switch req.Metric {
	case "orders":
		fieldName = "COUNT(*)"
	case "estimated_commission":
		fieldName = "SUM(pre_commission)"
	case "settled_commission":
		fieldName = "SUM(commission)"
	case "order_amount":
		fieldName = "SUM(pay_price)"
	default:
		fieldName = "COUNT(*)"
	}

	// 使用批量查询方式，减少单次查询的复杂度
	return s.executeBatchHourlyQuery(tableName, fieldName, planIds, req.StartDate)
}

// getFilteredPlanIds 获取过滤后的计划ID列表
func (s *dashboardTrendService) getFilteredPlanIds(req domain.DashboardTrendParam, userID int, scope string, departmentUsers []uint64) ([]string, error) {
	// 构建基础查询
	query := s.db.Table("ad_slot_plans").Select("id")

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加筛选条件 - 修改为只在大于0时添加过滤
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.MediaID > 0 {
		query = query.Where("media_id = ?", req.MediaID)
	}
	if req.PlanID > 0 {
		query = query.Where("id = ?", req.PlanID)
	}
	if req.ProductID > 0 {
		query = query.Where("ad_product_id = ?", req.ProductID)
	}

	// 处理合作类型筛选
	if req.Type > 0 {
		var cooperationType string
		switch req.Type {
		case 1: // 普通投放
			cooperationType = "traffic"
		case 2: // CPS合作
			cooperationType = "cps"
		case 3: // 灯火
			cooperationType = "dh"
		}

		if cooperationType != "" {
			var mediaIds []int
			err := s.db.Table("ad_media").
				Where("cooperation_type = ?", cooperationType).
				Pluck("id", &mediaIds).Error
			if err == nil && len(mediaIds) > 0 {
				query = query.Where("media_id IN (?)", mediaIds)
			}
		}
	}

	// 处理渠道分类筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"} // 添加wechat_plugin
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			var slotIds []int
			err := s.db.Table("ad_slots").
				Where("type IN (?)", slotTypes).
				Pluck("id", &slotIds).Error
			if err == nil && len(slotIds) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIds)
			}
		}
	}

	var planIds []string
	if err := query.Pluck("id", &planIds).Error; err != nil {
		return nil, err
	}

	return planIds, nil
}

// executeBatchHourlyQuery 执行批量小时查询
func (s *dashboardTrendService) executeBatchHourlyQuery(tableName, fieldName string, planIds []string, date string) ([]float64, []string, error) {
	// 生成24小时的时间点
	timePoints := make([]string, 24)
	for i := 0; i < 24; i++ {
		timePoints[i] = fmt.Sprintf("%02d:00", i)
	}

	// 初始化结果映射
	dataMap := make(map[int]float64)

	// 如果没有找到符合条件的计划ID，尝试直接查询所有数据
	if len(planIds) == 0 {
		fmt.Printf("没有找到符合条件的计划ID，尝试直接查询所有订单数据\n")
		return s.executeDirectHourlyQuery(tableName, fieldName, date)
	}

	// 分批查询，每次查询6小时的数据
	batchSize := 6
	for i := 0; i < 24; i += batchSize {
		endHour := i + batchSize
		if endHour > 24 {
			endHour = 24
		}

		// 构建时间范围
		startTime := fmt.Sprintf("%s %02d:00:00", date, i)
		endTime := fmt.Sprintf("%s %02d:59:59", date, endHour-1)

		// 构建查询参数
		args := make([]interface{}, 0, len(planIds)+2)
		args = append(args, startTime, endTime)

		// 构建计划ID条件
		planIdStr := ""
		if len(planIds) > 0 {
			planIdStr = "AND ad_plan_id IN ("
			for idx, planId := range planIds {
				if idx > 0 {
					planIdStr += ","
				}
				planIdStr += "?"
				args = append(args, planId)
			}
			planIdStr += ")"
		}

		// 构建查询SQL
		sqlQuery := fmt.Sprintf(`
			SELECT 
				HOUR(create_time) as hour, 
				%s as value 
			FROM %s
			WHERE create_time >= ? AND create_time <= ?
			AND order_status IN (2,3,4)
			AND leak = 0
			%s
			GROUP BY HOUR(create_time)
			ORDER BY hour ASC
		`, fieldName, tableName, planIdStr)

		// 执行查询
		var batchResults []struct {
			Hour  int
			Value float64
		}

		// 设置查询超时
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second) // 增加超时时间
		defer cancel()

		err := s.db.WithContext(ctx).Raw(sqlQuery, args...).Scan(&batchResults).Error
		if err != nil {
			fmt.Printf("批次查询失败 %d-%d: %v\n", i, endHour, err)
			continue
		}

		// 处理结果
		for _, result := range batchResults {
			if result.Hour >= i && result.Hour < endHour {
				dataMap[result.Hour] = result.Value
			}
		}
	}

	// 生成最终数据数组
	data := make([]float64, 24)
	for i := 0; i < 24; i++ {
		if value, exists := dataMap[i]; exists {
			data[i] = value
		}
	}

	fmt.Printf("分时数据查询完成: 共%d小时有数据, 总值=%f\n", len(dataMap), sum(data))

	return data, timePoints, nil
}

// executeDirectHourlyQuery 直接查询小时数据（不使用计划ID过滤）
func (s *dashboardTrendService) executeDirectHourlyQuery(tableName, fieldName string, date string) ([]float64, []string, error) {
	// 生成24小时的时间点
	timePoints := make([]string, 24)
	for i := 0; i < 24; i++ {
		timePoints[i] = fmt.Sprintf("%02d:00", i)
	}

	// 构建简化查询（不使用计划ID过滤）
	sqlQuery := fmt.Sprintf(`
		SELECT 
			HOUR(create_time) as hour, 
			%s as value 
		FROM %s
		WHERE create_time >= ? AND create_time <= ?
		AND order_status IN (2,3,4)
		AND leak = 0
		GROUP BY HOUR(create_time)
		ORDER BY hour ASC
	`, fieldName, tableName)

	// 执行查询
	var results []struct {
		Hour  int
		Value float64
	}

	startTime := fmt.Sprintf("%s 00:00:00", date)
	endTime := fmt.Sprintf("%s 23:59:59", date)

	// 设置查询超时
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second) // 增加超时时间
	defer cancel()

	err := s.db.WithContext(ctx).Raw(sqlQuery, startTime, endTime).Scan(&results).Error
	if err != nil {
		fmt.Printf("直接查询失败: %v\n", err)
		return make([]float64, 24), timePoints, nil
	}

	// 处理结果
	data := make([]float64, 24)
	for _, result := range results {
		if result.Hour >= 0 && result.Hour < 24 {
			data[result.Hour] = result.Value
		}
	}

	fmt.Printf("直接查询完成: 共%d小时有数据, 总值=%f\n", len(results), sum(data))

	return data, timePoints, nil
}

// TrendData 趋势数据结构
type TrendData struct {
	TimePoints []string
	Values     []float64
}

// getTrendByDateRange 获取指定日期范围的趋势数据
func (s *dashboardTrendService) getTrendByDateRange(req domain.DashboardTrendParam, userID int, userRole int, scope string, departmentUsers []uint64, startDate, endDate string) (TrendData, error) {
	// 获取日期区间内的所有日期
	dates, err := s.getAllDatesBetween(startDate, endDate)
	if err != nil {
		return TrendData{}, err
	}

	// 检查是否是订单相关指标，对于订单指标需要特殊处理
	if req.Metric == "orders" {
		// 对于订单量，需要从原始订单表统计
		return s.getOrderTrendByDateRange(req, userID, userRole, scope, departmentUsers, startDate, endDate)
	}

	// 选择指标对应的字段
	var field string
	var isIntegerMetric bool
	switch req.Metric {
	case "estimated_revenue", "estimated_commission":
		field = "bd_revenue"
	case "settled_revenue", "settled_commission":
		field = "bd_settle_revenue"
	case "cost":
		field = "cost"
	case "estimated_profit":
		field = "bd_profit"
	case "settled_profit":
		field = "bd_settle_profit"
	case "click_pv":
		field = "elm_click_pv"
		isIntegerMetric = true
	case "click_uv":
		field = "elm_click_uv"
		isIntegerMetric = true
	default:
		field = "bd_profit"
	}

	// 构建查询
	query := s.db.Table("ad_slot_plan_daily_stats").
		Select("DATE_FORMAT(date, '%Y-%m-%d') as date_str, SUM("+field+") as value").
		Where("date BETWEEN ? AND ?", startDate, endDate).
		Group("date_str").
		Order("date_str")

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加其他过滤条件
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.MediaID > 0 {
		query = query.Where("media_id = ?", req.MediaID)
	}
	if req.PlanID > 0 {
		query = query.Where("plan_id = ?", req.PlanID)
	}
	if req.ProductID > 0 {
		query = query.Where("ad_product_id = ?", req.ProductID)
	}

	// 根据合作类型过滤
	if req.Type > 0 {
		var cooperationType string
		switch req.Type {
		case 1:
			cooperationType = "traffic"
		case 2:
			cooperationType = "cps"
		case 3:
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int
			s.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("media_id IN (?)", mediaIDs)
			}
		}
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			s.db.Model(&model.AdSlots{}).
				Where("type IN (?)", slotTypes).
				Select("id").
				Scan(&slotIDs)

			if len(slotIDs) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIDs)
			}
		}
	}

	// 查询数据
	type ResultItem struct {
		DateStr string `gorm:"column:date_str"`
		Value   float64
	}
	var items []ResultItem
	if err := query.Find(&items).Error; err != nil {
		return TrendData{}, err
	}

	// 构造返回数据
	valueMap := make(map[string]float64)
	for _, item := range items {
		value := item.Value
		// 对于整数类型指标，确保值是整数
		if isIntegerMetric {
			value = float64(int(value + 0.5)) // 四舍五入取整
		}
		valueMap[item.DateStr] = value
	}

	// 填充每一天的数据
	values := make([]float64, len(dates))
	for i, date := range dates {
		values[i] = valueMap[date]
	}

	return TrendData{
		TimePoints: dates,
		Values:     values,
	}, nil
}

// getOrderTrendByDateRange 获取订单量趋势数据（修改为使用单日查询累加）
func (s *dashboardTrendService) getOrderTrendByDateRange(req domain.DashboardTrendParam, userID int, userRole int, scope string, departmentUsers []uint64, startDate, endDate string) (TrendData, error) {
	// 获取日期区间内的所有日期
	dates, err := s.getAllDatesBetween(startDate, endDate)
	if err != nil {
		return TrendData{}, err
	}

	// 为每一天单独查询订单数量，使用新的优化方法
	values := make([]float64, len(dates))

	for i, date := range dates {
		// 为每一天创建参数
		dailyReq := domain.DashboardTrendParam{
			UserID:    req.UserID,
			MediaID:   req.MediaID,
			PlanID:    req.PlanID,
			ProductID: req.ProductID,
			Metric:    req.Metric,
			StartDate: date,
			EndDate:   date,
			Type:      req.Type,
			Category:  req.Category,
		}

		// 获取该日期的小时数据
		hourlyData, _, err := s.getHourlyDataForSingleDay(dailyReq, userID, scope, departmentUsers)
		if err != nil {
			fmt.Printf("获取%s的小时数据失败: %v\n", date, err)
			values[i] = 0
			continue
		}

		// 计算当天总和
		var dayTotal float64
		for _, v := range hourlyData {
			dayTotal += v
		}
		values[i] = dayTotal
	}

	return TrendData{
		TimePoints: dates,
		Values:     values,
	}, nil
}

// getAllDatesBetween 获取两个日期之间的所有日期
func (s *dashboardTrendService) getAllDatesBetween(startDate, endDate string) ([]string, error) {
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return nil, err
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return nil, err
	}

	var dates []string
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d.Format("2006-01-02"))
	}

	return dates, nil
}

// getOrderTableName 根据日期获取订单表名(分表)
func getOrderTableName(date string) string {
	// 解析日期
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return "ad_orders" // 默认表
	}

	// 生成分表名 - 格式为 ad_orders_YYYYMM
	return fmt.Sprintf("ad_orders_%s", t.Format("200601"))
}

// queryHourlyDataDirectly 直接查询小时数据（尽可能简单地查询原始订单表）
func (s *dashboardTrendService) queryHourlyDataDirectly(date string, metric string, userID int, scope string, departmentUsers []uint64, req domain.DashboardTrendParam) ([]float64, []string, error) {
	// 生成24小时的时间点
	timePoints := make([]string, 24)
	for i := 0; i < 24; i++ {
		timePoints[i] = fmt.Sprintf("%02d:00", i)
	}

	// 初始化24小时的数据数组
	values := make([]float64, 24)

	// 获取对应日期的订单表名
	tableName := getOrderTableName(date)
	fmt.Printf("使用数据表: %s\n", tableName)

	// 检查表是否存在
	var tableExists bool
	err := s.db.Raw("SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?", tableName).Scan(&tableExists).Error
	if err != nil || !tableExists {
		fmt.Printf("表不存在或查询失败: %s, err=%v\n", tableName, err)
		return values, timePoints, fmt.Errorf("表不存在: %s", tableName)
	}

	// 根据指标选择查询字段
	var fieldName string
	switch metric {
	case "orders":
		fieldName = "COUNT(*)"
	case "estimated_commission", "estimated_revenue":
		fieldName = "SUM(pre_commission)"
	case "settled_commission", "settled_revenue":
		fieldName = "SUM(commission)"
	case "order_amount":
		fieldName = "SUM(pay_price)"
	default:
		fieldName = "COUNT(*)"
	}

	// 直接查询所有24小时数据
	baseSql := fmt.Sprintf(
		"SELECT HOUR(create_time) as hour, %s as value FROM %s WHERE create_time >= ? AND create_time <= ? AND order_status IN (2,3,4) AND leak = 0",
		fieldName, tableName)

	// 构建WHERE条件
	whereConditions := []string{}
	args := []interface{}{date + " 00:00:00", date + " 23:59:59"}

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		whereClause := "user_id IN ("
		for i, id := range departmentUsers {
			if i > 0 {
				whereClause += ","
			}
			whereClause += "?"
			args = append(args, id)
		}
		whereClause += ")"
		whereConditions = append(whereConditions, whereClause)
	} else if scope == "personal" {
		whereConditions = append(whereConditions, "user_id = ?")
		args = append(args, userID)
	}

	// *** 关键点 *** 对齐API参数处理逻辑，确保以字符串形式处理参数

	// 添加user_id过滤
	if req.UserID != 0 {
		whereConditions = append(whereConditions, "user_id = ?")
		args = append(args, req.UserID)
	}

	// 添加media_id过滤
	if req.MediaID != 0 {
		whereConditions = append(whereConditions, "media_id = ?")
		args = append(args, req.MediaID)
	}

	// 添加plan_id过滤
	if req.PlanID != 0 {
		whereConditions = append(whereConditions, "ad_plan_id = ?")
		args = append(args, req.PlanID)
	}

	// 添加product_id过滤
	if req.ProductID != 0 {
		whereConditions = append(whereConditions, "product_id = ?")
		args = append(args, req.ProductID)
	}

	// 根据类型筛选
	if req.Type != 0 {
		var cooperationType string
		switch req.Type {
		case 1:
			cooperationType = "traffic"
		case 2:
			cooperationType = "cps"
		case 3:
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID列表
			var mediaIDs []int
			subQuery := "SELECT id FROM ad_media WHERE cooperation_type = ?"
			if err := s.db.Raw(subQuery, cooperationType).Scan(&mediaIDs).Error; err == nil && len(mediaIDs) > 0 {
				mediaIDClause := "media_id IN ("
				for i, id := range mediaIDs {
					if i > 0 {
						mediaIDClause += ","
					}
					mediaIDClause += "?"
					args = append(args, id)
				}
				mediaIDClause += ")"
				whereConditions = append(whereConditions, mediaIDClause)
			}
		}
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			slotTypesPlaceholders := strings.Repeat("?,", len(slotTypes))
			slotTypesPlaceholders = slotTypesPlaceholders[:len(slotTypesPlaceholders)-1]

			slotQuery := fmt.Sprintf("SELECT id FROM ad_slots WHERE type IN (%s)", slotTypesPlaceholders)
			slotQueryArgs := make([]interface{}, len(slotTypes))
			for i, t := range slotTypes {
				slotQueryArgs[i] = t
			}

			if err := s.db.Raw(slotQuery, slotQueryArgs...).Scan(&slotIDs).Error; err == nil && len(slotIDs) > 0 {
				slotIDClause := "ad_slot_id IN ("
				for i, id := range slotIDs {
					if i > 0 {
						slotIDClause += ","
					}
					slotIDClause += "?"
					args = append(args, id)
				}
				slotIDClause += ")"
				whereConditions = append(whereConditions, slotIDClause)
			}
		}
	}

	// 组合所有WHERE条件
	for _, condition := range whereConditions {
		baseSql += " AND " + condition
	}

	// 添加分组和排序
	baseSql += " GROUP BY HOUR(create_time) ORDER BY hour ASC"

	// 打印生成的SQL和参数用于调试
	fmt.Printf("SQL: %s\n参数: %v\n", baseSql, args)

	// 设置查询超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()

	// 执行查询
	var results []struct {
		Hour  int
		Value float64
	}

	if err := s.db.WithContext(ctx).Raw(baseSql, args...).Scan(&results).Error; err != nil {
		fmt.Printf("查询失败: %v\n", err)
		return values, timePoints, err
	}

	// 填充数据
	for _, result := range results {
		if result.Hour >= 0 && result.Hour < 24 {
			values[result.Hour] = result.Value
		}
	}

	// 记录查询结果
	fmt.Printf("查询成功: 找到%d个小时数据, 总和=%f\n", len(results), sum(values))

	return values, timePoints, nil
}
