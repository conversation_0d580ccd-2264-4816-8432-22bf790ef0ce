package xiaohongshu

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// TestGetCreativityRealtimeReport 测试获取创意实时报表
func TestGetCreativityRealtimeReport(t *testing.T) {
	// 创建模拟服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 验证请求方法
		if r.Method != "POST" {
			t.<PERSON><PERSON><PERSON>("期望POST请求，实际为%s", r.Method)
		}

		// 验证Content-Type
		if r.Header.Get("Content-Type") != "application/json" {
			t.Errorf("期望Content-Type为application/json，实际为%s", r.Header.Get("Content-Type"))
		}

		// 验证Access-Token
		if r.Header.Get("Access-Token") == "" {
			t.Errorf("缺少Access-Token头")
		}

		// 返回模拟响应
		response := CreativityRealtimeReportResponse{
			Code:    0,
			Msg:     "成功",
			Success: true,
			RequestID: "test-request-id",
			Page: PageRespDTO{
				PageIndex:  1,
				TotalCount: 1,
			},
			TotalData: DataReportDTO{
				Fee:        "100.50",
				Impression: "1000",
				Click:      "50",
				Ctr:        "5.0",
			},
			CreativityDtos: []CreativityDTO{
				{
					Data: DataReportDTO{
						Fee:        "100.50",
						Impression: "1000",
						Click:      "50",
						Ctr:        "5.0",
					},
					BaseCampaign: BaseCampaignDTO{
						CampaignID:   123456,
						CampaignName: "测试计划",
					},
					BaseUnit: BaseUnitDTO{
						UnitID:   789012,
						UnitName: "测试单元",
					},
					BaseCreativity: BaseCreativityDTO{
						CreativityID:   345678,
						CreativityName: "测试创意",
					},
				},
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	// 创建测试客户端
	client := &Client{
		config: &Config{
			AppId:  "test_app_id",
			Secret: "test_secret",
			IsProd: false,
		},
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 创建测试请求
	request := &CreativityRealtimeReportRequest{
		AdvertiserID: 1234567890,
		StartDate:    "2024-01-01",
		EndDate:      "2024-01-01",
		PageNum:      1,
		PageSize:     20,
	}

	// 直接测试requestCreativityRealtimeReport方法，使用测试服务器URL
	realtimeResp, err := client.requestCreativityRealtimeReport(server.URL, "test_access_token", request)
	if err != nil {
		t.Fatalf("获取创意实时报表失败: %v", err)
	}

	if !realtimeResp.Success {
		t.Errorf("期望成功，实际失败: %s", realtimeResp.Msg)
	}

	if len(realtimeResp.CreativityDtos) != 1 {
		t.Errorf("期望创意数据列表长度为1，实际为%d", len(realtimeResp.CreativityDtos))
	}

	if realtimeResp.CreativityDtos[0].BaseCampaign.CampaignID != 123456 {
		t.Errorf("期望计划ID为123456，实际为%d", realtimeResp.CreativityDtos[0].BaseCampaign.CampaignID)
	}

	if realtimeResp.Page.TotalCount != 1 {
		t.Errorf("期望总记录数为1，实际为%d", realtimeResp.Page.TotalCount)
	}
}

// TestRealtimeReportBuilder 测试实时报表构建器
func TestRealtimeReportBuilder(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"

	// 测试基础构建
	builder := NewRealtimeReportBuilder(advertiserID, startDate, endDate)
	request := builder.Build()

	if request.AdvertiserID != advertiserID {
		t.Errorf("期望广告主ID为%d，实际为%d", advertiserID, request.AdvertiserID)
	}

	if request.StartDate != startDate {
		t.Errorf("期望开始日期为%s，实际为%s", startDate, request.StartDate)
	}

	if request.EndDate != endDate {
		t.Errorf("期望结束日期为%s，实际为%s", endDate, request.EndDate)
	}

	// 测试链式调用
	request2 := NewRealtimeReportBuilder(advertiserID, startDate, endDate).
		WithPagination(2, 50).
		WithSort("fee", SortDesc).
		WithPlacementList([]int{PlacementFeed, PlacementSearch}).
		WithCreativityFilterState(CreativityFilterStateActive).
		WithHourlyData(true).
		Build()

	if request2.PageNum != 2 {
		t.Errorf("期望页数为2，实际为%d", request2.PageNum)
	}

	if request2.PageSize != 50 {
		t.Errorf("期望页大小为50，实际为%d", request2.PageSize)
	}

	if request2.SortColumn != "fee" {
		t.Errorf("期望排序字段为fee，实际为%s", request2.SortColumn)
	}

	if request2.Sort != SortDesc {
		t.Errorf("期望排序方式为%s，实际为%s", SortDesc, request2.Sort)
	}

	if len(request2.PlacementList) != 2 {
		t.Errorf("期望广告类型列表长度为2，实际为%d", len(request2.PlacementList))
	}

	if request2.CreativityFilterState != CreativityFilterStateActive {
		t.Errorf("期望创意状态为%d，实际为%d", CreativityFilterStateActive, request2.CreativityFilterState)
	}

	if !request2.NeedHourlyData {
		t.Errorf("期望需要小时数据为true，实际为false")
	}
}

// TestCreateTodayRealtimeReport 测试创建今日实时报表
func TestCreateTodayRealtimeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	request := CreateTodayRealtimeReport(advertiserID)

	today := time.Now().Format("2006-01-02")

	if request.AdvertiserID != advertiserID {
		t.Errorf("期望广告主ID为%d，实际为%d", advertiserID, request.AdvertiserID)
	}

	if request.StartDate != today {
		t.Errorf("期望开始日期为%s，实际为%s", today, request.StartDate)
	}

	if request.EndDate != today {
		t.Errorf("期望结束日期为%s，实际为%s", today, request.EndDate)
	}

	if request.PageSize != 50 {
		t.Errorf("期望页大小为50，实际为%d", request.PageSize)
	}
}

// TestCreateYesterdayRealtimeReport 测试创建昨日实时报表
func TestCreateYesterdayRealtimeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	request := CreateYesterdayRealtimeReport(advertiserID)

	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	if request.StartDate != yesterday {
		t.Errorf("期望开始日期为%s，实际为%s", yesterday, request.StartDate)
	}

	if request.EndDate != yesterday {
		t.Errorf("期望结束日期为%s，实际为%s", yesterday, request.EndDate)
	}
}

// TestCreateLast7DaysRealtimeReport 测试创建最近7天实时报表
func TestCreateLast7DaysRealtimeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	request := CreateLast7DaysRealtimeReport(advertiserID)

	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -7).Format("2006-01-02")

	if request.StartDate != startDate {
		t.Errorf("期望开始日期为%s，实际为%s", startDate, request.StartDate)
	}

	if request.EndDate != endDate {
		t.Errorf("期望结束日期为%s，实际为%s", endDate, request.EndDate)
	}

	if request.PageSize != 100 {
		t.Errorf("期望页大小为100，实际为%d", request.PageSize)
	}
}

// TestCreateFeedRealtimeReport 测试创建信息流实时报表
func TestCreateFeedRealtimeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"

	request := CreateFeedRealtimeReport(advertiserID, startDate, endDate)

	if len(request.PlacementList) != 1 {
		t.Errorf("期望广告类型列表长度为1，实际为%d", len(request.PlacementList))
	}

	if request.PlacementList[0] != PlacementFeed {
		t.Errorf("期望广告类型为%d，实际为%d", PlacementFeed, request.PlacementList[0])
	}
}

// TestCreateSearchRealtimeReport 测试创建搜索实时报表
func TestCreateSearchRealtimeReport(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"

	request := CreateSearchRealtimeReport(advertiserID, startDate, endDate)

	if len(request.PlacementList) != 1 {
		t.Errorf("期望广告类型列表长度为1，实际为%d", len(request.PlacementList))
	}

	if request.PlacementList[0] != PlacementSearch {
		t.Errorf("期望广告类型为%d，实际为%d", PlacementSearch, request.PlacementList[0])
	}
}

// TestCreateActiveCreativityReport 测试创建有效创意实时报表
func TestCreateActiveCreativityReport(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"

	request := CreateActiveCreativityReport(advertiserID, startDate, endDate)

	if request.CreativityFilterState != CreativityFilterStateActive {
		t.Errorf("期望创意状态为%d，实际为%d", CreativityFilterStateActive, request.CreativityFilterState)
	}

	if request.CreativityAuditState != CreativityAuditStateApproved {
		t.Errorf("期望审核状态为%d，实际为%d", CreativityAuditStateApproved, request.CreativityAuditState)
	}
}

// TestCreateCreativityReportByName 测试根据创意名称创建实时报表
func TestCreateCreativityReportByName(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"
	creativityName := "测试创意"

	request := CreateCreativityReportByName(advertiserID, startDate, endDate, creativityName)

	if request.Name != creativityName {
		t.Errorf("期望创意名称为%s，实际为%s", creativityName, request.Name)
	}
}

// TestCreateCreativityReportByID 测试根据创意ID创建实时报表
func TestCreateCreativityReportByID(t *testing.T) {
	advertiserID := int64(1234567890)
	startDate := "2024-01-01"
	endDate := "2024-01-07"
	creativityID := 123456

	request := CreateCreativityReportByID(advertiserID, startDate, endDate, creativityID)

	if request.ID != creativityID {
		t.Errorf("期望创意ID为%d，实际为%d", creativityID, request.ID)
	}
}

// TestGetRealtimeMetrics 测试获取实时报表指标
func TestGetRealtimeMetrics(t *testing.T) {
	// 测试基础指标
	basicMetrics := GetRealtimeBasicMetrics()
	if len(basicMetrics) == 0 {
		t.Errorf("基础指标列表不应为空")
	}

	// 测试互动指标
	interactionMetrics := GetRealtimeInteractionMetrics()
	if len(interactionMetrics) == 0 {
		t.Errorf("互动指标列表不应为空")
	}

	// 测试转化指标
	conversionMetrics := GetRealtimeConversionMetrics()
	if len(conversionMetrics) == 0 {
		t.Errorf("转化指标列表不应为空")
	}

	// 测试直播间指标
	liveMetrics := GetRealtimeLiveMetrics()
	if len(liveMetrics) == 0 {
		t.Errorf("直播间指标列表不应为空")
	}

	// 测试所有指标
	allMetrics := GetRealtimeAllMetrics()
	expectedLength := len(basicMetrics) + len(interactionMetrics) + len(conversionMetrics) + len(liveMetrics)
	if len(allMetrics) != expectedLength {
		t.Errorf("期望所有指标长度为%d，实际为%d", expectedLength, len(allMetrics))
	}
}
