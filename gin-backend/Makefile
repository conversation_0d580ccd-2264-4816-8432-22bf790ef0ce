.PHONY: init run build test clean tidy

# 初始化项目
init:
	@echo "初始化Gin Backend项目..."
	@bash scripts/init.sh

# 运行项目
run:
	@echo "启动服务..."
	@go run cmd/main.go

# 构建项目
build:
	@echo "构建项目..."
	@go build -o bin/gin-backend cmd/main.go

# 运行测试
test:
	@echo "运行测试..."
	@go test -v ./...

# 整理依赖
tidy:
	@echo "整理Go模块依赖..."
	@go mod tidy

# 清理构建文件
clean:
	@echo "清理构建文件..."
	@rm -rf bin/
	@go clean

# 代码格式化
fmt:
	@echo "格式化代码..."
	@go fmt ./...

# 代码检查
vet:
	@echo "代码检查..."
	@go vet ./...

# 帮助信息
help:
	@echo "可用命令:"
	@echo "  init   - 初始化项目依赖"
	@echo "  run    - 运行项目"
	@echo "  build  - 构建项目"
	@echo "  test   - 运行测试"
	@echo "  tidy   - 整理依赖"
	@echo "  clean  - 清理构建文件"
	@echo "  fmt    - 格式化代码"
	@echo "  vet    - 代码检查"
	@echo "  help   - 显示帮助信息" 