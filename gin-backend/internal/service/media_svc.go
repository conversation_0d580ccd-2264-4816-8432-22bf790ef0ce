package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

// MediaService 媒体服务
type MediaService struct {
	db               *gorm.DB
	dataScopeService *DataScopeService
}

// NewMediaService 创建媒体服务
func NewMediaService() *MediaService {
	return &MediaService{
		db:               global.DB,
		dataScopeService: NewDataScopeService(),
	}
}

// GetList 获取媒体列表
func (s *MediaService) GetList(ctx context.Context, userInfo domain.UserEntity, param domain.MediaListParam) (domain.MediaListResult, error) {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "media")
	if err != nil {
		return domain.MediaListResult{}, fmt.Errorf("获取数据权限失败: %w", err)
	}

	query := s.db.WithContext(ctx).Model(&model.Media{})

	// 应用数据权限
	switch dataScope {
	case "all":
		// 管理员可以看到所有数据，不需要额外条件
	case "dept":
		// 部门权限：可以看到本部门所有用户的数据
		if len(departmentUserIDs) > 0 {
			query = query.Where("user_id IN ?", departmentUserIDs)
		} else {
			// 如果部门没有其他用户，只能看到自己的
			query = query.Where("user_id = ?", userInfo.ID)
		}
	case "self":
		// 个人权限：只能看到自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	default:
		// 默认只能看到自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	}

	// 添加筛选条件
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}

	if param.UserID != 0 {
		query = query.Where("user_id = ?", param.UserID)
	}

	if len(param.Types) > 0 {
		// 媒体类型是JSON数组，使用JSON_CONTAINS查询
		typeConditions := make([]string, len(param.Types))
		typeValues := make([]any, len(param.Types))
		for i, t := range param.Types {
			typeConditions[i] = "JSON_CONTAINS(types, ?)"
			typeValues[i] = fmt.Sprintf(`"%s"`, t)
		}
		if len(typeConditions) > 0 {
			query = query.Where(strings.Join(typeConditions, " OR "), typeValues...)
		}
	}

	if param.Industry != "" {
		query = query.Where("industry = ?", param.Industry)
	}

	if param.AuditStatus != "" {
		query = query.Where("audit_status = ?", param.AuditStatus)
	}

	if param.CooperationStatus != "" {
		query = query.Where("cooperation_status = ?", param.CooperationStatus)
	}

	if param.CooperationType != "" {
		query = query.Where("cooperation_type = ?", param.CooperationType)
	}

	// 添加软删除条件
	query = query.Where("deleted_at IS NULL")

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.MediaListResult{}, fmt.Errorf("获取媒体总数失败: %w", err)
	}

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}

	// 获取分页数据
	var medias []model.Media
	offset := (param.Page - 1) * param.PageSize
	if err := query.Offset(offset).Limit(param.PageSize).Order("id DESC").Find(&medias).Error; err != nil {
		return domain.MediaListResult{}, fmt.Errorf("获取媒体列表失败: %w", err)
	}

	// 转换为响应格式
	var mediaEntities []domain.MediaEntity
	for _, media := range medias {
		// 转换model.Media到domain.MediaEntity
		entity := s.convertModelToEntity(media)
		mediaEntities = append(mediaEntities, entity)
	}

	return domain.MediaListResult{
		List:     mediaEntities,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

// 将model.Media转换为domain.MediaEntity
func (s *MediaService) convertModelToEntity(m model.Media) domain.MediaEntity {
	// 转换媒体类型
	var types []string
	if m.Types != "" {
		mediaTypes, _ := m.GetTypes()
		types = mediaTypes
	}

	// 转换区域编码
	var regionCodes []string
	if m.RegionCodes != "" {
		regionCodes, _ = m.GetRegionCodes()
	}

	// 转换平台配置
	var platformConfig domain.MediaPlatformConfig
	if m.PlatformConfig != "" {
		modelConfig, _ := m.GetPlatformConfig()
		if modelConfig.Platform != "" {
			platformConfig = domain.MediaPlatformConfig{
				Platform: modelConfig.Platform,
				Info:     modelConfig.Info,
			}
		}
	}

	return domain.MediaEntity{
		ID:                m.ID,
		Code:              m.Code,
		AdAgentID:         m.AdAgentID,
		Name:              m.Name,
		AuditStatus:       m.AuditStatus,
		CooperationStatus: m.CooperationStatus,
		CooperationType:   m.CooperationType,
		Account:           m.Account,
		LastLoginAt:       m.LastLoginAt,
		Balance:           m.Balance,
		Types:             types,
		Industry:          m.Industry,
		CustomIndustry:    m.CustomIndustry,
		DailyActivity:     m.DailyActivity,
		TransactionVolume: m.TransactionVolume,
		RegionCodes:       regionCodes,
		CompanyName:       m.CompanyName,
		CompanyAddress:    m.CompanyAddress,
		ContactName:       m.ContactName,
		ContactPhone:      m.ContactPhone,
		RejectReason:      m.RejectReason,
		UserID:            m.UserID,
		Remark:            m.Remark,
		PlatformConfig:    platformConfig,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// GetSelectOptions 获取媒体选择器选项
func (s *MediaService) GetSelectOptions(ctx context.Context, userInfo domain.UserEntity) ([]domain.MediaSelectOption, error) {
	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "media")
	if err != nil {
		return nil, fmt.Errorf("获取数据权限失败: %w", err)
	}

	query := s.db.WithContext(ctx).Model(&model.Media{}).Select("id, name")

	// 应用数据权限
	switch dataScope {
	case "all":
		// 管理员可以看到所有数据，不需要额外条件
	case "dept":
		// 部门权限：可以看到本部门所有用户的数据
		if len(departmentUserIDs) > 0 {
			query = query.Where("user_id IN ?", departmentUserIDs)
		} else {
			// 如果部门没有其他用户，只能看到自己的
			query = query.Where("user_id = ?", userInfo.ID)
		}
	case "self":
		// 个人权限：只能看到自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	default:
		// 默认只能看到自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	}

	// 只获取已审核通过且未删除的媒体
	query = query.Where("audit_status = ? AND deleted_at IS NULL", "approved")

	// 按名称排序
	query = query.Order("name ASC")

	// 使用 model.Media 查询数据
	var options []model.Media
	if err := query.Find(&options).Error; err != nil {
		return nil, fmt.Errorf("获取媒体选择器选项失败: %w", err)
	}

	// 转换为 domain.MediaSelectOption 返回
	var result []domain.MediaSelectOption
	for _, opt := range options {
		result = append(result, domain.MediaSelectOption{
			ID:   opt.ID,
			Name: opt.Name,
		})
	}

	return result, nil
}

// GetDetail 获取媒体详情
func (s *MediaService) GetDetail(ctx context.Context, userInfo domain.UserEntity, id int64) (domain.MediaEntity, error) {
	if id <= 0 {
		return domain.MediaEntity{}, fmt.Errorf("媒体ID无效")
	}

	// 获取数据权限范围和用户ID列表
	dataScope, departmentUserIDs, err := s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "media")
	if err != nil {
		return domain.MediaEntity{}, fmt.Errorf("获取数据权限失败: %w", err)
	}

	// 构建查询
	query := s.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id)

	// 应用数据权限
	switch dataScope {
	case "all":
		// 所有数据，不添加过滤条件
	case "dept":
		if len(departmentUserIDs) > 0 {
			query = query.Where("user_id IN ?", departmentUserIDs)
		} else {
			query = query.Where("user_id = ?", userInfo.ID)
		}
	default:
		// 默认只能查看自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	}

	// 查询媒体
	var media model.Media
	if err := query.First(&media).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.MediaEntity{}, fmt.Errorf("媒体不存在或无权访问")
		}
		return domain.MediaEntity{}, fmt.Errorf("获取媒体详情失败: %w", err)
	}

	// 转换为实体
	entity := s.convertModelToEntity(media)

	// 判断entity是否为空（使用ID字段判断，ID为0表示空实体）
	if entity.ID == 0 {
		return domain.MediaEntity{}, nil
	}

	return entity, nil
}

// CheckNameExists 检查媒体名称是否已存在
func (s *MediaService) CheckNameExists(name string, excludeID int64) (bool, error) {
	var count int64
	query := s.db.Model(&model.Media{}).
		Where("name = ? AND deleted_at IS NULL", name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("检查媒体名称失败: %w", err)
	}

	return count > 0, nil
}

// generateMediaCode 生成媒体编号
func (s *MediaService) generateMediaCode() (string, error) {
	// 锁定生成过程，防止并发问题
	// 在生产环境中应该使用分布式锁或其他机制确保全局唯一
	// 这里简化实现，使用数据库事务

	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取当前最大编号
	var maxCode string
	err := tx.Model(&model.Media{}).
		Select("MAX(code)").
		Where("code LIKE ?", "M%").
		Scan(&maxCode).Error
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("获取最大编号失败: %w", err)
	}

	// 生成新编号
	var nextNum int
	if maxCode == "" {
		// 没有现有编号，从1开始
		nextNum = 1
	} else {
		// 解析现有最大编号
		numStr := maxCode[1:] // 去掉前缀 'M'
		num, err := strconv.Atoi(numStr)
		if err != nil {
			tx.Rollback()
			return "", fmt.Errorf("解析编号失败: %w", err)
		}
		nextNum = num + 1
	}

	// 格式化新编号
	newCode := fmt.Sprintf("M%06d", nextNum)

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return "", fmt.Errorf("提交事务失败: %w", err)
	}

	return newCode, nil
}

// validateCreateEntity 验证创建媒体实体
func (s *MediaService) validateCreateEntity(entity domain.MediaCreateEntity) error {
	// 验证名称不为空
	if entity.Name == "" {
		return fmt.Errorf("媒体名称不能为空")
	}

	// 验证媒体类型
	if len(entity.Types) == 0 {
		return fmt.Errorf("媒体类型不能为空")
	}
	for _, t := range entity.Types {
		if !s.isValidMediaType(t) {
			return fmt.Errorf("无效的媒体类型: %s", t)
		}
	}

	// 验证行业
	if entity.Industry != "" && entity.Industry != "other" && !s.isValidIndustry(entity.Industry) {
		return fmt.Errorf("无效的行业分类: %s", entity.Industry)
	}

	// 验证合作类型
	if entity.CooperationType != "" && !s.isValidCooperationType(entity.CooperationType) {
		return fmt.Errorf("无效的合作类型: %s", entity.CooperationType)
	}

	// 验证平台配置
	if entity.PlatformConfig.Platform != "" {
		if err := s.validatePlatformConfig(entity.PlatformConfig); err != nil {
			return err
		}
	}

	return nil
}

// validatePlatformConfig 验证平台配置
func (s *MediaService) validatePlatformConfig(config domain.MediaPlatformConfig) error {
	// 验证平台类型
	if config.Platform == "" {
		return fmt.Errorf("平台类型不能为空")
	}

	// 根据平台类型验证特定配置
	switch config.Platform {
	case "denghuoplus":
		// 验证灯火平台配置
		if config.Info == nil {
			return fmt.Errorf("灯火平台配置不能为空")
		}

		// 转换接口为具体结构
		infoBytes, err := json.Marshal(config.Info)
		if err != nil {
			return fmt.Errorf("平台配置格式错误: %w", err)
		}

		var info domain.DenghuoPlatformInfo
		if err := json.Unmarshal(infoBytes, &info); err != nil {
			return fmt.Errorf("灯火平台配置格式错误: %w", err)
		}

		// 验证必填字段
		if info.PID == "" {
			return fmt.Errorf("灯火平台PID不能为空")
		}
		if info.Token == "" {
			return fmt.Errorf("灯火平台Token不能为空")
		}
	case "xiaohongshu":
		// 验证小红书平台配置
		if config.Info == nil {
			return fmt.Errorf("小红书平台配置不能为空")
		}

		// 转换接口为具体结构
		infoBytes, err := json.Marshal(config.Info)
		if err != nil {
			return fmt.Errorf("平台配置格式错误: %w", err)
		}

		var info domain.XiaohongshuPlatformInfo
		if err := json.Unmarshal(infoBytes, &info); err != nil {
			return fmt.Errorf("小红书平台配置格式错误: %w", err)
		}

		// 验证必填字段
		if info.AppID == "" {
			return fmt.Errorf("小红书平台AppID不能为空")
		}
		if info.AppSecret == "" {
			return fmt.Errorf("小红书平台AppSecret不能为空")
		}
	// 可以添加其他平台的验证
	default:
		// 默认不做特殊验证
	}

	return nil
}

// Create 创建媒体
func (s *MediaService) Create(ctx context.Context, userInfo domain.UserEntity, entity domain.MediaCreateEntity) (domain.MediaCreateResult, error) {
	// 检查名称是否存在
	exists, err := s.CheckNameExists(entity.Name, 0)
	if err != nil {
		return domain.MediaCreateResult{}, fmt.Errorf("检查媒体名称失败: %w", err)
	}
	if exists {
		return domain.MediaCreateResult{}, fmt.Errorf("媒体名称'%s'已存在", entity.Name)
	}

	// 验证请求参数（可选）
	if err := s.validateCreateEntity(entity); err != nil {
		return domain.MediaCreateResult{}, err
	}

	// 生成媒体编码
	code, err := s.generateMediaCode()
	if err != nil {
		return domain.MediaCreateResult{}, fmt.Errorf("生成媒体编码失败: %w", err)
	}

	// 创建媒体实体
	media := model.Media{
		Code:              code,
		Name:              entity.Name,
		AdAgentID:         entity.AdAgentID,
		Account:           entity.Account,
		Password:          entity.Password,
		CooperationType:   entity.CooperationType,
		AuditStatus:       "pending",     // 默认待审核
		CooperationStatus: "not_started", // 默认未开始
		Industry:          entity.Industry,
		CustomIndustry:    entity.CustomIndustry,
		DailyActivity:     entity.DailyActivity,
		TransactionVolume: entity.TransactionVolume,
		CompanyName:       entity.CompanyName,
		CompanyAddress:    entity.CompanyAddress,
		ContactName:       entity.ContactName,
		ContactPhone:      entity.ContactPhone,
		UserID:            userInfo.ID, // 设置当前用户为媒体负责人
		CreatedBy:         userInfo.ID,
		UpdatedBy:         userInfo.ID,
		Remark:            entity.Remark,
	}

	// 设置媒体类型
	if err := media.SetTypes(model.MediaTypes(entity.Types)); err != nil {
		return domain.MediaCreateResult{}, fmt.Errorf("设置媒体类型失败: %w", err)
	}

	// 设置区域编码
	if err := media.SetRegionCodes(entity.RegionCodes); err != nil {
		return domain.MediaCreateResult{}, fmt.Errorf("设置区域编码失败: %w", err)
	}

	// 设置平台配置
	if entity.PlatformConfig.Platform != "" {
		// 验证平台配置
		if err := s.validatePlatformConfig(entity.PlatformConfig); err != nil {
			return domain.MediaCreateResult{}, err
		}

		// 将domain配置转换为model配置
		modelConfig := &model.MediaPlatformConfig{
			Platform: entity.PlatformConfig.Platform,
			Info:     entity.PlatformConfig.Info,
		}

		// 设置平台配置到媒体实体
		if err := media.SetPlatformConfig(modelConfig); err != nil {
			return domain.MediaCreateResult{}, fmt.Errorf("设置平台配置失败: %w", err)
		}
	}

	// 在事务中保存媒体
	tx := s.db.WithContext(ctx).Begin()
	if err := tx.Create(&media).Error; err != nil {
		tx.Rollback()
		return domain.MediaCreateResult{}, fmt.Errorf("创建媒体失败: %w", err)
	}
	tx.Commit()

	return domain.MediaCreateResult{
		ID: media.ID,
	}, nil
}

// validateUpdateEntity 验证更新媒体实体
func (s *MediaService) validateUpdateEntity(entity domain.MediaUpdateEntity) error {
	// 验证媒体ID
	if entity.ID <= 0 {
		return fmt.Errorf("媒体ID无效")
	}

	// 验证名称不为空
	if entity.Name == "" {
		return fmt.Errorf("媒体名称不能为空")
	}

	// 验证媒体类型
	if len(entity.Types) == 0 {
		return fmt.Errorf("媒体类型不能为空")
	}
	for _, t := range entity.Types {
		if !s.isValidMediaType(t) {
			return fmt.Errorf("无效的媒体类型: %s", t)
		}
	}

	// 验证行业
	if entity.Industry != "" && entity.Industry != "other" && !s.isValidIndustry(entity.Industry) {
		return fmt.Errorf("无效的行业分类: %s", entity.Industry)
	}

	// 验证合作类型
	if entity.CooperationType != "" && !s.isValidCooperationType(entity.CooperationType) {
		return fmt.Errorf("无效的合作类型: %s", entity.CooperationType)
	}

	// 其他字段验证可以根据实际业务需要添加

	return nil
}

// Update 更新媒体
func (s *MediaService) Update(ctx context.Context, userInfo domain.UserEntity, entity domain.MediaUpdateEntity) error {
	// 检查名称是否存在（排除自身）
	exists, err := s.CheckNameExists(entity.Name, entity.ID)
	if err != nil {
		return fmt.Errorf("检查媒体名称失败: %w", err)
	}
	if exists {
		return fmt.Errorf("媒体名称'%s'已存在", entity.Name)
	}

	// 验证更新权限
	if err := s.CheckDataPermission(ctx, entity.ID, userInfo, "", nil); err != nil {
		return err
	}

	// 验证请求参数
	if err := s.validateUpdateEntity(entity); err != nil {
		return err
	}

	// 首先获取现有媒体
	var existingMedia model.Media
	if err := s.db.WithContext(ctx).Where("id = ?", entity.ID).First(&existingMedia).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("媒体不存在")
		}
		return fmt.Errorf("获取媒体失败: %w", err)
	}

	// 更新媒体信息
	existingMedia.Name = entity.Name
	existingMedia.AdAgentID = entity.AdAgentID
	existingMedia.Account = entity.Account
	existingMedia.Industry = entity.Industry
	existingMedia.CustomIndustry = entity.CustomIndustry
	existingMedia.DailyActivity = entity.DailyActivity
	existingMedia.TransactionVolume = entity.TransactionVolume
	existingMedia.CompanyName = entity.CompanyName
	existingMedia.CompanyAddress = entity.CompanyAddress
	existingMedia.ContactName = entity.ContactName
	existingMedia.ContactPhone = entity.ContactPhone
	existingMedia.CooperationType = entity.CooperationType
	existingMedia.UpdatedBy = userInfo.ID
	existingMedia.Remark = entity.Remark

	// 只有在提供了新密码时才更新
	if entity.Password != "" {
		existingMedia.Password = entity.Password
	}

	// 设置媒体类型
	if err := existingMedia.SetTypes(model.MediaTypes(entity.Types)); err != nil {
		return fmt.Errorf("设置媒体类型失败: %w", err)
	}

	// 设置区域编码
	if err := existingMedia.SetRegionCodes(entity.RegionCodes); err != nil {
		return fmt.Errorf("设置区域编码失败: %w", err)
	}

	// 设置平台配置
	if entity.PlatformConfig.Platform != "" {
		// 验证平台配置
		if err := s.validatePlatformConfig(entity.PlatformConfig); err != nil {
			return err
		}

		// 将domain配置转换为model配置
		modelConfig := &model.MediaPlatformConfig{
			Platform: entity.PlatformConfig.Platform,
			Info:     entity.PlatformConfig.Info,
		}

		// 设置平台配置到媒体实体
		if err := existingMedia.SetPlatformConfig(modelConfig); err != nil {
			return fmt.Errorf("设置平台配置失败: %w", err)
		}
	}

	// 保存更新
	tx := s.db.WithContext(ctx).Begin()
	if err := tx.Save(&existingMedia).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("更新媒体失败: %w", err)
	}
	tx.Commit()

	return nil
}

// Delete 删除媒体
func (s *MediaService) Delete(ctx context.Context, userInfo domain.UserEntity, id int64) error {
	// 验证删除权限
	if err := s.CheckDataPermission(ctx, id, userInfo, "", nil); err != nil {
		return err
	}

	// 执行软删除
	tx := s.db.WithContext(ctx).Begin()
	if err := tx.Model(&model.Media{}).Where("id = ?", id).Update("deleted_at", time.Now()).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除媒体失败: %w", err)
	}
	tx.Commit()

	return nil
}

// Audit 审核媒体
func (s *MediaService) Audit(ctx context.Context, userInfo domain.UserEntity, entity domain.MediaAuditEntity) error {
	// 验证审核权限
	if err := s.checkPermission(ctx, userInfo, "media:audit"); err != nil {
		return err
	}

	// 验证媒体ID
	if entity.ID <= 0 {
		return fmt.Errorf("媒体ID无效")
	}

	// 验证审核状态
	if entity.AuditStatus != "approved" && entity.AuditStatus != "rejected" {
		return fmt.Errorf("无效的审核状态: %s", entity.AuditStatus)
	}

	// 如果拒绝，必须提供拒绝原因
	if entity.AuditStatus == "rejected" && entity.RejectReason == "" {
		return fmt.Errorf("拒绝时必须提供拒绝原因")
	}

	// 首先获取现有媒体
	var media model.Media
	if err := s.db.WithContext(ctx).Where("id = ?", entity.ID).First(&media).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("媒体不存在")
		}
		return fmt.Errorf("获取媒体失败: %w", err)
	}

	// 更新审核状态
	media.AuditStatus = entity.AuditStatus
	media.RejectReason = entity.RejectReason
	media.Remark = entity.Remark
	media.UpdatedBy = userInfo.ID

	// 保存更新
	tx := s.db.WithContext(ctx).Begin()
	if err := tx.Save(&media).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("审核媒体失败: %w", err)
	}
	tx.Commit()

	return nil
}

// CheckDataPermission 检查数据权限
func (s *MediaService) CheckDataPermission(ctx context.Context, id int64, userInfo domain.UserEntity, dataScope string, departmentUserIDs []int64) error {
	var count int64
	query := s.db.WithContext(ctx).Model(&model.Media{}).Where("id = ? AND deleted_at IS NULL", id)

	// 如果没有提供数据范围，先获取
	if dataScope == "" {
		var err error
		dataScope, departmentUserIDs, err = s.dataScopeService.GetDataScopeForUser(ctx, userInfo, "media")
		if err != nil {
			return fmt.Errorf("获取数据权限失败: %w", err)
		}
	}

	switch dataScope {
	case "all":
		// 所有数据，不添加过滤条件
	case "dept":
		if len(departmentUserIDs) > 0 {
			query = query.Where("user_id IN ?", departmentUserIDs)
		} else {
			query = query.Where("user_id = ?", userInfo.ID)
		}
	default:
		// 默认只能查看自己的数据
		query = query.Where("user_id = ?", userInfo.ID)
	}

	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("检查数据权限失败: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("没有权限操作该媒体")
	}

	return nil
}

// checkPermission 检查用户权限
func (s *MediaService) checkPermission(ctx context.Context, userInfo domain.UserEntity, permission string) error {
	// 在实际实现中，这里可能会调用权限服务来检查权限
	// 这里简化处理，假设管理员角色都有所有权限
	if userInfo.Role == 3 { // 角色3-管理层
		return nil
	}

	// 检查用户是否有特定权限
	// 这里应该调用权限服务来检查
	// 为简化示例，暂时直接返回nil
	return nil
}

// GetMediasByUserID 根据用户ID获取媒体列表
func (s *MediaService) GetMediasByUserID(userID int64) ([]model.Media, error) {
	var medias []model.Media
	if err := s.db.Where("user_id = ? AND deleted_at IS NULL", userID).Find(&medias).Error; err != nil {
		return nil, fmt.Errorf("获取用户媒体列表失败: %w", err)
	}
	return medias, nil
}

// GetApprovedMedias 获取已审核通过的媒体列表
func (s *MediaService) GetApprovedMedias() ([]model.Media, error) {
	var medias []model.Media
	if err := s.db.Where("audit_status = ? AND deleted_at IS NULL", "approved").
		Order("name ASC").Find(&medias).Error; err != nil {
		return nil, fmt.Errorf("获取已审核媒体列表失败: %w", err)
	}
	return medias, nil
}

// isValidMediaType 验证媒体类型是否有效
func (s *MediaService) isValidMediaType(mediaType string) bool {
	validTypes := map[string]bool{
		"self_media":    true, // 自媒体
		"shopping":      true, // 购物平台
		"news":          true, // 新闻资讯
		"video":         true, // 视频平台
		"social":        true, // 社交平台
		"blog":          true, // 博客
		"encyclopedia":  true, // 百科
		"forum":         true, // 论坛
		"vertical":      true, // 垂直领域
		"entertainment": true, // 娱乐
		"life":          true, // 生活服务
		"other":         true, // 其他
	}
	return validTypes[mediaType]
}

// isValidIndustry 验证所属行业是否有效
func (s *MediaService) isValidIndustry(industry string) bool {
	validIndustries := map[string]bool{
		"e-commerce":    true, // 电商
		"finance":       true, // 金融
		"education":     true, // 教育
		"technology":    true, // 科技
		"entertainment": true, // 娱乐
		"game":          true, // 游戏
		"healthcare":    true, // 医疗健康
		"fashion":       true, // 时尚
		"food":          true, // 餐饮美食
		"travel":        true, // 旅游
		"real_estate":   true, // 房地产
		"automotive":    true, // 汽车
		"sports":        true, // 体育
		"lifestyle":     true, // 生活方式
		"news_media":    true, // 新闻媒体
		"other":         true, // 其他
	}
	return validIndustries[industry]
}

// isValidCooperationType 验证合作类型是否有效
func (s *MediaService) isValidCooperationType(cooperationType string) bool {
	validTypes := map[string]bool{
		"cps":     true, // CPS合作
		"traffic": true, // 流量采买
		"dh":      true, // 灯火投放
	}
	return validTypes[cooperationType]
}
