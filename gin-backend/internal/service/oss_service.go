package service

import (
	"fmt"
	"gin-backend/internal/global"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
	"github.com/spf13/viper"
)

// OSSService OSS服务接口
type OSSService struct {
	client     *oss.Client
	bucketName string
	urlPrefix  string
}

// NewOSSService 创建OSS服务实例
func NewOSSService() *OSSService {
	// 从配置中读取OSS相关配置
	return &OSSService{
		client:     global.OssClient,
		bucketName: viper.GetString("oss.bucket_name"),
		urlPrefix:  viper.GetString("oss.url_prefix"),
	}
}

// UploadFile 上传文件到OSS
func (s *OSSService) UploadFile(file *multipart.FileHeader) (string, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 获取存储空间
	bucket, err := s.client.Bucket(s.bucketName)
	if err != nil {
		return "", fmt.Errorf("获取Bucket失败: %w", err)
	}

	// 生成唯一的文件名
	ext := filepath.Ext(file.Filename)
	objectName := fmt.Sprintf("uploads/%s/%s%s",
		time.Now().Format("2006/01/02"),
		uuid.New().String(),
		ext,
	)

	// 上传文件
	err = bucket.PutObject(objectName, src)
	if err != nil {
		return "", fmt.Errorf("上传文件失败: %w", err)
	}

	// 拼接URL
	url := s.urlPrefix
	if !strings.HasSuffix(url, "/") {
		url += "/"
	}
	url += objectName

	return url, nil
}

// UploadImage 上传图片到OSS
func (s *OSSService) UploadImage(file *multipart.FileHeader) (string, error) {
	// 验证文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	validExts := map[string]bool{
		".jpg": true, ".jpeg": true, ".png": true, ".gif": true, ".bmp": true, ".webp": true,
	}

	if !validExts[ext] {
		return "", fmt.Errorf("不支持的图片格式: %s", ext)
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 获取存储空间
	bucket, err := s.client.Bucket(s.bucketName)
	if err != nil {
		return "", fmt.Errorf("获取Bucket失败: %w", err)
	}

	// 生成唯一的文件名
	objectName := fmt.Sprintf("images/%s/%s%s",
		time.Now().Format("2006/01/02"),
		uuid.New().String(),
		ext,
	)

	// 上传图片
	err = bucket.PutObject(objectName, src)
	if err != nil {
		return "", fmt.Errorf("上传图片失败: %w", err)
	}

	// 拼接URL
	url := s.urlPrefix
	if !strings.HasSuffix(url, "/") {
		url += "/"
	}
	url += objectName

	return url, nil
}
