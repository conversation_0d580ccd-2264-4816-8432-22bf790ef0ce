-- 调试用户权限配置
-- 检查用户ID=1的详细权限信息

-- 1. 查看用户基本信息
SELECT 
    id,
    name,
    email,
    role,
    role_id,
    department,
    status,
    created_at
FROM users 
WHERE id = 1;

-- 2. 查看用户角色信息
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.role as old_role_field,
    u.role_id as new_role_id,
    r.id as role_table_id,
    r.name as role_name,
    r.code as role_code,
    r.status as role_status
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.id = 1;

-- 3. 查看该角色的所有权限
SELECT 
    p.id,
    p.name,
    p.code,
    p.module,
    p.type,
    p.data_scope,
    p.status
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1
ORDER BY p.module, p.code;

-- 4. 专门查看plan模块的数据权限
SELECT 
    p.id,
    p.name,
    p.code,
    p.module,
    p.type,
    p.data_scope,
    p.status
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1 
AND p.module = 'plan' 
AND p.code LIKE '%data%'
ORDER BY p.code;

-- 5. 查看所有包含data的权限
SELECT 
    p.id,
    p.name,
    p.code,
    p.module,
    p.type,
    p.data_scope,
    p.status
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1 
AND p.code LIKE '%:data:%'
ORDER BY p.module, p.code;

-- 6. 检查是否存在plan:data:all权限
SELECT COUNT(*) as count, 
       GROUP_CONCAT(DISTINCT p.code) as permission_codes
FROM permissions p
WHERE p.code = 'plan:data:all' AND p.status = 1;

-- 7. 检查用户是否有plan:data:all权限
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'YES' 
        ELSE 'NO' 
    END as has_plan_data_all_permission
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1 
AND p.code = 'plan:data:all' 
AND p.status = 1; 