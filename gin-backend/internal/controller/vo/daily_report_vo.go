package vo

import (
	"fmt"
	"gin-backend/internal/service/domain"
)

// BaseReportReq 基础报表请求参数，所有报表查询的基础结构
// 遵循VO设计模式，用于接收前端传入的请求参数
type BaseReportReq struct {
	StartDate string `form:"start_date" json:"start_date" binding:"required"` // 开始日期，必填
	EndDate   string `form:"end_date" json:"end_date" binding:"required"`     // 结束日期，必填
	MediaID   int64  `form:"media_id" json:"media_id"`                        // 媒体ID，可选过滤条件
}

// BaseDetailReq 基础详情请求参数
type BaseDetailReq struct {
	MediaID int64 `form:"media_id" json:"media_id"` // 媒体ID，可选过滤条件
}

// DailyReportReq 获取日报表请求参数
type DailyReportReq struct {
	BaseReportReq
}

// DailyDetailReq 获取日报表详情请求参数
type DailyDetailReq struct {
	BaseDetailReq
	Date string `form:"date" json:"date" binding:"required"` // 日期，必填
}

// WeeklyReportReq 获取周报表请求参数
type WeeklyReportReq struct {
	BaseReportReq
}

// WeeklyDetailReq 获取周报表详情请求参数
type WeeklyDetailReq struct {
	BaseDetailReq
	YearWeek string `form:"year_week" json:"year_week" binding:"required"` // 年周，必填
}

// BaseReportTotal 基础报表总计数据
type BaseReportTotal struct {
	Cost      float64 `json:"cost"`       // 成本
	Profit    float64 `json:"profit"`     // 利润
	Revenue   float64 `json:"revenue"`    // 收入
	ROI       float64 `json:"roi"`        // ROI
	DateRange string  `json:"date_range"` // 日期范围
}

// BaseItem 基础报表项数据
type BaseItem struct {
	Cost    float64 `json:"cost"`    // 成本
	Revenue float64 `json:"revenue"` // 收入
	Profit  float64 `json:"profit"`  // 利润
	ROI     float64 `json:"roi"`     // ROI
}

// DailyReportResp 日报表响应数据
type DailyReportResp struct {
	Total       BaseReportTotal   `json:"total"`        // 总计数据
	DailyReport []DailyReportItem `json:"daily_report"` // 日报表数据项
}

// DailyReportTotal 日报表总计数据
type DailyReportTotal BaseReportTotal

// DailyReportItem 日报表数据项
type DailyReportItem struct {
	BaseItem
	Date      string `json:"date"`       // 日期
	MediaID   int    `json:"media_id"`   // 媒体ID
	MediaName string `json:"media_name"` // 媒体名称
}

// DailyDetailResp 日报表详情响应数据
type DailyDetailResp struct {
	Date       string            `json:"date"`        // 日期
	Total      BaseDetailTotal   `json:"total"`       // 总计数据
	MediaItems []DetailMediaItem `json:"media_items"` // 媒体数据项
}

// BaseDetailTotal 详情总计数据
type BaseDetailTotal struct {
	Cost    float64 `json:"cost"`    // 成本
	Profit  float64 `json:"profit"`  // 利润
	Revenue float64 `json:"revenue"` // 收入
	ROI     float64 `json:"roi"`     // ROI
}

// DetailMediaItem 详情媒体数据
type DetailMediaItem struct {
	MediaID   int     `json:"media_id"`   // 媒体ID
	MediaName string  `json:"media_name"` // 媒体名称
	Cost      float64 `json:"cost"`       // 成本
	Revenue   float64 `json:"revenue"`    // 收入
	Profit    float64 `json:"profit"`     // 利润
	ROI       float64 `json:"roi"`        // ROI
}

// WeeklyReportResp 周报表响应数据
type WeeklyReportResp struct {
	Total        BaseReportTotal    `json:"total"`         // 总计数据
	WeeklyReport []WeeklyReportItem `json:"weekly_report"` // 周报表数据项
}

// WeeklyReportTotal 周报表总计数据
type WeeklyReportTotal BaseReportTotal

// WeeklyReportItem 周报表数据项
type WeeklyReportItem struct {
	BaseItem
	YearWeek  string `json:"year_week"`  // 年周
	WeekRange string `json:"week_range"` // 周范围
}

// WeeklyDetailResp 周报表详情响应数据
type WeeklyDetailResp struct {
	YearWeek   string                  `json:"year_week"`   // 年周
	WeekRange  string                  `json:"week_range"`  // 周范围
	Total      BaseDetailTotal         `json:"total"`       // 总计数据
	MediaItems []DetailMediaItem       `json:"media_items"` // 媒体数据项
	DailyItems []WeeklyDetailDailyItem `json:"daily_items"` // 每日数据项
}

// WeeklyDetailDailyItem 周报表详情每日数据
type WeeklyDetailDailyItem struct {
	Date    string  `json:"date"`    // 日期
	Cost    float64 `json:"cost"`    // 成本
	Revenue float64 `json:"revenue"` // 收入
	Profit  float64 `json:"profit"`  // 利润
	ROI     float64 `json:"roi"`     // ROI
}

// DetailItem 详情项通用结构
type DetailItem struct {
	MediaName        string  `json:"media_name"`         // 媒体名称
	MediaID          int     `json:"media_id"`           // 媒体ID
	PromotionChannel string  `json:"promotion_channel"`  // 推广渠道
	IsProfit         bool    `json:"is_profit"`          // 是否盈利
	Orders           int     `json:"orders"`             // 订单数
	Revenue          float64 `json:"revenue"`            // 收入
	Cost             float64 `json:"cost"`               // 成本
	Profit           float64 `json:"profit"`             // 利润
	Clicks           int     `json:"clicks"`             // 点击数
	ROI              float64 `json:"roi"`                // ROI
	ProfitMarginRate float64 `json:"profit_margin_rate"` // 利润率
	UserID           int     `json:"user_id"`            // 用户ID
	UserName         string  `json:"user_name"`          // 用户名称
}

// DetailSummary 详情汇总通用结构
type DetailSummary struct {
	TotalProfit      float64 `json:"total_profit"`       // 总利润
	TotalOrders      int     `json:"total_orders"`       // 总订单数
	ProfitMediaCount int     `json:"profit_media_count"` // 盈利媒体数
	LossMediaCount   int     `json:"loss_media_count"`   // 亏损媒体数
}

// ToBaseReportParam 将BaseReportReq转换为BaseReportParam
func (req BaseReportReq) ToBaseReportParam(userID int64) domain.BaseReportParam {
	return domain.BaseReportParam{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		MediaID:   req.MediaID,
		UserID:    userID,
	}
}

// ToBaseDetailParam 将BaseDetailReq转换为BaseDetailParam
func (req BaseDetailReq) ToBaseDetailParam(userID int64) domain.BaseDetailParam {
	return domain.BaseDetailParam{
		MediaID: req.MediaID,
		UserID:  userID,
	}
}

// ToDailyReportParam 将DailyReportReq转换为DailyReportParam
func (req DailyReportReq) ToDailyReportParam(userID int64) domain.DailyReportParam {
	baseParam := req.BaseReportReq.ToBaseReportParam(userID)
	return domain.DailyReportParam{
		BaseReportParam: baseParam,
	}
}

// ToDailyDetailParam 将DailyDetailReq转换为DailyDetailParam
func (req DailyDetailReq) ToDailyDetailParam(userID int64) domain.DailyDetailParam {
	baseParam := req.BaseDetailReq.ToBaseDetailParam(userID)
	return domain.DailyDetailParam{
		BaseDetailParam: baseParam,
		Date:            req.Date,
	}
}

// ToWeeklyReportParam 将WeeklyReportReq转换为WeeklyReportParam
func (req WeeklyReportReq) ToWeeklyReportParam(userID int64) domain.WeeklyReportParam {
	baseParam := req.BaseReportReq.ToBaseReportParam(userID)
	return domain.WeeklyReportParam{
		BaseReportParam: baseParam,
	}
}

// ToWeeklyDetailParam 将WeeklyDetailReq转换为WeeklyDetailParam
func (req WeeklyDetailReq) ToWeeklyDetailParam(userID int64) domain.WeeklyDetailParam {
	baseParam := req.BaseDetailReq.ToBaseDetailParam(userID)
	return domain.WeeklyDetailParam{
		BaseDetailParam: baseParam,
		YearWeek:        req.YearWeek,
	}
}

// FromDailyReportEntity 从DailyReportEntity创建DailyReportResp
func FromDailyReportEntity(entity domain.DailyReportEntity, startDate, endDate string) DailyReportResp {
	resp := DailyReportResp{
		Total: BaseReportTotal{
			DateRange: fmt.Sprintf("%s 至 %s", startDate, endDate),
		},
		DailyReport: make([]DailyReportItem, 0, len(entity.List)),
	}

	var totalCost, totalRevenue, totalProfit float64
	for _, item := range entity.List {
		dailyItem := DailyReportItem{
			BaseItem: BaseItem{
				Cost:    item.TotalCost,
				Revenue: item.TotalRevenue,
				Profit:  item.TotalProfit,
				ROI:     item.ROI,
			},
			Date:      item.Date,
			MediaID:   item.MediaID,
			MediaName: item.MediaName,
		}
		resp.DailyReport = append(resp.DailyReport, dailyItem)

		totalCost += item.TotalCost
		totalRevenue += item.TotalRevenue
		totalProfit += item.TotalProfit
	}

	resp.Total.Cost = totalCost
	resp.Total.Revenue = totalRevenue
	resp.Total.Profit = totalProfit
	if totalCost > 0 {
		resp.Total.ROI = totalRevenue / totalCost
	}

	return resp
}

// FromDailyDetailEntity 从DailyDetailEntity创建DailyDetailResp
func FromDailyDetailEntity(entity domain.DailyDetailEntity) DailyDetailResp {
	resp := DailyDetailResp{
		Date: entity.Date,
		Total: BaseDetailTotal{
			Cost:    0,
			Revenue: 0,
			Profit:  0,
			ROI:     0,
		},
		MediaItems: make([]DetailMediaItem, 0),
	}

	// 创建媒体ID到媒体项的映射
	mediaMap := make(map[int]*DetailMediaItem)

	for _, item := range entity.List {
		// 累加总计数据
		resp.Total.Cost += item.Cost
		resp.Total.Revenue += item.Revenue
		resp.Total.Profit += item.Profit

		// 按媒体分组
		if mediaItem, exists := mediaMap[item.MediaID]; exists {
			mediaItem.Cost += item.Cost
			mediaItem.Revenue += item.Revenue
			mediaItem.Profit += item.Profit
			if mediaItem.Cost > 0 {
				mediaItem.ROI = mediaItem.Revenue / mediaItem.Cost
			}
		} else {
			mediaItem := DetailMediaItem{
				MediaID:   item.MediaID,
				MediaName: item.MediaName,
				Cost:      item.Cost,
				Revenue:   item.Revenue,
				Profit:    item.Profit,
				ROI:       0,
			}
			if mediaItem.Cost > 0 {
				mediaItem.ROI = mediaItem.Revenue / mediaItem.Cost
			}
			mediaMap[item.MediaID] = &mediaItem
		}
	}

	// 计算总ROI
	if resp.Total.Cost > 0 {
		resp.Total.ROI = resp.Total.Revenue / resp.Total.Cost
	}

	// 转换媒体映射到切片
	for _, mediaItem := range mediaMap {
		resp.MediaItems = append(resp.MediaItems, *mediaItem)
	}

	return resp
}

// FromWeeklyReportEntity 从WeeklyReportEntity创建WeeklyReportResp
func FromWeeklyReportEntity(entity domain.WeeklyReportEntity, startDate, endDate string) WeeklyReportResp {
	resp := WeeklyReportResp{
		Total: BaseReportTotal{
			DateRange: fmt.Sprintf("%s 至 %s", startDate, endDate),
		},
		WeeklyReport: make([]WeeklyReportItem, 0, len(entity.List)),
	}

	var totalCost, totalRevenue, totalProfit float64
	for _, item := range entity.List {
		weeklyItem := WeeklyReportItem{
			BaseItem: BaseItem{
				Cost:    item.TotalCost,
				Revenue: item.TotalRevenue,
				Profit:  item.TotalProfit,
				ROI:     item.ROI,
			},
			YearWeek:  item.YearWeek,
			WeekRange: item.WeekRange,
		}
		resp.WeeklyReport = append(resp.WeeklyReport, weeklyItem)

		totalCost += item.TotalCost
		totalRevenue += item.TotalRevenue
		totalProfit += item.TotalProfit
	}

	resp.Total.Cost = totalCost
	resp.Total.Revenue = totalRevenue
	resp.Total.Profit = totalProfit
	if totalCost > 0 {
		resp.Total.ROI = totalRevenue / totalCost
	}

	return resp
}

// FromWeeklyDetailEntity 从WeeklyDetailEntity创建WeeklyDetailResp
func FromWeeklyDetailEntity(entity domain.WeeklyDetailEntity) WeeklyDetailResp {
	resp := WeeklyDetailResp{
		YearWeek:  entity.YearWeek,
		WeekRange: fmt.Sprintf("%s 至 %s", entity.WeekStart, entity.WeekEnd),
		Total: BaseDetailTotal{
			Cost:    0,
			Revenue: 0,
			Profit:  0,
			ROI:     0,
		},
		MediaItems: make([]DetailMediaItem, 0),
		DailyItems: make([]WeeklyDetailDailyItem, 0),
	}

	// 创建媒体ID到媒体项的映射
	mediaMap := make(map[int]*DetailMediaItem)

	// 按日期分组的数据
	dailyMap := make(map[string]*WeeklyDetailDailyItem)

	for _, item := range entity.List {
		// 累加总计数据
		resp.Total.Cost += item.Cost
		resp.Total.Revenue += item.Revenue
		resp.Total.Profit += item.Profit

		// 按媒体分组
		if mediaItem, exists := mediaMap[item.MediaID]; exists {
			mediaItem.Cost += item.Cost
			mediaItem.Revenue += item.Revenue
			mediaItem.Profit += item.Profit
			if mediaItem.Cost > 0 {
				mediaItem.ROI = mediaItem.Revenue / mediaItem.Cost
			}
		} else {
			mediaItem := DetailMediaItem{
				MediaID:   item.MediaID,
				MediaName: item.MediaName,
				Cost:      item.Cost,
				Revenue:   item.Revenue,
				Profit:    item.Profit,
				ROI:       0,
			}
			if mediaItem.Cost > 0 {
				mediaItem.ROI = mediaItem.Revenue / mediaItem.Cost
			}
			mediaMap[item.MediaID] = &mediaItem
		}

		// 这里需要模拟按日期聚合，但domain层没有提供该信息
		// 实际应用中应该从service层获取日期数据
	}

	// 计算总ROI
	if resp.Total.Cost > 0 {
		resp.Total.ROI = resp.Total.Revenue / resp.Total.Cost
	}

	// 转换媒体映射到切片
	for _, mediaItem := range mediaMap {
		resp.MediaItems = append(resp.MediaItems, *mediaItem)
	}

	// 转换日期映射到切片
	for _, dailyItem := range dailyMap {
		resp.DailyItems = append(resp.DailyItems, *dailyItem)
	}

	return resp
}

// FromDetailItemEntity 从DetailItemEntity创建DetailItem
func FromDetailItemEntity(entity domain.DetailItemEntity) DetailItem {
	return DetailItem{
		MediaName:        entity.MediaName,
		MediaID:          entity.MediaID,
		PromotionChannel: entity.PromotionChannel,
		IsProfit:         entity.IsProfit,
		Orders:           entity.Orders,
		Revenue:          entity.Revenue,
		Cost:             entity.Cost,
		Profit:           entity.Profit,
		Clicks:           entity.Clicks,
		ROI:              entity.ROI,
		ProfitMarginRate: entity.ProfitMarginRate,
		UserID:           entity.UserID,
		UserName:         entity.UserName,
	}
}

// FromDetailSummaryEntity 从DetailSummaryEntity创建DetailSummary
func FromDetailSummaryEntity(entity domain.DetailSummaryEntity) DetailSummary {
	return DetailSummary{
		TotalProfit:      entity.TotalProfit,
		TotalOrders:      entity.TotalOrders,
		ProfitMediaCount: entity.ProfitMediaCount,
		LossMediaCount:   entity.LossMediaCount,
	}
}
