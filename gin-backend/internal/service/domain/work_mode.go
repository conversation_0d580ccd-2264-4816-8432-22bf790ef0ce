package domain

// WorkMode 工作模式
type WorkMode struct {
	Code string // 模式代码
	Name string // 模式名称
}

// UserWorkModes 用户可用工作模式
type UserWorkModes struct {
	Modes       []WorkMode // 可用的工作模式列表
	DefaultMode string     // 默认工作模式
	CurrentMode string     // 当前工作模式
}

// WorkModeMenus 工作模式对应的菜单和权限
type WorkModeMenus struct {
	Mode        string   // 当前模式
	Permissions []string // 权限列表
	Menus       []Menu   // 菜单列表
}

// Menu 菜单结构
type Menu struct {
	Path      string // 路径
	Component string // 组件
	Name      string // 名称
	Meta      Meta   // 元数据
	Children  []Menu // 子菜单
}

// Meta 菜单元数据
type Meta struct {
	Title       string // 标题
	Icon        string // 图标
	Permissions string // 权限
}

// GetUserModesParam 获取用户模式参数
type GetUserModesParam struct {
	UserID int64 // 用户ID
}

// WorkModeSwitchParam 切换工作模式参数
type WorkModeSwitchParam struct {
	ModeCode string // 模式代码
}

// CheckModePermissionParam 检查模式权限参数
type CheckModePermissionParam struct {
	ModeCode string // 模式代码
}

// MenuItem VO层使用的菜单项
type MenuItem struct {
	Path      string     // 路径
	Component string     // 组件
	Name      string     // 名称
	Meta      MenuMeta   // 元数据
	Children  []MenuItem // 子菜单
}

// MenuMeta VO层使用的菜单元数据
type MenuMeta struct {
	Title      string // 标题
	Icon       string // 图标
	Permission string // 权限
}
