package service

import (
	"context"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/pkg/xiaohongshu"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// XHSRealtimeReportService 小红书实时报表服务
type XHSRealtimeReportService struct {
	db *gorm.DB
}

// NewXHSRealtimeReportService 创建小红书实时报表服务
func NewXHSRealtimeReportService() *XHSRealtimeReportService {
	return &XHSRealtimeReportService{
		db: global.DB,
	}
}

// SyncCurrentHourReports 同步当前小时的实时报表数据
func (s *XHSRealtimeReportService) SyncCurrentHourReports(ctx context.Context) error {
	zap.L().Info("开始同步当前小时的小红书实时报表数据")

	// 获取所有有效的小红书账号
	var accounts []model.AdAccounts
	err := s.db.WithContext(ctx).
		Where("platform = ? AND status = ? AND access_token IS NOT NULL AND access_token != ''", "xiaohongshu", 1).
		Find(&accounts).Error

	if err != nil {
		return fmt.Errorf("获取小红书账号列表失败: %w", err)
	}

	if len(accounts) == 0 {
		zap.L().Info("没有找到有效的小红书账号，跳过同步")
		return nil
	}

	zap.L().Info("找到小红书账号", zap.Int("count", len(accounts)))

	// 逐个账号同步数据
	successCount := 0
	errorCount := 0

	for _, account := range accounts {
		if err := s.syncAccountRealtimeReports(ctx, account); err != nil {
			zap.L().Error("同步账号实时报表数据失败",
				zap.Int64("account_id", account.ID),
				zap.String("account_name", account.AccountName),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}

		// 每个账号之间稍作休息，避免API调用过于频繁
		time.Sleep(500 * time.Millisecond)
	}

	zap.L().Info("小红书实时报表数据同步完成",
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))

	if errorCount > 0 {
		return fmt.Errorf("部分账号同步失败，成功: %d, 失败: %d", successCount, errorCount)
	}

	return nil
}

// syncAccountRealtimeReports 同步单个账号的实时报表数据
func (s *XHSRealtimeReportService) syncAccountRealtimeReports(ctx context.Context, account model.AdAccounts) error {
	zap.L().Info("开始同步账号实时报表数据",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName))

	// 创建小红书客户端
	client := xiaohongshu.NewClient(&xiaohongshu.Config{
		AppId:  config.GetXiaohongshuConfig().AppId,
		Secret: config.GetXiaohongshuConfig().Secret,
		IsProd: config.GetXiaohongshuConfig().IsProd,
	})

	// 获取当前时间，用于实时数据查询
	now := time.Now()
	today := now.Format("2006-01-02")

	// 构建实时报表请求 - 获取今日数据
	request := xiaohongshu.NewRealtimeReportBuilder(account.AdvertiserID, today, today).
		WithPagination(1, 100).                // 每页100条
		WithSort("fee", xiaohongshu.SortDesc). // 按消费降序
		Build()

	// 分页获取所有数据
	allReports := make([]model.XHSRealtimeReports, 0)
	pageNum := 1
	pageSize := 100

	for {
		// 更新分页参数
		request.PageNum = pageNum
		request.PageSize = pageSize

		// 调用API获取数据
		response, err := client.GetCreativityRealtimeReport(account.AccessToken, request)
		if err != nil {
			return fmt.Errorf("调用小红书实时报表API失败: %w", err)
		}

		if !response.Success {
			return fmt.Errorf("小红书API返回错误: code=%d, msg=%s", response.Code, response.Msg)
		}

		// 转换数据
		reports := s.convertToRealtimeReportModels(response.CreativityDtos, account, now)
		allReports = append(allReports, reports...)

		zap.L().Info("获取实时报表数据",
			zap.Int64("account_id", account.ID),
			zap.Int("page", pageNum),
			zap.Int("count", len(reports)))

		// 检查是否还有更多数据
		if len(response.CreativityDtos) < pageSize {
			break // 最后一页
		}

		pageNum++

		// 避免请求过于频繁
		time.Sleep(200 * time.Millisecond)
	}

	if len(allReports) == 0 {
		zap.L().Info("账号没有实时报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("account_name", account.AccountName))
		return nil
	}

	// 批量保存到数据库
	if err := s.batchSaveRealtimeReports(ctx, allReports); err != nil {
		return fmt.Errorf("保存实时报表数据失败: %w", err)
	}

	zap.L().Info("账号实时报表数据同步完成",
		zap.Int64("account_id", account.ID),
		zap.String("account_name", account.AccountName),
		zap.Int("total_count", len(allReports)))

	return nil
}

// convertToRealtimeReportModels 转换API响应数据为数据库模型
func (s *XHSRealtimeReportService) convertToRealtimeReportModels(creativityDtos []xiaohongshu.CreativityDTO, account model.AdAccounts, syncTime time.Time) []model.XHSRealtimeReports {
	reports := make([]model.XHSRealtimeReports, 0, len(creativityDtos))

	for _, data := range creativityDtos {
		report := model.XHSRealtimeReports{
			// 账号信息
			AccountID:   account.ID,
			AccountName: account.AccountName,

			// 业务字段
			CampaignID:     strconv.FormatInt(data.BaseCampaign.CampaignID, 10),
			CampaignName:   data.BaseCampaign.CampaignName,
			UnitID:         strconv.FormatInt(data.BaseUnit.UnitID, 10),
			UnitName:       data.BaseUnit.UnitName,
			CreativityID:   strconv.FormatInt(data.BaseCreativity.CreativityID, 10),
			CreativityName: data.BaseCreativity.CreativityName,
			NoteID:         data.BaseCreativity.NoteID,

			// 创意属性
			CreativityImage: "", // 实时API中可能没有这个字段
			PageID:          "", // 实时API中可能没有这个字段
			ItemID:          "", // 实时API中可能没有这个字段
			LiveRedID:       "", // 实时API中可能没有这个字段

			// 地域信息
			CountryName: "", // 实时API中可能没有这个字段
			Province:    "", // 实时API中可能没有这个字段
			City:        "", // 实时API中可能没有这个字段
			NoteUserID:  "", // 实时API中可能没有这个字段

			// 统计时间
			Time: syncTime,
		}

		// 解析创意名称
		var title, pwd, contentPeople, pitcherName string
		creativityNames := strings.Split(data.BaseCreativity.CreativityName, "_")
		if len(creativityNames) == 8 {
			title = creativityNames[0]
			pwd = creativityNames[1]
			peoples := strings.Split(creativityNames[2], "/")
			if len(peoples) == 2 {
				contentPeople = peoples[0]
				pitcherName = peoples[1]
			}
		}
		report.Title = title
		report.Pwd = pwd
		report.ContentPeople = contentPeople
		report.PitcherName = pitcherName

		// 转换枚举字段 - 从计划属性中获取
		report.Placement = int8(data.BaseCampaign.Placement)
		report.OptimizeTarget = int8(data.BaseCampaign.OptimizeTarget)
		report.PromotionTarget = int8(data.BaseCampaign.PromotionTarget)
		report.BiddingStrategy = int8(data.BaseCampaign.BiddingStrategy)
		report.BuildType = int8(data.BaseCampaign.BuildType)
		report.MarketingTarget = int8(data.BaseCampaign.MarketingTarget)

		// 转换基础指标
		report.Fee, _ = s.parseFloatField(data.Data.Fee)
		report.Impression, _ = s.parseIntField(data.Data.Impression)
		report.Click, _ = s.parseIntField(data.Data.Click)
		report.CTR, _ = s.parseFloatField(data.Data.Ctr)
		report.ACP, _ = s.parseFloatField(data.Data.Acp)
		report.CPM, _ = s.parseFloatField(data.Data.Cpm)

		// 转换互动指标
		report.Like, _ = s.parseIntField(data.Data.Like)
		report.Comment, _ = s.parseIntField(data.Data.Comment)
		report.Collect, _ = s.parseIntField(data.Data.Collect)
		report.Follow, _ = s.parseIntField(data.Data.Follow)
		report.Share, _ = s.parseIntField(data.Data.Share)
		report.Interaction, _ = s.parseIntField(data.Data.Interaction)
		report.CPI, _ = s.parseFloatField(data.Data.Cpi)
		report.ActionButtonClick, _ = s.parseIntField(data.Data.ActionButtonClick)
		report.ActionButtonCTR, _ = s.parseFloatField(data.Data.ActionButtonCtr)
		report.Screenshot, _ = s.parseIntField(data.Data.Screenshot)
		report.PicSave, _ = s.parseIntField(data.Data.PicSave)
		report.ReservePV, _ = s.parseIntField(data.Data.ReservePv)

		// 转换直播间互动指标
		report.ClkLiveEntryPV, _ = s.parseIntField(data.Data.ClkLiveEntryPv)
		report.ClkLiveEntryPVCost, _ = s.parseFloatField(data.Data.ClkLiveEntryPvCost)
		// 直播间停留时长：分钟转秒
		if avgViewTime, err := s.parseFloatField(data.Data.ClkLiveAvgViewTime); err == nil {
			report.ClkLiveAvgViewTime = int32(avgViewTime * 60)
		}
		report.ClkLiveAllFollow, _ = s.parseIntField(data.Data.ClkLiveAllFollow)
		report.ClkLive5sEntryPV, _ = s.parseIntField(data.Data.ClkLive5sEntryPv)
		report.ClkLive5sEntryUVCost, _ = s.parseFloatField(data.Data.ClkLive5sEntryUvCost)
		report.ClkLiveComment, _ = s.parseIntField(data.Data.ClkLiveComment)

		// 转换笔记种草指标
		report.SearchCmtClick, _ = s.parseIntField(data.Data.SearchCmtClick)
		report.SearchCmtClickCVR, _ = s.parseFloatField(data.Data.SearchCmtClickCvr)
		report.SearchCmtAfterRead, _ = s.parseIntField(data.Data.SearchCmtAfterRead)
		report.SearchCmtAfterReadAvg, _ = s.parseFloatField(data.Data.SearchCmtAfterReadAvg)
		report.IUserNum, _ = s.parseIntField(data.Data.IUserNum)
		report.TIUserNum, _ = s.parseIntField(data.Data.TiUserNum)
		report.IUserPrice, _ = s.parseFloatField(data.Data.IUserPrice)
		report.TIUserPrice, _ = s.parseFloatField(data.Data.TiUserPrice)

		// 转换电商转化指标 - 购买兴趣
		report.GoodsVisit, _ = s.parseIntField(data.Data.GoodsVisit)
		report.GoodsVisitPrice, _ = s.parseFloatField(data.Data.GoodsVisitPrice)
		report.SellerVisit, _ = s.parseIntField(data.Data.SellerVisit)
		report.SellerVisitPrice, _ = s.parseFloatField(data.Data.SellerVisitPrice)
		report.ShoppingCartAdd, _ = s.parseIntField(data.Data.ShoppingCartAdd)
		report.AddCartPrice, _ = s.parseFloatField(data.Data.AddCartPrice)

		// 转换电商转化指标 - 7日转化
		report.PresaleOrderNum7D, _ = s.parseIntField(data.Data.PresaleOrderNum7d)
		report.PresaleOrderGMV7D, _ = s.parseFloatField(data.Data.PresaleOrderGmv7d)
		report.GoodsOrder, _ = s.parseIntField(data.Data.GoodsOrder)
		report.GoodsOrderPrice, _ = s.parseFloatField(data.Data.GoodsOrderPrice)
		report.RGMV, _ = s.parseFloatField(data.Data.Rgmv)
		report.ROI, _ = s.parseFloatField(data.Data.Roi)
		report.SuccessGoodsOrder, _ = s.parseIntField(data.Data.SuccessGoodsOrder)
		report.ClickOrderCVR, _ = s.parseFloatField(data.Data.ClickOrderCvr)
		report.PurchaseOrderPrice7D, _ = s.parseFloatField(data.Data.PurchaseOrderPrice7d)
		report.PurchaseOrderGMV7D, _ = s.parseFloatField(data.Data.PurchaseOrderGmv7d)
		report.PurchaseOrderROI7D, _ = s.parseFloatField(data.Data.PurchaseOrderRoi7d)

		// 转换销售线索指标
		report.Leads, _ = s.parseIntField(data.Data.Leads)
		report.LeadsCPL, _ = s.parseFloatField(data.Data.LeadsCpl)
		report.LandingPageVisit, _ = s.parseIntField(data.Data.LandingPageVisit)
		report.ValidLeads, _ = s.parseIntField(data.Data.ValidLeads)
		report.ValidLeadsCPL, _ = s.parseFloatField(data.Data.ValidLeadsCpl)
		report.LeadsCVR, _ = s.parseFloatField(data.Data.LeadsCvr)

		// 转换私信营销指标
		report.MessageUser, _ = s.parseIntField(data.Data.MessageUser)
		report.Message, _ = s.parseIntField(data.Data.Message)
		report.MessageConsult, _ = s.parseIntField(data.Data.MessageConsult)
		// 私信平均响应时长：分钟转秒
		if replyTime, err := s.parseFloatField(data.Data.MessageFstReplyTimeAvg); err == nil {
			report.MessageFstReplyTimeAvg = int32(replyTime * 60)
		}
		report.InitiativeMessage, _ = s.parseIntField(data.Data.InitiativeMessage)
		report.MessageConsultCPL, _ = s.parseFloatField(data.Data.MessageConsultCpl)
		report.InitiativeMessageCPL, _ = s.parseFloatField(data.Data.InitiativeMessageCpl)
		report.MsgLeadsNum, _ = s.parseIntField(data.Data.MsgLeadsNum)
		report.MsgLeadsCost, _ = s.parseFloatField(data.Data.MsgLeadsCost)

		reports = append(reports, report)
	}

	return reports
}

// parseIntField 解析整数字段
func (s *XHSRealtimeReportService) parseIntField(value string) (int64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseInt(strings.TrimSpace(value), 10, 64)
}

// parseFloatField 解析浮点数字段
func (s *XHSRealtimeReportService) parseFloatField(value string) (float64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseFloat(strings.TrimSpace(value), 64)
}

// batchSaveRealtimeReports 批量保存实时报表数据
func (s *XHSRealtimeReportService) batchSaveRealtimeReports(ctx context.Context, reports []model.XHSRealtimeReports) error {
	if len(reports) == 0 {
		return nil
	}

	// 使用事务批量插入
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批1000条
		batchSize := 1000
		for i := 0; i < len(reports); i += batchSize {
			end := i + batchSize
			if end > len(reports) {
				end = len(reports)
			}

			batch := reports[i:end]
			if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
