package service

import (
	"context"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 设置测试数据库
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 自动迁移表结构
	db.AutoMigrate(&model.AdPlanStats{}, &model.AdAccounts{})

	return db
}

// TestAdPlanListService_getAccountNameByID 测试获取账号名称
func TestAdPlanListService_getAccountNameByID(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB()
	global.DB = db

	// 创建测试账号
	account := model.AdAccounts{
		ID:          1,
		AccountName: "测试账号",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	db.Create(&account)

	// 创建服务实例
	service := NewAdPlanListService()
	ctx := context.Background()

	// 测试获取账号名称
	accountName := service.getAccountNameByID(ctx, 1)
	assert.Equal(t, "测试账号", accountName)

	// 测试不存在的账号
	accountName = service.getAccountNameByID(ctx, 999)
	assert.Equal(t, "", accountName)
}

// TestAdPlanListService_getPlanListFromStats 测试从统计表获取计划列表
func TestAdPlanListService_getPlanListFromStats(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB()
	global.DB = db

	// 创建测试账号
	account := model.AdAccounts{
		ID:          1,
		AccountName: "测试账号",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	db.Create(&account)

	// 创建测试计划统计数据
	planStats := model.AdPlanStats{
		PlanId:           "plan_001",
		PlanName:         "测试计划",
		AccountId:        1,
		Cost:             1000.0,
		ActualCost:       900.0,
		Impressions:      10000,
		Clicks:           100,
		ClickThroughRate: 1.0,
		StatDate:         time.Now(),
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}
	db.Create(&planStats)

	// 创建服务实例
	service := NewAdPlanListService()
	ctx := context.Background()

	// 测试查询参数
	param := domain.AdPlanListParam{
		Page:     1,
		PageSize: 10,
	}

	// 测试获取计划列表
	plans, total, err := service.getPlanListFromStats(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(1), total)
	assert.Len(t, plans, 1)

	plan := plans[0]
	assert.Equal(t, "plan_001", plan.PlanID)
	assert.Equal(t, "测试计划", plan.PlanName)
	assert.Equal(t, "测试账号", plan.AccountName) // 验证账号名称是从账号表获取的
	assert.Equal(t, 1000.0, plan.Consumption)
	assert.Equal(t, 900.0, plan.ActualCost)
	assert.Equal(t, int64(10000), plan.Impressions)
	assert.Equal(t, int64(100), plan.Clicks)
	assert.Equal(t, 1.0, plan.ClickRate)
}

// TestAdPlanListService_GetPlanList 测试获取计划列表（单日查询）
func TestAdPlanListService_GetPlanList(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB()
	global.DB = db

	// 创建测试账号
	account := model.AdAccounts{
		ID:          1,
		AccountName: "测试账号",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	db.Create(&account)

	// 创建测试计划统计数据
	today := time.Now()
	planStats := model.AdPlanStats{
		PlanId:           "plan_001",
		PlanName:         "测试计划",
		AccountId:        1,
		Cost:             1000.0,
		ActualCost:       900.0,
		Impressions:      10000,
		Clicks:           100,
		ClickThroughRate: 1.0,
		StatDate:         today,
		CreatedAt:        today,
		UpdatedAt:        today,
	}
	db.Create(&planStats)

	// 创建服务实例
	service := NewAdPlanListService()
	ctx := context.Background()

	// 测试单日查询参数
	param := domain.AdPlanListParam{
		StartDate: &today,
		EndDate:   &today,
		Page:      1,
		PageSize:  10,
	}

	// 测试获取计划列表
	result, err := service.GetPlanList(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(1), result.Total)
	assert.Len(t, result.List, 1)

	plan := result.List[0]
	assert.Equal(t, "plan_001", plan.PlanID)
	assert.Equal(t, "测试计划", plan.PlanName)
	assert.Equal(t, "测试账号", plan.AccountName) // 验证账号名称是从账号表获取的
	assert.Equal(t, 1000.0, plan.Consumption)
}

// TestAdPlanListService_GetPlanList_WithAccountFilter 测试带账号筛选的计划列表查询
func TestAdPlanListService_GetPlanList_WithAccountFilter(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB()
	global.DB = db

	// 创建测试账号
	account1 := model.AdAccounts{
		ID:          1,
		AccountName: "测试账号1",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	account2 := model.AdAccounts{
		ID:          2,
		AccountName: "测试账号2",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	db.Create(&account1)
	db.Create(&account2)

	// 创建测试计划统计数据
	today := time.Now()
	planStats1 := model.AdPlanStats{
		PlanId:    "plan_001",
		PlanName:  "测试计划1",
		AccountId: 1,
		Cost:      1000.0,
		StatDate:  today,
		CreatedAt: today,
		UpdatedAt: today,
	}
	planStats2 := model.AdPlanStats{
		PlanId:    "plan_002",
		PlanName:  "测试计划2",
		AccountId: 2,
		Cost:      2000.0,
		StatDate:  today,
		CreatedAt: today,
		UpdatedAt: today,
	}
	db.Create(&planStats1)
	db.Create(&planStats2)

	// 创建服务实例
	service := NewAdPlanListService()
	ctx := context.Background()

	// 测试按账号ID筛选
	accountID := int64(1)
	param := domain.AdPlanListParam{
		AccountID: &accountID,
		StartDate: &today,
		EndDate:   &today,
		Page:      1,
		PageSize:  10,
	}

	result, err := service.GetPlanList(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(1), result.Total)
	assert.Len(t, result.List, 1)

	plan := result.List[0]
	assert.Equal(t, "plan_001", plan.PlanID)
	assert.Equal(t, "测试账号1", plan.AccountName)

	// 测试按账号名称筛选
	param2 := domain.AdPlanListParam{
		AccountName: "测试账号2",
		StartDate:   &today,
		EndDate:     &today,
		Page:        1,
		PageSize:    10,
	}

	result2, err := service.GetPlanList(ctx, param2)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(1), result2.Total)
	assert.Len(t, result2.List, 1)

	plan2 := result2.List[0]
	assert.Equal(t, "plan_002", plan2.PlanID)
	assert.Equal(t, "测试账号2", plan2.AccountName)
}

// TestAdPlanListService_GetPlanList_MultiDay 测试多日查询
func TestAdPlanListService_GetPlanList_MultiDay(t *testing.T) {
	// 设置测试数据库
	db := setupTestDB()
	global.DB = db

	// 创建测试账号
	account := model.AdAccounts{
		ID:          1,
		AccountName: "测试账号",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	db.Create(&account)

	// 创建多天的测试计划统计数据
	today := time.Now()
	yesterday := today.AddDate(0, 0, -1)

	// 第一天的数据
	planStats1 := model.AdPlanStats{
		PlanId:           "plan_001",
		PlanName:         "测试计划",
		AccountId:        1,
		Cost:             1000.0,
		ActualCost:       900.0,
		Impressions:      10000,
		Clicks:           100,
		ClickThroughRate: 1.0,
		StatDate:         yesterday,
		CreatedAt:        yesterday,
		UpdatedAt:        yesterday,
	}

	// 第二天的数据
	planStats2 := model.AdPlanStats{
		PlanId:           "plan_001", // 同一个计划
		PlanName:         "测试计划",
		AccountId:        1,
		Cost:             2000.0,
		ActualCost:       1800.0,
		Impressions:      20000,
		Clicks:           200,
		ClickThroughRate: 1.0,
		StatDate:         today,
		CreatedAt:        today,
		UpdatedAt:        today,
	}

	db.Create(&planStats1)
	db.Create(&planStats2)

	// 创建服务实例
	service := NewAdPlanListService()
	ctx := context.Background()

	// 测试多日查询参数
	param := domain.AdPlanListParam{
		StartDate: &yesterday,
		EndDate:   &today,
		Page:      1,
		PageSize:  10,
	}

	// 测试获取计划列表
	result, err := service.GetPlanList(ctx, param)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, int64(1), result.Total) // 应该只有一个计划（按plan_id聚合）
	assert.Len(t, result.List, 1)

	plan := result.List[0]
	assert.Equal(t, "plan_001", plan.PlanID)
	assert.Equal(t, "测试计划", plan.PlanName)
	assert.Equal(t, "测试账号", plan.AccountName)       // 验证账号名称是从账号表获取的
	assert.Equal(t, 3000.0, plan.Consumption)       // 两天的消费总和：1000 + 2000
	assert.Equal(t, 2700.0, plan.ActualCost)        // 两天的实际消费总和：900 + 1800
	assert.Equal(t, int64(30000), plan.Impressions) // 两天的展现量总和：10000 + 20000
	assert.Equal(t, int64(300), plan.Clicks)        // 两天的点击量总和：100 + 200
	assert.Equal(t, 1.0, plan.ClickRate)            // 重新计算的点击率：300/30000*100 = 1.0
}
