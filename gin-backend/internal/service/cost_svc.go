package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// CostService 费用服务
type CostService struct {
	db *gorm.DB
}

// NewCostService 创建费用服务实例
func NewCostService() *CostService {
	return &CostService{
		db: global.DB,
	}
}

// CreateCost 创建费用记录
func (s *CostService) CreateCost(entity domain.CostCreateEntity, userID int64) error {
	// 解析日期
	date, err := time.Parse("2006-01-02", entity.Date)
	if err != nil {
		return errors.New("日期格式不正确")
	}

	// 检查是否已存在该计划和日期的费用记录
	var existingCost model.AdSlotPlanCost
	err = s.db.Where("plan_id = ? AND date = ?", entity.PlanID, date).First(&existingCost).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if err == nil {
		return errors.New("该计划和日期的费用记录已存在")
	}

	// 创建费用记录
	cost := model.AdSlotPlanCost{
		PlanID:      entity.PlanID,
		Date:        date,
		MediaID:     entity.MediaID,
		AdSlotID:    entity.AdSlotID,
		AdProductID: entity.AdProductID,
		UserID:      entity.UserID,
		Clicks:      entity.Clicks,
		Cost:        entity.Cost,
		Remark:      entity.Remark,
		AuditStatus: domain.CostAuditStatusPending,
		CreatedBy:   userID,
		UpdatedBy:   userID,
	}

	return s.db.Create(&cost).Error
}

// UpdateCost 更新费用记录
func (s *CostService) UpdateCost(entity domain.CostUpdateEntity, userID int64) error {
	// 获取现有记录
	var cost model.AdSlotPlanCost
	if err := s.db.Preload("Plan").Preload("Media").Preload("AdSlot").Preload("AdProduct").Preload("User").First(&cost, entity.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("费用记录不存在")
		}
		return err
	}

	// 检查审核状态，已审核的记录不允许修改
	if cost.AuditStatus == domain.CostAuditStatusApproved {
		return errors.New("已审核的记录不允许修改")
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", entity.Date)
	if err != nil {
		return errors.New("日期格式不正确")
	}

	// 如果修改了计划ID或日期，需要检查是否与其他记录冲突
	if cost.PlanID != entity.PlanID || !cost.Date.Equal(date) {
		var existingCost model.AdSlotPlanCost
		err = s.db.Where("plan_id = ? AND date = ? AND id != ?", entity.PlanID, date, entity.ID).First(&existingCost).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if err == nil {
			return errors.New("该计划和日期的费用记录已存在")
		}
	}

	// 更新字段
	cost.PlanID = entity.PlanID
	cost.Date = date
	cost.MediaID = entity.MediaID
	cost.AdSlotID = entity.AdSlotID
	cost.AdProductID = entity.AdProductID
	cost.UserID = entity.UserID
	cost.Clicks = entity.Clicks
	cost.Cost = entity.Cost
	cost.Remark = entity.Remark
	cost.UpdatedBy = userID

	// 如果记录被拒绝，修改后重置为待审核状态
	if cost.AuditStatus == domain.CostAuditStatusRejected {
		cost.AuditStatus = domain.CostAuditStatusPending
	}

	return s.db.Save(&cost).Error
}

// DeleteCost 删除费用记录
func (s *CostService) DeleteCost(id uint64) error {
	// 获取现有记录
	var cost model.AdSlotPlanCost
	if err := s.db.First(&cost, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("费用记录不存在")
		}
		return err
	}

	// 检查审核状态，已审核的记录不允许删除
	if cost.AuditStatus == domain.CostAuditStatusApproved {
		return errors.New("已审核的记录不允许删除")
	}

	return s.db.Delete(&cost).Error
}

// GetCostList 获取费用记录列表
func (s *CostService) GetCostList(param domain.CostListParam) (domain.CostListResult, error) {
	// 参数验证
	if param.Page < 1 {
		param.Page = 1
	}
	if param.PageSize < 1 || param.PageSize > 100 {
		param.PageSize = 10
	}

	// 日期格式验证
	if param.StartDate != "" {
		if _, err := time.Parse("2006-01-02", param.StartDate); err != nil {
			return domain.CostListResult{}, errors.New("开始日期格式不正确")
		}
	}
	if param.EndDate != "" {
		if _, err := time.Parse("2006-01-02", param.EndDate); err != nil {
			return domain.CostListResult{}, errors.New("结束日期格式不正确")
		}
	}

	var costs []model.AdSlotPlanCost
	var total int64

	// 构建查询条件
	query := s.db.Model(&model.AdSlotPlanCost{})

	// 添加查询条件
	if param.PlanID != 0 {
		query = query.Where("plan_id = ?", param.PlanID)
	}
	if param.MediaID != 0 {
		query = query.Where("media_id = ?", param.MediaID)
	}
	if param.AdSlotID != 0 {
		query = query.Where("ad_slot_id = ?", param.AdSlotID)
	}
	if param.AdProductID != 0 {
		query = query.Where("ad_product_id = ?", param.AdProductID)
	}
	if param.UserID != 0 {
		query = query.Where("user_id = ?", param.UserID)
	}
	if param.AuditStatus != "" {
		query = query.Where("audit_status = ?", param.AuditStatus)
	}

	// 日期范围查询
	if param.StartDate != "" {
		startDate, _ := time.Parse("20060102", param.StartDate)
		query = query.Where("date >= ?", startDate)
	}
	if param.EndDate != "" {
		endDate, _ := time.Parse("20060102", param.EndDate)
		query = query.Where("date <= ?", endDate)
	}

	// 口令组查询 - 通过计划关联表查询
	if param.GroupID != 0 {
		query = query.Joins("JOIN plan_relate_rels prr ON ad_slot_plan_costs.plan_id = prr.plan_id").
			Where("prr.relate_type = ? AND prr.relate_id = ?", 2, param.GroupID)
	}

	// 分类查询 - 通过口令组的分类ID查询
	if param.CategoryID != 0 {
		query = query.Joins("JOIN plan_relate_rels prr ON ad_slot_plan_costs.plan_id = prr.plan_id").
			Joins("JOIN password_groups pg ON prr.relate_id = pg.id AND prr.relate_type = 2").
			Where("pg.category_id = ?", param.CategoryID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.CostListResult{}, err
	}

	// 分页查询
	offset := (param.Page - 1) * param.PageSize
	if err := query.Preload("Plan").
		Preload("Media").
		Preload("AdSlot").
		Preload("AdProduct").
		Preload("User").
		Order("created_at DESC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&costs).Error; err != nil {
		return domain.CostListResult{}, err
	}

	// 转换为响应格式并补充口令组信息
	result := make([]domain.CostEntity, 0, len(costs))
	for _, cost := range costs {
		item := s.convertToListItem(cost)

		// 获取口令组信息
		groupInfo := s.getGroupInfoByPlanID(cost.PlanID)
		if groupInfo != nil {
			item.GroupName = groupInfo.Name
			item.CategoryID = int64(groupInfo.CategoryID)
			item.CategoryName = s.getCategoryName(groupInfo.CategoryID)
		}

		result = append(result, item)
	}

	return domain.CostListResult{
		List:     result,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

// convertToListItem 转换为列表项
func (s *CostService) convertToListItem(cost model.AdSlotPlanCost) domain.CostEntity {
	item := domain.CostEntity{
		ID:          cost.ID,
		PlanID:      cost.PlanID,
		Date:        cost.Date,
		MediaID:     cost.MediaID,
		AdSlotID:    cost.AdSlotID,
		AdProductID: cost.AdProductID,
		UserID:      cost.UserID,
		Clicks:      cost.Clicks,
		Cost:        cost.Cost,
		Remark:      cost.Remark,
		AuditStatus: cost.AuditStatus,
		CreatedAt:   cost.CreatedAt,
		UpdatedAt:   cost.UpdatedAt,
	}

	// 设置关联信息
	if cost.Plan != nil {
		item.PlanCode = cost.Plan.Code
	}
	if cost.Media != nil {
		item.MediaName = cost.Media.Name
	}
	if cost.AdSlot != nil {
		item.AdSlotName = cost.AdSlot.Name
	}
	if cost.AdProduct != nil {
		item.ProductName = cost.AdProduct.Name
	}
	if cost.User != nil {
		item.UserName = cost.User.Name
	}

	// 设置审核信息
	if cost.AuditBy > 0 {
		item.AuditBy = cost.AuditBy
	}
	if !cost.AuditAt.IsZero() {
		item.AuditAt = cost.AuditAt
	}
	if cost.RejectReason != "" {
		item.RejectReason = cost.RejectReason
	}

	return item
}

// getGroupInfoByPlanID 根据计划ID获取口令组信息
func (s *CostService) getGroupInfoByPlanID(planID int64) *model.PasswordGroup {
	var group model.PasswordGroup

	err := s.db.Table("password_groups pg").
		Joins("JOIN plan_relate_rels prr ON pg.id = prr.relate_id").
		Where("prr.plan_id = ? AND prr.relate_type = 2", planID).
		First(&group).Error

	if err != nil {
		return nil
	}

	return &group
}

// getCategoryName 获取分类名称
func (s *CostService) getCategoryName(categoryID int) string {
	var category struct {
		Name string
	}

	err := s.db.Table("password_categories").
		Select("name").
		Where("id = ?", categoryID).
		First(&category).Error

	if err != nil {
		return ""
	}

	return category.Name
}

// GetCostByID 根据ID获取费用记录
func (s *CostService) GetCostByID(id uint64) (domain.CostEntity, error) {
	var cost model.AdSlotPlanCost
	if err := s.db.Preload("Plan").Preload("Media").Preload("AdSlot").Preload("AdProduct").Preload("User").First(&cost, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.CostEntity{}, errors.New("费用记录不存在")
		}
		return domain.CostEntity{}, err
	}
	return s.convertToListItem(cost), nil
}

// AuditCost 审核费用记录
func (s *CostService) AuditCost(entity domain.CostAuditEntity, userID int64) error {
	// 获取现有记录
	var cost model.AdSlotPlanCost
	if err := s.db.First(&cost, entity.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("费用记录不存在")
		}
		return err
	}

	// 检查当前状态
	if cost.AuditStatus != domain.CostAuditStatusPending {
		return errors.New("只能审核待审核状态的记录")
	}

	// 如果是拒绝，必须提供拒绝原因
	if entity.AuditStatus == domain.CostAuditStatusRejected && entity.RejectReason == "" {
		return errors.New("拒绝时必须提供拒绝原因")
	}

	// 更新审核状态
	updates := map[string]any{
		"audit_status":  entity.AuditStatus,
		"audit_by":      userID,
		"audit_at":      gorm.Expr("NOW()"),
		"reject_reason": entity.RejectReason,
		"updated_at":    gorm.Expr("NOW()"),
	}

	return s.db.Model(&model.AdSlotPlanCost{}).Where("id = ?", entity.ID).Updates(updates).Error
}

// GetApprovedCostsByPlanIDs 获取已审核的费用记录（用于报表统计）
func (s *CostService) GetApprovedCostsByPlanIDs(planIDs []uint64, startDate, endDate time.Time) ([]model.AdSlotPlanCost, error) {
	var costs []model.AdSlotPlanCost
	err := s.db.Where("plan_id IN ? AND date BETWEEN ? AND ? AND audit_status = ?",
		planIDs, startDate, endDate, domain.CostAuditStatusApproved).
		Find(&costs).Error
	if err != nil {
		return nil, fmt.Errorf("获取已审核费用记录失败: %w", err)
	}
	return costs, nil
}

// GetCostSumByPlanIDs 获取指定计划的费用总和
func (s *CostService) GetCostSumByPlanIDs(planIDs []uint64, startDate, endDate time.Time) (float64, error) {
	var sum float64
	err := s.db.Model(&model.AdSlotPlanCost{}).
		Where("plan_id IN ? AND date BETWEEN ? AND ? AND audit_status = ?",
			planIDs, startDate, endDate, domain.CostAuditStatusApproved).
		Select("COALESCE(SUM(cost), 0)").
		Scan(&sum).Error
	if err != nil {
		return 0, fmt.Errorf("获取费用总和失败: %w", err)
	}
	return sum, nil
}

// ImportCost 导入推广成本
func (s *CostService) ImportCost(ctx context.Context, param domain.CostImportParam) (bool, error) {
	// 1. 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(param.File.Filename))
	if ext != ".xlsx" && ext != ".xls" {
		return false, errors.New("只支持Excel文件格式(.xlsx/.xls)")
	}

	// 2. 打开文件
	src, err := param.File.Open()
	if err != nil {
		return false, errors.New("打开文件失败: " + err.Error())
	}
	defer src.Close()

	// 3. 解析Excel文件
	f, err := excelize.OpenReader(src)
	if err != nil {
		return false, errors.New("解析Excel文件失败: " + err.Error())
	}
	defer f.Close()

	// 4. 获取工作表
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		return false, errors.New("Excel文件中没有工作表")
	}

	// 5. 存储所有需要验证的口令组名称
	var groupNames []string
	for _, sheet := range sheets {
		// 检查sheet名称格式是否符合要求
		if !strings.HasSuffix(sheet, "-总") {
			continue
		}
		// 提取口令组名称并去除前后空格
		groupName := strings.TrimSpace(strings.TrimSuffix(sheet, "-总"))
		if groupName != "" {
			groupNames = append(groupNames, groupName)
		}
	}

	if len(groupNames) == 0 {
		return false, errors.New("Excel中没有找到符合格式的sheet页，sheet名称必须以'-总'结尾")
	}

	// 6. 验证所有口令组是否存在
	var groups []model.PasswordGroup
	if err := s.db.Where("name IN ?", groupNames).Where("category_id = ?", param.CategoryID).Find(&groups).Error; err != nil {
		return false, err
	}

	// 创建口令组名称到ID的映射
	groupMap := make(map[string]int64)
	for _, group := range groups {
		groupMap[group.Name] = int64(group.ID)
	}

	// 检查是否有未入库的口令组
	var missingGroups []string
	for _, name := range groupNames {
		if _, exists := groupMap[name]; !exists {
			missingGroups = append(missingGroups, name)
		}
	}

	if len(missingGroups) > 0 {
		return false, fmt.Errorf("以下口令组未入库，请先入库后再导入：%s", strings.Join(missingGroups, "、"))
	}

	// 用于跟踪是否创建了任务
	var hasTask bool

	// 7. 开启事务处理数据导入
	err = s.db.Transaction(func(tx *gorm.DB) error {
		currentYear := time.Now().Year()
		currentMonth := int(time.Now().Month())

		// 收集组信息
		var groupInfos []map[string]any

		for sheetName, groupId := range groupMap {
			// 获取sheet数据
			rows, err := f.GetRows(sheetName + "-总")
			if err != nil {
				return fmt.Errorf("读取sheet[%s]失败: %v", sheetName, err)
			}

			// 解析Excel中的所有日期和成本
			dailyCosts := make(map[string]float64)
			var minDate, maxExcelDate string

			// 跳过表头，处理所有数据行
			for i := 1; i < len(rows); i++ {
				row := rows[i]
				if len(row) < 2 {
					continue
				}

				// 解析日期
				dateStr := strings.TrimSpace(row[0])
				if dateStr == "" || strings.TrimSpace(dateStr) == "总计" {
					break
				}

				var year, month, day int
				// 尝试解析 Excel 数值日期
				if numDate, err := strconv.ParseFloat(dateStr, 64); err == nil {
					// Excel 的日期是从 1900-01-01 开始的天数
					// 将 Excel 数值转换为时间
					excelEpoch := time.Date(1899, time.December, 30, 0, 0, 0, 0, time.UTC)
					date := excelEpoch.Add(time.Duration(numDate) * 24 * time.Hour)
					year = date.Year()
					month = int(date.Month())
					day = date.Day()
				} else {
					// 如果不是数值，尝试解析 "MM月DD日" 格式
					parts := strings.Split(dateStr, "月")
					if len(parts) != 2 {
						return fmt.Errorf("sheet[%s]第%d行日期格式错误，应为'MM月DD日'格式或Excel日期格式", sheetName, i+1)
					}

					var err error
					month, err = strconv.Atoi(parts[0])
					if err != nil {
						return fmt.Errorf("sheet[%s]第%d行月份格式错误: %v", sheetName, i+1, err)
					}

					day, err = strconv.Atoi(strings.TrimSuffix(parts[1], "日"))
					if err != nil {
						return fmt.Errorf("sheet[%s]第%d行日期格式错误: %v", sheetName, i+1, err)
					}

					// 确定年份
					year = currentYear
					if month > currentMonth {
						year--
					}
				}

				// 构造报表日期
				reportDate := fmt.Sprintf("%d%02d%02d", year, month, day)

				// 如果日期超过今天了则忽略
				if reportDate > time.Now().Format("20060102") {
					continue
				}

				// 解析成本
				costStr := strings.TrimSpace(row[1])
				var cost float64
				if costStr == "" {
					cost = 0
				} else {
					cost, err = strconv.ParseFloat(costStr, 64)
					if err != nil {
						cost = 0
					}
				}

				dailyCosts[reportDate] = cost

				// 更新最小和最大日期
				if minDate == "" || reportDate < minDate {
					minDate = reportDate
				}
				if maxExcelDate == "" || reportDate > maxExcelDate {
					maxExcelDate = reportDate
				}
			}

			// 如果没有数据，跳过处理
			if minDate == "" || maxExcelDate == "" {
				continue
			}

			// 获取5天前的日期
			daysAgo := time.Now().AddDate(0, 0, -5).Format("20060102")

			// 记录需要更新订单数据的日期范围
			var needUpdateStartDate, needUpdateEndDate string

			// 处理每个日期的数据
			for reportDate, cost := range dailyCosts {
				// 只处理5天内的数据
				if reportDate < daysAgo {
					continue
				}

				// 将日期字符串转换为time.Time
				date, err := time.Parse("20060102", reportDate)
				if err != nil {
					return fmt.Errorf("日期格式转换错误: %v", err)
				}

				// 创建或更新费用记录
				costRecord := model.AdSlotPlanCost{
					PlanID:      groupId,
					Date:        date,
					Cost:        cost,
					AuditStatus: domain.CostAuditStatusPending,
					CreatedBy:   param.UserID,
					UpdatedBy:   param.UserID,
				}

				// 查找是否已存在该记录
				var existingRecord model.AdSlotPlanCost
				result := tx.Where("plan_id = ? AND date = ?", groupId, date).First(&existingRecord)

				if result.Error != nil {
					if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
						return result.Error
					}
					// 记录不存在，创建新记录
					if err := tx.Create(&costRecord).Error; err != nil {
						return err
					}
				} else {
					// 记录存在，且在5天内，更新记录
					if reportDate >= daysAgo {
						// 如果已审核通过，不更新
						if existingRecord.AuditStatus == domain.CostAuditStatusApproved {
							continue
						}

						existingRecord.Cost = cost
						existingRecord.UpdatedBy = param.UserID

						// 如果记录被拒绝，修改后重置为待审核状态
						if existingRecord.AuditStatus == domain.CostAuditStatusRejected {
							existingRecord.AuditStatus = domain.CostAuditStatusPending
						}

						if err := tx.Save(&existingRecord).Error; err != nil {
							return err
						}
					}
				}

				// 更新需要处理订单数据的日期范围
				if needUpdateStartDate == "" || reportDate < needUpdateStartDate {
					needUpdateStartDate = reportDate
				}
				if needUpdateEndDate == "" || reportDate > needUpdateEndDate {
					needUpdateEndDate = reportDate
				}
			}

			// 如果有需要更新的数据，收集组信息
			if needUpdateStartDate != "" && needUpdateEndDate != "" {
				groupInfos = append(groupInfos, map[string]any{
					"group_id":   groupId,
					"sheet_name": sheetName,
					"start_date": needUpdateStartDate,
					"end_date":   needUpdateEndDate,
				})
			}
		}

		// 如果有需要更新的数据，创建一个总的任务
		if len(groupInfos) > 0 {
			paramsJson, err := json.Marshal(groupInfos)
			if err != nil {
				return err
			}

			// 生成任务名称：年月日+文件名（不含扩展名）
			fileName := strings.TrimSuffix(param.File.Filename, filepath.Ext(param.File.Filename))
			taskName := time.Now().Format("20060102") + "_" + fileName

			// 创建导入任务
			task := model.Task{
				TaskType:  1, // 费用导入场景
				Params:    string(paramsJson),
				Status:    "pending",
				Name:      taskName,
				CreatedBy: param.UserID,
			}
			if err := tx.Create(&task).Error; err != nil {
				return err
			}
			hasTask = true
		}
		return nil
	})

	return hasTask, err
}
