# 小红书账号授权完整指南

## 概述

本指南详细说明如何使用广告账号管理API进行小红书账号的授权和令牌管理。

## 授权流程

### 1. 准备工作

在开始授权之前，确保：

1. **配置小红书应用信息**
   ```yaml
   # config.yaml
   xiaohongshu:
     app_id: "your-xiaohongshu-app-id"
     secret: "your-xiaohongshu-secret"
     is_prod: false  # 测试环境，生产环境设置为true
   ```

2. **创建广告账号**
   ```bash
   curl -X POST http://localhost:8080/api/v1/ad-accounts/master \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your_jwt_token" \
     -d '{
       "platform": 1,
       "account_name": "我的小红书主账号",
       "platform_account_id": "xhs_main_001",
       "owner": "运营团队"
     }'
   ```

### 2. 获取授权码

#### 方式一：通过小红书开放平台
1. 登录小红书开放平台
2. 进入应用管理页面
3. 点击"授权管理"
4. 选择要授权的广告主账号
5. 获取授权码（authorization_code）

#### 方式二：通过OAuth2授权链接
```
https://ads.xiaohongshu.com/oauth2/authorize?
  client_id=YOUR_APP_ID&
  response_type=code&
  redirect_uri=YOUR_REDIRECT_URI&
  scope=ads_management&
  state=YOUR_STATE
```

### 3. 执行授权

使用获取到的授权码进行账号授权：

```bash
curl -X POST http://localhost:8080/api/v1/ad-accounts/xiaohongshu/auth \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "account_id": 1,
    "auth_code": "your_authorization_code_here"
  }'
```

#### 成功响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "xhs_at_*********0abcdef",
    "refresh_token": "xhs_rt_abcdef*********0",
    "access_token_expires_in": 3600,
    "refresh_token_expires_in": 7200,
    "advertiser_id": *********,
    "advertiser_name": "测试广告主",
    "message": "小红书账号授权成功"
  }
}
```

### 4. 令牌管理

#### 检查令牌状态
```bash
curl -X GET http://localhost:8080/api/v1/ad-accounts/1 \
  -H "Authorization: Bearer your_jwt_token"
```

#### 刷新访问令牌
当访问令牌即将过期时，使用刷新令牌获取新的访问令牌：

```bash
curl -X POST http://localhost:8080/api/v1/ad-accounts/xiaohongshu/refresh-token \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "account_id": 1,
    "refresh_token": "xhs_rt_abcdef*********0"
  }'
```

#### 刷新成功响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "access_token": "xhs_at_new*********0abcdef",
    "refresh_token": "xhs_rt_newabcdef*********0",
    "access_token_expires_in": 3600,
    "refresh_token_expires_in": 7200,
    "message": "小红书令牌刷新成功"
  }
}
```

## 自动化令牌管理

### 定时刷新策略

建议实现定时任务来自动刷新即将过期的令牌：

```go
// 伪代码示例
func AutoRefreshTokens() {
    // 查询即将过期的令牌（例如：1小时内过期）
    accounts := getAccountsWithExpiringTokens(time.Hour)
    
    for _, account := range accounts {
        // 刷新令牌
        refreshTokenRequest := XiaohongshuRefreshTokenReq{
            AccountID:    account.ID,
            RefreshToken: account.RefreshToken,
        }
        
        // 调用刷新API
        result, err := adAccountService.XiaohongshuRefreshToken(ctx, refreshTokenRequest.ToParam())
        if err != nil {
            log.Printf("刷新账号 %d 的令牌失败: %v", account.ID, err)
            continue
        }
        
        log.Printf("账号 %d 的令牌刷新成功", account.ID)
    }
}
```

### 错误处理

#### 常见错误及处理方式

1. **授权码无效**
   ```json
   {
     "code": 500,
     "message": "小红书账号授权失败: 授权码无效或已过期"
   }
   ```
   **解决方案**：重新获取授权码

2. **刷新令牌过期**
   ```json
   {
     "code": 500,
     "message": "小红书刷新令牌失败: 刷新令牌已过期"
   }
   ```
   **解决方案**：重新进行完整的授权流程

3. **账号不存在**
   ```json
   {
     "code": 500,
     "message": "广告账号不存在或不是小红书平台账号"
   }
   ```
   **解决方案**：检查账号ID和平台设置

## 最佳实践

### 1. 安全性
- 妥善保管应用密钥（secret）
- 使用HTTPS进行所有API调用
- 定期轮换访问令牌
- 不要在客户端存储敏感令牌信息

### 2. 可靠性
- 实现令牌自动刷新机制
- 添加重试逻辑处理网络错误
- 监控令牌过期状态
- 记录授权和刷新操作的日志

### 3. 性能优化
- 缓存有效的访问令牌
- 批量处理令牌刷新操作
- 避免频繁的授权检查
- 使用连接池优化HTTP请求

### 4. 监控和告警
- 监控授权失败率
- 设置令牌过期告警
- 跟踪API调用成功率
- 记录异常情况的详细日志

## 故障排除

### 问题诊断步骤

1. **检查配置**
   - 验证小红书应用配置是否正确
   - 确认环境设置（测试/生产）

2. **验证账号状态**
   - 检查广告账号是否存在
   - 确认账号平台设置为小红书（platform=1）
   - 验证账号使用状态是否为启用

3. **检查授权码**
   - 确认授权码格式正确
   - 验证授权码是否已过期
   - 检查授权范围是否匹配

4. **网络连接**
   - 测试到小红书API的网络连接
   - 检查防火墙和代理设置
   - 验证DNS解析是否正常

### 常用调试命令

```bash
# 检查账号详情
curl -X GET http://localhost:8080/api/v1/ad-accounts/1 \
  -H "Authorization: Bearer your_jwt_token"

# 查看账号列表
curl -X GET "http://localhost:8080/api/v1/ad-accounts?platform=1" \
  -H "Authorization: Bearer your_jwt_token"

# 测试授权（使用测试授权码）
curl -X POST http://localhost:8080/api/v1/ad-accounts/xiaohongshu/auth \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "account_id": 1,
    "auth_code": "test_auth_code"
  }' \
  -v
```

## 总结

通过本指南，您应该能够：

1. 正确配置小红书应用信息
2. 创建和管理小红书广告账号
3. 执行账号授权流程
4. 实现令牌自动刷新机制
5. 处理常见的授权错误
6. 实施最佳安全实践

如果在使用过程中遇到问题，请参考故障排除部分或联系技术支持。
