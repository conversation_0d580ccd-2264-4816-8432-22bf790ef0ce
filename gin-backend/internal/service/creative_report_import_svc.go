package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CreativeReportImportService 创意报表导入服务
type CreativeReportImportService struct {
	db *gorm.DB
}

// NewCreativeReportImportService 创建创意报表导入服务
func NewCreativeReportImportService() *CreativeReportImportService {
	return &CreativeReportImportService{
		db: global.DB,
	}
}

// ImportCreativeReportFromExcel 从Excel导入创意报表修正数据
func (s *CreativeReportImportService) ImportCreativeReportFromExcel(ctx context.Context, file io.Reader) (domain.CreativeReportImportResult, error) {
	// 解析Excel文件
	rows, err := s.parseExcelFile(file)
	if err != nil {
		return domain.CreativeReportImportResult{}, fmt.Errorf("解析Excel文件失败: %w", err)
	}

	if len(rows) == 0 {
		return domain.CreativeReportImportResult{}, fmt.Errorf("excel文件中没有有效数据")
	}

	// 初始化结果
	result := domain.CreativeReportImportResult{
		TotalRows:      len(rows),
		SuccessRows:    0,
		FailedRows:     0,
		UpdatedRecords: 0,
		Errors:         make([]domain.CreativeReportImportError, 0),
		Summary: domain.CreativeReportImportSummary{
			NoteUpdates:     0,
			PlanUpdates:     0,
			ContentStaffSet: 0,
			OperatorSet:     0,
			PasswordSet:     0,
		},
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 处理每一行数据
	for _, row := range rows {
		if err := s.processImportRow(ctx, tx, row, &result); err != nil {
			result.FailedRows++
			result.Errors = append(result.Errors, domain.CreativeReportImportError{
				RowNumber: row.RowNumber,
				Error:     err.Error(),
			})
			zap.L().Error("处理导入行失败",
				zap.Int("row_number", row.RowNumber),
				zap.Error(err))
		} else {
			result.SuccessRows++
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return domain.CreativeReportImportResult{}, fmt.Errorf("提交事务失败: %w", err)
	}

	zap.L().Info("创意报表导入完成",
		zap.Int("total_rows", result.TotalRows),
		zap.Int("success_rows", result.SuccessRows),
		zap.Int("failed_rows", result.FailedRows),
		zap.Int("updated_records", result.UpdatedRecords))

	return result, nil
}

// parseExcelFile 解析Excel文件
func (s *CreativeReportImportService) parseExcelFile(file io.Reader) ([]domain.CreativeReportImportRow, error) {
	// 打开Excel文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("Excel文件中没有工作表")
	}

	// 获取所有行数据
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取Excel行数据失败: %w", err)
	}

	if len(rows) <= 1 {
		return nil, fmt.Errorf("Excel文件中没有数据行（除标题行外）")
	}

	// 解析数据行（跳过标题行）
	var importRows []domain.CreativeReportImportRow
	columnMapping := domain.GetDefaultColumnMapping()

	for i, row := range rows[1:] { // 跳过标题行
		rowNumber := i + 2 // Excel行号从2开始（第1行是标题）

		// 确保行有足够的列
		if len(row) < 7 {
			// 扩展行到7列
			for len(row) < 7 {
				row = append(row, "")
			}
		}

		importRow := domain.CreativeReportImportRow{
			NoteID:       strings.TrimSpace(row[columnMapping.NoteIDColumn]),
			ContentStaff: strings.TrimSpace(row[columnMapping.ContentStaffColumn]),
			PlanID:       strings.TrimSpace(row[columnMapping.PlanIDColumn]),
			Operator:     strings.TrimSpace(row[columnMapping.OperatorColumn]),
			Password:     strings.TrimSpace(row[columnMapping.PasswordColumn]),
			RowNumber:    rowNumber,
		}

		// 解析开始日期
		if startDateStr := strings.TrimSpace(row[columnMapping.StartDateColumn]); startDateStr != "" {
			if startDate, err := s.parseDate(startDateStr); err != nil {
				zap.L().Warn("解析开始日期失败",
					zap.Int("row", rowNumber),
					zap.String("date", startDateStr),
					zap.Error(err))
			} else {
				importRow.StartDate = startDate
			}
		}

		// 解析结束日期
		if endDateStr := strings.TrimSpace(row[columnMapping.EndDateColumn]); endDateStr != "" {
			if endDate, err := s.parseDate(endDateStr); err != nil {
				zap.L().Warn("解析结束日期失败",
					zap.Int("row", rowNumber),
					zap.String("date", endDateStr),
					zap.Error(err))
			} else {
				importRow.EndDate = endDate
			}
		}

		importRows = append(importRows, importRow)
	}

	return importRows, nil
}

// parseDate 解析日期字符串
func (s *CreativeReportImportService) parseDate(dateStr string) (time.Time, error) {
	// 支持多种日期格式
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"2006-1-2",
		"2006/1/2",
		"20060102",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}

	// 尝试解析Excel日期数字格式
	if dayNum, err := strconv.ParseFloat(dateStr, 64); err == nil {
		// Excel日期从1900年1月1日开始计算
		excelEpoch := time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC)
		// Excel有一个bug，认为1900年是闰年，所以需要减去2天
		return excelEpoch.AddDate(0, 0, int(dayNum)-2), nil
	}

	return time.Time{}, fmt.Errorf("无法解析日期格式: %s", dateStr)
}

// processImportRow 处理导入行数据
func (s *CreativeReportImportService) processImportRow(ctx context.Context, tx *gorm.DB, row domain.CreativeReportImportRow, result *domain.CreativeReportImportResult) error {
	// 验证行数据
	if err := domain.ValidateCreativeReportImportRow(row); err != nil {
		return err
	}

	var updatedCount int

	// 如果有笔记ID，更新内容人员
	if row.NoteID != "" {
		count, err := s.updateByNoteID(ctx, tx, row)
		if err != nil {
			return fmt.Errorf("按笔记ID更新失败: %w", err)
		}
		updatedCount += count
		if count > 0 {
			result.Summary.NoteUpdates++
			result.Summary.ContentStaffSet += count
		}
	}

	// 如果有计划ID，更新投手和口令词
	if row.PlanID != "" {
		count, err := s.updateByPlanID(ctx, tx, row)
		if err != nil {
			return fmt.Errorf("按计划ID更新失败: %w", err)
		}
		updatedCount += count
		if count > 0 {
			result.Summary.PlanUpdates++
			if row.Operator != "" {
				result.Summary.OperatorSet += count
			}
			if row.Password != "" {
				result.Summary.PasswordSet += count
			}
		}
	}

	result.UpdatedRecords += updatedCount

	if updatedCount == 0 {
		return fmt.Errorf("没有找到匹配的记录进行更新")
	}

	return nil
}

// updateByNoteID 根据笔记ID更新内容人员
func (s *CreativeReportImportService) updateByNoteID(ctx context.Context, tx *gorm.DB, row domain.CreativeReportImportRow) (int, error) {
	if row.NoteID == "" || row.ContentStaff == "" {
		return 0, nil
	}

	// 构建更新条件
	query := tx.Model(&model.XHSCreativeReports{}).
		Where("note_id = ?", row.NoteID).
		Where("DATE(time) >= ?", row.StartDate.Format("2006-01-02")).
		Where("DATE(time) <= ?", row.EndDate.Format("2006-01-02"))

	// 执行更新
	result := query.Updates(map[string]interface{}{
		"content_staff": row.ContentStaff,
		"updated_at":    time.Now(),
	})

	if result.Error != nil {
		return 0, result.Error
	}

	zap.L().Info("按笔记ID更新内容人员",
		zap.String("note_id", row.NoteID),
		zap.String("content_staff", row.ContentStaff),
		zap.String("date_range", domain.FormatDateRange(row.StartDate, row.EndDate)),
		zap.Int64("affected_rows", result.RowsAffected))

	return int(result.RowsAffected), nil
}

// updateByPlanID 根据计划ID更新投手和口令词
func (s *CreativeReportImportService) updateByPlanID(ctx context.Context, tx *gorm.DB, row domain.CreativeReportImportRow) (int, error) {
	if row.PlanID == "" {
		return 0, nil
	}

	// 构建更新字段
	updates := make(map[string]interface{})
	if row.Operator != "" {
		updates["operator"] = row.Operator
	}
	if row.Password != "" {
		updates["pwd"] = row.Password
	}
	updates["updated_at"] = time.Now()

	if len(updates) <= 1 { // 只有updated_at字段
		return 0, nil
	}

	// 构建更新条件
	query := tx.Model(&model.XHSCreativeReports{}).
		Where("campaign_id = ?", row.PlanID).
		Where("DATE(time) >= ?", row.StartDate.Format("2006-01-02")).
		Where("DATE(time) <= ?", row.EndDate.Format("2006-01-02"))

	// 执行更新
	result := query.Updates(updates)

	if result.Error != nil {
		return 0, result.Error
	}

	zap.L().Info("按计划ID更新投手和口令词",
		zap.String("plan_id", row.PlanID),
		zap.String("operator", row.Operator),
		zap.String("password", row.Password),
		zap.String("date_range", domain.FormatDateRange(row.StartDate, row.EndDate)),
		zap.Int64("affected_rows", result.RowsAffected))

	return int(result.RowsAffected), nil
}
