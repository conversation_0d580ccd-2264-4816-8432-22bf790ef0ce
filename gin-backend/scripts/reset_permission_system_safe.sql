-- ========================================
-- 权限系统安全重置脚本
-- 清空现有数据并重新插入完整的权限系统
-- 包含错误处理和表结构检查
-- ========================================

-- 显示当前数据库
SELECT DATABASE() as '当前数据库';

-- 检查必要的表是否存在
SELECT 
    CASE 
        WHEN COUNT(*) = 4 THEN '✓ 所有必要的表都存在'
        ELSE '✗ 缺少必要的表'
    END as '表检查结果'
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('roles', 'permissions', 'role_permissions', 'users');

-- 显示表结构信息
SELECT '=== 表结构检查 ===' as '信息';

-- 检查 roles 表结构
SELECT 'roles 表字段:' as '表信息';
SELECT 
    column_name as '字段名',
    data_type as '类型',
    is_nullable as '允许空',
    column_default as '默认值'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'roles'
ORDER BY ordinal_position;

-- 检查 permissions 表结构
SELECT 'permissions 表字段:' as '表信息';
SELECT 
    column_name as '字段名',
    data_type as '类型',
    is_nullable as '允许空',
    column_default as '默认值'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'permissions'
ORDER BY ordinal_position;

-- 检查并添加缺失的字段
SET @type_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'type');
SET @data_scope_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'data_scope');

-- 添加type字段（如果不存在）
SET @sql = IF(@type_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `type` varchar(20) DEFAULT ''function'' COMMENT ''权限类型：function-功能权限，data-数据权限'' AFTER `module`',
    'SELECT ''type字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加data_scope字段（如果不存在）
SET @sql = IF(@data_scope_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `data_scope` varchar(20) DEFAULT NULL COMMENT ''数据权限范围：all-全部，dept-部门，self-个人'' AFTER `type`',
    'SELECT ''data_scope字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查 role_permissions 表结构
SELECT 'role_permissions 表字段:' as '表信息';
SELECT 
    column_name as '字段名',
    data_type as '类型',
    is_nullable as '允许空',
    column_default as '默认值'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'role_permissions'
ORDER BY ordinal_position;

-- 暂停执行，让用户检查表结构
SELECT '请检查上述表结构是否正确，如果正确请继续执行后续脚本' as '提示';

-- ========================================
-- 开始数据重置（请确认表结构正确后再执行）
-- ========================================

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- 清空现有数据
TRUNCATE TABLE role_permissions;
TRUNCATE TABLE permissions;
TRUNCATE TABLE roles;

-- 重置自增ID
ALTER TABLE roles AUTO_INCREMENT = 1;
ALTER TABLE permissions AUTO_INCREMENT = 1;
ALTER TABLE role_permissions AUTO_INCREMENT = 1;

-- ========================================
-- 插入角色数据
-- ========================================
INSERT INTO roles (name, code, description, sort_order, status, is_system, created_at, updated_at) VALUES
('超级管理员', 'super_admin', '超级管理员，拥有所有权限', 0, 1, 1, NOW(), NOW()),
('媒介', 'media_specialist', '媒介专员，负责媒体和代理管理', 1, 1, 0, NOW(), NOW()),
('运营', 'operator', '运营专员，负责投放计划和创意管理', 2, 1, 0, NOW(), NOW()),
('管理层', 'manager', '管理层，拥有大部分管理权限', 3, 1, 0, NOW(), NOW()),
('投手', 'advertiser', '投手，负责投放执行和数据分析', 4, 1, 0, NOW(), NOW()),
('财务', 'finance', '财务人员，负责财务管理和审核', 5, 1, 0, NOW(), NOW()),
('技术', 'tech', '技术人员，负责系统维护和技术支持', 6, 1, 0, NOW(), NOW()),
('产品', 'product', '产品经理，负责产品规划和需求管理', 7, 1, 0, NOW(), NOW()),
('BI', 'bi_analyst', 'BI分析师，负责数据分析和报表制作', 8, 1, 0, NOW(), NOW());

-- 验证角色插入
SELECT '角色插入结果:' as '信息';
SELECT id, name, code FROM roles ORDER BY sort_order;

-- ========================================
-- 插入权限数据
-- ========================================

-- 仪表盘权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('仪表盘查看', 'dashboard:view', 'dashboard', 'function', '查看仪表盘页面', NULL, 1, NOW(), NOW()),
('仪表盘数据导出', 'dashboard:export', 'dashboard', 'function', '导出仪表盘数据', NULL, 1, NOW(), NOW()),
('仪表盘全部数据', 'dashboard:data:all', 'dashboard', 'data', '查看全部范围的仪表盘数据', 'all', 1, NOW(), NOW()),
('仪表盘部门数据', 'dashboard:data:dept', 'dashboard', 'data', '查看部门范围的仪表盘数据', 'dept', 1, NOW(), NOW()),
('仪表盘个人数据', 'dashboard:data:self', 'dashboard', 'data', '查看个人范围的仪表盘数据', 'self', 1, NOW(), NOW());

-- 代理管理权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('代理管理查看', 'agent:view', 'agent', 'function', '查看代理管理页面', NULL, 1, NOW(), NOW()),
('代理创建', 'agent:create', 'agent', 'function', '创建新代理', NULL, 1, NOW(), NOW()),
('代理编辑', 'agent:edit', 'agent', 'function', '编辑代理信息', NULL, 1, NOW(), NOW()),
('代理删除', 'agent:delete', 'agent', 'function', '删除代理', NULL, 1, NOW(), NOW()),
('代理审核', 'agent:audit', 'agent', 'function', '审核代理申请和变更', NULL, 1, NOW(), NOW()),
('代理全部数据', 'agent:data:all', 'agent', 'data', '查看全部范围的代理数据', 'all', 1, NOW(), NOW()),
('代理部门数据', 'agent:data:dept', 'agent', 'data', '查看部门范围的代理数据', 'dept', 1, NOW(), NOW()),
('代理个人数据', 'agent:data:self', 'agent', 'data', '查看个人范围的代理数据', 'self', 1, NOW(), NOW());

-- 媒体管理权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('媒体管理查看', 'media:view', 'media', 'function', '查看媒体管理页面', NULL, 1, NOW(), NOW()),
('媒体创建', 'media:create', 'media', 'function', '创建新媒体', NULL, 1, NOW(), NOW()),
('媒体编辑', 'media:edit', 'media', 'function', '编辑媒体信息', NULL, 1, NOW(), NOW()),
('媒体删除', 'media:delete', 'media', 'function', '删除媒体', NULL, 1, NOW(), NOW()),
('媒体审核', 'media:audit', 'media', 'function', '审核媒体申请和变更', NULL, 1, NOW(), NOW()),
('媒体全部数据', 'media:data:all', 'media', 'data', '查看全部范围的媒体数据', 'all', 1, NOW(), NOW()),
('媒体部门数据', 'media:data:dept', 'media', 'data', '查看部门范围的媒体数据', 'dept', 1, NOW(), NOW()),
('媒体个人数据', 'media:data:self', 'media', 'data', '查看个人范围的媒体数据', 'self', 1, NOW(), NOW());

-- 资源位管理权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('资源位管理查看', 'slot:view', 'slot', 'function', '查看资源位管理页面', NULL, 1, NOW(), NOW()),
('资源位创建', 'slot:create', 'slot', 'function', '创建新资源位', NULL, 1, NOW(), NOW()),
('资源位编辑', 'slot:edit', 'slot', 'function', '编辑资源位信息', NULL, 1, NOW(), NOW()),
('资源位删除', 'slot:delete', 'slot', 'function', '删除资源位', NULL, 1, NOW(), NOW()),
('资源位审核', 'slot:audit', 'slot', 'function', '审核资源位申请和变更', NULL, 1, NOW(), NOW()),
('资源位全部数据', 'slot:data:all', 'slot', 'data', '查看全部范围的资源位数据', 'all', 1, NOW(), NOW()),
('资源位部门数据', 'slot:data:dept', 'slot', 'data', '查看部门范围的资源位数据', 'dept', 1, NOW(), NOW()),
('资源位个人数据', 'slot:data:self', 'slot', 'data', '查看个人范围的资源位数据', 'self', 1, NOW(), NOW());

-- 投放计划权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('投放计划查看', 'plan:view', 'plan', 'function', '查看投放计划页面', NULL, 1, NOW(), NOW()),
('投放计划创建', 'plan:create', 'plan', 'function', '创建投放计划', NULL, 1, NOW(), NOW()),
('投放计划编辑', 'plan:edit', 'plan', 'function', '编辑投放计划', NULL, 1, NOW(), NOW()),
('投放计划删除', 'plan:delete', 'plan', 'function', '删除投放计划', NULL, 1, NOW(), NOW()),
('投放计划审核通过', 'plan:approve', 'plan', 'function', '审核通过投放计划', NULL, 1, NOW(), NOW()),
('投放计划审核拒绝', 'plan:reject', 'plan', 'function', '审核拒绝投放计划', NULL, 1, NOW(), NOW()),
('投放计划停止', 'plan:stop', 'plan', 'function', '停止投放计划', NULL, 1, NOW(), NOW()),
('投放计划重新投放', 'plan:restart', 'plan', 'function', '重新开始投放计划', NULL, 1, NOW(), NOW()),
('投放计划修改策略', 'plan:update_delivery_mode', 'plan', 'function', '修改投放策略', NULL, 1, NOW(), NOW()),
('投放计划生成链接', 'plan:generate_link', 'plan', 'function', '生成投放推广链接', NULL, 1, NOW(), NOW()),
('投放计划全部数据', 'plan:data:all', 'plan', 'data', '查看全部范围的投放计划数据', 'all', 1, NOW(), NOW()),
('投放计划部门数据', 'plan:data:dept', 'plan', 'data', '查看部门范围的投放计划数据', 'dept', 1, NOW(), NOW()),
('投放计划个人数据', 'plan:data:self', 'plan', 'data', '查看个人范围的投放计划数据', 'self', 1, NOW(), NOW());

-- 投放管理权限
INSERT INTO permissions (name, code, module, description, data_scope, status, created_at, updated_at) VALUES
('投放管理查看', 'delivery:view', 'delivery', '查看投放管理页面', 'all', 1, NOW(), NOW()),
('投放计划查看', 'delivery:plan:view', 'delivery', '查看投放计划页面', 'all', 1, NOW(), NOW()),
('投放计划列表', 'delivery:plan:list', 'delivery', '查看投放计划列表', 'all', 1, NOW(), NOW()),
('投放计划详情', 'delivery:plan:detail', 'delivery', '查看投放计划详情', 'all', 1, NOW(), NOW()),
('投放计划创建', 'delivery:plan:create', 'delivery', '创建投放计划', 'all', 1, NOW(), NOW()),
('投放计划编辑', 'delivery:plan:edit', 'delivery', '编辑投放计划', 'all', 1, NOW(), NOW()),
('投放计划删除', 'delivery:plan:delete', 'delivery', '删除投放计划', 'all', 1, NOW(), NOW()),
('口令管理查看', 'delivery:command:view', 'delivery', '查看口令管理页面', 'all', 1, NOW(), NOW()),
('口令组列表', 'delivery:command:list', 'delivery', '查看口令组列表', 'all', 1, NOW(), NOW()),
('口令组创建', 'delivery:command:create', 'delivery', '创建口令组', 'all', 1, NOW(), NOW()),
('口令组编辑', 'delivery:command:edit', 'delivery', '编辑口令组', 'all', 1, NOW(), NOW()),
('口令组删除', 'delivery:command:delete', 'delivery', '删除口令组', 'all', 1, NOW(), NOW()),
('费用管理查看', 'delivery:cost:view', 'delivery', '查看费用管理页面', 'all', 1, NOW(), NOW()),
('费用列表查看', 'delivery:cost:list', 'delivery', '查看费用列表', 'all', 1, NOW(), NOW()),
('费用创建', 'delivery:cost:create', 'delivery', '创建费用记录', 'all', 1, NOW(), NOW()),
('费用编辑', 'delivery:cost:edit', 'delivery', '编辑费用记录', 'all', 1, NOW(), NOW()),
('费用导入', 'delivery:cost:import', 'delivery', '导入费用数据', 'all', 1, NOW(), NOW()),
('费用任务查看', 'delivery:cost:task', 'delivery', '查看费用导入任务', 'all', 1, NOW(), NOW()),
('模型管理查看', 'delivery:model:view', 'delivery', '查看模型管理页面', 'all', 1, NOW(), NOW()),
('模型列表查看', 'delivery:model:list', 'delivery', '查看模型列表', 'all', 1, NOW(), NOW()),
('模型创建', 'delivery:model:create', 'delivery', '创建模型', 'all', 1, NOW(), NOW()),
('模型编辑', 'delivery:model:edit', 'delivery', '编辑模型', 'all', 1, NOW(), NOW()),
('模型删除', 'delivery:model:delete', 'delivery', '删除模型', 'all', 1, NOW(), NOW()),
('模型导入', 'delivery:model:import', 'delivery', '导入模型数据', 'all', 1, NOW(), NOW()),
('投放报表查看', 'delivery:report:view', 'delivery', '查看投放报表页面', 'all', 1, NOW(), NOW()),
('投放报表数据', 'delivery:report:data', 'delivery', '查看投放报表数据', 'all', 1, NOW(), NOW()),
('投放报表导出', 'delivery:report:export', 'delivery', '导出投放报表', 'all', 1, NOW(), NOW()),
('投放全部数据', 'delivery:data:all', 'delivery', '查看全部范围的投放数据', 'all', 1, NOW(), NOW()),
('投放部门数据', 'delivery:data:dept', 'delivery', '查看部门范围的投放数据', 'dept', 1, NOW(), NOW()),
('投放个人数据', 'delivery:data:self', 'delivery', '查看个人范围的投放数据', 'self', 1, NOW(), NOW());

-- 数据报表权限
INSERT INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('数据报表查看', 'report:view', 'report', 'function', '查看数据报表页面', NULL, 1, NOW(), NOW()),
('日报表查看', 'report:daily:view', 'report', 'function', '查看日报表页面', NULL, 1, NOW(), NOW()),
('日报表数据查询', 'report:daily:query', 'report', 'function', '查询日报表数据', NULL, 1, NOW(), NOW()),
('日报表详情查看', 'report:daily:detail', 'report', 'function', '查看日报表详情数据', NULL, 1, NOW(), NOW()),
('日报表数据导出', 'report:daily:export', 'report', 'function', '导出日报表数据', NULL, 1, NOW(), NOW()),
('周报表查看', 'report:weekly:view', 'report', 'function', '查看周报表页面', NULL, 1, NOW(), NOW()),
('周报表数据查询', 'report:weekly:query', 'report', 'function', '查询周报表数据', NULL, 1, NOW(), NOW()),
('周报表详情查看', 'report:weekly:detail', 'report', 'function', '查看周报表详情数据', NULL, 1, NOW(), NOW()),
('周报表数据导出', 'report:weekly:export', 'report', 'function', '导出周报表数据', NULL, 1, NOW(), NOW()),
('灯塔报表查看', 'report:lighthouse:view', 'report', 'function', '查看灯塔报表', NULL, 1, NOW(), NOW()),
('推广报表查看', 'report:promotion:view', 'report', 'function', '查看推广报表', NULL, 1, NOW(), NOW()),
('自定义报表', 'report:custom', 'report', 'function', '创建自定义报表', NULL, 1, NOW(), NOW()),
('报表数据导出', 'report:export', 'report', 'function', '导出报表数据', NULL, 1, NOW(), NOW()),
('报表定时任务', 'report:schedule', 'report', 'function', '管理报表定时任务', NULL, 1, NOW(), NOW()),
('报表分享', 'report:share', 'report', 'function', '分享报表', NULL, 1, NOW(), NOW()),
('报表全部数据', 'report:data:all', 'report', 'data', '查看全部范围的报表数据', 'all', 1, NOW(), NOW()),
('报表部门数据', 'report:data:dept', 'report', 'data', '查看部门范围的报表数据', 'dept', 1, NOW(), NOW()),
('报表个人数据', 'report:data:self', 'report', 'data', '查看个人范围的报表数据', 'self', 1, NOW(), NOW());

-- 财务管理权限
INSERT INTO permissions (name, code, module, description, data_scope, status, created_at, updated_at) VALUES
('财务管理查看', 'finance:view', 'finance', '查看财务管理页面', 'all', 1, NOW(), NOW()),
('淘联链接查看', 'finance:taobao:view', 'finance', '查看淘联链接页面', 'all', 1, NOW(), NOW()),
('淘联链接列表', 'finance:taobao:list', 'finance', '查看淘联链接列表', 'all', 1, NOW(), NOW()),
('淘联链接创建', 'finance:taobao:create', 'finance', '创建淘联链接', 'all', 1, NOW(), NOW()),
('淘联链接编辑', 'finance:taobao:edit', 'finance', '编辑淘联链接', 'all', 1, NOW(), NOW()),
('淘联链接删除', 'finance:taobao:delete', 'finance', '删除淘联链接', 'all', 1, NOW(), NOW());

-- 创意管理权限
INSERT INTO permissions (name, code, module, description, data_scope, status, created_at, updated_at) VALUES
('创意管理查看', 'creative:view', 'creative', '查看创意管理页面', 'all', 1, NOW(), NOW()),
('创意列表查看', 'creative:list', 'creative', '查看创意列表', 'all', 1, NOW(), NOW()),
('创意详情查看', 'creative:detail', 'creative', '查看创意详情', 'all', 1, NOW(), NOW()),
('创意创建', 'creative:create', 'creative', '创建创意', 'all', 1, NOW(), NOW()),
('创意编辑', 'creative:edit', 'creative', '编辑创意', 'all', 1, NOW(), NOW()),
('创意删除', 'creative:delete', 'creative', '删除创意', 'all', 1, NOW(), NOW()),
('创意全部数据', 'creative:data:all', 'creative', '查看全部范围的创意数据', 'all', 1, NOW(), NOW()),
('创意部门数据', 'creative:data:dept', 'creative', '查看部门范围的创意数据', 'dept', 1, NOW(), NOW()),
('创意个人数据', 'creative:data:self', 'creative', '查看个人范围的创意数据', 'self', 1, NOW(), NOW()),

-- 系统管理权限
INSERT INTO permissions (name, code, module, description, data_scope, status, created_at, updated_at) VALUES
('系统管理查看', 'system:view', 'system', '查看系统管理页面', 'all', 1, NOW(), NOW()),
('用户管理查看', 'system:user:view', 'system', '查看用户管理', 'all', 1, NOW(), NOW()),
('用户创建', 'system:user:create', 'system', '创建用户', 'all', 1, NOW(), NOW()),
('用户编辑', 'system:user:edit', 'system', '编辑用户', 'all', 1, NOW(), NOW()),
('用户删除', 'system:user:delete', 'system', '删除用户', 'all', 1, NOW(), NOW()),
('用户状态管理', 'system:user:status', 'system', '管理用户状态', 'all', 1, NOW(), NOW()),
('用户密码重置', 'system:user:reset-password', 'system', '重置用户密码', 'all', 1, NOW(), NOW()),
('角色管理查看', 'system:role:view', 'system', '查看角色管理', 'all', 1, NOW(), NOW()),
('角色创建', 'system:role:create', 'system', '创建角色', 'all', 1, NOW(), NOW()),
('角色编辑', 'system:role:edit', 'system', '编辑角色', 'all', 1, NOW(), NOW()),
('角色删除', 'system:role:delete', 'system', '删除角色', 'all', 1, NOW(), NOW()),
('权限管理查看', 'system:permission:view', 'system', '查看权限管理', 'all', 1, NOW(), NOW()),
('权限创建', 'system:permission:create', 'system', '创建权限', 'all', 1, NOW(), NOW()),
('权限编辑', 'system:permission:edit', 'system', '编辑权限', 'all', 1, NOW(), NOW()),
('权限删除', 'system:permission:delete', 'system', '删除权限', 'all', 1, NOW(), NOW()),
('权限分配', 'system:permission:assign', 'system', '分配权限', 'all', 1, NOW(), NOW()),
('部门管理查看', 'system:dept:view', 'system', '查看部门管理', 'all', 1, NOW(), NOW()),
('部门创建', 'system:dept:create', 'system', '创建部门', 'all', 1, NOW(), NOW()),
('部门编辑', 'system:dept:edit', 'system', '编辑部门', 'all', 1, NOW(), NOW()),
('部门删除', 'system:dept:delete', 'system', '删除部门', 'all', 1, NOW(), NOW()),
('菜单管理查看', 'system:menu:view', 'system', '查看菜单管理', 'all', 1, NOW(), NOW()),
('菜单创建', 'system:menu:create', 'system', '创建菜单', 'all', 1, NOW(), NOW()),
('菜单编辑', 'system:menu:edit', 'system', '编辑菜单', 'all', 1, NOW(), NOW()),
('菜单删除', 'system:menu:delete', 'system', '删除菜单', 'all', 1, NOW(), NOW()),
('系统配置管理', 'system:config', 'system', '管理系统配置', 'all', 1, NOW(), NOW()),
('系统日志查看', 'system:log:view', 'system', '查看系统日志', 'all', 1, NOW(), NOW()),
('系统监控查看', 'system:monitor:view', 'system', '查看系统监控', 'all', 1, NOW(), NOW()),
('系统备份', 'system:backup', 'system', '系统备份', 'all', 1, NOW(), NOW()),
('账户删除', 'system:account:delete', 'system', '删除账户（敏感操作）', 'all', 1, NOW(), NOW()),
('操作日志查看', 'system:operation-log:view', 'system', '查看操作日志', 'all', 1, NOW(), NOW());

-- 插件权限
INSERT INTO permissions (name, code, module, description, data_scope, status, created_at, updated_at) VALUES
('插件管理查看', 'plugin:view', 'plugin', '查看插件管理页面', 'all', 1, NOW(), NOW()),
('插件安装', 'plugin:install', 'plugin', '安装插件', 'all', 1, NOW(), NOW()),
('插件配置', 'plugin:config', 'plugin', '配置插件', 'all', 1, NOW(), NOW()),
('插件卸载', 'plugin:uninstall', 'plugin', '卸载插件', 'all', 1, NOW(), NOW());

-- 验证权限插入
SELECT '权限插入结果:' as '信息';
SELECT module as '模块', COUNT(*) as '权限数量' FROM permissions GROUP BY module ORDER BY module;

-- ========================================
-- 分配角色权限
-- ========================================

-- 1. 超级管理员权限分配（所有权限）
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'super_admin'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1;

-- 2. 管理层权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'manager'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.module = 'dashboard'
    OR p.module = 'agent'
    OR p.module = 'media'
    OR p.module = 'slot'
    OR p.module = 'plan'
    OR p.module = 'delivery'
    OR p.module = 'report'
    OR p.module = 'creative'
    OR (p.module = 'system' AND p.code NOT IN (
        'system:config', 'system:backup', 'system:account:delete'
    ))
    OR p.module = 'plugin'
);

-- 3. 媒介权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'media_specialist'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:dept', 'dashboard:data:self')
    OR p.module = 'agent'
    OR p.module = 'media'
    OR p.module = 'slot'
    OR p.code IN ('plan:view', 'plan:list', 'plan:detail', 'plan:data:dept', 'plan:data:self')
    OR p.code IN ('report:view', 'report:daily:view', 'report:weekly:view', 'report:lighthouse:view', 'report:data:dept', 'report:data:self')
    OR p.code IN ('creative:view', 'creative:list', 'creative:detail', 'creative:data:dept', 'creative:data:self')
);

-- 4. 运营权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'operator'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:dept', 'dashboard:data:self', 'dashboard:export')
    OR p.code IN ('agent:view', 'agent:list', 'agent:detail', 'agent:data:dept', 'agent:data:self')
    OR p.code IN ('media:view', 'media:list', 'media:detail', 'media:data:dept', 'media:data:self')
    OR p.code IN ('slot:view', 'slot:list', 'slot:detail', 'slot:data:dept', 'slot:data:self')
    OR p.module = 'plan'
    OR p.module = 'delivery'
    OR p.module = 'report'
    OR p.module = 'creative'
);

-- 5. 投手权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'advertiser'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:self', 'dashboard:export')
    OR p.code IN (
        'plan:view', 'plan:create', 'plan:edit', 'plan:approve', 'plan:reject',
        'plan:stop', 'plan:restart', 'plan:update_delivery_mode', 'plan:generate_link', 'plan:data:self'
    )
    OR p.code IN (
        'delivery:view', 'delivery:plan:view', 'delivery:plan:list', 'delivery:plan:detail',
        'delivery:plan:create', 'delivery:plan:edit', 'delivery:command:view', 'delivery:command:list',
        'delivery:cost:view', 'delivery:cost:list', 'delivery:model:view', 'delivery:model:list',
        'delivery:report:view', 'delivery:report:data', 'delivery:data:self'
    )
    OR p.code IN ('report:view', 'report:daily:view', 'report:daily:query', 'report:daily:detail', 'report:weekly:view', 'report:weekly:query', 'report:weekly:detail', 'report:lighthouse:view', 'report:promotion:view', 'report:data:self')
    OR p.code IN (
        'creative:view', 'creative:list', 'creative:detail', 'creative:create',
        'creative:edit', 'creative:data:self'
    )
    OR p.code IN ('agent:view', 'agent:list', 'agent:detail', 'agent:data:self')
    OR p.code IN ('media:view', 'media:list', 'media:detail', 'media:data:self')
    OR p.code IN ('slot:view', 'slot:list', 'slot:detail', 'slot:data:self')
);

-- 6. 财务权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'finance'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:view', 'dashboard:export')
    OR p.module = 'finance'
    OR p.code LIKE '%cost%'
    OR p.module = 'report'
    OR p.code IN ('plan:view', 'plan:list', 'plan:detail')
    OR p.code IN (
        'delivery:view', 'delivery:cost:view', 'delivery:cost:list', 'delivery:cost:create',
        'delivery:cost:edit', 'delivery:cost:import', 'delivery:cost:task',
        'delivery:report:view', 'delivery:report:data', 'delivery:report:export'
    )
);

-- 7. 技术权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'tech'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:view', 'dashboard:export')
    OR p.module = 'system'
    OR p.module = 'plugin'
    OR p.code IN ('report:view', 'report:custom', 'report:export', 'report:schedule')
    OR p.code IN (
        'agent:view', 'agent:list', 'agent:detail',
        'media:view', 'media:list', 'media:detail',
        'slot:view', 'slot:list', 'slot:detail',
        'plan:view', 'plan:list', 'plan:detail'
    )
    OR p.code IN (
        'delivery:view', 'delivery:model:view', 'delivery:model:list', 'delivery:model:create',
        'delivery:model:edit', 'delivery:model:delete', 'delivery:model:import',
        'delivery:report:view', 'delivery:report:data'
    )
    OR p.code IN ('creative:view', 'creative:list', 'creative:detail')
);

-- 8. 产品权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'product'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.code IN ('dashboard:view', 'dashboard:data:view', 'dashboard:export')
    OR p.code IN (
        'agent:view', 'agent:list', 'agent:detail', 'agent:create', 'agent:edit',
        'agent:data:dept', 'agent:data:self'
    )
    OR p.code IN (
        'media:view', 'media:list', 'media:detail', 'media:create', 'media:edit',
        'media:data:dept', 'media:data:self'
    )
    OR p.code IN (
        'slot:view', 'slot:list', 'slot:detail', 'slot:create', 'slot:edit',
        'slot:data:dept', 'slot:data:self'
    )
    OR p.code IN (
        'plan:view', 'plan:list', 'plan:detail', 'plan:create', 'plan:edit',
        'plan:data:dept', 'plan:data:self'
    )
    OR p.module = 'creative'
    OR p.module = 'report'
    OR p.code IN (
        'delivery:view', 'delivery:plan:view', 'delivery:plan:list', 'delivery:plan:detail',
        'delivery:command:view', 'delivery:command:list', 'delivery:model:view', 'delivery:model:list',
        'delivery:report:view', 'delivery:report:data', 'delivery:data:dept', 'delivery:data:self'
    )
    OR p.code IN (
        'system:user:view', 'system:role:view', 'system:permission:view',
        'system:dept:view', 'system:menu:view'
    )
);

-- 9. BI分析师权限分配
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'bi_analyst'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND (
    p.module = 'dashboard'
    OR p.module = 'report'
    OR p.code IN (
        'agent:view', 'agent:list', 'agent:detail', 'agent:data:dept', 'agent:data:self'
    )
    OR p.code IN (
        'media:view', 'media:list', 'media:detail', 'media:data:dept', 'media:data:self'
    )
    OR p.code IN (
        'slot:view', 'slot:list', 'slot:detail', 'slot:data:dept', 'slot:data:self'
    )
    OR p.code IN (
        'plan:view', 'plan:list', 'plan:detail', 'plan:data:dept', 'plan:data:self'
    )
    OR p.code IN (
        'delivery:view', 'delivery:plan:view', 'delivery:plan:list', 'delivery:plan:detail',
        'delivery:cost:view', 'delivery:cost:list', 'delivery:model:view', 'delivery:model:list',
        'delivery:report:view', 'delivery:report:data', 'delivery:report:export',
        'delivery:data:dept', 'delivery:data:self'
    )
    OR p.code IN (
        'creative:view', 'creative:list', 'creative:detail', 'creative:data:dept', 'creative:data:self'
    )
    OR p.code IN (
        'finance:view', 'finance:taobao:view', 'finance:taobao:list'
    )
    OR p.code IN ('system:operation-log:view')
);

SET FOREIGN_KEY_CHECKS = 1;
SET SQL_MODE = '';

-- ========================================
-- 显示最终结果
-- ========================================
SELECT '权限系统重置完成！' as '完成信息';

SELECT 
    r.name as '角色名称',
    r.code as '角色编码',
    COUNT(rp.permission_id) as '权限数量'
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
GROUP BY r.id, r.name, r.code
ORDER BY r.sort_order;

SELECT 
    r.name as '角色',
    p.module as '模块',
    COUNT(p.id) as '权限数量'
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.name, p.module
ORDER BY r.name, p.module; 

