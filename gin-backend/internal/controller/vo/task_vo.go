package vo

import (
	"time"

	"gin-backend/internal/service/domain"
)

// TaskListReq 任务列表请求
type TaskListReq struct {
	Page     int    `form:"page" json:"page" binding:"required,min=1"`                   // 页码
	PageSize int    `form:"page_size" json:"page_size" binding:"required,min=1,max=100"` // 每页数量
	Status   string `form:"status" json:"status"`                                        // 任务状态
}

// TaskItem 任务项视图对象
type TaskItem struct {
	ID        int64    `json:"id"`         // 任务ID
	Name      string    `json:"name"`       // 任务名称
	Status    string    `json:"status"`     // 任务状态
	CreatedAt time.Time `json:"created_at"` // 创建时间
}

// TaskListResp 任务列表响应
type TaskListResp struct {
	List  []TaskItem `json:"list"`  // 任务列表
	Total int64      `json:"total"` // 总数量
}

// ToTaskItem 将domain层的实体转换为VO层的任务项
func ToTaskItem(entity domain.TaskEntity) TaskItem {
	return TaskItem{
		ID:        entity.ID,
		Name:      entity.Name,
		Status:    entity.Status,
		CreatedAt: entity.CreatedAt,
	}
}

// ToTaskItems 将domain层的实体列表转换为VO层的任务项列表
func ToTaskItems(entities []domain.TaskEntity) []TaskItem {
	items := make([]TaskItem, 0, len(entities))
	for _, entity := range entities {
		items = append(items, ToTaskItem(entity))
	}
	return items
}

// ToTaskListResp 将domain层的结果转换为VO层的响应
func ToTaskListResp(result domain.TaskListResult) TaskListResp {
	return TaskListResp{
		List:  ToTaskItems(result.List),
		Total: result.Total,
	}
}

// ConvertToTaskListResp 将domain层的实体列表和总数转换为VO层的响应
func ConvertToTaskListResp(entities []domain.TaskEntity, total int64) TaskListResp {
	return TaskListResp{
		List:  ToTaskItems(entities),
		Total: total,
	}
}
