package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PlatformCreativeController 广告创意控制器
type PlatformCreativeController struct {
	service *service.PlatformCreativeService
}

// NewPlatformCreativeController 创建广告创意控制器
func NewPlatformCreativeController() *PlatformCreativeController {
	return &PlatformCreativeController{
		service: service.NewPlatformCreativeService(),
	}
}

// RegisterRoutes 注册路由
func (c *PlatformCreativeController) RegisterRoutes(router *gin.Engine) {
	v1 := router.Group("/api/v1")
	v1.Use(middleware.JWTAuth()) // 需要认证

	deliveryGroup := v1.Group("/delivery")
	{
		creativesGroup := deliveryGroup.Group("/creatives")
		{
			creativesGroup.GET("/list", c.List)        // 获取广告创意列表
			creativesGroup.POST("/create", c.Create)   // 创建广告创意
			creativesGroup.GET("/detail", c.GetDetail) // 获取广告创意详情 (查询参数 ?id=123)
			creativesGroup.PUT("/update", c.Update)    // 更新广告创意 (ID在请求体中)
			creativesGroup.DELETE("/delete", c.Delete) // 删除广告创意 (查询参数 ?id=123)
		}
	}
}

// List 获取广告创意列表
// @Summary 获取广告创意列表
// @Description 获取广告创意列表，支持分页和筛选
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param size query int false "每页数量(兼容参数)" default(10)
// @Param agentId query int false "代理ID"
// @Param mediaId query int false "媒体ID"
// @Param marketTargetName query string false "营销目标名称"
// @Param planKeyword query string false "计划关键词"
// @Param groupKeyword query string false "广告组关键词"
// @Param keyword query string false "关键词"
// @Param withPlan query bool false "是否包含计划信息" default(false)
// @Param platformType query string false "平台类型"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeListResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/list [get]
func (c *PlatformCreativeController) List(ctx *gin.Context) {
	var req vo.PlatformCreativeListReq

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换为domain参数
	param := req.ToDomain()

	// 调用服务层
	result, err := c.service.List(ctx.Request.Context(), param)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意列表失败: "+err.Error())
		return
	}

	// 使用转换函数将domain结果转换为VO
	voResult := vo.ToListResp(result.Total, result.List)

	response.Success(ctx, voResult)
}

// Create 创建广告创意
// @Summary 创建广告创意
// @Description 创建新的广告创意
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param request body vo.PlatformCreativeCreateReq true "创建广告创意请求"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeCreateResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/create [post]
func (c *PlatformCreativeController) Create(ctx *gin.Context) {
	var req vo.PlatformCreativeCreateReq

	// 绑定请求体参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := req.ToDomain()

	// 调用服务层
	result, err := c.service.Create(ctx.Request.Context(), entity)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "创建广告创意失败: "+err.Error())
		return
	}

	voResult := vo.ToCreateResp(result.ID)

	response.Success(ctx, voResult)
}

// GetDetail 获取广告创意详情
// @Summary 获取广告创意详情
// @Description 根据ID获取广告创意详情
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param id query int true "广告创意ID"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeDetailResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/detail [get]
func (c *PlatformCreativeController) GetDetail(ctx *gin.Context) {
	// 获取ID参数
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "广告创意ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "广告创意ID格式错误")
		return
	}

	// 调用服务层
	result, err := c.service.GetByID(ctx.Request.Context(), id)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意详情失败: "+err.Error())
		return
	}

	// 使用转换函数将domain结果转换为VO
	voResult := vo.ToDetailResp(result)

	response.Success(ctx, voResult)
}

// Update 更新广告创意
// @Summary 更新广告创意
// @Description 更新广告创意信息
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param request body vo.PlatformCreativeUpdateReq true "更新广告创意请求"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeUpdateResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/update [put]
func (c *PlatformCreativeController) Update(ctx *gin.Context) {
	var req vo.PlatformCreativeUpdateReq

	// 绑定请求体参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := req.ToDomain()

	// 调用服务层
	result, err := c.service.Update(ctx.Request.Context(), entity)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "更新广告创意失败: "+err.Error())
		return
	}

	voResult := vo.ToUpdateResp(result.ID)

	response.Success(ctx, voResult)
}

// Delete 删除广告创意
// @Summary 删除广告创意
// @Description 根据ID删除广告创意
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param id query int true "广告创意ID"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeDeleteResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/delete [delete]
func (c *PlatformCreativeController) Delete(ctx *gin.Context) {
	// 获取ID参数
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "广告创意ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "广告创意ID格式错误")
		return
	}

	// 创建参数实体
	deleteParam := domain.PlatformCreativeDeleteParam{
		ID: id,
	}

	// 调用服务层
	result, err := c.service.Delete(ctx.Request.Context(), deleteParam)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "删除广告创意失败: "+err.Error())
		return
	}

	voResult := vo.ToDeleteResp(result.ID)

	response.Success(ctx, voResult)
}

// GetByDataId 根据DataId获取广告创意
// @Summary 根据DataId获取广告创意
// @Description 根据DataId获取广告创意
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param platformType query string true "平台类型"
// @Param dataId query string true "DataID"
// @Success 200 {object} response.Response{data=vo.PlatformCreativeDetailResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/by-data-id [get]
func (c *PlatformCreativeController) GetByDataId(ctx *gin.Context) {
	// 获取参数
	platformType := ctx.Query("platformType")
	dataId := ctx.Query("dataId")

	if platformType == "" || dataId == "" {
		response.Error(ctx, http.StatusBadRequest, "平台类型和DataID不能为空")
		return
	}

	// 调用服务层
	result, err := c.service.GetByDataId(ctx.Request.Context(), platformType, dataId)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意失败: "+err.Error())
		return
	}

	// 使用转换函数将domain结果转换为VO
	voResult := vo.ToDetailResp(result)

	response.Success(ctx, voResult)
}

// GetByPlanId 根据PlanId获取广告创意列表
// @Summary 根据PlanId获取广告创意列表
// @Description 根据PlanId获取广告创意列表
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param planId query int true "计划ID"
// @Success 200 {object} response.Response{data=[]vo.PlatformCreativeItem}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/by-plan-id [get]
func (c *PlatformCreativeController) GetByPlanId(ctx *gin.Context) {
	// 获取planId参数
	planIdStr := ctx.Query("planId")
	if planIdStr == "" {
		response.Error(ctx, http.StatusBadRequest, "计划ID不能为空")
		return
	}

	planId, err := strconv.ParseUint(planIdStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "计划ID格式错误")
		return
	}

	// 调用服务层
	result, err := c.service.GetByPlanId(ctx.Request.Context(), planId)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意列表失败: "+err.Error())
		return
	}

	// 使用转换函数将domain结果转换为VO
	items := vo.FromDomainToItemList(result)

	response.Success(ctx, gin.H{
		"list": items,
	})
}

// GetByGroupId 根据GroupId获取广告创意列表
// @Summary 根据GroupId获取广告创意列表
// @Description 根据GroupId获取广告创意列表
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Param groupId query int true "组ID"
// @Success 200 {object} response.Response{data=[]vo.PlatformCreativeItem}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/by-group-id [get]
func (c *PlatformCreativeController) GetByGroupId(ctx *gin.Context) {
	// 获取groupId参数
	groupIdStr := ctx.Query("groupId")
	if groupIdStr == "" {
		response.Error(ctx, http.StatusBadRequest, "组ID不能为空")
		return
	}

	groupId, err := strconv.ParseUint(groupIdStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "组ID格式错误")
		return
	}

	// 调用服务层
	result, err := c.service.GetByGroupId(ctx.Request.Context(), groupId)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意列表失败: "+err.Error())
		return
	}

	// 使用转换函数将domain结果转换为VO
	items := vo.FromDomainToItemList(result)

	response.Success(ctx, gin.H{
		"list": items,
	})
}

// GetStatistics 获取创意统计数据
// @Summary 获取创意统计数据
// @Description 获取创意统计数据
// @Tags 广告创意管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/creatives/statistics [get]
func (c *PlatformCreativeController) GetStatistics(ctx *gin.Context) {
	// 调用服务层
	stats, err := c.service.GetStatistics(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告创意统计数据失败: "+err.Error())
		return
	}

	response.Success(ctx, stats)
}
