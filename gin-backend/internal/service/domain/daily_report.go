package domain

// BaseReportParam 基础报表查询参数
type BaseReportParam struct {
	StartDate string // 开始日期，格式YYYY-MM-DD
	EndDate   string // 结束日期，格式YYYY-MM-DD
	MediaID   int64  // 媒体ID，可选过滤条件
	UserID    int64  // 用户ID，用于权限控制
}

// BaseDetailParam 基础详情查询参数
type BaseDetailParam struct {
	MediaID int64 // 媒体ID，可选过滤条件
	UserID  int64 // 用户ID，用于权限控制
}

// DailyReportParam 日报表查询参数
type DailyReportParam struct {
	BaseReportParam
}

// DailyDetailParam 日报表详情查询参数
type DailyDetailParam struct {
	BaseDetailParam
	Date string // 查询日期，格式YYYY-MM-DD
}

// WeeklyReportParam 周报表查询参数
type WeeklyReportParam struct {
	BaseReportParam
}

// WeeklyDetailParam 周报表详情查询参数
type WeeklyDetailParam struct {
	BaseDetailParam
	YearWeek string // 年周，格式YYYYWW
}

// ReportTotalEntity 报表总计数据实体
type ReportTotalEntity struct {
	Cost      float64 // 总成本
	Profit    float64 // 总利润
	Revenue   float64 // 总收入
	ROI       float64 // ROI(投资回报率)
	DateRange string  // 日期范围
}

// DailyReportEntity 日报表实体
type DailyReportEntity struct {
	List []DailyReportItemEntity // 日报表数据项列表
}

// BaseReportItemEntity 基础报表数据项实体
type BaseReportItemEntity struct {
	AlipayProfit     float64 // 支付宝渠道利润
	AlipayOrders     int     // 支付宝渠道订单数
	AlipayRevenue    float64 // 支付宝渠道收入
	AlipayCost       float64 // 支付宝渠道成本
	WechatProfit     float64 // 微信渠道利润
	WechatOrders     int     // 微信渠道订单数
	WechatRevenue    float64 // 微信渠道收入
	WechatCost       float64 // 微信渠道成本
	OtherProfit      float64 // 其他渠道利润
	OtherRevenue     float64 // 其他渠道收入
	OtherCost        float64 // 其他渠道成本
	TotalProfit      float64 // 总利润
	TotalOrders      int     // 总订单数
	TotalRevenue     float64 // 总收入
	TotalCost        float64 // 总成本
	ROI              float64 // ROI(投资回报率)
	ProfitMarginRate float64 // 利润率
}

// DailyReportItemEntity 日报表数据项实体
type DailyReportItemEntity struct {
	BaseReportItemEntity
	Date      string // 日期，格式YYYY-MM-DD
	MediaID   int    // 媒体ID
	MediaName string // 媒体名称
}

// DailyDetailEntity 日报表详情实体
type DailyDetailEntity struct {
	Date    string              // 日期，格式YYYY-MM-DD
	List    []DetailItemEntity  // 详情数据列表
	Summary DetailSummaryEntity // 汇总数据
}

// DetailItemEntity 详情项目实体
type DetailItemEntity struct {
	MediaName        string  // 媒体名称
	MediaID          int     // 媒体ID
	PromotionChannel string  // 推广渠道
	IsProfit         bool    // 是否盈利
	Orders           int     // 订单数
	Revenue          float64 // 收入
	Cost             float64 // 成本
	Profit           float64 // 利润
	Clicks           int     // 点击量
	ROI              float64 // ROI(投资回报率)
	ProfitMarginRate float64 // 利润率
	UserID           int     // 用户ID
	UserName         string  // 用户名称
}

// DetailSummaryEntity 详情汇总实体
type DetailSummaryEntity struct {
	TotalProfit      float64 // 总利润
	TotalOrders      int     // 总订单数
	ProfitMediaCount int     // 盈利媒体数量
	LossMediaCount   int     // 亏损媒体数量
}

// WeeklyReportEntity 周报表实体
type WeeklyReportEntity struct {
	List []WeeklyReportItemEntity // 周报表数据项列表
}

// WeeklyReportItemEntity 周报表数据项实体
type WeeklyReportItemEntity struct {
	BaseReportItemEntity
	YearWeek  string // 年周，格式YYYYWW
	WeekStart string // 周开始日期
	WeekEnd   string // 周结束日期
	WeekRange string // 周日期范围
}

// WeeklyDetailEntity 周报表详情实体
type WeeklyDetailEntity struct {
	YearWeek  string              // 年周，格式YYYYWW
	WeekStart string              // 周开始日期
	WeekEnd   string              // 周结束日期
	List      []DetailItemEntity  // 详情数据列表
	Summary   DetailSummaryEntity // 汇总数据
}

// DailyReportTotal 日报表总计数据
type DailyReportTotal ReportTotalEntity

// WeeklyReportTotal 周报表总计数据
type WeeklyReportTotal ReportTotalEntity

// DetailTotal 详情总计数据
type DetailTotal struct {
	Cost    float64 // 总成本
	Profit  float64 // 总利润
	Revenue float64 // 总收入
	ROI     float64 // ROI(投资回报率)
}

// DetailMedia 详情媒体数据
type DetailMedia struct {
	MediaID   int     // 媒体ID
	MediaName string  // 媒体名称
	Cost      float64 // 成本
	Revenue   float64 // 收入
	Profit    float64 // 利润
	ROI       float64 // ROI(投资回报率)
}

// Metric 指标数据结构
type Metric struct {
	Orders  int     // 订单数
	Revenue float64 // 收入
	Cost    float64 // 成本
	Profit  float64 // 利润
	ROI     float64 // ROI(投资回报率)
}

// WeeklyDetailDaily 周报表详情每日数据
type WeeklyDetailDaily struct {
	Date    string  // 日期，格式YYYY-MM-DD
	Cost    float64 // 成本
	Revenue float64 // 收入
	Profit  float64 // 利润
	ROI     float64 // ROI(投资回报率)
}

// DailyDetailItem 日报表详情项目
type DailyDetailItem DetailItemEntity

// DailyDetailSummary 日报表详情汇总
type DailyDetailSummary DetailSummaryEntity

// WeeklyDetailItem 周报表详情项目
type WeeklyDetailItem DetailItemEntity

// WeeklyDetailSummary 周报表详情汇总
type WeeklyDetailSummary DetailSummaryEntity
