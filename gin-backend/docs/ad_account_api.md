# 广告账号管理API文档

## 概述

广告账号管理API提供了完整的广告账号CRUD操作，支持主账号和子账号的创建、编辑、删除和查询功能。支持按状态、平台、账号名称/ID进行联合查询。

## API端点

### 基础路径
```
/api/v1/ad-accounts
```

## 1. 获取广告账号列表

### 请求
```http
GET /api/v1/ad-accounts
```

### 查询参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20，最大100 |
| account_name | string | 否 | 账号名称/ID联合查询 |
| platform | int | 否 | 平台筛选（1:小红书） |
| authorization_status | int | 否 | 授权状态筛选（0:未授权,1:已授权,2:已过期,3:继承授权） |
| usage_status | int | 否 | 使用状态筛选（1:启用,2:禁用） |
| account_type | int | 否 | 账号类型筛选（1:主账号,2:子账号） |
| parent_id | int | 否 | 父账号ID筛选 |

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_type": 1,
        "account_type_name": "主账号",
        "parent_id": 0,
        "parent_account_name": "",
        "platform": 1,
        "platform_name": "小红书",
        "account_name": "测试主账号",
        "platform_account_id": "xhs_123456",
        "authorization_status": 1,
        "authorization_status_name": "已授权",
        "token_expire_time": "2024-12-31T23:59:59Z",
        "usage_status": 1,
        "usage_status_name": "启用",
        "account_balance": 10000.50,
        "owner": "张三",
        "last_sync": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

## 2. 创建广告账号

### 请求
```http
POST /api/v1/ad-accounts
```

### 请求体
```json
{
  "account_type": 1,
  "parent_id": 0,
  "platform": 1,
  "account_name": "测试主账号",
  "platform_account_id": "xhs_123456",
  "token": "access_token_here",
  "token_expire_time": "2024-12-31T23:59:59Z",
  "account_balance": 10000.50,
  "owner": "张三"
}
```

### 字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_type | int | 是 | 账号类型（1:主账号,2:子账号） |
| parent_id | int | 否 | 父账号ID（子账号时必填） |
| platform | int | 是 | 所属平台（1:小红书） |
| account_name | string | 是 | 账号名称 |
| platform_account_id | string | 是 | 平台账号ID |
| token | string | 否 | 访问令牌 |
| token_expire_time | string | 否 | 令牌过期时间 |
| account_balance | float | 否 | 账户余额 |
| owner | string | 是 | 归属人员 |

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

## 3. 创建主账号（便捷接口）

### 请求
```http
POST /api/v1/ad-accounts/master
```

### 请求体
```json
{
  "platform": 1,
  "account_name": "测试主账号",
  "platform_account_id": "xhs_123456",
  "token": "access_token_here",
  "token_expire_time": "2024-12-31T23:59:59Z",
  "account_balance": 10000.50,
  "owner": "张三"
}
```

注意：此接口会自动设置 `account_type=1` 和 `parent_id=0`

## 4. 创建子账号（便捷接口）

### 请求
```http
POST /api/v1/ad-accounts/sub
```

### 请求体
```json
{
  "parent_id": 1,
  "platform": 1,
  "account_name": "测试子账号",
  "platform_account_id": "xhs_sub_123456",
  "token": "access_token_here",
  "token_expire_time": "2024-12-31T23:59:59Z",
  "account_balance": 5000.00,
  "owner": "李四"
}
```

注意：此接口会自动设置 `account_type=2`，`parent_id` 必填

## 5. 更新广告账号

### 请求
```http
PUT /api/v1/ad-accounts/{id}
```

### 请求体
```json
{
  "account_name": "更新后的账号名称",
  "platform_account_id": "xhs_updated_123456",
  "token": "new_access_token_here",
  "token_expire_time": "2025-12-31T23:59:59Z",
  "usage_status": 1,
  "account_balance": 15000.00,
  "owner": "王五"
}
```

### 字段说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_name | string | 是 | 账号名称 |
| platform_account_id | string | 是 | 平台账号ID |
| token | string | 否 | 访问令牌 |
| token_expire_time | string | 否 | 令牌过期时间 |
| usage_status | int | 是 | 使用状态（1:启用,2:禁用） |
| account_balance | float | 否 | 账户余额 |
| owner | string | 是 | 归属人员 |

## 6. 获取广告账号详情

### 请求
```http
GET /api/v1/ad-accounts/{id}
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "account_type": 1,
    "account_type_name": "主账号",
    "parent_id": 0,
    "parent_account_name": "",
    "platform": 1,
    "platform_name": "小红书",
    "account_name": "测试主账号",
    "platform_account_id": "xhs_123456",
    "authorization_status": 1,
    "authorization_status_name": "已授权",
    "token_expire_time": "2024-12-31T23:59:59Z",
    "usage_status": 1,
    "usage_status_name": "启用",
    "account_balance": 10000.50,
    "owner": "张三",
    "last_sync": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "sub_accounts": [
      {
        "id": 2,
        "account_type": 2,
        "account_type_name": "子账号",
        "parent_id": 1,
        "parent_account_name": "测试主账号",
        "platform": 1,
        "platform_name": "小红书",
        "account_name": "测试子账号",
        "platform_account_id": "xhs_sub_123456",
        "authorization_status": 3,
        "authorization_status_name": "继承授权",
        "usage_status": 1,
        "usage_status_name": "启用",
        "account_balance": 5000.00,
        "owner": "李四",
        "created_at": "2024-01-02T00:00:00Z",
        "updated_at": "2024-01-02T00:00:00Z"
      }
    ]
  }
}
```

注意：只有主账号会返回 `sub_accounts` 字段

## 7. 删除广告账号

### 请求
```http
DELETE /api/v1/ad-accounts/{id}
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

注意：
- 删除主账号前必须先删除所有子账号
- 删除操作是硬删除，数据将被永久删除

## 8. 获取广告账号选项

### 请求
```http
GET /api/v1/ad-accounts/options
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "platforms": [
      {
        "value": 1,
        "label": "小红书"
      }
    ],
    "account_types": [
      {
        "value": 1,
        "label": "主账号"
      },
      {
        "value": 2,
        "label": "子账号"
      }
    ],
    "parent_accounts": [
      {
        "value": 1,
        "label": "测试主账号"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数验证失败 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 1. 创建主账号
```bash
curl -X POST http://localhost:8080/api/v1/ad-accounts/master \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "platform": 1,
    "account_name": "我的小红书主账号",
    "platform_account_id": "xhs_main_001",
    "token": "xhs_access_token_123",
    "account_balance": 50000.00,
    "owner": "运营团队"
  }'
```

### 2. 创建子账号
```bash
curl -X POST http://localhost:8080/api/v1/ad-accounts/sub \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "parent_id": 1,
    "platform": 1,
    "account_name": "子账号-美妆类目",
    "platform_account_id": "xhs_sub_beauty_001",
    "account_balance": 10000.00,
    "owner": "美妆运营"
  }'
```

### 3. 查询账号列表
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts?platform=1&usage_status=1&page=1&page_size=20" \
  -H "Authorization: Bearer your_jwt_token"
```

### 4. 按名称搜索账号
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts?account_name=美妆" \
  -H "Authorization: Bearer your_jwt_token"
```

## 注意事项

1. **权限控制**：所有API都需要JWT认证
2. **数据验证**：创建和更新时会进行严格的参数验证
3. **关联关系**：子账号必须关联到有效的主账号
4. **状态管理**：账号状态会影响子账号的创建和使用
5. **令牌管理**：系统会自动根据令牌状态更新授权状态
