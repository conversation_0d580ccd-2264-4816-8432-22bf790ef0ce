package vo

import (
	"gin-backend/internal/service/domain"
)

// 工作模式相关VO

// WorkModeInfo 工作模式信息
type WorkModeInfo struct {
	Code string `json:"code" form:"code"` // 模式代码
	Name string `json:"name" form:"name"` // 模式名称
}

// GetUserModesReq 获取用户可用工作模式请求
type GetUserModesReq struct {
	UserID int64 `json:"user_id" form:"user_id" binding:"required"` // 用户ID
}

// GetUserModesResp 获取用户可用工作模式响应
type GetUserModesResp struct {
	Modes       []WorkModeInfo `json:"modes"`        // 可用的工作模式列表
	DefaultMode string         `json:"default_mode"` // 默认工作模式
	CurrentMode string         `json:"current_mode"` // 当前工作模式
}

// CheckModePermissionReq 检查模式权限请求
type CheckModePermissionReq struct {
	ModeCode string `json:"mode_code" form:"mode_code" binding:"required"` // 模式代码
}

// CheckModePermissionResp 检查模式权限响应
type CheckModePermissionResp struct {
	HasPermission bool `json:"has_permission"` // 是否有权限
}

// ConvertToDomainGetUserModesParam 转换为领域层获取用户模式参数
func (req GetUserModesReq) ConvertToDomainGetUserModesParam() domain.GetUserModesParam {
	return domain.GetUserModesParam{
		UserID: req.UserID,
	}
}

// NewGetUserModesRespFromDomain 从领域实体创建响应
func NewGetUserModesRespFromDomain(domainEntity domain.UserWorkModes) GetUserModesResp {
	modesVO := make([]WorkModeInfo, 0, len(domainEntity.Modes))
	for _, mode := range domainEntity.Modes {
		modesVO = append(modesVO, WorkModeInfo{
			Code: mode.Code,
			Name: mode.Name,
		})
	}

	return GetUserModesResp{
		Modes:       modesVO,
		DefaultMode: domainEntity.DefaultMode,
		CurrentMode: domainEntity.CurrentMode,
	}
}

// ConvertToDomainWorkModeSwitchParam 转换为领域层切换工作模式参数
func (req SwitchWorkModeReq) ConvertToDomainWorkModeSwitchParam() domain.WorkModeSwitchParam {
	return domain.WorkModeSwitchParam{
		ModeCode: req.Mode,
	}
}

// ConvertToDomainCheckModePermissionParam 转换为领域层检查模式权限参数
func (req CheckModePermissionReq) ConvertToDomainCheckModePermissionParam() domain.CheckModePermissionParam {
	return domain.CheckModePermissionParam{
		ModeCode: req.ModeCode,
	}
}

// NewCheckModePermissionRespFromDomain 从领域实体创建响应
func NewCheckModePermissionRespFromDomain(hasPermission bool) CheckModePermissionResp {
	return CheckModePermissionResp{
		HasPermission: hasPermission,
	}
}

// NewSwitchWorkModeRespFromDomain 从领域实体创建响应
func NewSwitchWorkModeRespFromDomain(data domain.WorkModeMenus) SwitchWorkModeResp {
	menuItems := make([]MenuItem, 0, len(data.Menus))
	for _, menu := range data.Menus {
		menuItems = append(menuItems, convertDomainMenuToVO(menu))
	}

	return SwitchWorkModeResp{
		Mode:        data.Mode,
		MenuItems:   menuItems,
		Permissions: data.Permissions,
	}
}

// convertDomainMenuToVO 将领域层菜单转换为VO层菜单
func convertDomainMenuToVO(menu domain.Menu) MenuItem {
	voMenu := MenuItem{
		Path:      menu.Path,
		Component: menu.Component,
		Name:      menu.Name,
		Meta: MenuMeta{
			Title:      menu.Meta.Title,
			Icon:       menu.Meta.Icon,
			Permission: menu.Meta.Permissions,
		},
	}

	if len(menu.Children) > 0 {
		voMenu.Children = make([]MenuItem, 0, len(menu.Children))
		for _, child := range menu.Children {
			voMenu.Children = append(voMenu.Children, convertDomainMenuToVO(child))
		}
	}

	return voMenu
}
