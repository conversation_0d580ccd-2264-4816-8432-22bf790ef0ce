package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RoleController struct {
	roleService *service.RoleService
}

func NewRoleController() *RoleController {
	return &RoleController{
		roleService: service.NewRoleService(),
	}
}

// ======================== 角色管理 ========================

// GetRoles 获取角色列表
// @Summary 获取角色列表
// @Description 获取角色列表，支持分页和筛选
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param name query string false "角色名称"
// @Param code query string false "角色编码"
// @Param status query int false "状态：1-启用，0-禁用"
// @Success 200 {object} vo.GetRolesResp
// @Router /api/roles [get]
func (c *RoleController) GetRoles(ctx *gin.Context) {
	var req vo.GetRolesReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法将请求转为领域参数
	param := req.ToDomain()

	// 调用服务
	entity, err := c.roleService.GetRoles(ctx.Request.Context(), param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法生成响应
	resp := vo.FromDomainRoleList(entity, param.Page, param.PageSize)
	response.Success(ctx, resp)
}

// GetRoleByID 根据ID获取角色
// @Summary 根据ID获取角色
// @Description 根据ID获取角色详情
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} vo.RoleItem
// @Router /api/roles/{id} [get]
func (c *RoleController) GetRoleByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的角色ID")
		return
	}

	roleEntity, err := c.roleService.GetRoleByID(ctx.Request.Context(), id)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	roleItem := vo.FromDomainRole(roleEntity)
	response.Success(ctx, roleItem)
}

// CreateRole 创建角色
// @Summary 创建角色
// @Description 创建新角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param role body vo.CreateRoleReq true "角色信息"
// @Success 200 {object} vo.RoleItem
// @Router /api/roles [post]
func (c *RoleController) CreateRole(ctx *gin.Context) {
	var req vo.CreateRoleReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法获取domain.RoleEntity
	entity := req.ToDomain()

	// 调用服务创建角色
	roleEntity, err := c.roleService.CreateRole(ctx.Request.Context(), entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	roleItem := vo.FromDomainRole(roleEntity)
	response.Success(ctx, roleItem)
}

// UpdateRole 更新角色
// @Summary 更新角色
// @Description 更新角色信息
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Param role body vo.UpdateRoleReq true "角色信息"
// @Success 200 {object} vo.RoleItem
// @Router /api/roles/{id} [put]
func (c *RoleController) UpdateRole(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的角色ID")
		return
	}

	var req vo.UpdateRoleReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法获取domain.RoleEntity
	entity := req.ToDomain()

	// 调用服务更新角色
	roleEntity, err := c.roleService.UpdateRole(ctx.Request.Context(), id, entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	roleItem := vo.FromDomainRole(roleEntity)
	response.Success(ctx, roleItem)
}

// DeleteRole 删除角色
// @Summary 删除角色
// @Description 删除角色
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param id path int true "角色ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/roles/{id} [delete]
func (c *RoleController) DeleteRole(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的角色ID")
		return
	}

	if err := c.roleService.DeleteRole(ctx.Request.Context(), id); err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{})
}

// GetRoleOptions 获取角色选项列表
// @Summary 获取角色选项列表
// @Description 获取角色选项列表，用于下拉选择
// @Tags 角色管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.GetRoleOptionsResp
// @Router /api/roles/options [get]
func (c *RoleController) GetRoleOptions(ctx *gin.Context) {
	entity, err := c.roleService.GetRoleOptions(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 转换为VO响应
	resp := vo.FromDomainRoleOptions(entity)
	response.Success(ctx, resp)
}

// ======================== 权限管理 ========================

// GetPermissions 获取权限列表
// @Summary 获取权限列表
// @Description 获取权限列表，支持分页和筛选
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param name query string false "权限名称"
// @Param code query string false "权限编码"
// @Param module query string false "所属模块"
// @Param type query string false "权限类型：function-功能权限，data-数据权限"
// @Param data_scope query string false "数据范围：all-全部，dept-部门，self-个人"
// @Param status query int false "状态：1-启用，0-禁用"
// @Success 200 {object} vo.GetPermissionsResp
// @Router /api/permissions [get]
func (c *RoleController) GetPermissions(ctx *gin.Context) {
	var req vo.GetPermissionsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	param := req.ToDomain()

	// 调用服务
	entity, err := c.roleService.GetPermissions(ctx.Request.Context(), param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	resp := vo.FromDomainPermissionList(entity, param.Page, param.PageSize)
	response.Success(ctx, resp)
}

// GetPermissionByID 根据ID获取权限
// @Summary 根据ID获取权限
// @Description 根据ID获取权限详情
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} vo.PermissionItem
// @Router /api/permissions/{id} [get]
func (c *RoleController) GetPermissionByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的权限ID")
		return
	}

	permEntity, err := c.roleService.GetPermissionByID(ctx.Request.Context(), id)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	permItem := vo.FromDomainPermission(permEntity)
	response.Success(ctx, permItem)
}

// CreatePermission 创建权限
// @Summary 创建权限
// @Description 创建新权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param permission body vo.CreatePermissionReq true "权限信息"
// @Success 200 {object} vo.PermissionItem
// @Router /api/permissions [post]
func (c *RoleController) CreatePermission(ctx *gin.Context) {
	var req vo.CreatePermissionReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	entity := req.ToDomain()

	// 调用服务创建权限
	permEntity, err := c.roleService.CreatePermission(ctx.Request.Context(), entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 转换为VO响应
	permItem := vo.FromDomainPermission(permEntity)
	response.Success(ctx, permItem)
}

// UpdatePermission 更新权限
// @Summary 更新权限
// @Description 更新权限信息
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Param permission body vo.UpdatePermissionReq true "权限信息"
// @Success 200 {object} vo.PermissionItem
// @Router /api/permissions/{id} [put]
func (c *RoleController) UpdatePermission(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的权限ID")
		return
	}

	var req vo.UpdatePermissionReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	entity := req.ToDomain()

	// 调用服务更新权限
	permEntity, err := c.roleService.UpdatePermission(ctx.Request.Context(), id, entity)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	permItem := vo.FromDomainPermission(permEntity)
	response.Success(ctx, permItem)
}

// DeletePermission 删除权限
// @Summary 删除权限
// @Description 删除权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param id path int true "权限ID"
// @Success 200 {object} map[string]interface{}
// @Router /api/permissions/{id} [delete]
func (c *RoleController) DeletePermission(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的权限ID")
		return
	}

	if err := c.roleService.DeletePermission(ctx.Request.Context(), id); err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{})
}

// GetPermissionModules 获取权限模块列表
// @Summary 获取权限模块列表
// @Description 获取系统中所有的权限模块列表
// @Tags 权限管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.GetPermissionModulesResp
// @Router /api/permissions/modules [get]
func (c *RoleController) GetPermissionModules(ctx *gin.Context) {
	entity, err := c.roleService.GetPermissionModules(ctx.Request.Context())
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 使用VO中的转换方法
	resp := vo.FromDomainPermissionModules(entity)
	response.Success(ctx, resp)
}

// ======================== 角色权限管理 ========================

// GetRolePermissions 获取角色权限
// @Summary 获取角色权限
// @Description 获取指定角色拥有的权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param role_id query int true "角色ID"
// @Success 200 {object} vo.GetRolePermissionsResp
// @Router /api/roles/permissions [get]
func (c *RoleController) GetRolePermissions(ctx *gin.Context) {
	var req vo.GetRolePermissionsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 获取角色信息
	roleEntity, err := c.roleService.GetRoleByID(ctx.Request.Context(), req.RoleID)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 获取角色权限ID列表
	rolePermEntity, err := c.roleService.GetRolePermissions(ctx.Request.Context(), req.RoleID)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 获取权限详情
	permissions := make([]domain.PermissionEntity, 0)
	for _, permID := range rolePermEntity.PermissionIDs {
		permEntity, err := c.roleService.GetPermissionByID(ctx.Request.Context(), permID)
		if err == nil {
			permissions = append(permissions, permEntity)
		}
	}

	// 按模块分组
	moduleMap := make(map[string][]domain.PermissionEntity)
	for _, perm := range permissions {
		moduleMap[perm.Module] = append(moduleMap[perm.Module], perm)
	}

	// 转换为VO响应
	resp := vo.FromDomainRolePermissions(roleEntity.ID, roleEntity.Name, permissions, moduleMap)
	response.Success(ctx, resp)
}

// AssignPermissions 分配权限
// @Summary 分配权限
// @Description 为角色分配权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param permissions body vo.AssignPermissionsReq true "权限分配信息"
// @Success 200 {object} map[string]interface{}
// @Router /api/roles/permissions/assign [post]
func (c *RoleController) AssignPermissions(ctx *gin.Context) {
	var req vo.AssignPermissionsReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	entity := req.ToDomain()

	// 获取当前用户ID作为操作人
	operatorID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, response.CodeUnauthorized, "未授权操作")
		return
	}

	// 调用服务分配权限
	if err := c.roleService.AssignPermissions(ctx.Request.Context(), entity, operatorID.(int64)); err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{})
}

// RevokePermissions 撤销权限
// @Summary 撤销权限
// @Description 撤销角色权限
// @Tags 角色管理
// @Accept json
// @Produce json
// @Param permissions body vo.RevokePermissionsReq true "权限撤销信息"
// @Success 200 {object} map[string]interface{}
// @Router /api/roles/permissions/revoke [post]
func (c *RoleController) RevokePermissions(ctx *gin.Context) {
	var req vo.RevokePermissionsReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	entity := req.ToDomain()

	// 获取当前用户ID作为操作人
	operatorID, exists := ctx.Get("user_id")
	if !exists {
		response.Error(ctx, response.CodeUnauthorized, "未授权操作")
		return
	}

	// 调用服务撤销权限
	if err := c.roleService.RevokePermissions(ctx.Request.Context(), entity, operatorID.(int64)); err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	response.Success(ctx, gin.H{})
}

// ======================== 用户权限管理 ========================

// CheckUserPermission 检查用户权限
// @Summary 检查用户权限
// @Description 检查用户是否拥有指定权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param permission body vo.CheckUserPermissionReq true "权限检查参数"
// @Success 200 {object} vo.CheckUserPermissionResp
// @Router /api/permissions/check [post]
func (c *RoleController) CheckUserPermission(ctx *gin.Context) {
	var req vo.CheckUserPermissionReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 调用服务检查用户权限
	hasPermission, err := c.roleService.CheckUserPermission(ctx.Request.Context(), req.UserID, req.Permission)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	resp := vo.CheckUserPermissionResp{
		HasPermission: hasPermission,
	}
	response.Success(ctx, resp)
}

// GetUserPermissions 获取用户权限
// @Summary 获取用户权限
// @Description 获取用户拥有的角色和权限
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param user_id query int true "用户ID"
// @Success 200 {object} vo.GetUserPermissionsResp
// @Router /api/permissions/user [get]
func (c *RoleController) GetUserPermissions(ctx *gin.Context) {
	var req vo.GetUserPermissionsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 调用服务获取用户权限
	userPermEntity, err := c.roleService.GetUserPermissions(ctx.Request.Context(), req.UserID)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 获取用户信息
	var userName string
	// 这里应该调用用户服务获取用户名，简化为直接返回ID
	userName = strconv.FormatInt(req.UserID, 10)

	// 权限按模块分组
	permissions := make([]domain.PermissionEntity, 0)
	// 如果需要获取权限详情，应该根据权限编码查询权限实体
	// 简化为空列表

	moduleMap := make(map[string][]domain.PermissionEntity)
	// 简化处理，实际应该根据权限编码查询权限实体后进行分组

	// 转换为VO响应
	resp := vo.FromDomainUserPermissions(req.UserID, userName, userPermEntity.Roles, permissions, moduleMap)
	response.Success(ctx, resp)
}

// ======================== 权限操作日志 ========================

// GetPermissionLogs 获取权限操作日志
// @Summary 获取权限操作日志
// @Description 获取权限操作日志记录
// @Tags 权限管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param role_id query int false "角色ID"
// @Param operator_id query int false "操作人ID"
// @Param action query string false "操作类型：assign-分配，revoke-撤销"
// @Param permission_id query int false "权限ID"
// @Success 200 {object} vo.GetPermissionLogsResp
// @Router /api/permissions/logs [get]
func (c *RoleController) GetPermissionLogs(ctx *gin.Context) {
	var req vo.GetPermissionLogsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用VO中的转换方法
	param := req.ToDomain()

	// 调用服务获取权限日志
	entity, err := c.roleService.GetPermissionLogs(ctx.Request.Context(), param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, err.Error())
		return
	}

	// 转换为VO响应
	resp := vo.FromDomainPermissionLogList(entity, param.Page, param.PageSize)
	response.Success(ctx, resp)
}
