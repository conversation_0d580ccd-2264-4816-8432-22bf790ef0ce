# 计划列表多日查询优化

## 修改概述

将计划列表的多日查询从 `xhs_creative_reports` 创意报表改为从 `ad_plan_stats` 计划统计表查询，通过 `plan_id` 进行聚合，提升查询性能和数据一致性。

## 修改内容

### 1. 数据查询策略调整

**修改前**：
- 单日查询：从 `ad_plan_stats` 表查询
- 多日查询：从 `xhs_creative_reports` 表聚合

**修改后**：
- 单日查询：从 `ad_plan_stats` 表查询
- 多日查询：从 `ad_plan_stats` 表按 `plan_id` 聚合

### 2. 核心方法修改

#### 2.1 getPlanListFromReports 方法

**修改前**：
```go
// 从创意报表聚合
query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

selectFields := `
    campaign_id as plan_id,
    MAX(campaign_name) as plan_name,
    MAX(account_name) as account_name,
    SUM(fee) as consumption,
    // ...
`
```

**修改后**：
```go
// 从计划统计表聚合
query := s.db.WithContext(ctx).Model(&model.AdPlanStats{})

selectFields := `
    plan_id,
    MAX(plan_name) as plan_name,
    MAX(account_id) as account_id,
    SUM(cost) as consumption,
    SUM(actual_cost) as actual_cost,
    SUM(impressions) as impressions,
    SUM(clicks) as clicks,
    // ...
`
```

#### 2.2 新增筛选条件方法

添加了 `addPlanStatsFilterConditions` 方法，专门处理多日查询的筛选条件：

```go
func (s *AdPlanListService) addPlanStatsFilterConditions(query *gorm.DB, param domain.AdPlanListParam) {
    if param.AccountID != nil {
        query.Where("account_id = ?", *param.AccountID)
    }
    if param.AccountName != "" {
        // 通过账号名称查找账号ID
        var accountIDs []int64
        s.db.Model(&model.AdAccounts{}).
            Where("account_name LIKE ?", "%"+param.AccountName+"%").
            Pluck("id", &accountIDs)
        if len(accountIDs) > 0 {
            query.Where("account_id IN ?", accountIDs)
        } else {
            query.Where("account_id = -1")
        }
    }
    if param.PlanName != "" {
        query.Where("plan_name LIKE ?", "%"+param.PlanName+"%")
    }
    if param.StartDate != nil {
        query.Where("DATE(stat_date) >= ?", param.StartDate.Format("2006-01-02"))
    }
    if param.EndDate != nil {
        query.Where("DATE(stat_date) <= ?", param.EndDate.Format("2006-01-02"))
    }
}
```

### 3. 聚合逻辑

#### 3.1 多日查询聚合SQL

```sql
SELECT 
    plan_id,
    MAX(plan_name) as plan_name,
    MAX(account_id) as account_id,
    SUM(cost) as consumption,
    SUM(actual_cost) as actual_cost,
    SUM(impressions) as impressions,
    SUM(clicks) as clicks,
    CASE 
        WHEN SUM(impressions) > 0 THEN (SUM(clicks) * 100.0 / SUM(impressions))
        ELSE 0 
    END as click_rate,
    MAX(updated_at) as last_update
FROM ad_plan_stats
WHERE DATE(stat_date) BETWEEN ? AND ?
GROUP BY plan_id
ORDER BY consumption DESC, plan_id ASC
```

#### 3.2 账号名称获取

多日查询中，账号名称通过 `getAccountNameByID` 方法从账号表获取：

```go
for _, result := range results {
    // 从账号表获取账号名称
    accountName := s.getAccountNameByID(ctx, result.AccountID)
    
    plans = append(plans, domain.AdPlanListItem{
        PlanID:      result.PlanID,
        PlanName:    result.PlanName,
        AccountName: accountName, // 从账号表获取
        // ...
    })
}
```

### 4. 影响的功能模块

#### 4.1 计划列表查询
- `GetPlanList` - 多日查询现在从统计表聚合
- 查询性能提升，数据一致性更好

#### 4.2 计划列表导出
- `getExportDataFromReports` - 导出功能也改为从统计表聚合
- 导出数据与列表查询保持一致

#### 4.3 计划统计信息
- `getPlanStatsFromReports` - 统计信息也从统计表聚合
- 统计数据更准确

### 5. 性能优化

#### 5.1 查询性能提升
- `ad_plan_stats` 表数据量相对较小，查询更快
- 避免了从大量创意报表数据中聚合
- 统计表已经预聚合了基础指标

#### 5.2 数据一致性
- 单日和多日查询都基于同一数据源
- 避免了不同数据源可能的数据差异

### 6. 字段映射对比

| 功能 | 原字段（创意报表） | 新字段（统计表） |
|------|------------------|-----------------|
| 计划ID | campaign_id | plan_id |
| 计划名称 | campaign_name | plan_name |
| 账号名称 | account_name | 通过account_id查询 |
| 消费 | fee | cost |
| 实际消费 | fee | actual_cost |
| 展现量 | impression | impressions |
| 点击量 | click | clicks |
| 时间字段 | time | stat_date |

### 7. 筛选条件处理

#### 7.1 账号筛选
- **账号ID筛选**：直接使用 `account_id` 字段
- **账号名称筛选**：先查询账号表获取ID列表，再筛选

#### 7.2 时间筛选
- 使用 `stat_date` 字段进行日期范围筛选
- 支持 `>=` 和 `<=` 操作符

#### 7.3 计划名称筛选
- 直接使用 `plan_name` 字段进行模糊查询

### 8. 使用示例

#### 8.1 多日查询示例
```bash
# 查询最近7天的计划数据
curl -X GET "http://localhost:8080/api/v1/plans?start_date=2024-06-10&end_date=2024-06-16&page=1&page_size=20"
```

#### 8.2 响应数据结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "plan_id": "plan_001",
        "plan_name": "测试计划",
        "account_name": "营销树长大", // 从账号表获取
        "consumption": 5000.0,      // 7天消费总和
        "actual_cost": 4500.0,      // 7天实际消费总和
        "impressions": 50000,       // 7天展现量总和
        "clicks": 500,              // 7天点击量总和
        "click_rate": 1.0,          // 重新计算的点击率
        "last_update": "2024-06-16 12:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 9. 优势总结

#### 9.1 性能优势
- 查询速度更快（统计表数据量小）
- 减少了复杂的跨表聚合操作
- 索引利用更充分

#### 9.2 数据一致性
- 单日和多日查询使用相同数据源
- 避免了数据源不一致的问题
- 计算逻辑统一

#### 9.3 维护性
- 代码逻辑更简洁
- 筛选条件处理统一
- 易于扩展和维护

### 10. 注意事项

#### 10.1 数据依赖
- 需要确保 `ad_plan_stats` 表数据及时更新
- 依赖创意报表数据的聚合任务正常运行

#### 10.2 账号名称查询
- 每个计划都会查询一次账号名称
- 如果性能成为问题，可以考虑批量查询优化

#### 10.3 数据完整性
- 确保统计表中的数据覆盖了所需的时间范围
- 监控数据聚合任务的执行状态
