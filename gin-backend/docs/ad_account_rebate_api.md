# 账号返利比例管理API文档

## 功能概述

账号返利比例管理功能提供了完整的账号返利比例设置、变更和历史记录管理能力。当修改账号返利比例时，系统会自动记录变更历史，包括变更前后的比例、变更原因、操作人等信息。

## 核心特性

1. **返利比例管理**：支持新增、修改、删除账号返利比例
2. **变更记录追踪**：自动记录所有变更操作的详细历史
3. **多维度查询**：支持按账号、时间、操作人等维度查询
4. **统计分析**：提供返利比例的统计分析功能

## API接口列表

### 1. 变更账号返利比例

**接口地址**：`POST /api/v1/account-rebates/change`

**功能描述**：变更账号返利比例，自动记录变更历史

**请求参数**：
```json
{
  "account_id": 1,
  "new_rate": 15.5,
  "effective_date": "2024-06-20",
  "change_reason": "根据业务调整提升返利比例",
  "operator_id": 100,
  "operator_name": "张三"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int64 | 是 | 账号ID |
| new_rate | float64 | 是 | 新返利比例（0-100） |
| effective_date | string | 是 | 生效日期，格式：YYYY-MM-DD |
| change_reason | string | 否 | 变更原因（最多500字符） |
| operator_id | int64 | 否 | 操作人ID |
| operator_name | string | 否 | 操作人姓名（最多100字符） |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "账号返利比例变更成功"
  }
}
```

### 2. 获取账号返利比例列表

**接口地址**：`GET /api/v1/account-rebates`

**功能描述**：获取账号返利比例列表，支持账号筛选

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称（模糊查询） |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_id": 1,
        "account_name": "营销树长大",
        "rate": 15.5,
        "effective_date": "2024-06-20",
        "created_at": "2024-06-16 14:30:00",
        "updated_at": "2024-06-16 14:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 3. 获取变更记录

**接口地址**：`GET /api/v1/account-rebates/history`

**功能描述**：获取账号返利比例变更记录，支持多维度筛选

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称（模糊查询） |
| change_type | string | 否 | 变更类型：create/update/delete |
| operator_name | string | 否 | 操作人姓名（模糊查询） |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_id": 1,
        "account_name": "营销树长大",
        "old_rate": 12.0,
        "new_rate": 15.5,
        "rate_change": "12.00% → 15.50% (+3.50%)",
        "effective_date": "2024-06-20",
        "change_type": "update",
        "change_type_desc": "修改返利比例",
        "change_reason": "根据业务调整提升返利比例",
        "operator_id": 100,
        "operator_name": "张三",
        "created_at": "2024-06-16 14:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 4. 获取统计信息

**接口地址**：`GET /api/v1/account-rebates/stats`

**功能描述**：获取账号返利比例统计信息

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_accounts": 156,
    "active_accounts": 142,
    "average_rate": 13.25,
    "max_rate": 20.0,
    "min_rate": 8.0,
    "recent_changes": 23
  }
}
```

**字段说明**：

| 字段名 | 说明 |
|--------|------|
| total_accounts | 总账号数 |
| active_accounts | 有效账号数（生效日期<=今天） |
| average_rate | 平均返利比例 |
| max_rate | 最高返利比例 |
| min_rate | 最低返利比例 |
| recent_changes | 最近30天变更次数 |

### 5. 获取账号当前返利比例

**接口地址**：`GET /api/v1/account-rebates/{account_id}/current`

**功能描述**：获取指定账号的当前返利比例

**路径参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 是 | 账号ID |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "account_id": 1,
    "rate": 15.5,
    "effective_date": "2024-06-20T00:00:00Z",
    "created_at": "2024-06-16T14:30:00Z",
    "updated_at": "2024-06-16T14:30:00Z"
  }
}
```

### 6. 删除账号返利比例

**接口地址**：`DELETE /api/v1/account-rebates/{account_id}`

**功能描述**：删除指定账号的返利比例，自动记录删除历史

**路径参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 是 | 账号ID |

**请求参数**：
```json
{
  "operator_id": 100,
  "operator_name": "张三",
  "reason": "账号已停用，删除返利比例"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "账号返利比例删除成功"
  }
}
```

### 7. 获取变更类型列表

**接口地址**：`GET /api/v1/account-rebates/change-types`

**功能描述**：获取支持的变更类型列表

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {"value": "create", "label": "新增返利比例"},
    {"value": "update", "label": "修改返利比例"},
    {"value": "delete", "label": "删除返利比例"}
  ]
}
```

## 数据模型

### 账号返利比例表（ad_account_rebate）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| account_id | bigint | 账号ID |
| rate | decimal(5,2) | 返利比例 |
| effective_date | date | 生效日期 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 账号返利比例变更记录表（ad_account_rebate_history）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| account_id | bigint | 账号ID |
| old_rate | decimal(5,2) | 原返利比例 |
| new_rate | decimal(5,2) | 新返利比例 |
| effective_date | date | 生效日期 |
| change_type | varchar(20) | 变更类型 |
| change_reason | varchar(500) | 变更原因 |
| operator_id | bigint | 操作人ID |
| operator_name | varchar(100) | 操作人姓名 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

## 业务规则

### 1. 返利比例规则
- 返利比例范围：0-100%
- 精度：支持小数点后2位
- 生效日期：不能早于当前日期

### 2. 变更记录规则
- 所有变更操作都会自动记录历史
- 变更类型：create（新增）、update（修改）、delete（删除）
- 删除操作会将新比例设为0

### 3. 权限控制
- 建议添加操作权限验证
- 记录操作人信息用于审计

## 使用示例

### 1. 新增账号返利比例

```bash
curl -X POST "http://localhost:8080/api/v1/account-rebates/change" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "account_id": 1,
    "new_rate": 15.5,
    "effective_date": "2024-06-20",
    "change_reason": "新账号设置返利比例",
    "operator_name": "张三"
  }'
```

### 2. 修改账号返利比例

```bash
curl -X POST "http://localhost:8080/api/v1/account-rebates/change" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "account_id": 1,
    "new_rate": 18.0,
    "effective_date": "2024-07-01",
    "change_reason": "业务调整，提升返利比例",
    "operator_name": "李四"
  }'
```

### 3. 查询变更记录

```bash
curl -X GET "http://localhost:8080/api/v1/account-rebates/history?account_id=1&page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

### 4. 查询账号列表

```bash
curl -X GET "http://localhost:8080/api/v1/account-rebates?account_name=营销&page=1&page_size=20" \
  -H "Authorization: Bearer your_token"
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. **数据一致性**：变更操作使用事务确保数据一致性
2. **历史记录**：所有变更都会记录历史，不可删除
3. **生效日期**：建议设置合理的生效日期，避免频繁变更
4. **权限控制**：建议在实际使用中添加操作权限验证
5. **审计追踪**：变更记录可用于业务审计和问题排查
