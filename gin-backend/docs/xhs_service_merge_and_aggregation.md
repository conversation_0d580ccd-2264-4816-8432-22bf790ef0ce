# 小红书服务合并与数据聚合功能

## 功能概述

成功将 `XHSRealtimeReportService` 合并到 `XHSCreativeReportService` 中，并在拉取创意报表数据时自动将计划和口令数据按天维度聚合到相应的统计表中。

## 架构变更

### 1. 服务合并
- **原架构**：`XHSCreativeReportService` + `XHSRealtimeReportService`（两个独立服务）
- **新架构**：`XHSCreativeReportService`（统一服务，包含实时数据同步功能）

### 2. 功能整合
- 离线报表数据同步
- 实时报表数据同步
- 计划维度数据聚合
- 口令维度数据聚合

## 核心功能

### 1. 实时数据同步（已合并）
```go
// 同步当前小时的实时报表数据
func (s *XHSCreativeReportService) SyncCurrentHourReports(ctx context.Context) error

// 同步单个账号的实时报表数据
func (s *XHSCreativeReportService) syncAccountRealtimeReports(ctx context.Context, account model.AdAccounts) error
```

### 2. 数据聚合功能（新增）
```go
// 批量保存报表数据并聚合统计数据
func (s *XHSCreativeReportService) batchSaveReportsWithAggregation(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time) error

// 聚合计划统计数据到ad_plan_stats表
func (s *XHSCreativeReportService) aggregatePlanStats(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time, tx *gorm.DB) error

// 聚合口令统计数据到password_stats表
func (s *XHSCreativeReportService) aggregatePasswordStats(ctx context.Context, reports []model.XHSCreativeReports, account model.AdAccounts, syncTime time.Time, tx *gorm.DB) error
```

## 数据聚合策略

### 1. 计划维度聚合（ad_plan_stats表）

#### 聚合维度
- **主键**：`plan_id` + `account_id` + `stat_date`（按天）
- **数据来源**：创意报表中的 `campaign_id`、`campaign_name`

#### 聚合字段
| 字段 | 聚合方式 | 说明 |
|------|----------|------|
| cost | SUM | 计划总消费 |
| actual_cost | SUM | 实际消费（等于cost） |
| total_cost | SUM | 累计总消费 |
| impressions | SUM | 总展现量 |
| clicks | SUM | 总点击量 |
| click_through_rate | 计算 | 点击率 = clicks / impressions * 100 |

#### 更新策略
- **新记录**：直接插入
- **已存在**：累加数值字段，重新计算比率字段

### 2. 口令维度聚合（password_stats表）

#### 聚合维度
- **主键**：`password_name` + `account_id` + `stat_date`（按天）
- **数据来源**：创意报表中的 `pwd` 字段

#### 聚合字段
| 字段 | 聚合方式 | 说明 |
|------|----------|------|
| consumption | SUM | 口令总消费 |
| actual_consumption | SUM | 实际消费（等于consumption） |
| impressions | SUM | 总展现量 |
| click_count | SUM | 总点击量 |
| click_rate | 计算 | 点击率 = click_count / impressions * 100 |
| new_orders_today | SUM | 今日新订单数（来自goods_order） |
| total_orders | SUM | 累计订单数（来自success_goods_order） |
| total_income | SUM | 累计收入（来自rgmv） |
| total_recovery_rate | 计算 | 回收率 = total_income / consumption * 100 |
| order_cost_today | 计算 | 订单成本 = consumption / new_orders_today |

#### 更新策略
- **新记录**：直接插入
- **已存在**：累加数值字段，重新计算比率字段

## 数据流程

### 1. 实时数据同步流程
```
1. 获取有效小红书账号
2. 逐个账号调用实时API
3. 转换API数据为创意报表模型
4. 批量保存并聚合统计数据
   ├── 保存创意报表数据
   ├── 聚合计划统计数据
   └── 聚合口令统计数据
```

### 2. 事务保证
- 使用数据库事务确保数据一致性
- 创意报表保存、计划聚合、口令聚合在同一事务中
- 任一步骤失败则全部回滚

### 3. 错误处理
- 账号级别的错误隔离
- 详细的错误日志记录
- 部分失败不影响其他账号

## 性能优化

### 1. 批量操作
- 创意报表数据：每批1000条
- 统计数据：按维度分组后批量处理

### 2. 查询优化
- 使用复合索引：`plan_id` + `account_id` + `stat_date`
- 使用复合索引：`password_name` + `account_id` + `stat_date`

### 3. 内存优化
- 使用map进行内存中聚合
- 避免重复数据库查询

## 监控和日志

### 1. 关键日志
```
开始同步当前小时的小红书实时报表数据
找到小红书账号 count=3
开始同步账号实时报表数据 account_id=1 account_name=测试账号
获取实时报表数据 account_id=1 page=1 count=50
计划统计数据聚合完成 account_id=1 plan_count=5 stat_date=2024-01-23
口令统计数据聚合完成 account_id=1 password_count=8 stat_date=2024-01-23
账号实时报表数据同步完成 account_id=1 total_count=150
小红书实时报表数据同步完成 success_count=3 error_count=0
```

### 2. 错误监控
- 账号级别的同步失败
- API调用失败
- 数据库操作失败
- 数据聚合失败

## 使用方式

### 1. 定时任务
- **频率**：每小时执行一次
- **时间**：整点执行（如：10:00, 11:00, 12:00）
- **数据范围**：当日实时数据

### 2. 手动触发
```bash
# 通过API手动触发实时数据同步
curl -X POST http://localhost:8080/api/v1/xhs-reports/sync/realtime
```

### 3. 数据查询
```sql
-- 查询计划统计数据
SELECT * FROM ad_plan_stats 
WHERE account_id = 1 AND stat_date = '2024-01-23'
ORDER BY cost DESC;

-- 查询口令统计数据
SELECT * FROM password_stats 
WHERE account_id = 1 AND stat_date = '2024-01-23'
ORDER BY consumption DESC;
```

## 数据一致性

### 1. 幂等性
- 同一时间段的数据可重复同步
- 统计数据支持累加更新

### 2. 数据校验
- 聚合前验证必要字段
- 计算比率时检查分母不为零

### 3. 异常恢复
- 支持部分失败后的重新同步
- 事务回滚保证数据完整性

## 扩展功能

### 1. 更多维度聚合
- 单元维度（unit_id）
- 创意维度（creativity_id）
- 时间维度（小时级别）

### 2. 实时监控
- 聚合数据的实时更新
- 异常数据的自动告警

### 3. 数据分析
- 计划效果对比
- 口令效果排行
- 趋势分析报表
