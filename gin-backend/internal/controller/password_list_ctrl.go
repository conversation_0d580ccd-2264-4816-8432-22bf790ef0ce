package controller

import (
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PasswordListController 口令列表控制器
type PasswordListController struct {
	passwordListService *service.PasswordListService
}

// NewPasswordListController 创建口令列表控制器
func NewPasswordListController() *PasswordListController {
	return &PasswordListController{
		passwordListService: service.NewPasswordListService(),
	}
}

// GetPasswordListRequest 获取口令列表请求
type GetPasswordListRequest struct {
	AccountID    *int64 `form:"account_id"`    // 账号ID
	AccountName  string `form:"account_name"`  // 账号名称
	PasswordName string `form:"password_name"` // 口令名称
	StartDate    string `form:"start_date"`    // 开始日期 YYYY-MM-DD
	EndDate      string `form:"end_date"`      // 结束日期 YYYY-MM-DD
	Page         int    `form:"page"`          // 页码
	PageSize     int    `form:"page_size"`     // 每页数量
}

// GetPasswordList 获取口令列表
// @Summary 获取口令列表
// @Description 获取口令列表，支持账号查询、时间筛选，根据时间维度聚合数据
// @Tags 口令列表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param password_name query string false "口令名称"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} response.Response{data=domain.PasswordListResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/passwords [get]
func (ctrl *PasswordListController) GetPasswordList(c *gin.Context) {
	var req GetPasswordListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 转换参数
	param := domain.PasswordListParam{
		AccountID:    req.AccountID,
		AccountName:  req.AccountName,
		PasswordName: req.PasswordName,
		Page:         req.Page,
		PageSize:     req.PageSize,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 调用服务
	result, err := ctrl.passwordListService.GetPasswordList(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取口令列表失败", zap.Error(err))
		response.Error(c, 500, "获取口令列表失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// GetPasswordStatsRequest 获取口令统计请求
type GetPasswordStatsRequest struct {
	AccountID   *int64 `form:"account_id"`   // 账号ID
	AccountName string `form:"account_name"` // 账号名称
	StartDate   string `form:"start_date"`   // 开始日期 YYYY-MM-DD
	EndDate     string `form:"end_date"`     // 结束日期 YYYY-MM-DD
}

// GetPasswordStats 获取口令统计信息
// @Summary 获取口令统计信息
// @Description 获取口令统计信息，包括总数量、总消费、总订单等
// @Tags 口令列表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Success 200 {object} response.Response{data=domain.PasswordStatsResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/passwords/stats [get]
func (ctrl *PasswordListController) GetPasswordStats(c *gin.Context) {
	var req GetPasswordStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 转换参数
	param := domain.PasswordStatsParam{
		AccountID:   req.AccountID,
		AccountName: req.AccountName,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 调用服务
	result, err := ctrl.passwordListService.GetPasswordStats(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取口令统计失败", zap.Error(err))
		response.Error(c, 500, "获取口令统计失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// ExportPasswordListRequest 导出口令列表请求
type ExportPasswordListRequest struct {
	AccountID    *int64 `json:"account_id"`    // 账号ID
	AccountName  string `json:"account_name"`  // 账号名称
	PasswordName string `json:"password_name"` // 口令名称
	StartDate    string `json:"start_date"`    // 开始日期 YYYY-MM-DD
	EndDate      string `json:"end_date"`      // 结束日期 YYYY-MM-DD
	Format       string `json:"format"`        // 导出格式：xlsx 或 csv
}

// ExportPasswordList 导出口令列表
// @Summary 导出口令列表
// @Description 导出口令列表数据，支持Excel和CSV格式
// @Tags 口令列表
// @Accept json
// @Produce json
// @Param request body ExportPasswordListRequest true "导出参数"
// @Success 200 {object} response.Response{data=domain.PasswordExportResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/passwords/export [post]
func (ctrl *PasswordListController) ExportPasswordList(c *gin.Context) {
	var req ExportPasswordListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认格式
	if req.Format == "" {
		req.Format = "xlsx"
	}

	// 转换参数
	param := domain.PasswordExportParam{
		AccountID:    req.AccountID,
		AccountName:  req.AccountName,
		PasswordName: req.PasswordName,
		Format:       req.Format,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 异步执行导出任务
	go func() {
		result, err := ctrl.passwordListService.ExportPasswordList(c.Request.Context(), param)
		if err != nil {
			zap.L().Error("导出口令列表失败", zap.Error(err))
		} else {
			zap.L().Info("口令列表导出完成",
				zap.String("file_name", result.FileName),
				zap.String("file_url", result.FileURL))
		}
	}()

	response.Success(c, map[string]string{
		"message": "导出任务已启动，请稍后查看导出结果",
	})
}

// GetPasswordDetail 获取口令详情
// @Summary 获取口令详情
// @Description 根据口令名称获取口令详细信息
// @Tags 口令列表
// @Accept json
// @Produce json
// @Param password_name path string true "口令名称"
// @Success 200 {object} response.Response{data=domain.PasswordListItem}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/passwords/{password_name} [get]
func (ctrl *PasswordListController) GetPasswordDetail(c *gin.Context) {
	passwordName := c.Param("password_name")
	if passwordName == "" {
		response.Error(c, 400, "口令名称不能为空")
		return
	}

	// 这里可以实现获取单个口令详情的逻辑
	// 暂时返回一个示例响应
	detail := domain.PasswordListItem{
		PasswordName:        passwordName,
		AccountName:         "示例账号",
		Consumption:         1000.0,
		ActualConsumption:   1000.0,
		Impressions:         10000,
		ClickCount:          100,
		ClickRate:           1.0,
		SearchCount:         1233,
		NewOrdersToday:      4431,
		TodayOrderCost:      19.78,
		CumulativeOrders:    23231,
		CumulativeIncome:    124141,
		CumulativeRecovery:  124.11,
		LastUpdate:          time.Now().Format("2006-01-02 15:04:05"),
	}

	response.Success(c, detail)
}

// GetAccountList 获取账号列表（用于下拉选择）
// @Summary 获取账号列表
// @Description 获取可用的账号列表，用于筛选条件
// @Tags 口令列表
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/passwords/accounts [get]
func (ctrl *PasswordListController) GetAccountList(c *gin.Context) {
	// 这里可以实现获取账号列表的逻辑
	// 暂时返回一个示例响应
	accounts := []map[string]interface{}{
		{"id": "", "name": "全部", "value": ""},
		{"id": 1, "name": "账号1", "value": "1"},
		{"id": 2, "name": "账号2", "value": "2"},
	}

	response.Success(c, accounts)
}
