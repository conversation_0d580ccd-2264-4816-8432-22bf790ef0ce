package vo

import "gin-backend/internal/service/domain"

// DashboardMediaItem 仪表盘媒体项
type DashboardMediaItem struct {
	ID   string `json:"id"`   // 媒体ID
	Name string `json:"name"` // 媒体名称
}

// DashboardMetricsReq 仪表盘指标查询请求
type DashboardMetricsReq struct {
	StartDate string `form:"start_date" json:"start_date" label:"开始日期"` // YYYY-MM-DD格式，可选
	EndDate   string `form:"end_date" json:"end_date" label:"结束日期"`     // YYYY-MM-DD格式，可选
	UserID    string `form:"user_id" json:"user_id" label:"用户ID"`       // 可选
	MediaID   string `form:"media_id" json:"media_id" label:"媒体ID"`     // 可选
	PlanID    string `form:"plan_id" json:"plan_id" label:"计划ID"`       // 可选
	ProductID string `form:"product_id" json:"product_id" label:"产品ID"` // 可选
	Type      string `form:"type" json:"type" label:"合作类型"`             // 1:普通投放 2:CPS合作 3:灯火，可选
	Category  string `form:"category" json:"category" label:"渠道分类"`     // alipay:支付宝 wechat:微信 other:其他，可选
}

// ToDomain 转换为Domain对象
func (r DashboardMetricsReq) ToDomain() domain.DashboardMetricsParam {
	return domain.DashboardMetricsParam{
		StartDate: r.StartDate,
		EndDate:   r.EndDate,
		UserID:    r.UserID,
		MediaID:   r.MediaID,
		PlanID:    r.PlanID,
		ProductID: r.ProductID,
		Type:      r.Type,
		Category:  r.Category,
	}
}

// DashboardMetricsResp 仪表盘指标响应
type DashboardMetricsResp struct {
	Metrics map[string]MetricItem `json:"metrics"`
}

// FromDomain 从Domain对象转换
func FromDomainDashboardMetrics(entity domain.DashboardMetricsEntity) DashboardMetricsResp {
	result := DashboardMetricsResp{
		Metrics: make(map[string]MetricItem),
	}
	for k, v := range entity.Metrics {
		result.Metrics[k] = MetricItem{
			Label:      v.Label,
			Value:      v.Value,
			Change:     v.Change,
			WeekChange: v.WeekChange,
			HasTrend:   v.HasTrend,
		}
	}
	return result
}

// MetricItem 指标项
type MetricItem struct {
	Label      string  `json:"label"`      // 指标名称
	Value      float64 `json:"value"`      // 指标值
	Change     float64 `json:"change"`     // 环比变化（小数形式，如0.1表示10%）
	WeekChange float64 `json:"weekChange"` // 周同比变化（小数形式）
	HasTrend   bool    `json:"hasTrend"`   // 是否支持趋势分析
}

// DashboardFilterOptionsResp 筛选选项响应
type DashboardFilterOptionsResp struct {
	ShowMediaList bool                 `json:"showMediaList"` // 是否显示媒介列表
	MediaList     []DashboardMediaItem `json:"mediaList"`     // 媒介列表
	ProductList   []ProductItem        `json:"productList"`   // 产品列表
}

// FromDomain 从Domain对象转换
func FromDomainFilterOptions(entity domain.FilterOptionsEntity) DashboardFilterOptionsResp {
	result := DashboardFilterOptionsResp{
		ShowMediaList: entity.ShowMediaList,
		MediaList:     make([]DashboardMediaItem, len(entity.MediaList)),
		ProductList:   make([]ProductItem, len(entity.ProductList)),
	}
	for i, v := range entity.MediaList {
		result.MediaList[i] = DashboardMediaItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	for i, v := range entity.ProductList {
		result.ProductList[i] = ProductItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

// ProductItem 产品项
type ProductItem struct {
	ID   string `json:"id"`   // 产品ID
	Name string `json:"name"` // 产品名称
}

// DashboardProductsResp 产品列表响应
type DashboardProductsResp struct {
	List []ProductItem `json:"list"`
}

// FromDomain 从Domain对象转换
func FromDomainProducts(entity domain.ProductsEntity) DashboardProductsResp {
	result := DashboardProductsResp{
		List: make([]ProductItem, len(entity.List)),
	}
	for i, v := range entity.List {
		result.List[i] = ProductItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

// DashboardTrendReq 趋势数据请求
type DashboardTrendReq struct {
	UserID    string `form:"user_id" json:"user_id" label:"用户ID"`                  // 可选
	MediaID   string `form:"media_id" json:"media_id" label:"媒体ID"`                // 可选
	PlanID    string `form:"plan_id" json:"plan_id" label:"计划ID"`                  // 可选
	ProductID string `form:"product_id" json:"product_id" label:"产品ID"`            // 可选
	Metric    string `form:"metric" json:"metric" binding:"required" label:"指标名称"` // 必填：click_pv, click_uv, orders, estimated_commission等
	StartDate string `form:"start_date" json:"start_date" label:"开始日期"`            // 可选，YYYY-MM-DD
	EndDate   string `form:"end_date" json:"end_date" label:"结束日期"`                // 可选，YYYY-MM-DD
	Type      string `form:"type" json:"type" label:"合作类型"`                        // 可选
	Category  string `form:"category" json:"category" label:"渠道分类"`                // 可选
}

// ToDomain 转换为Domain对象
func (r DashboardTrendReq) ToDomain() domain.DashboardTrendParam {
	return domain.DashboardTrendParam{
		UserID:    r.UserID,
		MediaID:   r.MediaID,
		PlanID:    r.PlanID,
		ProductID: r.ProductID,
		Metric:    r.Metric,
		StartDate: r.StartDate,
		EndDate:   r.EndDate,
		Type:      r.Type,
		Category:  r.Category,
	}
}

// DashboardTrendResp 趋势数据响应
type DashboardTrendResp struct {
	TimePoints []string  `json:"time_points"` // 时间点数组
	Current    []float64 `json:"current"`     // 当前数据
	Previous   []float64 `json:"previous"`    // 前期数据
	SamePeriod []float64 `json:"same_period"` // 同期数据
	Labels     struct {
		Current    string `json:"current"`     // 当前数据标签
		Previous   string `json:"previous"`    // 前期数据标签
		SamePeriod string `json:"same_period"` // 同期数据标签
	} `json:"labels"`
}

// FromDomain 从Domain对象转换
func FromDomainTrend(entity domain.TrendEntity) DashboardTrendResp {
	result := DashboardTrendResp{
		TimePoints: entity.TimePoints,
		Current:    entity.Current,
		Previous:   entity.Previous,
		SamePeriod: entity.SamePeriod,
	}
	result.Labels.Current = entity.Labels.Current
	result.Labels.Previous = entity.Labels.Previous
	result.Labels.SamePeriod = entity.Labels.SamePeriod
	return result
}

// DashboardPlanStatsReq 分计划统计请求
type DashboardPlanStatsReq struct {
	StartDate string `form:"start_date" json:"start_date" binding:"required" label:"开始日期"` // 必填，YYYY-MM-DD
	EndDate   string `form:"end_date" json:"end_date" binding:"required" label:"结束日期"`     // 必填，YYYY-MM-DD
	UserID    string `form:"user_id" json:"user_id" label:"用户ID"`                          // 可选
	MediaID   string `form:"media_id" json:"media_id" label:"媒体ID"`                        // 可选
	PlanID    string `form:"plan_id" json:"plan_id" label:"计划ID"`                          // 可选
	ProductID string `form:"product_id" json:"product_id" label:"产品ID"`                    // 可选
	Type      string `form:"type" json:"type" label:"合作类型"`                                // 可选
	Category  string `form:"category" json:"category" label:"渠道分类"`                        // 可选
}

// ToDomain 转换为Domain对象
func (r DashboardPlanStatsReq) ToDomain() domain.DashboardPlanStatsParam {
	return domain.DashboardPlanStatsParam{
		StartDate: r.StartDate,
		EndDate:   r.EndDate,
		UserID:    r.UserID,
		MediaID:   r.MediaID,
		PlanID:    r.PlanID,
		ProductID: r.ProductID,
		Type:      r.Type,
		Category:  r.Category,
	}
}

// DashboardPlanStatsResp 分计划统计响应
type DashboardPlanStatsResp struct {
	List []PlanStatsItem `json:"list"`
}

// FromDomain 从Domain对象转换
func FromDomainPlanStats(entity domain.PlanStatsEntity) DashboardPlanStatsResp {
	result := DashboardPlanStatsResp{
		List: make([]PlanStatsItem, len(entity.List)),
	}
	for i, v := range entity.List {
		result.List[i] = PlanStatsItem{
			PlanID:        v.PlanID,
			PlanCode:      v.PlanCode,
			MediaName:     v.MediaName,
			ProductID:     v.ProductID,
			Clicks:        v.Clicks,
			Cost:          v.Cost,
			Orders:        v.Orders,
			Revenue:       v.Revenue,
			Profit:        v.Profit,
			ROI:           v.ROI,
			SettledProfit: v.SettledProfit,
			PV:            v.PV,
			UV:            v.UV,
		}
	}
	return result
}

// PlanStatsItem 分计划统计项
type PlanStatsItem struct {
	PlanID        string  `json:"plan_id"`        // 计划ID
	PlanCode      string  `json:"plan_code"`      // 计划编码
	MediaName     string  `json:"media_name"`     // 媒体名称
	ProductID     string  `json:"product_id"`     // 产品ID
	Clicks        int     `json:"clicks"`         // 点击量
	Cost          float64 `json:"cost"`           // 成本
	Orders        int     `json:"orders"`         // 订单量
	Revenue       float64 `json:"revenue"`        // 收入
	Profit        float64 `json:"profit"`         // 利润
	ROI           float64 `json:"roi"`            // ROI
	SettledProfit float64 `json:"settled_profit"` // 结算利润
	PV            int     `json:"pv"`             // PV
	UV            int     `json:"uv"`             // UV
}

// DashboardMediaListReq 仪表盘媒体列表请求
type DashboardMediaListReq struct {
	UserID   string `form:"user_id" json:"user_id" label:"用户ID"`   // 可选，按用户ID筛选
	Type     string `form:"type" json:"type" label:"投放类型"`         // 可选，1:普通投放 2:CPS合作 3:灯火 0:全部
	Category string `form:"category" json:"category" label:"渠道分类"` // 可选，alipay:支付宝 wechat:微信 other:其他
}

// ToDomain 转换为Domain对象
func (r DashboardMediaListReq) ToDomain() domain.DashboardMediaListParam {
	return domain.DashboardMediaListParam{
		UserID:   r.UserID,
		Type:     r.Type,
		Category: r.Category,
	}
}

// DashboardMediaListResp 仪表盘媒体列表响应
type DashboardMediaListResp struct {
	List []DashboardMediaItem `json:"list"`
}

// FromDomain 从Domain对象转换
func FromDomainMediaList(entity domain.MediaListEntity) DashboardMediaListResp {
	result := DashboardMediaListResp{
		List: make([]DashboardMediaItem, len(entity.List)),
	}
	for i, v := range entity.List {
		result.List[i] = DashboardMediaItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

// DashboardPlansReq 获取投放计划列表请求
type DashboardPlansReq struct {
	MediaAccountID string `json:"media_account_id" form:"media_account_id" binding:"required"` // 媒体账号ID
}

// ToDomain 转换为Domain对象
func (r DashboardPlansReq) ToDomain() domain.DashboardPlansParam {
	return domain.DashboardPlansParam{
		MediaAccountID: r.MediaAccountID,
	}
}

// DashboardPlansResp 获取投放计划列表响应
type DashboardPlansResp struct {
	List []DashboardPlanItem `json:"list"`
}

// FromDomain 从Domain对象转换
func FromDomainPlans(entity domain.PlansEntity) DashboardPlansResp {
	result := DashboardPlansResp{
		List: make([]DashboardPlanItem, len(entity.List)),
	}
	for i, v := range entity.List {
		result.List[i] = DashboardPlanItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

// DashboardPlanItem 仪表盘计划项
type DashboardPlanItem struct {
	ID   string `json:"id"`   // 计划ID
	Name string `json:"name"` // 计划名称
}

// DashboardDynamicFilterReq 动态筛选选项请求
type DashboardDynamicFilterReq struct {
	Type            string `form:"type" json:"type" binding:"required"`      // 筛选类型：users, media_accounts, plans
	UserID          string `form:"user_id" json:"user_id"`                   // 媒介ID（获取媒体账号时使用）
	MediaAccountID  string `form:"media_account_id" json:"media_account_id"` // 媒体账号ID（获取计划时使用）
	CooperationType string `form:"cooperation_type" json:"cooperation_type"` // 投放类型（获取媒体账号时使用）
	Category        string `form:"category" json:"category"`                 // 渠道分类（获取媒体账号和计划时使用）
}

// ToDomain 转换为Domain对象
func (r DashboardDynamicFilterReq) ToDomain() domain.DynamicFilterParam {
	return domain.DynamicFilterParam{
		Type:            r.Type,
		UserID:          r.UserID,
		MediaAccountID:  r.MediaAccountID,
		CooperationType: r.CooperationType,
		Category:        r.Category,
	}
}

// DashboardDynamicFilterResp 动态筛选选项响应
type DashboardDynamicFilterResp struct {
	List []DashboardFilterItem `json:"list"`
}

// FromDomain 从Domain对象转换
func FromDomainDynamicFilter(entity domain.DynamicFilterEntity) DashboardDynamicFilterResp {
	result := DashboardDynamicFilterResp{
		List: make([]DashboardFilterItem, len(entity.List)),
	}
	for i, v := range entity.List {
		result.List[i] = DashboardFilterItem{
			ID:   v.ID,
			Name: v.Name,
		}
	}
	return result
}

// DashboardFilterItem 筛选项
type DashboardFilterItem struct {
	ID   string `json:"id"`   // 项目ID
	Name string `json:"name"` // 项目名称
}

// DashboardRegionReq 地域分析请求
type DashboardRegionReq struct {
	StartDate string `form:"start_date" json:"start_date" label:"开始日期"`            // YYYY-MM-DD格式，可选
	EndDate   string `form:"end_date" json:"end_date" label:"结束日期"`                // YYYY-MM-DD格式，可选
	UserID    string `form:"user_id" json:"user_id" label:"用户ID"`                  // 可选
	MediaID   string `form:"media_id" json:"media_id" label:"媒体ID"`                // 可选
	PlanID    string `form:"plan_id" json:"plan_id" label:"计划ID"`                  // 可选
	ProductID string `form:"product_id" json:"product_id" label:"产品ID"`            // 可选
	Type      string `form:"type" json:"type" label:"合作类型"`                        // 1:普通投放 2:CPS合作 3:灯火，可选
	Category  string `form:"category" json:"category" label:"渠道分类"`                // alipay:支付宝 wechat:微信 other:其他，可选
	Metric    string `form:"metric" json:"metric" binding:"required" label:"指标类型"` // orders:订单量 estimated_commission:预估佣金 settled_commission:结算佣金 order_amount:订单金额
}

// ToDomain 转换为Domain对象
func (r DashboardRegionReq) ToDomain() domain.RegionParam {
	return domain.RegionParam{
		StartDate: r.StartDate,
		EndDate:   r.EndDate,
		UserID:    r.UserID,
		MediaID:   r.MediaID,
		PlanID:    r.PlanID,
		ProductID: r.ProductID,
		Type:      r.Type,
		Category:  r.Category,
		Metric:    r.Metric,
	}
}

// DashboardRegionResp 地域分析响应
type DashboardRegionResp struct {
	Regions []RegionDataItem `json:"regions"`
}

// FromDomain 从Domain对象转换
func FromDomainRegion(entity domain.RegionEntity) DashboardRegionResp {
	result := DashboardRegionResp{
		Regions: make([]RegionDataItem, len(entity.Regions)),
	}
	for i, v := range entity.Regions {
		result.Regions[i] = RegionDataItem{
			Name:  v.Name,
			Value: v.Value,
		}
	}
	return result
}

// RegionDataItem 地域数据项
type RegionDataItem struct {
	Name  string  `json:"name"`  // 地区名称
	Value float64 `json:"value"` // 指标值
}

// DashboardOrderTypeReq 订单类型分析请求
type DashboardOrderTypeReq struct {
	StartDate string `form:"start_date" json:"start_date" label:"开始日期"`            // YYYY-MM-DD格式，可选
	EndDate   string `form:"end_date" json:"end_date" label:"结束日期"`                // YYYY-MM-DD格式，可选
	UserID    string `form:"user_id" json:"user_id" label:"用户ID"`                  // 可选
	MediaID   string `form:"media_id" json:"media_id" label:"媒体ID"`                // 可选
	PlanID    string `form:"plan_id" json:"plan_id" label:"计划ID"`                  // 可选
	ProductID string `form:"product_id" json:"product_id" label:"产品ID"`            // 可选
	Type      string `form:"type" json:"type" label:"合作类型"`                        // 1:普通投放 2:CPS合作 3:灯火，可选
	Category  string `form:"category" json:"category" label:"渠道分类"`                // alipay:支付宝 wechat:微信 other:其他，可选
	Metric    string `form:"metric" json:"metric" binding:"required" label:"指标类型"` // orders:订单量 estimated_commission:预估佣金 settled_commission:结算佣金 order_amount:订单金额
}

// ToDomain 转换为Domain对象
func (r DashboardOrderTypeReq) ToDomain() domain.OrderTypeParam {
	return domain.OrderTypeParam{
		StartDate: r.StartDate,
		EndDate:   r.EndDate,
		UserID:    r.UserID,
		MediaID:   r.MediaID,
		PlanID:    r.PlanID,
		ProductID: r.ProductID,
		Type:      r.Type,
		Category:  r.Category,
		Metric:    r.Metric,
	}
}

// DashboardOrderTypeResp 订单类型分析响应
type DashboardOrderTypeResp struct {
	OrderTypes []OrderTypeDataItem `json:"order_types"`
}

// FromDomain 从Domain对象转换
func FromDomainOrderType(entity domain.OrderTypeEntity) DashboardOrderTypeResp {
	result := DashboardOrderTypeResp{
		OrderTypes: make([]OrderTypeDataItem, len(entity.OrderTypes)),
	}
	for i, v := range entity.OrderTypes {
		result.OrderTypes[i] = OrderTypeDataItem{
			Name:  v.Name,
			Value: v.Value,
		}
	}
	return result
}

// OrderTypeDataItem 订单类型数据项
type OrderTypeDataItem struct {
	Name  string  `json:"name"`  // 订单类型名称
	Value float64 `json:"value"` // 指标值
}
