package model

import (
	"time"
)

// PlatformCreative 广告创意数据库模型
type PlatformCreative struct {
	ID           int64     `gorm:"primaryKey;autoIncrement"`                          // 主键ID
	PlatformType string    `gorm:"column:platform_type;type:varchar(32);not null"`    // 平台类型
	AgentID      int64     `gorm:"column:agent_id;not null;default:0"`                // 代理ID
	MediaID      int64     `gorm:"column:media_id;not null;default:0"`                // 媒体ID
	DataID       string    `gorm:"column:data_id;type:varchar(64);not null"`          // 创意外部系统ID
	PlanID       int64     `gorm:"column:plan_id;not null;default:0"`                 // 关联的计划ID
	GroupID      int64     `gorm:"column:group_id;not null;default:0"`                // 关联的单元ID
	Name         string    `gorm:"column:name;type:varchar(128);not null;default:''"` // 创意名称
	Remark       string    `gorm:"column:remark;type:text"`                           // 备注信息
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime"`                  // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime"`                  // 更新时间

	// 关联数据，用于 ORM 关联查询
	Plan  *PlatformPlan  `gorm:"foreignKey:PlanID"`  // 关联的计划
	Group *PlatformGroup `gorm:"foreignKey:GroupID"` // 关联的广告组
	Agent *Agent         `gorm:"foreignKey:AgentID"` // 关联的代理
	Media *Media         `gorm:"foreignKey:MediaID"` // 关联的媒体
}

// TableName 返回表名
func (PlatformCreative) TableName() string {
	return "platform_creatives"
}
