-- 为媒介角色添加模型管理权限
-- 这个脚本用于解决媒介用户无法访问模型管理功能的问题

-- 为媒介角色添加模型管理相关权限
INSERT INTO role_permissions (role_id, permission_id, created_at)
SELECT 
    (SELECT id FROM roles WHERE code = 'media_specialist'),
    p.id,
    NOW()
FROM permissions p
WHERE p.status = 1
AND p.code IN (
    'delivery:view',
    'delivery:model:view',
    'delivery:model:list'
)
AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = (SELECT id FROM roles WHERE code = 'media_specialist')
    AND rp.permission_id = p.id
);

-- 验证权限添加结果
SELECT 
    r.name as '角色名称',
    p.name as '权限名称',
    p.code as '权限代码'
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.code = 'media_specialist'
AND p.code LIKE 'delivery:model:%'
ORDER BY p.code;

SELECT '媒介角色模型管理权限添加完成！' as '完成信息'; 