package bootstrap

import (
	"context"
	"gin-backend/internal/global"
	"gin-backend/internal/service"
	"gin-backend/job"
	"time"

	rlock "github.com/gotomicro/redis-lock"
	"github.com/robfig/cron/v3"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// Job 任务引导程序
type Job struct {
	costService *service.CostService
	taskService *service.TaskService
	cron        *cron.Cron
	logger      *zap.Logger
}

// NewJob 创建任务引导程序
func NewJob(logger *zap.Logger) *Job {
	return &Job{
		costService: service.NewCostService(),
		taskService: service.NewTaskService(),
		logger:      logger,
	}
}

// Setup 设置定时任务
func (j *Job) Setup() error {
	j.cron = cron.New(cron.WithSeconds())

	// 添加处理导入任务的定时任务
	_, err := j.cron.AddFunc("0 */1 * * * *", j.processImportTasks) // 每分钟执行一次
	if err != nil {
		return err
	}

	// 启动定时任务
	j.cron.Start()

	return nil
}

// Shutdown 关闭定时任务
func (j *Job) Shutdown() {
	if j.cron != nil {
		j.cron.Stop()
	}
}

// processImportTasks 处理导入任务
func (j *Job) processImportTasks() {
	j.logger.Info("Start processing import tasks")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 调用服务层处理导入任务
	if err := j.taskService.ProcessTasks(ctx); err != nil {
		j.logger.Error("Process import tasks failed", zap.Error(err))
	}

	j.logger.Info("Finished processing import tasks")
}

func CreateJob() *cron.Cron {
	redisClient := global.Cache
	c := cron.New(cron.WithSeconds())

	// 创建分布式锁构建器
	cronBuilder := job.NewOneRunningCronJobBuilder(rlock.NewClient(redisClient))

	// 获取服务环境配置
	serverEnv := viper.GetString("server.env")

	// 仅在生产环境下注册定时任务
	if serverEnv == "prod" {
		// 注册同步小红书成本定时任务 - 每天11点,14点执行
		_, err := c.AddJob("0 0 11,14 * * *", cronBuilder.Build(job.NewXhsCostSyncJob()))
		if err != nil {
			zap.L().Fatal("注册同步小红书成本定时任务失败", zap.Error(err))
		}

		// 注册计划利润通知定时任务 - 每天9点、12点、13点、14点、16点、18点、20点、22点执行
		_, err = c.AddJob("0 0 9,12,13,14,16,18,20,22 * * *", cronBuilder.Build(job.NewPlanProfitNoticeJob()))
		if err != nil {
			zap.L().Fatal("注册计划利润通知定时任务失败", zap.Error(err))
		}

		// 注册口令日报生成定时任务 - 每10分钟执行一次
		_, err = c.AddJob("0 */10 * * * *", cronBuilder.Build(job.NewPwdDailyReportJob()))
		if err != nil {
			zap.L().Fatal("注册口令日报生成定时任务失败", zap.Error(err))
		}

		// 注册同步平台数据定时任务 - 每天凌晨3点执行
		_, err = c.AddJob("0 0 3 * * *", cronBuilder.Build(job.NewPlatformDataSyncJob()))
		if err != nil {
			zap.L().Fatal("注册同步平台数据定时任务失败", zap.Error(err))
		}

		// 注册小红书创意报表数据同步定时任务 - 每天凌晨2点执行
		_, err = c.AddJob("0 0 2 * * *", cronBuilder.Build(job.NewXHSCreativeReportSyncJob()))
		if err != nil {
			zap.L().Fatal("注册小红书创意报表数据同步定时任务失败", zap.Error(err))
		}

		// 注册小红书实时报表数据同步定时任务 - 每小时执行一次
		_, err = c.AddJob("0 0 * * * *", cronBuilder.Build(job.NewXHSRealtimeReportSyncJob()))
		if err != nil {
			zap.L().Fatal("注册小红书实时报表数据同步定时任务失败", zap.Error(err))
		}

		// 如果需要添加更多任务，可以在此处添加

		zap.L().Info("定时任务已注册，将在启动后执行")
	} else {
		zap.L().Info("非生产环境，定时任务未注册")
	}

	return c
}
