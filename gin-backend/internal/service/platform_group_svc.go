package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strings"
	"time"

	"gorm.io/gorm"
)

// PlatformGroupService 广告组服务层
type PlatformGroupService struct {
	db *gorm.DB
}

// NewPlatformGroupService 创建广告组服务实例
func NewPlatformGroupService() PlatformGroupService {
	return PlatformGroupService{
		db: global.DB,
	}
}

// List 获取广告组列表
func (s PlatformGroupService) List(ctx context.Context, param domain.PlatformGroupQueryParam) (domain.PlatformGroupPageEntity, error) {
	// 参数验证
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}
	if param.PageSize > 100 {
		param.PageSize = 100 // 限制最大每页数量
	}

	var total int64
	var items []model.PlatformGroupItem

	// 使用原生SQL方式查询，避免Preload和JOIN混用的问题
	baseQuery := `
		SELECT 
			g.id,
			g.name,
			g.data_id,
			g.platform_type,
			g.remark,
			g.created_at,
			g.updated_at,
			COALESCE(p.name, '') as plan_name,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name
		FROM platform_groups g
		LEFT JOIN platform_plans p ON p.id = g.plan_id
		LEFT JOIN ad_agents a ON a.id = g.agent_id  
		LEFT JOIN ad_media m ON m.id = g.media_id
		WHERE 1=1`

	countQuery := `
		SELECT COUNT(1) as total
		FROM platform_groups g
		LEFT JOIN platform_plans p ON p.id = g.plan_id
		WHERE 1=1`

	var conditions []string
	var args []interface{}
	var countArgs []interface{}

	// 应用筛选条件
	if param.PlatformType != "" {
		conditions = append(conditions, "g.platform_type = ?")
		args = append(args, param.PlatformType)
		countArgs = append(countArgs, param.PlatformType)
	}
	if param.AgentID > 0 {
		conditions = append(conditions, "g.agent_id = ?")
		args = append(args, param.AgentID)
		countArgs = append(countArgs, param.AgentID)
	}
	if param.MediaID > 0 {
		conditions = append(conditions, "g.media_id = ?")
		args = append(args, param.MediaID)
		countArgs = append(countArgs, param.MediaID)
	}
	if param.MarketTargetName != "" {
		conditions = append(conditions, "p.market_target_name = ?")
		args = append(args, param.MarketTargetName)
		countArgs = append(countArgs, param.MarketTargetName)
	}
	if param.PlanKeyword != "" {
		conditions = append(conditions, "(p.name LIKE ? OR p.data_id = ?)")
		keyword := "%" + param.PlanKeyword + "%"
		args = append(args, keyword, param.PlanKeyword)
		countArgs = append(countArgs, keyword, param.PlanKeyword)
	}
	if param.Keyword != "" {
		conditions = append(conditions, "(g.name LIKE ? OR g.data_id = ?)")
		keyword := "%" + param.Keyword + "%"
		args = append(args, keyword, param.Keyword)
		countArgs = append(countArgs, keyword, param.Keyword)
	}

	// 添加筛选条件到查询
	if len(conditions) > 0 {
		whereClause := " AND " + strings.Join(conditions, " AND ")
		baseQuery += whereClause
		countQuery += whereClause
	}

	// 获取总数
	var countResult struct {
		Total int64 `db:"total"`
	}
	if err := s.db.WithContext(ctx).Raw(countQuery, countArgs...).Scan(&countResult).Error; err != nil {
		return domain.PlatformGroupPageEntity{}, fmt.Errorf("查询广告组总数失败: %v", err)
	}
	total = countResult.Total

	// 添加排序和分页
	baseQuery += " ORDER BY g.id DESC LIMIT ? OFFSET ?"
	args = append(args, param.PageSize, (param.Page-1)*param.PageSize)

	// 查询数据
	if err := s.db.WithContext(ctx).Raw(baseQuery, args...).Scan(&items).Error; err != nil {
		return domain.PlatformGroupPageEntity{}, fmt.Errorf("查询广告组列表失败: %v", err)
	}

	// 转换为domain实体
	domainItems := make([]domain.PlatformGroupItemEntity, 0, len(items))
	for _, item := range items {
		createdAt, _ := time.Parse("2006-01-02 15:04:05", item.CreatedAt)
		updatedAt, _ := time.Parse("2006-01-02 15:04:05", item.UpdatedAt)

		domainItems = append(domainItems, domain.PlatformGroupItemEntity{
			ID:           item.ID,
			Name:         item.Name,
			DataID:       item.DataID,
			PlatformType: item.PlatformType,
			PlanName:     item.PlanName,
			MediaName:    item.MediaName,
			AgentName:    item.AgentName,
			Remark:       item.Remark,
			CreatedAt:    createdAt,
			UpdatedAt:    updatedAt,
		})
	}

	return domain.PlatformGroupPageEntity{
		List:  domainItems,
		Total: total,
	}, nil
}

// GetByID 根据ID获取广告组
func (s PlatformGroupService) GetByID(ctx context.Context, id uint64) (domain.PlatformGroupEntity, error) {
	if id <= 0 {
		return domain.PlatformGroupEntity{}, fmt.Errorf("无效的ID")
	}

	var group model.PlatformGroup
	if err := s.db.WithContext(ctx).
		Preload("Plan").
		Preload("Agent").
		Preload("Media").
		First(&group, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformGroupEntity{}, fmt.Errorf("广告组不存在")
		}
		return domain.PlatformGroupEntity{}, fmt.Errorf("获取广告组失败: %v", err)
	}

	return domain.PlatformGroupEntity{
		ID:           group.ID,
		PlatformType: group.PlatformType,
		AgentID:      group.AgentID,
		MediaID:      group.MediaID,
		DataID:       group.DataID,
		PlanID:       group.PlanID,
		Name:         group.Name,
		Remark:       group.Remark,
		CreatedAt:    group.CreatedAt,
		UpdatedAt:    group.UpdatedAt,
	}, nil
}

// Create 创建广告组
func (s PlatformGroupService) Create(ctx context.Context, entity domain.PlatformGroupCreateEntity) (domain.PlatformGroupIDEntity, error) {
	// 参数验证
	if entity.Name == "" {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("广告组名称不能为空")
	}
	if entity.PlanID <= 0 {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("计划ID不能为空")
	}
	if entity.PlatformType == "" {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("平台类型不能为空")
	}

	// 检查名称是否已存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.PlatformGroup{}).
		Where("name = ?", entity.Name).
		Count(&count).Error; err != nil {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("检查名称是否存在失败: %v", err)
	}
	if count > 0 {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("广告组名称已存在")
	}

	// 创建广告组
	group := model.PlatformGroup{
		Name:         entity.Name,
		PlanID:       entity.PlanID,
		PlatformType: entity.PlatformType,
		AgentID:      entity.AgentID,
		MediaID:      entity.MediaID,
		DataID:       entity.DataID,
		Remark:       entity.Remark,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(&group).Error; err != nil {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("创建广告组失败: %v", err)
	}

	return domain.PlatformGroupIDEntity{
		ID: group.ID,
	}, nil
}

// Update 更新广告组
func (s PlatformGroupService) Update(ctx context.Context, entity domain.PlatformGroupUpdateEntity) (domain.PlatformGroupIDEntity, error) {
	// 参数验证
	if entity.ID <= 0 {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("无效的ID")
	}

	// 检查广告组是否存在
	var group model.PlatformGroup
	if err := s.db.WithContext(ctx).First(&group, entity.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformGroupIDEntity{}, fmt.Errorf("广告组不存在")
		}
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("获取广告组失败: %v", err)
	}

	// 准备更新数据
	updates := make(map[string]interface{})

	if entity.Name != "" {
		// 检查名称是否已被其他记录使用
		var count int64
		if err := s.db.WithContext(ctx).Model(&model.PlatformGroup{}).
			Where("name = ? AND id != ?", entity.Name, entity.ID).
			Count(&count).Error; err != nil {
			return domain.PlatformGroupIDEntity{}, fmt.Errorf("检查名称是否存在失败: %v", err)
		}
		if count > 0 {
			return domain.PlatformGroupIDEntity{}, fmt.Errorf("广告组名称已存在")
		}
		updates["name"] = entity.Name
	}

	if entity.Remark != "" {
		updates["remark"] = entity.Remark
	}

	if entity.PlatformType != "" {
		updates["platform_type"] = entity.PlatformType
	}

	updates["updated_at"] = time.Now()

	// 执行更新
	if len(updates) > 1 { // 除了updated_at之外还有其他字段要更新
		if err := s.db.WithContext(ctx).Model(&group).Updates(updates).Error; err != nil {
			return domain.PlatformGroupIDEntity{}, fmt.Errorf("更新广告组失败: %v", err)
		}
	}

	return domain.PlatformGroupIDEntity{
		ID: group.ID,
	}, nil
}

// Delete 删除广告组
func (s PlatformGroupService) Delete(ctx context.Context, entity domain.PlatformGroupDeleteEntity) (domain.PlatformGroupIDEntity, error) {
	// 参数验证
	if entity.ID <= 0 {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("无效的ID")
	}

	// 检查广告组是否存在
	var group model.PlatformGroup
	if err := s.db.WithContext(ctx).First(&group, entity.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformGroupIDEntity{}, fmt.Errorf("广告组不存在")
		}
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("获取广告组失败: %v", err)
	}

	// 删除广告组
	if err := s.db.WithContext(ctx).Delete(&group).Error; err != nil {
		return domain.PlatformGroupIDEntity{}, fmt.Errorf("删除广告组失败: %v", err)
	}

	return domain.PlatformGroupIDEntity{
		ID: entity.ID,
	}, nil
}

// GetByPlanID 根据计划ID获取广告组
func (s PlatformGroupService) GetByPlanID(ctx context.Context, planID uint64) ([]domain.PlatformGroupEntity, error) {
	if planID <= 0 {
		return nil, fmt.Errorf("无效的计划ID")
	}

	var groups []model.PlatformGroup
	if err := s.db.WithContext(ctx).
		Where("plan_id = ?", planID).
		Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("获取广告组失败: %v", err)
	}

	// 转换为domain实体
	result := make([]domain.PlatformGroupEntity, 0, len(groups))
	for _, g := range groups {
		result = append(result, domain.PlatformGroupEntity{
			ID:           g.ID,
			PlatformType: g.PlatformType,
			AgentID:      g.AgentID,
			MediaID:      g.MediaID,
			DataID:       g.DataID,
			PlanID:       g.PlanID,
			Name:         g.Name,
			Remark:       g.Remark,
			CreatedAt:    g.CreatedAt,
			UpdatedAt:    g.UpdatedAt,
		})
	}

	return result, nil
}

// GetByDataID 根据平台数据ID获取广告组
func (s PlatformGroupService) GetByDataID(ctx context.Context, dataID string, platformType string) (domain.PlatformGroupEntity, error) {
	if dataID == "" || platformType == "" {
		return domain.PlatformGroupEntity{}, fmt.Errorf("无效的参数")
	}

	var group model.PlatformGroup
	if err := s.db.WithContext(ctx).
		Where("data_id = ? AND platform_type = ?", dataID, platformType).
		First(&group).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformGroupEntity{}, fmt.Errorf("广告组不存在")
		}
		return domain.PlatformGroupEntity{}, fmt.Errorf("获取广告组失败: %v", err)
	}

	// 转换为domain实体
	return domain.PlatformGroupEntity{
		ID:           group.ID,
		PlatformType: group.PlatformType,
		AgentID:      group.AgentID,
		MediaID:      group.MediaID,
		DataID:       group.DataID,
		PlanID:       group.PlanID,
		Name:         group.Name,
		Remark:       group.Remark,
		CreatedAt:    group.CreatedAt,
		UpdatedAt:    group.UpdatedAt,
	}, nil
}
