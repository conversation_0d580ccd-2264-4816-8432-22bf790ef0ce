package domain

import (
	"time"
)

// AdAgentEntity  广告代理账号实体
type AdAgentEntity struct {
	ID                int64     //  主键id
	CreatedAt         time.Time //  创建时间
	UpdatedAt         time.Time //  更新时间
	Platform          int8      //  所属平台 1-小红书 2-灯火
	AgentName         string    //  代理商名称
	PlatformAccountId string    //  平台账号id
	Token             string    //  token
	UsageStatus       int8      //  使用状态：1-启用 2-禁用
	AccountBalance    float64   //  账户余额（最多百亿级）
	Remark            string    //  备注
	ApiType           int8      // 1-直连 2-代理封装 3-人工上传
}
