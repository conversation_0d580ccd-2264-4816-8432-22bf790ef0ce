# 广告账号层级结构前端集成示例

## Vue.js 集成示例

### 1. 账号管理页面组件

```vue
<template>
  <div class="account-management">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <el-form :model="filterForm" inline>
        <el-form-item label="账号名称">
          <el-input 
            v-model="filterForm.account_name" 
            placeholder="请输入账号名称或ID"
            clearable>
          </el-input>
        </el-form-item>
        
        <el-form-item label="平台">
          <el-select v-model="filterForm.platform" placeholder="全部平台" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="小红书" :value="1"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="使用状态">
          <el-select v-model="filterForm.usage_status" placeholder="全部状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="2"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAddMasterAccount">新增主账号</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 账号层级表格 -->
    <div class="table-section">
      <el-table
        :data="flatAccountList"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        border
        stripe>
        
        <el-table-column label="账号名称" min-width="200">
          <template #default="scope">
            <div :style="{ paddingLeft: scope.row.level * 20 + 'px' }">
              <i 
                v-if="scope.row.account_type === 1" 
                class="el-icon-folder" 
                style="color: #409EFF; margin-right: 5px;">
              </i>
              <i 
                v-else 
                class="el-icon-document" 
                style="color: #67C23A; margin-right: 5px;">
              </i>
              <span>{{ scope.row.account_name }}</span>
              <el-tag 
                v-if="scope.row.account_type === 1" 
                type="primary" 
                size="small" 
                style="margin-left: 8px;">
                主账号
              </el-tag>
              <el-tag 
                v-else 
                type="success" 
                size="small" 
                style="margin-left: 8px;">
                子账号
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="platform_name" label="平台" width="100" />
        <el-table-column prop="platform_account_id" label="平台账号ID" width="120" />
        
        <el-table-column label="授权状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="getAuthStatusType(scope.row.authorization_status)"
              size="small">
              {{ scope.row.authorization_status_name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="使用状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.usage_status === 1 ? 'success' : 'danger'"
              size="small">
              {{ scope.row.usage_status_name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="account_balance" label="账户余额" width="120" align="right">
          <template #default="scope">
            ¥{{ scope.row.account_balance.toFixed(2) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="owner" label="负责人" width="100" />
        <el-table-column prop="last_sync" label="最后同步" width="160" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button 
                type="text" 
                size="small" 
                @click="handleEdit(scope.row)">
                编辑
              </el-button>
              
              <el-button 
                v-if="scope.row.account_type === 1"
                type="text" 
                size="small" 
                @click="handleAddSubAccount(scope.row)">
                添加子账号
              </el-button>
              
              <el-button 
                v-if="scope.row.authorization_status !== 1"
                type="text" 
                size="small" 
                @click="handleAuthorize(scope.row)">
                授权
              </el-button>
              
              <el-button 
                type="text" 
                size="small" 
                style="color: #F56C6C"
                @click="handleDelete(scope.row)"
                :disabled="!canDelete(scope.row)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAdAccounts, deleteAdAccount } from '@/api/account'

// 响应式数据
const loading = ref(false)
const accountHierarchyList = ref([])

// 筛选表单
const filterForm = reactive({
  account_name: '',
  platform: '',
  usage_status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 计算属性：将层级数据转换为平铺列表用于表格显示
const flatAccountList = computed(() => {
  const flatList = []
  
  accountHierarchyList.value.forEach(hierarchy => {
    // 添加主账号
    flatList.push({
      ...hierarchy,
      level: 0,
      hasChildren: hierarchy.sub_accounts.length > 0
    })
    
    // 添加子账号
    hierarchy.sub_accounts.forEach(subAccount => {
      flatList.push({
        ...subAccount,
        level: 1,
        hasChildren: false
      })
    })
  })
  
  return flatList
})

// 获取账号列表
const fetchAccountList = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...filterForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getAdAccounts(params)
    accountHierarchyList.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取账号列表失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAccountList()
}

// 重置
const handleReset = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  pagination.page = 1
  fetchAccountList()
}

// 新增主账号
const handleAddMasterAccount = () => {
  // 跳转到新增主账号页面或打开弹窗
  console.log('新增主账号')
}

// 编辑账号
const handleEdit = (account) => {
  // 跳转到编辑页面或打开编辑弹窗
  console.log('编辑账号：', account)
}

// 添加子账号
const handleAddSubAccount = (masterAccount) => {
  // 跳转到新增子账号页面或打开弹窗
  console.log('为主账号添加子账号：', masterAccount)
}

// 授权账号
const handleAuthorize = (account) => {
  // 跳转到授权页面或打开授权弹窗
  console.log('授权账号：', account)
}

// 删除账号
const handleDelete = async (account) => {
  const accountType = account.account_type === 1 ? '主账号' : '子账号'
  const hasSubAccounts = account.account_type === 1 && account.sub_accounts && account.sub_accounts.length > 0
  
  if (hasSubAccounts) {
    ElMessage.warning('该主账号下还有子账号，请先删除子账号')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除${accountType}"${account.account_name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteAdAccount(account.id)
    ElMessage.success('删除成功')
    fetchAccountList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 判断是否可以删除
const canDelete = (account) => {
  // 主账号：没有子账号时可以删除
  if (account.account_type === 1) {
    return !account.sub_accounts || account.sub_accounts.length === 0
  }
  // 子账号：总是可以删除
  return true
}

// 获取授权状态类型
const getAuthStatusType = (status) => {
  const statusMap = {
    0: 'danger',   // 未授权
    1: 'success',  // 已授权
    2: 'warning',  // 已过期
    3: 'info'      // 继承授权
  }
  return statusMap[status] || 'info'
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.page_size = size
  pagination.page = 1
  fetchAccountList()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.page = page
  fetchAccountList()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountList()
})
</script>

<style scoped>
.account-management {
  padding: 20px;
}

.filter-section {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-section {
  background: white;
  border-radius: 4px;
  padding: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}

.el-table .el-table__row {
  cursor: pointer;
}

.el-table .el-table__row:hover {
  background-color: #f5f7fa;
}
</style>
```

### 2. API 服务文件

```javascript
// api/account.js
import request from '@/utils/request'

// 获取广告账号层级结构列表
export function getAdAccounts(params) {
  return request({
    url: '/api/v1/ad-accounts',
    method: 'get',
    params
  })
}

// 创建主账号
export function createMasterAccount(data) {
  return request({
    url: '/api/v1/ad-accounts/master',
    method: 'post',
    data
  })
}

// 创建子账号
export function createSubAccount(data) {
  return request({
    url: '/api/v1/ad-accounts/sub',
    method: 'post',
    data
  })
}

// 编辑账号
export function updateAdAccount(id, data) {
  return request({
    url: `/api/v1/ad-accounts/${id}`,
    method: 'put',
    data
  })
}

// 删除账号
export function deleteAdAccount(id) {
  return request({
    url: `/api/v1/ad-accounts/${id}`,
    method: 'delete'
  })
}

// 授权账号
export function authorizeAccount(id, data) {
  return request({
    url: `/api/v1/ad-accounts/${id}/authorize`,
    method: 'post',
    data
  })
}
```

## React.js 集成示例

### 1. 账号管理组件

```jsx
import React, { useState, useEffect, useMemo } from 'react'
import {
  Table,
  Form,
  Input,
  Select,
  Button,
  Card,
  Tag,
  Space,
  Popconfirm,
  message,
  Pagination
} from 'antd'
import {
  FolderOutlined,
  FileOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SafetyOutlined
} from '@ant-design/icons'
import { getAdAccounts, deleteAdAccount } from '../api/account'

const { Option } = Select

const AccountManagement = () => {
  const [loading, setLoading] = useState(false)
  const [accountHierarchyList, setAccountHierarchyList] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })
  
  const [form] = Form.useForm()

  // 将层级数据转换为平铺列表
  const flatAccountList = useMemo(() => {
    const flatList = []
    
    accountHierarchyList.forEach(hierarchy => {
      // 添加主账号
      flatList.push({
        ...hierarchy,
        level: 0,
        hasChildren: hierarchy.sub_accounts.length > 0,
        key: `master-${hierarchy.id}`
      })
      
      // 添加子账号
      hierarchy.sub_accounts.forEach(subAccount => {
        flatList.push({
          ...subAccount,
          level: 1,
          hasChildren: false,
          key: `sub-${subAccount.id}`
        })
      })
    })
    
    return flatList
  }, [accountHierarchyList])

  // 表格列定义
  const columns = [
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
      render: (text, record) => (
        <div style={{ paddingLeft: record.level * 20 }}>
          {record.account_type === 1 ? (
            <FolderOutlined style={{ color: '#1890ff', marginRight: 8 }} />
          ) : (
            <FileOutlined style={{ color: '#52c41a', marginRight: 8 }} />
          )}
          <span>{text}</span>
          <Tag 
            color={record.account_type === 1 ? 'blue' : 'green'} 
            style={{ marginLeft: 8 }}
          >
            {record.account_type === 1 ? '主账号' : '子账号'}
          </Tag>
        </div>
      )
    },
    {
      title: '平台',
      dataIndex: 'platform_name',
      key: 'platform_name',
      width: 100
    },
    {
      title: '平台账号ID',
      dataIndex: 'platform_account_id',
      key: 'platform_account_id',
      width: 120
    },
    {
      title: '授权状态',
      dataIndex: 'authorization_status',
      key: 'authorization_status',
      width: 100,
      render: (status, record) => {
        const statusConfig = {
          0: { color: 'red', text: '未授权' },
          1: { color: 'green', text: '已授权' },
          2: { color: 'orange', text: '已过期' },
          3: { color: 'blue', text: '继承授权' }
        }
        const config = statusConfig[status] || { color: 'default', text: '未知' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '使用状态',
      dataIndex: 'usage_status',
      key: 'usage_status',
      width: 100,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '账户余额',
      dataIndex: 'account_balance',
      key: 'account_balance',
      width: 120,
      align: 'right',
      render: (balance) => `¥${balance.toFixed(2)}`
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      width: 100
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync',
      key: 'last_sync',
      width: 160
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small" wrap>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          
          {record.account_type === 1 && (
            <Button 
              type="link" 
              icon={<PlusOutlined />} 
              onClick={() => handleAddSubAccount(record)}
            >
              添加子账号
            </Button>
          )}
          
          {record.authorization_status !== 1 && (
            <Button 
              type="link" 
              icon={<SafetyOutlined />} 
              onClick={() => handleAuthorize(record)}
            >
              授权
            </Button>
          )}
          
          <Popconfirm
            title={`确定要删除${record.account_type === 1 ? '主账号' : '子账号'}"${record.account_name}"吗？`}
            onConfirm={() => handleDelete(record)}
            disabled={!canDelete(record)}
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
              disabled={!canDelete(record)}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 获取账号列表
  const fetchAccountList = async () => {
    try {
      setLoading(true)
      const values = form.getFieldsValue()
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        ...values
      }
      
      // 过滤空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      const response = await getAdAccounts(params)
      setAccountHierarchyList(response.data.list)
      setPagination(prev => ({
        ...prev,
        total: response.data.total
      }))
    } catch (error) {
      message.error('获取账号列表失败：' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchAccountList()
  }

  // 重置
  const handleReset = () => {
    form.resetFields()
    setPagination(prev => ({ ...prev, current: 1 }))
    fetchAccountList()
  }

  // 编辑账号
  const handleEdit = (account) => {
    console.log('编辑账号：', account)
  }

  // 添加子账号
  const handleAddSubAccount = (masterAccount) => {
    console.log('为主账号添加子账号：', masterAccount)
  }

  // 授权账号
  const handleAuthorize = (account) => {
    console.log('授权账号：', account)
  }

  // 删除账号
  const handleDelete = async (account) => {
    try {
      await deleteAdAccount(account.id)
      message.success('删除成功')
      fetchAccountList()
    } catch (error) {
      message.error('删除失败：' + error.message)
    }
  }

  // 判断是否可以删除
  const canDelete = (account) => {
    if (account.account_type === 1) {
      // 主账号：没有子账号时可以删除
      const hierarchy = accountHierarchyList.find(h => h.id === account.id)
      return !hierarchy || !hierarchy.sub_accounts || hierarchy.sub_accounts.length === 0
    }
    // 子账号：总是可以删除
    return true
  }

  // 分页改变
  const handleTableChange = (page, pageSize) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize
    }))
  }

  useEffect(() => {
    fetchAccountList()
  }, [pagination.current, pagination.pageSize])

  return (
    <div style={{ padding: 24 }}>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Form form={form} layout="inline">
          <Form.Item name="account_name" label="账号名称">
            <Input placeholder="请输入账号名称或ID" />
          </Form.Item>
          
          <Form.Item name="platform" label="平台">
            <Select placeholder="全部平台" allowClear style={{ width: 120 }}>
              <Option value={1}>小红书</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="usage_status" label="使用状态">
            <Select placeholder="全部状态" allowClear style={{ width: 120 }}>
              <Option value={1}>启用</Option>
              <Option value={2}>禁用</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />}>
                新增主账号
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 账号表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={flatAccountList}
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default AccountManagement
```

## 关键实现要点

### 1. 数据结构转换
- 将层级结构转换为平铺列表用于表格展示
- 保持父子关系的视觉层次
- 添加level字段控制缩进

### 2. 视觉层次展示
- 使用不同图标区分主账号和子账号
- 通过缩进体现层级关系
- 使用标签标识账号类型

### 3. 操作权限控制
- 主账号可以添加子账号
- 有子账号的主账号不能删除
- 根据授权状态显示授权按钮

### 4. 用户体验优化
- 筛选条件同时应用于主账号和子账号
- 分页基于主账号数量
- 提供清晰的操作反馈
