package model

import (
	"gorm.io/gorm"
	"time"
)

// AdAgent  广告代理账号表
type AdAgent struct {
	ID                int64          //  主键id
	CreatedAt         time.Time      //  创建时间
	UpdatedAt         time.Time      //  更新时间
	DeletedAt         gorm.DeletedAt //  删除时间
	Platform          int8           //  所属平台 1-小红书 2-灯火
	AgentName         string         //  代理商名称
	PlatformAccountId string         //  平台账号id
	Token             string         //  token
	UsageStatus       int8           //  使用状态：1-启用 2-禁用
	AccountBalance    float64        //  账户余额（最多百亿级）
	Remark            string         //  备注
	ApiType           int8           // 1-直连 2-代理封装 3-人工上传
}

func (m *AdAgent) TableName() string {
	return "ad_agent_accounts"
}
