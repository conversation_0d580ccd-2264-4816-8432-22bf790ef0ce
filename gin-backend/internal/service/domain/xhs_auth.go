package domain

import (
	"errors"
	"net/url"
	"strings"
)

// XHSAuthScope 小红书授权范围
type XHSAuthScope string

const (
	// 报表服务权限
	ScopeReportService XHSAuthScope = "report_service"
	// 广告查询权限
	ScopeAdQuery XHSAuthScope = "ad_query"
	// 广告管理权限
	ScopeAdManage XHSAuthScope = "ad_manage"
	// 账户管理权限
	ScopeAccountManage XHSAuthScope = "account_manage"
)

// GetXHSAuthUrlParam 获取小红书授权链接参数
type GetXHSAuthUrlParam struct {
	AppId       string         // 应用ID
	RedirectUri string         // 重定向URI
	Scopes      []XHSAuthScope // 授权范围
	State       string         // 状态参数，用于防止CSRF攻击
}

// Validate 验证参数
func (p GetXHSAuthUrlParam) Validate() error {
	if p.AppId == "" {
		return errors.New("应用ID不能为空")
	}
	if p.RedirectUri == "" {
		return errors.New("重定向URI不能为空")
	}
	if len(p.Scopes) == 0 {
		return errors.New("授权范围不能为空")
	}

	// 验证重定向URI格式
	if _, err := url.Parse(p.RedirectUri); err != nil {
		return errors.New("重定向URI格式不正确")
	}

	// 验证授权范围
	validScopes := map[XHSAuthScope]bool{
		ScopeReportService: true,
		ScopeAdQuery:       true,
		ScopeAdManage:      true,
		ScopeAccountManage: true,
	}

	for _, scope := range p.Scopes {
		if !validScopes[scope] {
			return errors.New("无效的授权范围: " + string(scope))
		}
	}

	return nil
}

// GetXHSAuthUrlResult 获取小红书授权链接结果
type GetXHSAuthUrlResult struct {
	AuthUrl     string         // 授权链接
	AppId       string         // 应用ID
	RedirectUri string         // 重定向URI
	Scopes      []XHSAuthScope // 授权范围
	State       string         // 状态参数
}

// XHSAuthCallbackParam 小红书授权回调参数
type XHSAuthCallbackParam struct {
	Code  string // 授权码
	State string // 状态参数
	Error string // 错误信息
}

// Validate 验证回调参数
func (p XHSAuthCallbackParam) Validate() error {
	if p.Error != "" {
		return errors.New("授权失败: " + p.Error)
	}
	if p.Code == "" {
		return errors.New("授权码不能为空")
	}
	if p.State == "" {
		return errors.New("状态参数不能为空")
	}
	return nil
}

// XHSAuthTokenParam 小红书获取访问令牌参数
type XHSAuthTokenParam struct {
	AppId       string // 应用ID
	Secret      string // 应用密钥
	Code        string // 授权码
	RedirectUri string // 重定向URI
}

// Validate 验证获取令牌参数
func (p XHSAuthTokenParam) Validate() error {
	if p.AppId == "" {
		return errors.New("应用ID不能为空")
	}
	if p.Secret == "" {
		return errors.New("应用密钥不能为空")
	}
	if p.Code == "" {
		return errors.New("授权码不能为空")
	}
	if p.RedirectUri == "" {
		return errors.New("重定向URI不能为空")
	}
	return nil
}

// XHSAuthTokenResult 小红书访问令牌结果
type XHSAuthTokenResult struct {
	AccessToken  string `json:"access_token"`  // 访问令牌
	RefreshToken string `json:"refresh_token"` // 刷新令牌
	ExpiresIn    int64  `json:"expires_in"`    // 过期时间（秒）
	TokenType    string `json:"token_type"`    // 令牌类型
	Scope        string `json:"scope"`         // 授权范围
}

// GetDefaultXHSAuthScopes 获取默认的小红书授权范围
func GetDefaultXHSAuthScopes() []XHSAuthScope {
	return []XHSAuthScope{
		ScopeReportService,
		ScopeAdQuery,
		ScopeAdManage,
		ScopeAccountManage,
	}
}

// FormatXHSAuthScopes 格式化小红书授权范围为URL参数
func FormatXHSAuthScopes(scopes []XHSAuthScope) string {
	scopeStrings := make([]string, len(scopes))
	for i, scope := range scopes {
		scopeStrings[i] = `"` + string(scope) + `"`
	}
	return "[" + strings.Join(scopeStrings, ",") + "]"
}

// ParseXHSAuthScopes 解析小红书授权范围字符串
func ParseXHSAuthScopes(scopeStr string) ([]XHSAuthScope, error) {
	// 移除方括号和引号
	scopeStr = strings.Trim(scopeStr, "[]")
	scopeStr = strings.ReplaceAll(scopeStr, `"`, "")

	if scopeStr == "" {
		return nil, errors.New("授权范围字符串为空")
	}

	scopeStrings := strings.Split(scopeStr, ",")
	scopes := make([]XHSAuthScope, 0, len(scopeStrings))

	validScopes := map[string]bool{
		string(ScopeReportService): true,
		string(ScopeAdQuery):       true,
		string(ScopeAdManage):      true,
		string(ScopeAccountManage): true,
	}

	for _, scopeString := range scopeStrings {
		scopeString = strings.TrimSpace(scopeString)
		if scopeString == "" {
			continue
		}

		if !validScopes[scopeString] {
			return nil, errors.New("无效的授权范围: " + scopeString)
		}

		scopes = append(scopes, XHSAuthScope(scopeString))
	}

	if len(scopes) == 0 {
		return nil, errors.New("没有有效的授权范围")
	}

	return scopes, nil
}

// GenerateXHSAuthState 生成小红书授权状态参数
func GenerateXHSAuthState() string {
	// 这里可以生成一个随机字符串，用于防止CSRF攻击
	// 简单实现，实际项目中应该使用更安全的方法
	return "xhs_auth_state_"
}

// ValidateXHSAuthState 验证小红书授权状态参数
func ValidateXHSAuthState(state string) bool {
	// 这里应该验证state参数的有效性
	// 简单实现，实际项目中应该验证state是否是之前生成的
	return strings.HasPrefix(state, "xhs_auth_state_")
}

// BuildXHSAuthUrl 构建小红书授权链接
func BuildXHSAuthUrl(param GetXHSAuthUrlParam) (string, error) {
	if err := param.Validate(); err != nil {
		return "", err
	}

	// 构建授权链接
	baseUrl := "https://ad-market.xiaohongshu.com/auth"

	// 构建查询参数
	values := url.Values{}
	values.Set("appId", param.AppId)
	values.Set("scope", FormatXHSAuthScopes(param.Scopes))
	values.Set("redirectUri", param.RedirectUri)
	values.Set("state", param.State)

	return baseUrl + "?" + values.Encode(), nil
}

// ValidateGetXHSAuthUrlParam 验证获取小红书授权链接参数
func ValidateGetXHSAuthUrlParam(param GetXHSAuthUrlParam) error {
	return param.Validate()
}

// ValidateXHSAuthCallbackParam 验证小红书授权回调参数
func ValidateXHSAuthCallbackParam(param XHSAuthCallbackParam) error {
	return param.Validate()
}

// ValidateXHSAuthTokenParam 验证小红书获取访问令牌参数
func ValidateXHSAuthTokenParam(param XHSAuthTokenParam) error {
	return param.Validate()
}
