# 计划列表账号名称获取优化

## 问题描述

在 `GetPlanList` 单日查询功能中，账号名称（AccountName）字段为空，没有从账号表中读取实际的账号名称。

## 解决方案

### 1. 添加账号名称获取方法

在 `AdPlanListService` 中添加了 `getAccountNameByID` 方法，用于根据账号ID从账号表中获取账号名称：

```go
// getAccountNameByID 根据账号ID获取账号名称
func (s *AdPlanListService) getAccountNameByID(ctx context.Context, accountID int64) string {
	var accountName string
	s.db.WithContext(ctx).Model(&model.AdAccounts{}).
		Where("id = ?", accountID).
		Pluck("account_name", &accountName)
	return accountName
}
```

### 2. 修改单日查询逻辑

在 `getPlanListFromStats` 方法中，修改了数据转换逻辑，使其从账号表获取账号名称：

**修改前**：
```go
plans = append(plans, domain.AdPlanListItem{
    PlanID:      stat.PlanId,
    PlanName:    stat.PlanName,
    AccountName: "", // 空字符串
    // ... 其他字段
})
```

**修改后**：
```go
// 从账号表获取账号名称
accountName := s.getAccountNameByID(ctx, stat.AccountId)

plans = append(plans, domain.AdPlanListItem{
    PlanID:      stat.PlanId,
    PlanName:    stat.PlanName,
    AccountName: accountName, // 从账号表获取的真实账号名称
    // ... 其他字段
})
```

### 3. 修改导出功能

同样在 `getExportDataFromStats` 方法中应用了相同的修改，确保导出的数据也包含正确的账号名称。

### 4. 修复相关编译错误

修复了 `xhs_creative_report_svc.go` 中的编译错误，移除了不存在的 `Account` 字段引用。

## 影响范围

### 受影响的功能
1. **计划列表查询**（单日查询）- 现在会显示正确的账号名称
2. **计划列表导出**（单日查询）- 导出的数据包含正确的账号名称
3. **多日查询** - 不受影响，因为多日查询直接从创意报表中获取账号名称

### 数据查询策略
- **单日查询**：从 `ad_plan_stats` 统计表查询 + 从 `ad_accounts` 表获取账号名称
- **多日查询**：从 `xhs_creative_reports` 创意报表聚合，账号名称直接来自报表数据

## 性能考虑

### 查询优化
- 使用 `Pluck` 方法只查询需要的字段，减少数据传输
- 每个计划记录都会执行一次账号名称查询，对于大量数据可能有性能影响

### 潜在优化方案
如果性能成为问题，可以考虑以下优化：

1. **批量查询优化**：
```go
// 收集所有账号ID
accountIDs := make([]int64, 0, len(stats))
for _, stat := range stats {
    accountIDs = append(accountIDs, stat.AccountId)
}

// 批量查询账号名称
var accounts []model.AdAccounts
s.db.WithContext(ctx).Where("id IN ?", accountIDs).Find(&accounts)

// 构建ID到名称的映射
accountMap := make(map[int64]string)
for _, account := range accounts {
    accountMap[account.ID] = account.AccountName
}
```

2. **缓存优化**：
```go
// 使用内存缓存存储账号ID到名称的映射
// 可以使用 sync.Map 或其他缓存方案
```

## 测试验证

创建了相应的单元测试来验证：
1. `getAccountNameByID` 方法的正确性
2. 单日查询返回正确的账号名称
3. 账号筛选功能的正确性

## 使用示例

### API调用示例
```bash
# 单日查询 - 现在会返回正确的账号名称
curl -X GET "http://localhost:8080/api/v1/plans?start_date=2024-06-16&end_date=2024-06-16"
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "plan_id": "plan_001",
        "plan_name": "测试计划",
        "account_name": "营销树长大", // 现在会显示真实的账号名称
        "consumption": 1000.0,
        "actual_cost": 900.0,
        "impressions": 10000,
        "clicks": 100,
        "click_rate": 1.0,
        "last_update": "2024-06-16 12:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

## 总结

这个修改确保了计划列表功能在单日查询时能够正确显示账号名称，提升了数据的完整性和用户体验。同时保持了多日查询的现有逻辑不变，确保了功能的稳定性。
