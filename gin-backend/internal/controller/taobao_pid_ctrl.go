package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TaobaoPidController struct {
	pidService *service.TaobaoPidService
}

func NewTaobaoPidController() *TaobaoPidController {
	return &TaobaoPidController{
		pidService: service.NewTaobaoPidService(),
	}
}

// List 获取淘联链接列表
// @Summary 获取淘联链接列表
// @Description 分页获取淘联链接列表，支持关键词搜索、使用状态筛选等
// @Tags 淘联链接
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param keyword query string false "关键词搜索"
// @Param is_used query int false "是否已使用：-1-全部，0-未使用，1-已使用" default(-1)
// @Param act_type query int false "活动类型：1-飞猪，2-福利购"
// @Param sort query string false "排序：+id或-id" default("+id")
// @Success 200 {object} response.Response{data=vo.TaobaoPidListResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/taobao-pids [get]
func (c *TaobaoPidController) List(ctx *gin.Context) {
	var req vo.TaobaoPidListReq

	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 转换为领域参数
	param := req.ToTaobaoPidListParam()

	// 调用服务层
	result, err := c.pidService.GetList(param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取淘联链接列表失败: "+err.Error())
		return
	}

	// 转换为VO对象
	resp := vo.TaobaoPidListRespFromEntity(result)

	response.Success(ctx, resp)
}

// Create 创建淘联链接
// @Summary 创建淘联链接
// @Description 创建新的淘联链接
// @Tags 淘联链接
// @Accept json
// @Produce json
// @Param request body vo.TaobaoPidCreateReq true "创建请求"
// @Success 200 {object} response.Response{data=map[string]uint64}
// @Failure 400 {object} response.Response
// @Router /api/v1/taobao-pids/create [post]
func (c *TaobaoPidController) Create(ctx *gin.Context) {
	var req vo.TaobaoPidCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 转换为领域参数
	param := req.ToTaobaoPidCreateParam()

	// 调用服务层
	id, err := c.pidService.Create(param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "创建淘联链接失败: "+err.Error())
		return
	}

	response.Success(ctx, map[string]int64{"id": id})
}

// GetByID 根据ID获取淘联链接
// @Summary 根据ID获取淘联链接
// @Description 根据ID获取淘联链接详情
// @Tags 淘联链接
// @Accept json
// @Produce json
// @Param id query uint64 true "淘联链接ID"
// @Success 200 {object} response.Response{data=vo.TaobaoPidItem}
// @Failure 400 {object} response.Response
// @Router /api/v1/taobao-pids/detail [get]
func (c *TaobaoPidController) GetByID(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "ID参数不能为空")
		return
	}
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的ID")
		return
	}

	// 调用服务层
	result, err := c.pidService.GetByID(id)
	if err != nil {
		response.Error(ctx, response.CodeNotFound, "获取淘联链接失败: "+err.Error())
		return
	}

	// 转换为VO对象
	item := vo.TaobaoPidItemFromEntity(result)

	response.Success(ctx, item)
}

// Update 更新淘联链接
// @Summary 更新淘联链接
// @Description 更新淘联链接信息
// @Tags 淘联链接
// @Accept json
// @Produce json
// @Param request body vo.TaobaoPidUpdateReq true "更新请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/taobao-pids/update [put]
func (c *TaobaoPidController) Update(ctx *gin.Context) {
	var req vo.TaobaoPidUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, response.CodeInvalidParam, "参数错误: "+err.Error())
		return
	}

	// 转换为领域参数
	param := req.ToTaobaoPidUpdateParam()

	// 调用服务层
	err := c.pidService.Update(param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "更新淘联链接失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Delete 删除淘联链接
// @Summary 删除淘联链接
// @Description 删除淘联链接
// @Tags 淘联链接
// @Accept json
// @Produce json
// @Param id query uint64 true "淘联链接ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Router /api/v1/taobao-pids/delete [delete]
func (c *TaobaoPidController) Delete(ctx *gin.Context) {
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, response.CodeInvalidParam, "ID参数不能为空")
		return
	}
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, response.CodeInvalidParam, "无效的ID")
		return
	}

	// 调用服务层
	err = c.pidService.Delete(id)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "删除淘联链接失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}
