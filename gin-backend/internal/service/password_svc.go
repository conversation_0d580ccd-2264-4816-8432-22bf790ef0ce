package service

import (
	"context"
	"errors"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// PasswordService 口令服务
type PasswordService struct {
	db *gorm.DB
}

// NewPasswordService 创建口令服务实例
func NewPasswordService() *PasswordService {
	return &PasswordService{
		db: global.DB,
	}
}

// CreateWithGroup 创建口令组及口令
func (s *PasswordService) CreateWithGroup(ctx context.Context, entity domain.PasswordEntity) (domain.PasswordActionResult, error) {
	// 1. 验证分类ID
	if entity.CategoryID != 55 && entity.CategoryID != 173 && entity.CategoryID != 284 {
		return domain.PasswordActionResult{}, errors.New("分类ID只能是55(小红书)、173(抖音)或284(闪购)")
	}

	// 2. 检查组名是否已存在
	var existingGroup model.PasswordGroup
	err := s.db.WithContext(ctx).Where("name = ?", entity.GroupName).First(&existingGroup).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return domain.PasswordActionResult{}, fmt.Errorf("检查组名失败: %v", err)
	}
	if err == nil {
		return domain.PasswordActionResult{}, fmt.Errorf("组名 '%s' 已存在", entity.GroupName)
	}

	// 3. 验证口令列表
	if len(entity.Passwords) == 0 {
		return domain.PasswordActionResult{}, errors.New("口令列表不能为空")
	}

	// 4. 创建口令组
	group := model.PasswordGroup{
		Name:       entity.GroupName,
		CategoryID: entity.CategoryID,
	}

	err = s.db.WithContext(ctx).Create(&group).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("创建口令组失败: %v", err)
	}

	// 5. 创建口令
	passwords := make([]model.Password, len(entity.Passwords))
	for i, p := range entity.Passwords {
		passwords[i] = model.Password{
			PID:     p.PID,
			Name:    p.Name,
			GroupID: group.ID,
		}
	}

	err = s.db.WithContext(ctx).Create(&passwords).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("创建口令失败: %v", err)
	}

	result := domain.PasswordActionResult{
		ID: int(group.ID),
	}

	return result, nil
}

// List 获取口令组列表
func (s *PasswordService) List(ctx context.Context, param domain.PasswordQueryParam) (domain.PasswordResult, error) {
	// 处理分页参数
	pageSize := param.PageSize
	if param.Size > 0 {
		pageSize = param.Size
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 1000 {
		pageSize = 1000
	}

	page := param.Page
	if page <= 0 {
		page = 1
	}

	// 获取口令组列表
	var groups []model.PasswordGroup
	var total int64

	query := s.db.WithContext(ctx).Model(&model.PasswordGroup{})

	// 添加筛选条件
	if param.GroupName != "" {
		query = query.Where("name LIKE ?", "%"+param.GroupName+"%")
	}
	if param.CategoryID > 0 {
		query = query.Where("category_id = ?", param.CategoryID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.PasswordResult{}, fmt.Errorf("获取口令组列表失败: %v", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("id DESC").Find(&groups).Error; err != nil {
		return domain.PasswordResult{}, fmt.Errorf("获取口令组列表失败: %v", err)
	}

	result := domain.PasswordResult{
		List:  make([]domain.PasswordGroupItem, 0, len(groups)),
		Total: total,
		Page:  page,
	}

	if len(groups) == 0 {
		return result, nil
	}

	// 提取组ID
	groupIDs := make([]uint, len(groups))
	for i, group := range groups {
		groupIDs[i] = group.ID
	}

	// 批量获取口令
	var passwords []model.Password
	if err := s.db.WithContext(ctx).Where("group_id IN ?", groupIDs).Find(&passwords).Error; err != nil {
		return domain.PasswordResult{}, fmt.Errorf("获取口令列表失败: %v", err)
	}

	// 按组ID分组口令
	passwordMap := make(map[uint][]domain.PasswordItem)
	for _, pwd := range passwords {
		if _, ok := passwordMap[pwd.GroupID]; !ok {
			passwordMap[pwd.GroupID] = make([]domain.PasswordItem, 0)
		}
		passwordMap[pwd.GroupID] = append(passwordMap[pwd.GroupID], domain.PasswordItem{
			ID:   int64(pwd.ID),
			Name: pwd.Name,
			PID:  pwd.PID,
		})
	}

	// 构建响应
	for _, group := range groups {
		groupItem := domain.PasswordGroupItem{
			ID:         int(group.ID),
			GroupName:  group.Name,
			CategoryID: int64(group.CategoryID),
			CreatedAt:  group.CreatedAt,
			UpdatedAt:  group.UpdatedAt,
			Passwords:  passwordMap[group.ID],
		}
		if groupItem.Passwords == nil {
			groupItem.Passwords = make([]domain.PasswordItem, 0)
		}
		result.List = append(result.List, groupItem)
	}

	return result, nil
}

// OriList 获取原始口令列表
func (s *PasswordService) OriList(ctx context.Context, param domain.PasswordOriQueryParam) (domain.PasswordOriResult, error) {
	// 处理分页参数
	pageSize := param.PageSize

	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 2000 {
		pageSize = 2000
	}

	page := param.Page
	if page <= 0 {
		page = 1
	}

	// 验证分类ID
	if len(param.CategoryID) == 0 {
		return domain.PasswordOriResult{}, errors.New("分类ID不能为空")
	}

	// 递归获取所有子分类ID
	allCategoryIDs, err := s.getAllChildCategories(ctx, param.CategoryID)
	if err != nil {
		return domain.PasswordOriResult{}, err
	}

	if len(allCategoryIDs) == 0 {
		return domain.PasswordOriResult{
			List:  []domain.PasswordOriItem{},
			Total: 0,
			Page:  page,
		}, nil
	}

	// 计算总数的SQL
	countSQL := `
		SELECT COUNT(1) as total
		FROM warehouse.elm_pwds e
		JOIN byn_data.elm_adzone_category_rels r ON SUBSTRING_INDEX(e.pid, '_', -1) COLLATE utf8mb4_general_ci = r.adzone COLLATE utf8mb4_general_ci
		WHERE r.category_id IN ?
	`

	var total int
	if err := s.db.WithContext(ctx).Raw(countSQL, allCategoryIDs).Scan(&total).Error; err != nil {
		return domain.PasswordOriResult{}, fmt.Errorf("获取原始口令列表失败: %v", err)
	}

	if total == 0 {
		return domain.PasswordOriResult{
			List:  []domain.PasswordOriItem{},
			Total: 0,
			Page:  page,
		}, nil
	}

	// 查询数据的SQL - 完全按照原项目的方式
	dataSQL := `
		SELECT 
			e.pwd_name, 
			e.pid
		FROM warehouse.elm_pwds e
		JOIN byn_data.elm_adzone_category_rels r ON SUBSTRING_INDEX(e.pid, '_', -1) COLLATE utf8mb4_general_ci = r.adzone COLLATE utf8mb4_general_ci
		WHERE r.category_id IN ?
		LIMIT ? OFFSET ?
	`

	offset := (page - 1) * pageSize

	// 使用与原项目相同的结构体标签
	var results []struct {
		PwdName string `json:"pwd_name"`
		Pid     string `json:"pid"`
	}

	if err := s.db.WithContext(ctx).Raw(dataSQL, allCategoryIDs, pageSize, offset).Scan(&results).Error; err != nil {
		return domain.PasswordOriResult{}, fmt.Errorf("获取原始口令列表失败: %v", err)
	}

	// 构造响应
	passwordOriItems := make([]domain.PasswordOriItem, len(results))
	for i, item := range results {
		passwordOriItems[i] = domain.PasswordOriItem{
			Name: item.PwdName,
			PID:  item.Pid,
		}
	}

	return domain.PasswordOriResult{
		List:  passwordOriItems,
		Total: total,
		Page:  page,
	}, nil
}

// Update 更新口令组
func (s *PasswordService) Update(ctx context.Context, id uint, entity domain.PasswordEntity) (domain.PasswordActionResult, error) {
	// 1. 验证分类ID
	if entity.CategoryID != 55 && entity.CategoryID != 173 && entity.CategoryID != 284 {
		return domain.PasswordActionResult{}, errors.New("分类ID只能是55(小红书)、173(抖音)或284(闪购)")
	}

	// 2. 检查组是否存在
	var existingGroup model.PasswordGroup
	err := s.db.WithContext(ctx).Where("id = ?", id).First(&existingGroup).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PasswordActionResult{}, fmt.Errorf("口令组不存在: ID=%d", id)
		}
		return domain.PasswordActionResult{}, fmt.Errorf("查询口令组失败: %v", err)
	}

	// 3. 检查组名是否已被其他组使用
	var dupGroup model.PasswordGroup
	err = s.db.WithContext(ctx).Where("name = ? AND id != ?", entity.GroupName, id).First(&dupGroup).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return domain.PasswordActionResult{}, fmt.Errorf("检查组名失败: %v", err)
	}
	if err == nil {
		return domain.PasswordActionResult{}, fmt.Errorf("组名 '%s' 已被其他口令组使用", entity.GroupName)
	}

	// 4. 验证口令列表
	if len(entity.Passwords) == 0 {
		return domain.PasswordActionResult{}, errors.New("口令列表不能为空")
	}

	// 5. 更新口令组
	existingGroup.Name = entity.GroupName
	existingGroup.CategoryID = entity.CategoryID
	err = s.db.WithContext(ctx).Save(&existingGroup).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("更新口令组失败: %v", err)
	}

	// 6. 删除旧的口令
	err = s.db.WithContext(ctx).Where("group_id = ?", id).Delete(&model.Password{}).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("删除旧口令失败: %v", err)
	}

	// 7. 创建新的口令
	passwords := make([]model.Password, len(entity.Passwords))
	for i, p := range entity.Passwords {
		passwords[i] = model.Password{
			PID:     p.PID,
			Name:    p.Name,
			GroupID: id,
		}
	}

	err = s.db.WithContext(ctx).Create(&passwords).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("创建新口令失败: %v", err)
	}

	result := domain.PasswordActionResult{
		ID: int(id),
	}

	return result, nil
}

// Delete 删除口令组
func (s *PasswordService) Delete(ctx context.Context, id uint) (domain.PasswordActionResult, error) {
	// 1. 检查口令组是否存在
	var group model.PasswordGroup
	err := s.db.WithContext(ctx).Where("id = ?", id).First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PasswordActionResult{}, fmt.Errorf("口令组不存在: ID=%d", id)
		}
		return domain.PasswordActionResult{}, fmt.Errorf("查询口令组失败: %v", err)
	}

	// 2. 删除关联的口令
	err = s.db.WithContext(ctx).Where("group_id = ?", id).Delete(&model.Password{}).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("删除口令失败: %v", err)
	}

	// 3. 删除口令组
	err = s.db.WithContext(ctx).Delete(&group).Error
	if err != nil {
		return domain.PasswordActionResult{}, fmt.Errorf("删除口令组失败: %v", err)
	}

	result := domain.PasswordActionResult{
		ID: int(id),
	}

	return result, nil
}

// getAllChildCategories 递归获取所有子分类ID
func (s *PasswordService) getAllChildCategories(ctx context.Context, parentIDs []int) ([]int, error) {
	if len(parentIDs) == 0 {
		return []int{}, nil
	}

	// 统计所有有效的分类ID
	validIDsMap := make(map[int]bool)
	for _, id := range parentIDs {
		if id > 0 {
			validIDsMap[id] = true
		}
	}

	// 获取所有有效的分类ID
	var validIDs []int
	for id := range validIDsMap {
		validIDs = append(validIDs, id)
	}

	// 如果没有有效的分类ID，则返回空
	if len(validIDs) == 0 {
		return []int{}, nil
	}

	// 查询所有子分类
	childSQL := "SELECT DISTINCT category_id FROM byn_data.elm_adzone_category_rels WHERE category_id IN ?"
	var childIDs []int
	if err := s.db.WithContext(ctx).Raw(childSQL, validIDs).Scan(&childIDs).Error; err != nil {
		return nil, fmt.Errorf("获取子分类失败: %v", err)
	}

	// 返回结果
	return childIDs, nil
}
