package model

import (
	"time"
)

// PromotionZone 推广区域
type PromotionZone struct {
	ID              uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	PlanID          uint64     `gorm:"column:plan_id;index" json:"planId"`
	AdProductID     uint64     `gorm:"column:ad_product_id;index" json:"adProductId"`
	ZoneID          string     `gorm:"column:zone_id;type:varchar(100)" json:"zoneId"`
	ZoneName        string     `gorm:"column:zone_name;type:varchar(200)" json:"zoneName"`
	PID             string     `gorm:"column:pid;type:varchar(200)" json:"pid"`
	ZoneType        string     `gorm:"column:zone_type;type:varchar(50)" json:"zoneType"`
	PromotionLink   string     `gorm:"column:promotion_link;type:text" json:"promotionLink"`
	PromotionQrcode string     `gorm:"column:promotion_qrcode;type:text" json:"promotionQrcode"`
	ZoneParams      string     `gorm:"column:zone_params;type:text" json:"zoneParams"`
	PromotionParams string     `gorm:"column:promotion_params;type:text" json:"promotionParams"`
	MergedPageURL   string     `gorm:"column:merged_page_url;type:text" json:"mergedPageUrl"`
	MergeParams     string     `gorm:"column:merge_params;type:text" json:"mergeParams"`
	CreatedBy       uint64     `gorm:"column:created_by" json:"createdBy"`
	UpdatedBy       uint64     `gorm:"column:updated_by" json:"updatedBy"`
	CreatedAt       time.Time  `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt       time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
	DeletedAt       *time.Time `gorm:"column:deleted_at;index" json:"deletedAt"`
}

func (PromotionZone) TableName() string {
	return "promotion_zones"
}
