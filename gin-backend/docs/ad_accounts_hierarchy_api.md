# 广告账号层级结构API文档

## 功能概述

修改了 `GetAdAccounts` API，现在返回主账号下挂子账号的层级结构，而不是平铺的账号列表。这样的设计更符合业务逻辑，便于前端展示和管理。

## API变更说明

### 原有结构（平铺列表）
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_type": 1,
        "account_name": "主账号1",
        "sub_accounts": []
      },
      {
        "id": 2,
        "account_type": 2,
        "account_name": "子账号1",
        "parent_id": 1
      },
      {
        "id": 3,
        "account_type": 2,
        "account_name": "子账号2", 
        "parent_id": 1
      }
    ]
  }
}
```

### 新结构（层级结构）
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_type": 1,
        "account_name": "主账号1",
        "sub_accounts": [
          {
            "id": 2,
            "account_type": 2,
            "account_name": "子账号1",
            "parent_id": 1,
            "parent_account_name": "主账号1"
          },
          {
            "id": 3,
            "account_type": 2,
            "account_name": "子账号2",
            "parent_id": 1,
            "parent_account_name": "主账号1"
          }
        ]
      }
    ]
  }
}
```

## API接口详情

### 获取广告账号层级结构列表

**接口地址**：`GET /api/v1/ad-accounts`

**功能描述**：获取广告账号层级结构列表，返回主账号下挂子账号的结构

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20，最大100 |
| account_name | string | 否 | 账号名称/ID筛选（支持模糊查询） |
| platform | int | 否 | 平台筛选（1=小红书） |
| authorization_status | int | 否 | 授权状态筛选（0=未授权，1=已授权，2=已过期，3=继承授权） |
| usage_status | int | 否 | 使用状态筛选（1=启用，2=禁用） |
| account_type | int | 否 | 账号类型筛选（仅对主账号生效，1=主账号，2=子账号） |
| parent_id | int | 否 | 父账号ID筛选（仅对主账号生效） |

**响应结构**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_type": 1,
        "account_type_name": "主账号",
        "parent_id": 0,
        "parent_account_name": "",
        "platform": 1,
        "platform_name": "小红书",
        "account_name": "营销树长大",
        "platform_account_id": "***********",
        "authorization_status": 1,
        "authorization_status_name": "已授权",
        "token": "access_token_xxx",
        "token_expire_time": "2024-12-31 23:59:59",
        "usage_status": 1,
        "usage_status_name": "启用",
        "account_balance": 10000.00,
        "owner": "张三",
        "last_sync": "2024-06-16 12:00:00",
        "created_at": "2024-01-01 10:00:00",
        "updated_at": "2024-06-16 12:00:00",
        "sub_accounts": [
          {
            "id": 2,
            "account_type": 2,
            "account_type_name": "子账号",
            "parent_id": 1,
            "parent_account_name": "营销树长大",
            "platform": 1,
            "platform_name": "小红书",
            "account_name": "营销树长大-子账号1",
            "platform_account_id": "***********",
            "authorization_status": 3,
            "authorization_status_name": "继承授权",
            "token": "",
            "token_expire_time": "0001-01-01 00:00:00",
            "usage_status": 1,
            "usage_status_name": "启用",
            "account_balance": 5000.00,
            "owner": "李四",
            "last_sync": "2024-06-16 12:00:00",
            "created_at": "2024-01-15 10:00:00",
            "updated_at": "2024-06-16 12:00:00"
          }
        ]
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

## 数据查询逻辑

### 1. 主账号查询
- 只查询 `account_type = 1` 的主账号
- 对主账号应用所有筛选条件
- 支持分页查询

### 2. 子账号查询
- 对每个主账号，查询 `parent_id = 主账号ID` 的子账号
- 对子账号也应用相同的筛选条件（除了account_type和parent_id）
- 子账号按创建时间倒序排列

### 3. 筛选条件应用
- **account_name**：同时应用于主账号和子账号的名称/ID筛选
- **platform**：同时应用于主账号和子账号
- **authorization_status**：同时应用于主账号和子账号
- **usage_status**：同时应用于主账号和子账号
- **account_type**：仅应用于主账号查询
- **parent_id**：仅应用于主账号查询

## 业务优势

### 1. 层级清晰
- 主账号和子账号的关系一目了然
- 便于前端树形展示
- 符合业务逻辑

### 2. 数据完整
- 每个主账号包含完整的子账号信息
- 子账号自动填充父账号名称
- 减少前端数据处理复杂度

### 3. 查询高效
- 分页基于主账号数量
- 避免子账号过多导致的分页问题
- 支持灵活的筛选条件

## 前端适配建议

### 1. 数据结构处理
```javascript
// 处理层级数据
const processAccountData = (hierarchyData) => {
  const flatList = []
  
  hierarchyData.list.forEach(masterAccount => {
    // 添加主账号
    flatList.push({
      ...masterAccount,
      level: 0,
      hasChildren: masterAccount.sub_accounts.length > 0
    })
    
    // 添加子账号
    masterAccount.sub_accounts.forEach(subAccount => {
      flatList.push({
        ...subAccount,
        level: 1,
        hasChildren: false
      })
    })
  })
  
  return flatList
}
```

### 2. 树形展示
```vue
<template>
  <el-table :data="accountList" row-key="id">
    <el-table-column label="账号名称">
      <template #default="scope">
        <span :style="{ paddingLeft: scope.row.level * 20 + 'px' }">
          <i v-if="scope.row.level === 0" class="el-icon-folder"></i>
          <i v-else class="el-icon-document"></i>
          {{ scope.row.account_name }}
        </span>
      </template>
    </el-table-column>
    <!-- 其他列 -->
  </el-table>
</template>
```

### 3. 操作权限
```javascript
// 根据账号类型控制操作权限
const getAccountActions = (account) => {
  const actions = []
  
  if (account.account_type === 1) {
    // 主账号操作
    actions.push('edit', 'addSubAccount', 'authorize')
    if (account.sub_accounts.length === 0) {
      actions.push('delete')
    }
  } else {
    // 子账号操作
    actions.push('edit', 'delete')
  }
  
  return actions
}
```

## 兼容性说明

### 1. API路径不变
- 接口路径保持 `GET /api/v1/ad-accounts`
- 请求参数保持兼容

### 2. 响应结构变化
- 从平铺列表改为层级结构
- 需要前端适配新的数据结构
- 建议逐步迁移

### 3. 分页逻辑变化
- 分页基于主账号数量
- total 表示主账号总数
- 子账号不计入分页总数

## 使用示例

### 1. 获取所有账号
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts" \
  -H "Authorization: Bearer your_token"
```

### 2. 筛选特定平台账号
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts?platform=1" \
  -H "Authorization: Bearer your_token"
```

### 3. 搜索账号名称
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts?account_name=营销树" \
  -H "Authorization: Bearer your_token"
```

### 4. 分页查询
```bash
curl -X GET "http://localhost:8080/api/v1/ad-accounts?page=1&page_size=10" \
  -H "Authorization: Bearer your_token"
```

## 注意事项

1. **分页计算**：分页基于主账号数量，不包含子账号
2. **筛选逻辑**：筛选条件同时应用于主账号和子账号
3. **权限控制**：确保用户只能查看有权限的账号
4. **性能考虑**：大量子账号时可能影响响应时间
5. **数据一致性**：确保父子账号关系的数据一致性
