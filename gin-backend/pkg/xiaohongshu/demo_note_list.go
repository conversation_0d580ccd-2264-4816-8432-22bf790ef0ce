package xiaohongshu

import (
	"fmt"
	"log"
)

// DemoNoteListAPI 演示笔记列表API的使用
func DemoNoteListAPI() {
	fmt.Println("=== 小红书笔记列表API演示 ===\n")

	// 1. 创建配置和客户端
	config := &Config{
		AppId:  "your_app_id", // 替换为实际的App ID
		Secret: "your_secret", // 替换为实际的Secret
		IsProd: false,         // 测试环境，生产环境设置为true
	}
	// client := NewClient(config)
	// helper := NewNoteListHelper(client)

	// 模拟访问令牌和广告主ID（实际使用时需要通过OAuth获取）
	// accessToken := "your_access_token"  // 替换为实际的访问令牌
	advertiserID := int64(123456) // 替换为实际的广告主ID

	fmt.Printf("配置信息:\n")
	fmt.Printf("  App ID: %s\n", config.AppId)
	fmt.Printf("  环境: %s\n", map[bool]string{true: "生产环境", false: "测试环境"}[config.IsProd])
	fmt.Printf("  API地址: %s\n", config.GetBaseUrl())
	fmt.Printf("  广告主ID: %d\n\n", advertiserID)

	// 2. 基本笔记列表请求示例
	fmt.Println("1. 基本笔记列表请求")
	fmt.Println("-------------------")

	request := &NoteListRequest{
		AdvertiserID:    advertiserID,
		NoteType:        2, // 合作笔记
		Page:            1,
		PageSize:        5,     // 只获取5条用于演示
		BaseOnly:        false, // 获取完整信息
		OrderField:      "create_time",
		OrderType:       "desc",
		NoteContentType: 1, // 图文笔记
	}

	// 验证请求参数
	if err := ValidateNoteListRequest(request); err != nil {
		log.Printf("❌ 请求参数验证失败: %v\n", err)
	} else {
		fmt.Println("✅ 请求参数验证通过")
	}

	// 模拟API调用（实际使用时取消注释）
	/*
		response, err := client.GetNoteList(accessToken, request)
		if err != nil {
			log.Printf("❌ 获取笔记列表失败: %v\n", err)
		} else {
			fmt.Printf("✅ 成功获取 %d 条笔记\n", len(response.Data.Notes))
			printNotesSummary(response.Data.Notes)
		}
	*/
	fmt.Println("📝 模拟API调用（请配置真实的访问令牌后使用）")

	// 3. 使用辅助工具的示例
	fmt.Println("\n2. 使用辅助工具")
	fmt.Println("---------------")

	// 演示不同类型的笔记获取
	noteTypes := map[string]int{
		"我的笔记":  1,
		"合作笔记":  2,
		"主理人笔记": 4,
		"员工笔记":  6,
		"授权笔记":  11,
	}

	for typeName, typeCode := range noteTypes {
		fmt.Printf("📋 %s (类型代码: %d)\n", typeName, typeCode)

		// 模拟调用（实际使用时取消注释）
		/*
			switch typeCode {
			case 1:
				notes, err := helper.GetMyNotes(accessToken, advertiserID, 1, 3)
			case 2:
				notes, err := helper.GetCooperationNotes(accessToken, advertiserID, 1, 3)
			case 6:
				notes, err := helper.GetStaffNotes(accessToken, advertiserID, 1, 3)
			case 11:
				notes, err := helper.GetAuthorizedNotes(accessToken, advertiserID, 1, 3)
			}
		*/
	}

	// 4. 搜索功能示例
	fmt.Println("\n3. 搜索功能")
	fmt.Println("-----------")

	searchKeywords := []string{"美妆", "护肤", "时尚", "生活"}
	for _, keyword := range searchKeywords {
		fmt.Printf("🔍 搜索关键词: %s\n", keyword)

		// 模拟搜索调用（实际使用时取消注释）
		/*
			searchResults, err := helper.SearchNotesByKeyword(accessToken, advertiserID, 2, keyword, 1, 3)
			if err != nil {
				log.Printf("❌ 搜索失败: %v\n", err)
			} else {
				fmt.Printf("✅ 找到 %d 条相关笔记\n", len(searchResults.Data.Notes))
			}
		*/
	}

	// 5. 筛选功能示例
	fmt.Println("\n4. 筛选功能")
	fmt.Println("-----------")

	fmt.Println("📊 按内容类型筛选:")
	contentTypes := map[string]int{
		"图文笔记": 1,
		"视频笔记": 2,
	}

	for typeName, typeCode := range contentTypes {
		fmt.Printf("  %s (代码: %d)\n", typeName, typeCode)

		// 模拟筛选调用（实际使用时取消注释）
		/*
			filteredNotes, err := helper.GetNotesByContentType(accessToken, advertiserID, 2, typeCode, 1, 3)
			if err != nil {
				log.Printf("❌ 筛选失败: %v\n", err)
			} else {
				fmt.Printf("✅ 找到 %d 条%s\n", len(filteredNotes.Data.Notes), typeName)
			}
		*/
	}

	// 6. 高级功能示例
	fmt.Println("\n5. 高级功能")
	fmt.Println("-----------")

	fmt.Println("⭐ 获取优质笔记:")
	// 模拟获取优质笔记（实际使用时取消注释）
	/*
		highQualityNotes, err := helper.GetHighQualityNotes(accessToken, advertiserID, 2, 1, 10)
		if err != nil {
			log.Printf("❌ 获取优质笔记失败: %v\n", err)
		} else {
			fmt.Printf("✅ 找到 %d 条优质笔记\n", len(highQualityNotes.Data.Notes))
		}
	*/

	fmt.Println("📚 获取所有笔记（自动分页）:")
	// 模拟获取所有笔记（实际使用时取消注释）
	/*
		allNotes, err := helper.GetAllNotes(accessToken, advertiserID, 2)
		if err != nil {
			log.Printf("❌ 获取所有笔记失败: %v\n", err)
		} else {
			fmt.Printf("✅ 总共获取到 %d 条笔记\n", len(allNotes))

			// 统计分析
			analyzeNotes(allNotes)
		}
	*/

	// 7. 使用建议
	fmt.Println("\n6. 使用建议")
	fmt.Println("-----------")
	fmt.Println("💡 最佳实践:")
	fmt.Println("  1. 使用ValidateNoteListRequest验证请求参数")
	fmt.Println("  2. 合理设置页面大小（建议20-50条）")
	fmt.Println("  3. 使用辅助工具简化常见操作")
	fmt.Println("  4. 对于大量数据，使用GetAllNotes自动分页")
	fmt.Println("  5. 根据业务需求选择base_only参数")
	fmt.Println("  6. 适当添加延迟避免请求过于频繁")

	fmt.Println("\n⚠️  注意事项:")
	fmt.Println("  1. 确保访问令牌有效且未过期")
	fmt.Println("  2. 广告主ID必须是授权的广告主")
	fmt.Println("  3. 注意API调用频率限制")
	fmt.Println("  4. 处理网络错误和API错误")
	fmt.Println("  5. 敏感信息不要硬编码在代码中")

	fmt.Println("\n=== 演示完成 ===")
}

// printNotesSummary 打印笔记摘要信息
func printNotesSummary(notes []BaseNoteItem) {
	if len(notes) == 0 {
		fmt.Println("  📝 暂无笔记")
		return
	}

	fmt.Printf("  📝 笔记列表 (%d条):\n", len(notes))
	for i, note := range notes {
		fmt.Printf("    %d. %s\n", i+1, note.Title)
		fmt.Printf("       作者: %s | 阅读: %d | 互动: %d\n",
			note.Author, note.ReadCount, note.InteractCount)
		if note.HighQuality == 1 {
			fmt.Printf("       ⭐ 优质笔记\n")
		}
		if note.HighPotential == 1 {
			fmt.Printf("       🚀 高潜笔记\n")
		}
	}
}

// analyzeNotes 分析笔记数据
func analyzeNotes(notes []BaseNoteItem) {
	if len(notes) == 0 {
		return
	}

	var totalRead, totalInteract int
	var highQualityCount, highPotentialCount int
	contentTypeCount := make(map[int]int)

	for _, note := range notes {
		totalRead += note.ReadCount
		totalInteract += note.InteractCount
		if note.HighQuality == 1 {
			highQualityCount++
		}
		if note.HighPotential == 1 {
			highPotentialCount++
		}
		contentTypeCount[note.NoteContentType]++
	}

	fmt.Printf("  📊 数据分析:\n")
	fmt.Printf("    总阅读数: %d\n", totalRead)
	fmt.Printf("    总互动数: %d\n", totalInteract)
	fmt.Printf("    优质笔记: %d 条 (%.1f%%)\n",
		highQualityCount, float64(highQualityCount)/float64(len(notes))*100)
	fmt.Printf("    高潜笔记: %d 条 (%.1f%%)\n",
		highPotentialCount, float64(highPotentialCount)/float64(len(notes))*100)
	fmt.Printf("    图文笔记: %d 条\n", contentTypeCount[1])
	fmt.Printf("    视频笔记: %d 条\n", contentTypeCount[2])
	fmt.Printf("    平均阅读数: %.1f\n", float64(totalRead)/float64(len(notes)))
	fmt.Printf("    平均互动数: %.1f\n", float64(totalInteract)/float64(len(notes)))
}

// RunNoteListDemo 运行笔记列表API演示
// 这个函数可以在main函数中调用来演示功能
func RunNoteListDemo() {
	DemoNoteListAPI()
}
