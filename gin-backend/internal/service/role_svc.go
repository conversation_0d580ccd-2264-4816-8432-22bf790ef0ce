package service

import (
	"context"
	"errors"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// RoleService 角色服务
type RoleService struct {
	db *gorm.DB
}

// NewRoleService 创建角色服务实例
func NewRoleService() *RoleService {
	return &RoleService{
		db: global.DB,
	}
}

// ======================== 角色管理 ========================

// GetRoles 获取角色列表
func (s *RoleService) GetRoles(ctx context.Context, param domain.RoleListParam) (domain.RoleListEntity, error) {
	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}

	// 构建筛选条件
	query := s.db.WithContext(ctx).Model(&model.Role{})

	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("code LIKE ?", "%"+param.Code+"%")
	}
	if param.Status != 0 { // 0表示不筛选状态
		query = query.Where("status = ?", param.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.RoleListEntity{}, fmt.Errorf("获取角色总数失败: %w", err)
	}

	// 获取分页数据
	var roles []model.Role
	if err := query.Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Order("sort_order ASC, id ASC").
		Find(&roles).Error; err != nil {
		return domain.RoleListEntity{}, fmt.Errorf("获取角色列表失败: %w", err)
	}

	// 转换为响应格式
	roleEntities := make([]domain.RoleEntity, 0, len(roles))
	for _, role := range roles {
		entity := domain.RoleEntity{
			ID:          role.ID,
			Name:        role.Name,
			Code:        role.Code,
			Description: role.Description,
			Status:      role.Status,
			IsSystem:    role.IsSystem,
			CreatedAt:   role.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   role.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		roleEntities = append(roleEntities, entity)
	}

	return domain.RoleListEntity{
		List:  roleEntities,
		Total: total,
	}, nil
}

// GetRoleByID 根据ID获取角色
func (s *RoleService) GetRoleByID(ctx context.Context, id int64) (domain.RoleEntity, error) {
	if id == 0 {
		return domain.RoleEntity{}, errors.New("角色ID不能为空")
	}

	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.RoleEntity{}, errors.New("角色不存在")
		}
		return domain.RoleEntity{}, fmt.Errorf("获取角色失败: %w", err)
	}

	return domain.RoleEntity{
		ID:          role.ID,
		Name:        role.Name,
		Code:        role.Code,
		Description: role.Description,
		Status:      role.Status,
		IsSystem:    role.IsSystem,
		CreatedAt:   role.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   role.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, entity domain.RoleEntity) (domain.RoleEntity, error) {
	// 检查角色编码是否已存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Role{}).
		Where("code = ?", entity.Code).
		Count(&count).Error; err != nil {
		return domain.RoleEntity{}, fmt.Errorf("检查角色编码失败: %w", err)
	}
	if count > 0 {
		return domain.RoleEntity{}, errors.New("角色编码已存在")
	}

	// 创建角色实体
	role := &model.Role{
		Name:        entity.Name,
		Code:        entity.Code,
		Description: entity.Description,
		Status:      entity.Status,
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(role).Error; err != nil {
		return domain.RoleEntity{}, fmt.Errorf("创建角色失败: %w", err)
	}

	return domain.RoleEntity{
		ID:          role.ID,
		Name:        role.Name,
		Code:        role.Code,
		Description: role.Description,
		Status:      role.Status,
		IsSystem:    role.IsSystem,
		CreatedAt:   role.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   role.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// UpdateRole 更新角色
func (s *RoleService) UpdateRole(ctx context.Context, id int64, entity domain.RoleEntity) (domain.RoleEntity, error) {
	if id == 0 {
		return domain.RoleEntity{}, errors.New("角色ID不能为空")
	}

	// 获取现有角色
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.RoleEntity{}, errors.New("角色不存在")
		}
		return domain.RoleEntity{}, fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查是否为系统角色
	if role.IsSystem == 1 {
		return domain.RoleEntity{}, errors.New("系统角色不允许修改")
	}

	// 检查角色编码是否已存在（排除当前角色）
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Role{}).
		Where("code = ? AND id != ?", entity.Code, id).
		Count(&count).Error; err != nil {
		return domain.RoleEntity{}, fmt.Errorf("检查角色编码失败: %w", err)
	}
	if count > 0 {
		return domain.RoleEntity{}, errors.New("角色编码已存在")
	}

	// 更新角色信息
	role.Name = entity.Name
	role.Code = entity.Code
	role.Description = entity.Description
	role.Status = entity.Status

	// 保存到数据库
	if err := s.db.WithContext(ctx).Save(&role).Error; err != nil {
		return domain.RoleEntity{}, fmt.Errorf("更新角色失败: %w", err)
	}

	return domain.RoleEntity{
		ID:          role.ID,
		Name:        role.Name,
		Code:        role.Code,
		Description: role.Description,
		Status:      role.Status,
		IsSystem:    role.IsSystem,
		CreatedAt:   role.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   role.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(ctx context.Context, id int64) error {
	if id == 0 {
		return errors.New("角色ID不能为空")
	}

	// 获取现有角色
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 检查是否为系统角色
	if role.IsSystem == 1 {
		return errors.New("系统角色不允许删除")
	}

	// 检查是否有用户关联
	var userCount int64
	if err := s.db.WithContext(ctx).Model(&model.User{}).
		Where("role_id = ?", id).
		Count(&userCount).Error; err != nil {
		return fmt.Errorf("检查角色关联用户失败: %w", err)
	}
	if userCount > 0 {
		return errors.New("该角色下存在用户，不允许删除")
	}

	// 删除角色权限关联
	if err := s.db.WithContext(ctx).Where("role_id = ?", id).
		Delete(&model.RolePermission{}).Error; err != nil {
		return fmt.Errorf("删除角色权限关联失败: %w", err)
	}

	// 删除角色
	if err := s.db.WithContext(ctx).Delete(&role).Error; err != nil {
		return fmt.Errorf("删除角色失败: %w", err)
	}

	return nil
}

// GetRoleOptions 获取角色选项列表
func (s *RoleService) GetRoleOptions(ctx context.Context) (domain.RoleOptionsEntity, error) {
	var roles []model.Role
	if err := s.db.WithContext(ctx).
		Where("status = ?", 1).
		Order("sort_order ASC, id ASC").
		Find(&roles).Error; err != nil {
		return domain.RoleOptionsEntity{}, fmt.Errorf("获取角色列表失败: %w", err)
	}

	options := make([]domain.RoleOption, 0, len(roles))
	for _, role := range roles {
		options = append(options, domain.RoleOption{
			ID:   role.ID,
			Name: role.Name,
			Code: role.Code,
		})
	}

	return domain.RoleOptionsEntity{
		Options: options,
	}, nil
}

// ======================== 权限管理 ========================

// GetPermissions 获取权限列表
func (s *RoleService) GetPermissions(ctx context.Context, param domain.PermissionListParam) (domain.PermissionListEntity, error) {
	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}

	// 构建筛选条件
	query := s.db.WithContext(ctx).Model(&model.Permission{})

	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("code LIKE ?", "%"+param.Code+"%")
	}
	if param.Module != "" {
		query = query.Where("module = ?", param.Module)
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}
	if param.DataScope != "" {
		query = query.Where("data_scope = ?", param.DataScope)
	}
	if param.Status != 0 { // 0表示不筛选状态
		query = query.Where("status = ?", param.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.PermissionListEntity{}, fmt.Errorf("获取权限总数失败: %w", err)
	}

	// 获取分页数据
	var permissions []model.Permission
	if err := query.Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Order("module ASC, id ASC").
		Find(&permissions).Error; err != nil {
		return domain.PermissionListEntity{}, fmt.Errorf("获取权限列表失败: %w", err)
	}

	// 转换为响应格式
	permissionEntities := make([]domain.PermissionEntity, 0, len(permissions))
	for _, perm := range permissions {
		permissionEntities = append(permissionEntities, domain.PermissionEntity{
			ID:          perm.ID,
			Name:        perm.Name,
			Code:        perm.Code,
			Module:      perm.Module,
			Type:        perm.Type,
			DataScope:   perm.DataScope,
			Description: perm.Description,
			Status:      perm.Status,
			CreatedAt:   perm.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   perm.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return domain.PermissionListEntity{
		List:  permissionEntities,
		Total: total,
	}, nil
}

// GetPermissionByID 根据ID获取权限
func (s *RoleService) GetPermissionByID(ctx context.Context, id int64) (domain.PermissionEntity, error) {
	if id == 0 {
		return domain.PermissionEntity{}, errors.New("权限ID不能为空")
	}

	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.PermissionEntity{}, errors.New("权限不存在")
		}
		return domain.PermissionEntity{}, fmt.Errorf("获取权限失败: %w", err)
	}

	return domain.PermissionEntity{
		ID:          permission.ID,
		Name:        permission.Name,
		Code:        permission.Code,
		Module:      permission.Module,
		Type:        permission.Type,
		DataScope:   permission.DataScope,
		Description: permission.Description,
		Status:      permission.Status,
		CreatedAt:   permission.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   permission.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// CreatePermission 创建权限
func (s *RoleService) CreatePermission(ctx context.Context, entity domain.PermissionEntity) (domain.PermissionEntity, error) {
	// 检查权限编码是否已存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Permission{}).
		Where("code = ?", entity.Code).
		Count(&count).Error; err != nil {
		return domain.PermissionEntity{}, fmt.Errorf("检查权限编码失败: %w", err)
	}
	if count > 0 {
		return domain.PermissionEntity{}, errors.New("权限编码已存在")
	}

	// 创建权限实体
	permission := &model.Permission{
		Name:        entity.Name,
		Code:        entity.Code,
		Module:      entity.Module,
		Type:        entity.Type,
		DataScope:   entity.DataScope,
		Description: entity.Description,
		Status:      entity.Status,
	}

	// 保存到数据库
	if err := s.db.WithContext(ctx).Create(permission).Error; err != nil {
		return domain.PermissionEntity{}, fmt.Errorf("创建权限失败: %w", err)
	}

	return domain.PermissionEntity{
		ID:          permission.ID,
		Name:        permission.Name,
		Code:        permission.Code,
		Module:      permission.Module,
		Type:        permission.Type,
		DataScope:   permission.DataScope,
		Description: permission.Description,
		Status:      permission.Status,
		CreatedAt:   permission.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   permission.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// UpdatePermission 更新权限
func (s *RoleService) UpdatePermission(ctx context.Context, id int64, entity domain.PermissionEntity) (domain.PermissionEntity, error) {
	if id == 0 {
		return domain.PermissionEntity{}, errors.New("权限ID不能为空")
	}

	// 获取现有权限
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.PermissionEntity{}, errors.New("权限不存在")
		}
		return domain.PermissionEntity{}, fmt.Errorf("获取权限失败: %w", err)
	}

	// 检查权限编码是否已存在（排除当前权限）
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.Permission{}).
		Where("code = ? AND id != ?", entity.Code, id).
		Count(&count).Error; err != nil {
		return domain.PermissionEntity{}, fmt.Errorf("检查权限编码失败: %w", err)
	}
	if count > 0 {
		return domain.PermissionEntity{}, errors.New("权限编码已存在")
	}

	// 更新权限信息
	permission.Name = entity.Name
	permission.Code = entity.Code
	permission.Module = entity.Module
	permission.Type = entity.Type
	permission.DataScope = entity.DataScope
	permission.Description = entity.Description
	permission.Status = entity.Status

	// 保存到数据库
	if err := s.db.WithContext(ctx).Save(&permission).Error; err != nil {
		return domain.PermissionEntity{}, fmt.Errorf("更新权限失败: %w", err)
	}

	return domain.PermissionEntity{
		ID:          permission.ID,
		Name:        permission.Name,
		Code:        permission.Code,
		Module:      permission.Module,
		Type:        permission.Type,
		DataScope:   permission.DataScope,
		Description: permission.Description,
		Status:      permission.Status,
		CreatedAt:   permission.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   permission.UpdatedAt.Format("2006-01-02 15:04:05"),
	}, nil
}

// DeletePermission 删除权限
func (s *RoleService) DeletePermission(ctx context.Context, id int64) error {
	if id == 0 {
		return errors.New("权限ID不能为空")
	}

	// 获取现有权限
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("权限不存在")
		}
		return fmt.Errorf("获取权限失败: %w", err)
	}

	// 检查是否有角色关联此权限
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.RolePermission{}).
		Where("permission_id = ?", id).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查权限关联失败: %w", err)
	}
	if count > 0 {
		return errors.New("该权限已被角色关联，不允许删除")
	}

	// 删除权限
	if err := s.db.WithContext(ctx).Delete(&permission).Error; err != nil {
		return fmt.Errorf("删除权限失败: %w", err)
	}

	return nil
}

// GetPermissionModules 获取权限模块
func (s *RoleService) GetPermissionModules(ctx context.Context) (domain.PermissionModulesEntity, error) {
	var modules []string
	if err := s.db.WithContext(ctx).Model(&model.Permission{}).
		Distinct("module").
		Order("module ASC").
		Pluck("module", &modules).Error; err != nil {
		return domain.PermissionModulesEntity{}, fmt.Errorf("获取权限模块失败: %w", err)
	}

	moduleEntities := make([]domain.PermissionModule, 0, len(modules))
	for _, module := range modules {
		moduleEntities = append(moduleEntities, domain.PermissionModule{
			Name: module,
			Code: module,
		})
	}

	return domain.PermissionModulesEntity{
		Modules: moduleEntities,
	}, nil
}

// ======================== 角色权限关联管理 ========================

// GetRolePermissions 获取角色权限
func (s *RoleService) GetRolePermissions(ctx context.Context, roleID int64) (domain.RolePermissionEntity, error) {
	if roleID == 0 {
		return domain.RolePermissionEntity{}, errors.New("角色ID不能为空")
	}

	// 获取角色权限关联
	var rolePermissions []model.RolePermission
	if err := s.db.WithContext(ctx).
		Where("role_id = ?", roleID).
		Find(&rolePermissions).Error; err != nil {
		return domain.RolePermissionEntity{}, fmt.Errorf("获取角色权限关联失败: %w", err)
	}

	// 提取权限ID
	permissionIDs := make([]int64, 0, len(rolePermissions))
	for _, rp := range rolePermissions {
		permissionIDs = append(permissionIDs, rp.PermissionID)
	}

	return domain.RolePermissionEntity{
		PermissionIDs: permissionIDs,
	}, nil
}

// AssignPermissions 分配权限
func (s *RoleService) AssignPermissions(ctx context.Context, entity domain.AssignPermissionEntity, operatorID int64) error {
	if entity.RoleID == 0 {
		return errors.New("角色ID不能为空")
	}
	if len(entity.PermissionIDs) == 0 {
		return errors.New("权限ID列表不能为空")
	}

	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, entity.RoleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 获取现有权限
	var existingPermissions []model.RolePermission
	if err := s.db.WithContext(ctx).
		Where("role_id = ?", entity.RoleID).
		Find(&existingPermissions).Error; err != nil {
		return fmt.Errorf("获取现有权限失败: %w", err)
	}

	// 找出新增的权限ID
	existingPermMap := make(map[int64]bool)
	for _, perm := range existingPermissions {
		existingPermMap[perm.PermissionID] = true
	}

	newPermissions := make([]model.RolePermission, 0)
	for _, permID := range entity.PermissionIDs {
		if !existingPermMap[permID] {
			newPermissions = append(newPermissions, model.RolePermission{
				RoleID:       entity.RoleID,
				PermissionID: permID,
			})
		}
	}

	if len(newPermissions) == 0 {
		return nil // 没有新增权限
	}

	// 开启事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 批量创建权限关联
		if err := tx.CreateInBatches(newPermissions, 100).Error; err != nil {
			return fmt.Errorf("批量创建权限关联失败: %w", err)
		}

		// 记录操作日志
		for _, perm := range newPermissions {
			var permission model.Permission
			if err := tx.First(&permission, perm.PermissionID).Error; err != nil {
				continue
			}

			// 创建操作日志
			log := model.PermissionLog{
				RoleID:       entity.RoleID,
				PermissionID: perm.PermissionID,
				Action:       "assign",
				OperatorID:   operatorID,
			}
			if err := tx.Create(&log).Error; err != nil {
				return fmt.Errorf("创建操作日志失败: %w", err)
			}
		}

		return nil
	})
}

// RevokePermissions 撤销权限
func (s *RoleService) RevokePermissions(ctx context.Context, entity domain.RevokePermissionEntity, operatorID int64) error {
	if entity.RoleID == 0 {
		return errors.New("角色ID不能为空")
	}
	if len(entity.PermissionIDs) == 0 {
		return errors.New("权限ID列表不能为空")
	}

	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, entity.RoleID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("角色不存在")
		}
		return fmt.Errorf("获取角色失败: %w", err)
	}

	// 开启事务
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取要撤销的权限信息
		var permissions []model.Permission
		if err := tx.Where("id IN ?", entity.PermissionIDs).Find(&permissions).Error; err != nil {
			return fmt.Errorf("获取权限信息失败: %w", err)
		}

		// 批量删除权限关联
		if err := tx.Where("role_id = ? AND permission_id IN ?", entity.RoleID, entity.PermissionIDs).
			Delete(&model.RolePermission{}).Error; err != nil {
			return fmt.Errorf("批量删除权限关联失败: %w", err)
		}

		// 记录操作日志
		for _, perm := range permissions {
			log := model.PermissionLog{
				RoleID:       entity.RoleID,
				PermissionID: perm.ID,
				Action:       "revoke",
				OperatorID:   operatorID,
			}
			if err := tx.Create(&log).Error; err != nil {
				return fmt.Errorf("创建操作日志失败: %w", err)
			}
		}

		return nil
	})
}

// ======================== 用户权限管理 ========================

// CheckUserPermission 检查用户是否有权限
func (s *RoleService) CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error) {
	if userID == 0 {
		return false, errors.New("用户ID不能为空")
	}
	if permissionCode == "" {
		return false, errors.New("权限编码不能为空")
	}

	// 查询用户的角色ID
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, errors.New("用户不存在")
		}
		return false, fmt.Errorf("获取用户失败: %w", err)
	}

	// 首先检查是否为超级管理员
	if user.RoleID == 1 { // 假设ID为1的角色是超级管理员
		return true, nil
	}

	// 查询用户所属角色的权限编码
	var permissionCodes []string
	query := s.db.WithContext(ctx).
		Table("permissions").
		Select("permissions.code").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", user.RoleID).
		Where("permissions.status = ?", 1) // 只查询启用状态的权限

	if err := query.Pluck("code", &permissionCodes).Error; err != nil {
		return false, fmt.Errorf("获取用户权限失败: %w", err)
	}

	// 检查是否包含目标权限编码
	for _, code := range permissionCodes {
		if code == permissionCode {
			return true, nil
		}
	}

	return false, nil
}

// GetUserPermissions 获取用户权限
func (s *RoleService) GetUserPermissions(ctx context.Context, userID int64) (domain.UserPermissionEntity, error) {
	if userID == 0 {
		return domain.UserPermissionEntity{}, errors.New("用户ID不能为空")
	}

	// 查询用户信息
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.UserPermissionEntity{}, errors.New("用户不存在")
		}
		return domain.UserPermissionEntity{}, fmt.Errorf("获取用户失败: %w", err)
	}

	// 查询用户角色
	var role model.Role
	var roles []domain.RoleEntity
	if user.RoleID > 0 {
		if err := s.db.WithContext(ctx).First(&role, user.RoleID).Error; err == nil {
			roles = append(roles, domain.RoleEntity{
				ID:          role.ID,
				Name:        role.Name,
				Code:        role.Code,
				Description: role.Description,
				Status:      role.Status,
				IsSystem:    role.IsSystem,
			})
		}
	}

	// 查询用户所拥有的权限
	var permissionCodes []string
	query := s.db.WithContext(ctx).
		Table("permissions").
		Select("permissions.code").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", user.RoleID).
		Where("permissions.status = ?", 1) // 只查询启用状态的权限

	if err := query.Pluck("code", &permissionCodes).Error; err != nil {
		return domain.UserPermissionEntity{}, fmt.Errorf("获取用户权限失败: %w", err)
	}

	return domain.UserPermissionEntity{
		Roles:       roles,
		Permissions: permissionCodes,
	}, nil
}

// ======================== 权限操作日志 ========================

// GetPermissionLogs 获取权限日志
func (s *RoleService) GetPermissionLogs(ctx context.Context, param domain.PermissionLogListParam) (domain.PermissionLogListEntity, error) {
	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}

	// 构建查询
	query := s.db.WithContext(ctx).
		Model(&model.PermissionLog{}).
		Preload("Role").
		Preload("Permission").
		Preload("Operator")

	// 添加筛选条件
	if param.RoleID > 0 {
		query = query.Where("role_id = ?", param.RoleID)
	}
	if param.OperatorID > 0 {
		query = query.Where("operator_id = ?", param.OperatorID)
	}
	if param.Action != "" {
		query = query.Where("action = ?", param.Action)
	}
	if param.PermissionID > 0 {
		query = query.Where("permission_id = ?", param.PermissionID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.PermissionLogListEntity{}, fmt.Errorf("获取权限日志总数失败: %w", err)
	}

	// 获取分页数据
	var logs []model.PermissionLog
	if err := query.Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Order("id DESC").
		Find(&logs).Error; err != nil {
		return domain.PermissionLogListEntity{}, fmt.Errorf("获取权限日志列表失败: %w", err)
	}

	// 转换为响应格式
	logEntities := make([]domain.PermissionLogEntity, 0, len(logs))
	for _, log := range logs {
		// 获取操作人名称
		operatorName := ""
		if log.Operator.ID > 0 {
			operatorName = log.Operator.Name // 使用 User 模型的 Name 字段
		}

		// 获取角色名称
		roleName := ""
		if log.Role.ID > 0 {
			roleName = log.Role.Name
		}

		// 获取权限名称
		permissionName := ""
		if log.Permission.ID > 0 {
			permissionName = log.Permission.Name
		}

		logEntities = append(logEntities, domain.PermissionLogEntity{
			ID:           log.ID,
			RoleID:       log.RoleID,
			RoleName:     roleName,
			PermissionID: log.PermissionID,
			Permission:   permissionName,
			Action:       log.Action,
			OperatorID:   log.OperatorID,
			OperatorName: operatorName,
			CreatedAt:    log.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return domain.PermissionLogListEntity{
		Total: total,
		List:  logEntities,
	}, nil
}
