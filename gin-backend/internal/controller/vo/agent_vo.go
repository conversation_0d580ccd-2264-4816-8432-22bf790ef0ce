package vo

import (
	"gin-backend/internal/service/domain"
)

// PlatformConfigVO 平台配置结构
type PlatformConfig struct {
	Platform string         `json:"platform"` // 平台类型 denghuoplus:灯火 xiaohongshu:小红书
	Info     map[string]any `json:"info"`     // 平台配置信息
}

// ToDomain 将VO转换为Domain对象
func (p PlatformConfig) ToDomain() domain.PlatformConfig {
	return domain.PlatformConfig{
		Platform: p.Platform,
		Info:     p.Info,
	}
}

// FromDomain 从Domain对象转换为VO
func (p *PlatformConfig) FromDomain(config domain.PlatformConfig) {
	p.Platform = config.Platform
	p.Info = config.Info
}

// AgentListRequestVO 代理列表请求结构
type AgentListReq struct {
	Name        string `json:"name" form:"name"`                 // 代理名称
	Type        string `json:"type" form:"type"`                 // 代理类型
	AuditStatus string `json:"audit_status" form:"audit_status"` // 审核状态
	MediaID     int64  `json:"media_id" form:"media_id"`         // 媒介ID
	Page        int    `json:"page" form:"page"`                 // 页码
	PageSize    int    `json:"page_size" form:"size"`            // 每页数量，前端使用size参数
}

// ToDomain 将VO转换为Domain对象
func (r AgentListReq) ToDomain() domain.AgentParam {
	return domain.AgentParam{
		Name:        r.Name,
		Type:        r.Type,
		AuditStatus: r.AuditStatus,
		MediaID:     r.MediaID,
		Page:        r.Page,
		PageSize:    r.PageSize,
	}
}

// AgentInfoVO 代理信息结构
type AgentItem struct {
	ID                int64          `json:"id"`
	Code              string         `json:"code"`
	Name              string         `json:"name"`
	Type              string         `json:"type"`
	UserID            int64          `json:"user_id"`
	AuditStatus       string         `json:"audit_status"`
	CooperationStatus string         `json:"cooperation_status"`
	CompanyName       string         `json:"company_name"`
	CompanyAddress    string         `json:"company_address"`
	ContactName       string         `json:"contact_name"`
	ContactPhone      string         `json:"contact_phone"`
	RejectReason      string         `json:"reject_reason"`
	Remarks           string         `json:"remarks"`
	PlatformConfig    PlatformConfig `json:"platform_config"`
}

// FromDomain 从Domain对象转换为VO
func (a *AgentItem) FromDomain(info domain.AgentInfo) {
	a.ID = info.ID
	a.Code = info.Code
	a.Name = info.Name
	a.Type = info.Type
	a.UserID = info.UserID
	a.AuditStatus = info.AuditStatus
	a.CooperationStatus = info.CooperationStatus
	a.CompanyName = info.CompanyName
	a.CompanyAddress = info.CompanyAddress
	a.ContactName = info.ContactName
	a.ContactPhone = info.ContactPhone
	a.RejectReason = info.RejectReason
	a.Remarks = info.Remarks
	a.PlatformConfig.FromDomain(info.PlatformConfig)

}

// AgentListResponseVO 代理列表响应结构
type AgentListResp struct {
	List     []*AgentItem `json:"list"`
	Total    int64        `json:"total"`
	Page     int          `json:"page"`
	PageSize int          `json:"page_size"`
}

// FromDomain 从Domain对象转换为VO
func (r *AgentListResp) FromDomain(result domain.AgentListResult) {
	r.Total = result.Total
	r.Page = result.Page
	r.PageSize = result.PageSize

	r.List = make([]*AgentItem, len(result.List))
	for i, info := range result.List {
		r.List[i] = &AgentItem{}
		r.List[i].FromDomain(info)
	}
}

// AgentCreateRequestVO 创建代理请求结构
type AgentCreateReq struct {
	Name           string         `json:"name" binding:"required"` // 代理名称
	Type           string         `json:"type" binding:"required"` // 代理类型
	CompanyName    string         `json:"company_name"`            // 公司名称
	CompanyAddress string         `json:"company_address"`         // 公司地址
	ContactName    string         `json:"contact_name"`            // 联系人
	ContactPhone   string         `json:"contact_phone"`           // 联系电话
	Remarks        string         `json:"remarks"`                 // 备注
	PlatformConfig PlatformConfig `json:"platform_config"`         // 平台配置
}

// ToDomain 将VO转换为Domain对象
func (r AgentCreateReq) ToDomain() domain.AgentEntity {
	return domain.AgentEntity{
		Name:           r.Name,
		Type:           r.Type,
		CompanyName:    r.CompanyName,
		CompanyAddress: r.CompanyAddress,
		ContactName:    r.ContactName,
		ContactPhone:   r.ContactPhone,
		Remarks:        r.Remarks,
		PlatformConfig: r.PlatformConfig.ToDomain(),
	}
}

// AgentCreateResponseVO 创建代理响应结构
type AgentCreateResp struct {
	ID int64 `json:"id"`
}

// AgentUpdateRequestVO 更新代理请求结构
type AgentUpdateReq struct {
	ID             int64          `json:"id" binding:"required"`   // 代理ID
	Name           string         `json:"name" binding:"required"` // 代理名称
	Type           string         `json:"type" binding:"required"` // 代理类型
	CompanyName    string         `json:"company_name"`            // 公司名称
	CompanyAddress string         `json:"company_address"`         // 公司地址
	ContactName    string         `json:"contact_name"`            // 联系人
	ContactPhone   string         `json:"contact_phone"`           // 联系电话
	Remarks        string         `json:"remarks"`                 // 备注
	PlatformConfig PlatformConfig `json:"platform_config"`         // 平台配置
}

// ToDomain 将VO转换为Domain对象
func (r AgentUpdateReq) ToDomain() domain.AgentEntity {
	return domain.AgentEntity{
		ID:             r.ID,
		Name:           r.Name,
		Type:           r.Type,
		CompanyName:    r.CompanyName,
		CompanyAddress: r.CompanyAddress,
		ContactName:    r.ContactName,
		ContactPhone:   r.ContactPhone,
		Remarks:        r.Remarks,
		PlatformConfig: r.PlatformConfig.ToDomain(),
	}
}

// AgentUpdateResponseVO 更新代理响应结构
type AgentUpdateResp struct{}

// AgentDeleteResponseVO 删除代理响应结构
type AgentDeleteResp struct{}

// AgentAuditRequestVO 审核代理请求结构
type AgentAuditReq struct {
	ID           int64  `json:"id" binding:"required"`                                   // 代理ID
	AuditStatus  string `json:"audit_status" binding:"required,oneof=approved rejected"` // 审核状态
	RejectReason string `json:"reject_reason"`                                           // 拒绝原因
}

// ToDomain 将VO转换为Domain对象
func (r AgentAuditReq) ToDomain() domain.AgentAuditEntity {
	return domain.AgentAuditEntity{
		ID:           r.ID,
		AuditStatus:  r.AuditStatus,
		RejectReason: r.RejectReason,
	}
}

// AgentAuditResponseVO 审核代理响应结构
type AgentAuditResp struct{}
