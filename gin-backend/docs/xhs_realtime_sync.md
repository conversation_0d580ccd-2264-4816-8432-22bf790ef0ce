# 小红书创意层级实时数据同步功能

## 功能概述

本功能实现了小红书创意层级实时报表数据的自动同步，包括：

1. **定时同步**：每小时自动同步当前小时的实时数据
2. **手动同步**：通过API接口手动触发实时数据同步
3. **数据存储**：实时数据存储在现有的 `xhs_creative_reports` 表中

## 核心组件

### 1. 数据模型
- **表名**：`xhs_creative_reports`
- **说明**：复用现有的创意报表表结构，实时数据和离线数据统一存储

### 2. 服务层
- **文件**：`internal/service/xhs_realtime_report_svc.go`
- **主要方法**：
  - `SyncCurrentHourReports()`: 同步当前小时的实时数据
  - `syncAccountRealtimeReports()`: 同步单个账号的实时数据
  - `convertToRealtimeReportModels()`: 转换API数据为数据库模型

### 3. 定时任务
- **文件**：`job/xhs_realtime_report_sync.go`
- **调度**：每小时执行一次（cron: `0 0 * * * *`）
- **超时**：15分钟

### 4. API接口
- **路由**：`POST /api/v1/xhs-reports/sync/realtime`
- **功能**：手动触发实时数据同步

## 数据同步流程

### 1. 账号筛选
```sql
SELECT * FROM ad_accounts 
WHERE platform = 1 
  AND usage_status = 1 
  AND token IS NOT NULL 
  AND token != ''
```

### 2. API调用
- **接口**：小红书创意层级实时报表API
- **时间范围**：当天（今日数据）
- **分页**：每页100条，自动分页获取全部数据

### 3. 数据转换
- 将API返回的实时数据转换为 `XHSCreativeReports` 模型
- 解析创意名称提取标题、口令、内容人员、投手姓名
- 转换各类指标数据（基础指标、互动指标、转化指标等）

### 4. 数据存储
- 批量插入数据库（每批1000条）
- 使用事务确保数据一致性

## 配置要求

### 1. 小红书API配置
```yaml
xiaohongshu:
  app_id: "your_app_id"
  secret: "your_secret"
  is_prod: true
```

### 2. 账号配置
- 确保小红书账号已正确配置在 `ad_accounts` 表中
- 账号状态为启用（`usage_status = 1`）
- 账号token有效且未过期

## API使用示例

### 手动触发实时数据同步

```bash
curl -X POST http://localhost:8080/api/v1/xhs-reports/sync/realtime \
  -H "Content-Type: application/json" \
  -d '{}'
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "实时数据同步任务已启动，请查看日志了解执行结果"
  }
}
```

## 监控和日志

### 1. 日志级别
- **INFO**：正常同步流程日志
- **ERROR**：同步失败错误日志
- **WARN**：警告信息（如账号token过期）

### 2. 关键日志
```
开始同步当前小时的小红书实时报表数据
找到小红书账号 count=3
开始同步账号实时报表数据 account_id=1 account_name=测试账号
获取实时报表数据 account_id=1 page=1 count=50
账号实时报表数据同步完成 account_id=1 total_count=150
小红书实时报表数据同步完成 success_count=3 error_count=0
```

## 数据特点

### 1. 实时性
- 数据更新频率：每小时
- 数据时效性：当日实时数据

### 2. 数据范围
- **基础指标**：消费、展现、点击、CTR、ACP、CPM
- **互动指标**：点赞、评论、收藏、关注、分享等
- **转化指标**：电商转化、销售线索、私信营销等
- **直播指标**：直播间观看、互动、转化数据

### 3. 数据去重
- 当前实现为追加模式，建议在查询时按时间范围去重
- 后续可考虑实现基于创意ID+日期的去重逻辑

## 故障排查

### 1. 常见问题
- **账号token过期**：检查 `ad_accounts` 表中的token状态
- **API调用失败**：检查网络连接和API配置
- **数据转换错误**：检查API响应格式是否变更

### 2. 调试方法
- 查看应用日志了解详细错误信息
- 使用手动同步接口测试单次同步
- 检查数据库中的数据是否正确插入

## 扩展功能

### 1. 数据查询优化
- 建议为 `account_id` + `time` 添加复合索引
- 考虑按日期分表存储大量历史数据

### 2. 监控告警
- 可集成监控系统，对同步失败进行告警
- 添加数据质量检查，确保数据完整性

### 3. 性能优化
- 支持并发同步多个账号
- 实现增量同步，避免重复数据
