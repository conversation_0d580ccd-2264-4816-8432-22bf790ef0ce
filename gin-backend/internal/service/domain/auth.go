package domain

// LoginEntity 登录相关实体
type LoginEntity struct {
	Email    string // 登录邮箱
	Password string // 登录密码
}

// UserProfileEntity 用户资料实体
type UserProfileEntity struct {
	ID              int64    // 用户ID
	Name            string   // 用户名称
	RealName        string   // 用户真实姓名
	Email           string   // 用户邮箱
	Phone           string   // 用户电话
	Role            int      // 用户角色
	DefaultWorkMode string   // 默认工作模式
	AvailableModes  []string // 用户可访问的工作模式列表
	Status          int      // 用户状态
	Department      string   // 用户所属部门
	Position        string   // 用户职位
	Token           string   // 用于登录认证响应
}

// ChangePasswordEntity 修改密码实体
type ChangePasswordEntity struct {
	UserID      int64  // 用户ID
	OldPassword string // 旧密码
	NewPassword string // 新密码
}

// JWTClaimsEntity JWT声明实体
type JWTClaimsEntity struct {
	UserID    int64  // 用户ID
	Email     string // 用户邮箱
	Role      int    // 用户角色
	ExpiresAt int64  // 过期时间
	IssuedAt  int64  // 发布时间
	NotBefore int64  // 生效时间
}
