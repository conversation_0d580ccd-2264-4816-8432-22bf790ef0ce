package model

import (
	"time"

	"gorm.io/gorm"
)

// Agent 代理模型 - 对应数据库ad_agents表
type Agent struct {
	ID        int64          `gorm:"primarykey;column:id"`
	CreatedAt time.Time      `gorm:"column:created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at"`

	Code              string `gorm:"column:code;uniqueIndex;size:255"`              // 代理编号
	Name              string `gorm:"column:name;size:255"`                          // 代理名称
	UserID            int64  `gorm:"column:user_id;index"`                          // 用户ID
	Type              string `gorm:"column:type;size:50"`                           // 代理类型：traffic-流量采买,delivery-投放
	AuditStatus       string `gorm:"column:audit_status;default:pending"`           // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	CooperationStatus string `gorm:"column:cooperation_status;default:not_started"` // 合作状态：not_started-未开始,active-合作中,terminated-已终止
	CompanyName       string `gorm:"column:company_name;size:255"`                  // 公司名称
	CompanyAddress    string `gorm:"column:company_address;size:255"`               // 公司地址
	ContactName       string `gorm:"column:contact_name;size:255"`                  // 联系人
	ContactPhone      string `gorm:"column:contact_phone;size:255"`                 // 联系电话
	RejectReason      string `gorm:"column:reject_reason;size:255"`                 // 拒绝原因
	Remarks           string `gorm:"column:remarks;type:text"`                      // 备注
	PlatformConfig    string `gorm:"column:platform_config;type:json"`              // 平台配置信息
	CreatedBy         int64  `gorm:"column:created_by"`                             // 创建人ID
	UpdatedBy         int64  `gorm:"column:updated_by"`                             // 更新人ID
	InstanceID        string `gorm:"column:instance_id;size:255;default:''"`        // 审批实例ID
}

// TableName 指定表名
func (Agent) TableName() string {
	return "ad_agents"
}
