-- 媒体管理权限配置
-- 添加媒体相关权限

-- 检查并添加缺失的字段（如果permissions表没有type和data_scope字段）
SET @type_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'type');
SET @data_scope_exists = (SELECT COUNT(*) FROM information_schema.columns 
    WHERE table_schema = DATABASE() AND table_name = 'permissions' AND column_name = 'data_scope');

-- 添加type字段（如果不存在）
SET @sql = IF(@type_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `type` varchar(20) DEFAULT ''function'' COMMENT ''权限类型：function-功能权限，data-数据权限'' AFTER `module`',
    'SELECT ''type字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加data_scope字段（如果不存在）
SET @sql = IF(@data_scope_exists = 0, 
    'ALTER TABLE permissions ADD COLUMN `data_scope` varchar(20) DEFAULT NULL COMMENT ''数据权限范围：all-全部，dept-部门，self-个人'' AFTER `type`',
    'SELECT ''data_scope字段已存在'' as ''信息''');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 1. 添加媒体功能权限
INSERT INTO permissions (code, name, module, type, description, data_scope, created_at, updated_at) VALUES
('media:view', '查看媒体', 'media', 'function', '查看媒体列表和详情', NULL, NOW(), NOW()),
('media:create', '创建媒体', 'media', 'function', '创建新媒体', NULL, NOW(), NOW()),
('media:edit', '编辑媒体', 'media', 'function', '编辑媒体信息', NULL, NOW(), NOW()),
('media:delete', '删除媒体', 'media', 'function', '删除媒体', NULL, NOW(), NOW()),
('media:audit', '审核媒体', 'media', 'function', '审核媒体状态', NULL, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
type = VALUES(type),
description = VALUES(description),
data_scope = VALUES(data_scope),
updated_at = NOW();

-- 2. 添加媒体数据权限
INSERT INTO permissions (code, name, module, type, data_scope, description, created_at, updated_at) VALUES
('media:data:all', '查看所有媒体数据', 'media', 'data', 'all', '可以查看所有媒体数据', NOW(), NOW()),
('media:data:dept', '查看部门媒体数据', 'media', 'data', 'dept', '可以查看本部门的媒体数据', NOW(), NOW()),
('media:data:self', '查看个人媒体数据', 'media', 'data', 'self', '只能查看自己的媒体数据', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
name = VALUES(name),
type = VALUES(type),
description = VALUES(description),
data_scope = VALUES(data_scope),
updated_at = NOW();

-- 3. 为超级管理员角色分配所有媒体权限
INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'super_admin' 
  AND p.module = 'media'
  AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
  );

-- 4. 为管理层角色分配所有媒体权限
INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'manager' 
  AND p.module = 'media'
  AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
  );

-- 5. 为运营角色分配媒体权限（除了删除）
INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'operator' 
  AND p.module = 'media'
  AND p.code != 'media:delete'  -- 运营不能删除媒体
  AND p.code != 'media:data:all'  -- 运营不能查看全部数据
  AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
  );

-- 6. 为媒介角色分配基础媒体权限
INSERT INTO role_permissions (role_id, permission_id, created_at, updated_at)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as created_at,
    NOW() as updated_at
FROM roles r
CROSS JOIN permissions p
WHERE r.code = 'media_specialist' 
  AND p.module = 'media'
  AND p.code IN ('media:view', 'media:create', 'media:edit', 'media:data:self')  -- 媒介只能操作自己的数据
  AND NOT EXISTS (
    SELECT 1 FROM role_permissions rp 
    WHERE rp.role_id = r.id AND rp.permission_id = p.id
  );

-- 7. 验证权限配置
SELECT 
    r.name as role_name,
    r.code as role_code,
    p.name as permission_name,
    p.code as permission_code,
    p.type as permission_type,
    p.data_scope
FROM roles r
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE p.module = 'media'
ORDER BY r.id, p.type, p.code; 