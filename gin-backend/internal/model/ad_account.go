package model

import "time"

// AdAccounts  广告投放账号表
type AdAccounts struct {
	ID                  int64
	AccountType         int8
	ParentId            int64
	Platform            int64
	AccountName         string
	PlatformAccountId   string
	AuthorizationStatus int8
	Token               string
	TokenExpireTime     time.Time
	UsageStatus         int8
	AccountBalance      float64
	Owner               string
	LastSync            time.Time
	CreatedAt           time.Time
	UpdatedAt           time.Time
}

func (m *AdAccounts) TableName() string {
	return "ad_accounts"
}
