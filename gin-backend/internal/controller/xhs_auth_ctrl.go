package controller

import (
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// XHSAuthController 小红书授权控制器
type XHSAuthController struct {
	authService *service.XHSAuthService
}

// NewXHSAuthController 创建小红书授权控制器
func NewXHSAuthController() *XHSAuthController {
	return &XHSAuthController{
		authService: service.NewXHSAuthService(),
	}
}

// GetAuthUrlRequest 获取授权链接请求
type GetAuthUrlRequest struct {
	Scopes []string `json:"scopes"` // 自定义授权范围（可选）
	State  string   `json:"state"`  // 自定义状态参数（可选）
}

// GetAuthUrlResponse 获取授权链接响应
type GetAuthUrlResponse struct {
	AuthUrl     string   `json:"auth_url"`     // 授权链接
	AppId       string   `json:"app_id"`       // 应用ID
	RedirectUri string   `json:"redirect_uri"` // 重定向URI
	Scopes      []string `json:"scopes"`       // 授权范围
	State       string   `json:"state"`        // 状态参数
}

// GetAuthUrl 获取小红书授权链接
// @Summary 获取小红书授权链接
// @Description 获取小红书OAuth授权链接，用于用户授权
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Param request body GetAuthUrlRequest false "自定义参数（可选）"
// @Success 200 {object} response.Response{data=GetAuthUrlResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/url [post]
func (ctrl *XHSAuthController) GetAuthUrl(c *gin.Context) {
	var req GetAuthUrlRequest
	
	// 尝试绑定JSON参数，如果失败则使用默认参数
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果没有请求体或解析失败，使用默认参数
		req = GetAuthUrlRequest{}
	}

	var result domain.GetXHSAuthUrlResult
	var err error

	// 如果有自定义参数，使用自定义参数
	if len(req.Scopes) > 0 || req.State != "" {
		// 转换授权范围
		customScopes := make([]domain.XHSAuthScope, 0, len(req.Scopes))
		for _, scope := range req.Scopes {
			customScopes = append(customScopes, domain.XHSAuthScope(scope))
		}

		result, err = ctrl.authService.GetAuthUrlWithCustomParams(c.Request.Context(), customScopes, req.State)
	} else {
		// 使用默认参数
		result, err = ctrl.authService.GetAuthUrl(c.Request.Context())
	}

	if err != nil {
		zap.L().Error("获取小红书授权链接失败", zap.Error(err))
		response.Error(c, 500, "获取授权链接失败: "+err.Error())
		return
	}

	// 转换响应
	scopeStrings := make([]string, len(result.Scopes))
	for i, scope := range result.Scopes {
		scopeStrings[i] = string(scope)
	}

	resp := GetAuthUrlResponse{
		AuthUrl:     result.AuthUrl,
		AppId:       result.AppId,
		RedirectUri: result.RedirectUri,
		Scopes:      scopeStrings,
		State:       result.State,
	}

	response.Success(c, resp)
}

// GetAuthUrlSimple 获取小红书授权链接（简化版）
// @Summary 获取小红书授权链接（简化版）
// @Description 获取小红书OAuth授权链接，使用默认参数
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=GetAuthUrlResponse}
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/url [get]
func (ctrl *XHSAuthController) GetAuthUrlSimple(c *gin.Context) {
	result, err := ctrl.authService.GetAuthUrl(c.Request.Context())
	if err != nil {
		zap.L().Error("获取小红书授权链接失败", zap.Error(err))
		response.Error(c, 500, "获取授权链接失败: "+err.Error())
		return
	}

	// 转换响应
	scopeStrings := make([]string, len(result.Scopes))
	for i, scope := range result.Scopes {
		scopeStrings[i] = string(scope)
	}

	resp := GetAuthUrlResponse{
		AuthUrl:     result.AuthUrl,
		AppId:       result.AppId,
		RedirectUri: result.RedirectUri,
		Scopes:      scopeStrings,
		State:       result.State,
	}

	response.Success(c, resp)
}

// AuthCallbackRequest 授权回调请求
type AuthCallbackRequest struct {
	Code  string `form:"code" json:"code"`   // 授权码
	State string `form:"state" json:"state"` // 状态参数
	Error string `form:"error" json:"error"` // 错误信息
}

// HandleAuthCallback 处理小红书授权回调
// @Summary 处理小红书授权回调
// @Description 处理小红书OAuth授权回调，验证授权结果
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Param code query string false "授权码"
// @Param state query string false "状态参数"
// @Param error query string false "错误信息"
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/callback [get]
func (ctrl *XHSAuthController) HandleAuthCallback(c *gin.Context) {
	var req AuthCallbackRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 转换参数
	param := domain.XHSAuthCallbackParam{
		Code:  req.Code,
		State: req.State,
		Error: req.Error,
	}

	// 处理回调
	if err := ctrl.authService.HandleAuthCallback(c.Request.Context(), param); err != nil {
		zap.L().Error("处理小红书授权回调失败", zap.Error(err))
		response.Error(c, 400, "授权回调处理失败: "+err.Error())
		return
	}

	// 如果有授权码，尝试获取访问令牌
	var tokenResult *domain.XHSAuthTokenResult
	if req.Code != "" {
		if token, err := ctrl.authService.GetAccessToken(c.Request.Context(), req.Code); err == nil {
			tokenResult = &token
		} else {
			zap.L().Warn("获取访问令牌失败", zap.Error(err))
		}
	}

	// 构建响应
	respData := map[string]interface{}{
		"message": "授权回调处理成功",
		"code":    req.Code,
		"state":   req.State,
	}

	if tokenResult != nil {
		respData["token"] = map[string]interface{}{
			"access_token": tokenResult.AccessToken,
			"token_type":   tokenResult.TokenType,
			"expires_in":   tokenResult.ExpiresIn,
			"scope":        tokenResult.Scope,
		}
	}

	response.Success(c, respData)
}

// GetAuthConfig 获取小红书授权配置信息
// @Summary 获取小红书授权配置信息
// @Description 获取小红书授权相关的配置信息（脱敏）
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/config [get]
func (ctrl *XHSAuthController) GetAuthConfig(c *gin.Context) {
	// 验证配置
	if err := ctrl.authService.ValidateConfig(); err != nil {
		response.Error(c, 500, "配置验证失败: "+err.Error())
		return
	}

	// 获取配置信息
	config := ctrl.authService.GetConfig()
	
	// 获取支持的授权范围
	scopes := ctrl.authService.GetSupportedScopes()
	
	respData := map[string]interface{}{
		"config": config,
		"scopes": scopes,
	}

	response.Success(c, respData)
}

// GetSupportedScopes 获取支持的授权范围
// @Summary 获取支持的授权范围
// @Description 获取小红书支持的所有授权范围列表
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/scopes [get]
func (ctrl *XHSAuthController) GetSupportedScopes(c *gin.Context) {
	scopes := ctrl.authService.GetSupportedScopes()
	response.Success(c, scopes)
}

// ValidateAuthConfig 验证小红书授权配置
// @Summary 验证小红书授权配置
// @Description 验证小红书授权相关配置是否正确
// @Tags 小红书授权
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs/auth/validate [get]
func (ctrl *XHSAuthController) ValidateAuthConfig(c *gin.Context) {
	if err := ctrl.authService.ValidateConfig(); err != nil {
		response.Error(c, 500, "配置验证失败: "+err.Error())
		return
	}

	response.Success(c, map[string]interface{}{
		"message": "配置验证成功",
		"valid":   true,
	})
}
