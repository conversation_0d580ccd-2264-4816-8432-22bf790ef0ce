package model

import (
	"time"
)

// PlatformGroup 广告组实体（对应platform_groups表）
type PlatformGroup struct {
	ID           int64     `gorm:"primaryKey;autoIncrement"` // 主键ID
	PlatformType string    `gorm:"column:platform_type"`     // 平台类型
	AgentID      int64     `gorm:"column:agent_id"`          // 代理商ID
	MediaID      int64     `gorm:"column:media_id"`          // 媒体ID
	DataID       string    `gorm:"column:data_id"`           // 平台数据ID
	PlanID       int64     `gorm:"column:plan_id"`           // 计划ID
	Name         string    `gorm:"column:name"`              // 广告组名称
	Remark       string    `gorm:"column:remark"`            // 备注
	CreatedAt    time.Time `gorm:"column:created_at"`        // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at"`        // 更新时间

	// 关联表
	Plan  *PlatformPlan `gorm:"foreignKey:PlanID"`  // 关联平台计划
	Agent *Agent        `gorm:"foreignKey:AgentID"` // 关联代理商
	Media *Media        `gorm:"foreignKey:MediaID"` // 关联媒体
}

// TableName 指定表名
func (PlatformGroup) TableName() string {
	return "platform_groups"
}

// PlatformGroupItem 广告组列表项 (用于原生SQL查询)
type PlatformGroupItem struct {
	ID           int64  `gorm:"column:id"`            // 主键ID
	Name         string `gorm:"column:name"`          // 广告组名称
	DataID       string `gorm:"column:data_id"`       // 平台数据ID
	PlatformType string `gorm:"column:platform_type"` // 平台类型
	PlanName     string `gorm:"column:plan_name"`     // 计划名称
	MediaName    string `gorm:"column:media_name"`    // 媒体名称
	AgentName    string `gorm:"column:agent_name"`    // 代理商名称
	Remark       string `gorm:"column:remark"`        // 备注
	CreatedAt    string `gorm:"column:created_at"`    // 创建时间
	UpdatedAt    string `gorm:"column:updated_at"`    // 更新时间
}
