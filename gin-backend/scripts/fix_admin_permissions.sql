-- 修复管理员用户的数据权限问题
-- 确保管理员能看到所有数据

-- 1. 检查并修复用户表结构（确保role_id字段存在）
ALTER TABLE users ADD COLUMN IF NOT EXISTS role_id INT UNSIGNED DEFAULT NULL COMMENT '角色ID' AFTER role;

-- 2. 首先确保超级管理员角色存在
INSERT IGNORE INTO roles (id, name, code, description, status, created_at, updated_at) VALUES
(1, '超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, NOW(), NOW());

-- 3. 确保用户ID=1被设置为超级管理员
UPDATE users SET role_id = 1 WHERE id = 1;

-- 4. 确保所有数据权限存在
INSERT IGNORE INTO permissions (name, code, module, type, description, data_scope, status, created_at, updated_at) VALUES
('投放计划全部数据', 'plan:data:all', 'plan', 'data', '查看全部范围的投放计划数据', 'all', 1, NOW(), NOW()),
('投放计划部门数据', 'plan:data:dept', 'plan', 'data', '查看部门范围的投放计划数据', 'dept', 1, NOW(), NOW()),
('投放计划个人数据', 'plan:data:self', 'plan', 'data', '查看个人范围的投放计划数据', 'self', 1, NOW(), NOW()),

('代理全部数据', 'agent:data:all', 'agent', 'data', '查看全部范围的代理数据', 'all', 1, NOW(), NOW()),
('代理部门数据', 'agent:data:dept', 'agent', 'data', '查看部门范围的代理数据', 'dept', 1, NOW(), NOW()),
('代理个人数据', 'agent:data:self', 'agent', 'data', '查看个人范围的代理数据', 'self', 1, NOW(), NOW()),

('媒体全部数据', 'media:data:all', 'media', 'data', '查看全部范围的媒体数据', 'all', 1, NOW(), NOW()),
('媒体部门数据', 'media:data:dept', 'media', 'data', '查看部门范围的媒体数据', 'dept', 1, NOW(), NOW()),
('媒体个人数据', 'media:data:self', 'media', 'data', '查看个人范围的媒体数据', 'self', 1, NOW(), NOW()),

('资源位全部数据', 'slot:data:all', 'slot', 'data', '查看全部范围的资源位数据', 'all', 1, NOW(), NOW()),
('资源位部门数据', 'slot:data:dept', 'slot', 'data', '查看部门范围的资源位数据', 'dept', 1, NOW(), NOW()),
('资源位个人数据', 'slot:data:self', 'slot', 'data', '查看个人范围的资源位数据', 'self', 1, NOW(), NOW());

-- 5. 给超级管理员分配所有权限（包括数据权限）
INSERT IGNORE INTO role_permissions (role_id, permission_id, created_at)
SELECT 1, p.id, NOW()
FROM permissions p
WHERE p.status = 1;

-- 6. 验证修复结果
SELECT 
    '=== 修复结果验证 ===' as info;

-- 6.1 用户角色信息
SELECT 
    'USER_ROLE_INFO' as type,
    u.id as user_id,
    u.name as user_name,
    u.role as old_role_field,
    u.role_id as new_role_id,
    r.name as role_name,
    r.code as role_code
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.id = 1;

-- 6.2 用户权限统计
SELECT 
    'PERMISSION_STATS' as type,
    COUNT(DISTINCT p.id) as total_permissions,
    COUNT(DISTINCT CASE WHEN p.code LIKE '%:data:all' THEN p.id END) as all_data_permissions,
    COUNT(DISTINCT CASE WHEN p.module = 'plan' THEN p.id END) as plan_permissions
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1 AND p.status = 1;

-- 6.3 验证plan:data:all权限
SELECT 
    'PLAN_DATA_ALL_CHECK' as type,
    CASE 
        WHEN COUNT(*) > 0 THEN 'YES' 
        ELSE 'NO' 
    END as has_plan_data_all_permission,
    GROUP_CONCAT(DISTINCT p.code) as permission_codes
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 1 
AND p.code = 'plan:data:all' 
AND p.status = 1; 