package vo

import "gin-backend/internal/service/domain"

// GetAdLinkReq 获取广告链接请求
type GetAdLinkReq struct {
	SoltId int64  `json:"solt_id" form:"solt_id" binding:"required"`
	OpenId string `json:"open_id" form:"open_id" binding:"required"`
	Way    int    `json:"way" form:"way"`
}

func (r GetAdLinkReq) ToAdLinkParam() domain.AdLinkParam {
	return domain.AdLinkParam{
		SlotID: r.SoltId,
		OpenID: r.OpenId,
		Way:    r.Way,
	}
}

// GetAdLinkResp 获取广告链接响应
type GetAdLinkResp struct {
	ID                int64          `json:"id"`
	PromotionLinkInfo any            `json:"promotion_link_info"` // 推广链接信息
	CreativeInfo      map[string]any `json:"creative_info"`       // 创意信息
}

// GetAdPageReq 获取广告页面请求
type GetAdPageReq struct {
	SoltId int64  `json:"solt_id" form:"solt_id" binding:"required"`
	OpenId string `json:"open_id" form:"open_id" binding:"required"`
	Way    int    `json:"way" form:"way"`
}

func (r GetAdPageReq) ToAdPageParam() domain.AdPageParam {
	return domain.AdPageParam{
		SlotID: r.SoltId,
		OpenID: r.OpenId,
		Way:    r.Way,
	}
}

// GetAdPageResp 获取广告页面响应
type GetAdPageResp struct {
	BackgroundImage string `json:"background_image"`
	BackgroundLink  any    `json:"background_link"`
	DialogType      int8   `json:"dialog_type"`
	Dialog          any    `json:"dialog"`
	DialogLinkInfo  any    `json:"dialog_link_info"`
}
