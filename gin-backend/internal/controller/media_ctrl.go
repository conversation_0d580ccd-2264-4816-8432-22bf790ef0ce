package controller

import (
	"errors"
	"strconv"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// MediaController 媒体控制器
type MediaController struct {
	mediaService      *service.MediaService
	permissionService *service.PermissionService
	roleService       *service.RoleService
}

// NewMediaController 创建媒体控制器实例
func NewMediaController() *MediaController {
	return &MediaController{
		mediaService:      service.NewMediaService(),
		permissionService: service.NewPermissionService(),
		roleService:       service.NewRoleService(),
	}
}

// checkPermission 检查权限
func (mc *MediaController) checkPermission(c *gin.Context, permission string) error {
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		return errors.New("用户未登录")
	}

	hasPermission, err := mc.roleService.CheckUserPermission(c, userInfo.ID, permission)
	if err != nil {
		return err
	}

	if !hasPermission {
		return errors.New("权限不足")
	}

	return nil
}

// parseMediaID 解析媒体ID（支持路径参数和查询参数）
func (mc *MediaController) parseMediaID(c *gin.Context) (int64, error) {
	// 先尝试从路径参数获取
	if idStr := c.Param("id"); idStr != "" {
		return strconv.ParseInt(idStr, 10, 64)
	}

	// 再尝试从查询参数获取
	if idStr := c.Query("id"); idStr != "" {
		return strconv.ParseInt(idStr, 10, 64)
	}

	return 0, errors.New("missing media id")
}

// GetList 获取媒体列表
// @Summary 获取媒体列表
// @Description 获取媒体列表，支持分页和筛选
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param name query string false "媒体名称"
// @Param user_id query int false "媒介ID"
// @Param types query array false "媒体类型列表"
// @Param industry query string false "所属行业"
// @Param audit_status query string false "审核状态"
// @Param cooperation_status query string false "合作状态"
// @Param cooperation_type query string false "合作类型"
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=vo.MediaListResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media/list [get]
func (mc *MediaController) GetList(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:view"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	var req vo.MediaListReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(c, "参数绑定失败: "+err.Error())
		return
	}

	// 使用VO的转换方法获取Service Domain参数
	param := req.ToMediaListParam()

	// 调用服务层
	result, err := mc.mediaService.GetList(c, userInfo, param)
	if err != nil {
		response.ServerError(c, "获取媒体列表失败: "+err.Error())
		return
	}

	// 使用VO的转换方法将Service Domain结果转换为VO响应
	resp := vo.FromMediaListResult(result)

	response.Success(c, resp)
}

// GetDetail 获取媒体详情
// @Summary 获取媒体详情
// @Description 根据ID获取媒体详细信息
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param id query int true "媒体ID"
// @Success 200 {object} response.Response{data=vo.MediaDetailItem}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media/detail [get]
func (mc *MediaController) GetDetail(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:view"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 解析媒体ID
	id, err := mc.parseMediaID(c)
	if err != nil {
		response.InvalidParam(c, err.Error())
		return
	}

	// 调用服务层
	result, err := mc.mediaService.GetDetail(c, userInfo, id)
	if err != nil {
		response.ServerError(c, "获取媒体详情失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	mediaInfo := vo.FromMediaEntity(result)

	response.Success(c, mediaInfo)
}

// GetSelectOptions 获取媒体选择器选项
// @Summary 获取媒体选择器选项
// @Description 获取用于下拉选择的媒体列表
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.MediaSelectItem}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media/select-options [get]
func (mc *MediaController) GetSelectOptions(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:view"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 调用服务层
	options, err := mc.mediaService.GetSelectOptions(c, userInfo)
	if err != nil {
		response.ServerError(c, "获取媒体选择器选项失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	result := vo.FromMediaSelectOptions(options)

	response.Success(c, result)
}

// Create 创建媒体
// @Summary 创建媒体
// @Description 创建新的媒体信息
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param data body vo.MediaCreateReq true "创建媒体请求结构"
// @Success 200 {object} response.Response{data=vo.MediaCreateResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media [post]
func (mc *MediaController) Create(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:create"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	var req vo.MediaCreateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数绑定失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	entity := req.ToMediaCreateEntity()

	// 调用服务层
	result, err := mc.mediaService.Create(c, userInfo, entity)
	if err != nil {
		response.ServerError(c, "创建媒体失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	resp := vo.FromMediaCreateResult(result)

	response.Success(c, resp)
}

// Update 更新媒体
// @Summary 更新媒体
// @Description 更新媒体信息
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param data body vo.MediaUpdateReq true "更新媒体请求结构"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media [put]
func (mc *MediaController) Update(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:edit"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	var req vo.MediaUpdateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数绑定失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	entity := req.ToMediaUpdateEntity()

	// 调用服务层
	if err := mc.mediaService.Update(c, userInfo, entity); err != nil {
		response.ServerError(c, "更新媒体失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{"success": true})
}

// Delete 删除媒体
// @Summary 删除媒体
// @Description 根据ID删除媒体信息
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param id path int true "媒体ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media/{id} [delete]
func (mc *MediaController) Delete(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:delete"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	// 解析媒体ID
	id, err := mc.parseMediaID(c)
	if err != nil {
		response.InvalidParam(c, err.Error())
		return
	}

	// 调用服务层
	if err := mc.mediaService.Delete(c, userInfo, id); err != nil {
		response.ServerError(c, "删除媒体失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{"success": true})
}

// Audit 审核媒体
// @Summary 审核媒体
// @Description 审核媒体信息
// @Tags 媒体管理
// @Accept json
// @Produce json
// @Param data body vo.MediaAuditReq true "审核媒体请求结构"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/media/audit [post]
func (mc *MediaController) Audit(c *gin.Context) {
	// 权限检查
	if err := mc.checkPermission(c, "media:audit"); err != nil {
		response.Forbidden(c, err.Error())
		return
	}

	// 获取用户信息
	userInfo := middleware.GetCurrentUser(c)
	if userInfo.ID == 0 {
		response.Unauthorized(c, "用户未登录")
		return
	}

	var req vo.MediaAuditReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数绑定失败: "+err.Error())
		return
	}

	// 使用VO的转换方法
	entity := req.ToMediaAuditEntity()

	// 调用服务层
	if err := mc.mediaService.Audit(c, userInfo, entity); err != nil {
		response.ServerError(c, "审核媒体失败: "+err.Error())
		return
	}

	response.Success(c, gin.H{"success": true})
}

// RegisterRoutes 注册路由
func (mc *MediaController) RegisterRoutes(router *gin.RouterGroup) {
	mediaGroup := router.Group("/media")
	{
		mediaGroup.GET("/list", mc.GetList)
		mediaGroup.GET("/detail", mc.GetDetail)
		mediaGroup.GET("/select-options", mc.GetSelectOptions)
		mediaGroup.POST("", mc.Create)
		mediaGroup.PUT("", mc.Update)
		mediaGroup.DELETE("/:id", mc.Delete)
		mediaGroup.POST("/audit", mc.Audit)
	}
}
