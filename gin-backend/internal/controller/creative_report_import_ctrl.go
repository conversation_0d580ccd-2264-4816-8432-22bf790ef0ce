package controller

import (
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"path/filepath"
)

// CreativeReportImportController 创意报表导入控制器
type CreativeReportImportController struct {
	importService *service.CreativeReportImportService
}

// NewCreativeReportImportController 创建创意报表导入控制器
func NewCreativeReportImportController() *CreativeReportImportController {
	return &CreativeReportImportController{
		importService: service.NewCreativeReportImportService(),
	}
}

// ExecuteImport 执行导入
// @Summary 执行创意报表数据导入
// @Description 执行Excel文件数据导入，更新创意报表数据
// @Tags 创意报表导入
// @Accept json
// @Produce json
// @Param request body map[string]string true "文件路径"
// @Success 200 {object} response.Response{data=domain.CreativeReportImportResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/creative-reports/import/execute [post]
func (ctrl *CreativeReportImportController) ExecuteImport(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		response.Error(c, 400, "获取上传文件失败: "+err.Error())
		return
	}

	// 验证文件类型
	if !isExcelFile(file.Filename) {
		response.Error(c, 400, "只支持Excel文件格式(.xlsx, .xls)")
		return
	}

	// 验证文件大小（限制10MB）
	if file.Size > 20*1024*1024 {
		response.Error(c, 400, "文件大小不能超过10MB")
		return
	}

	f, _ := file.Open()

	result, err := ctrl.importService.ImportCreativeReportFromExcel(c.Request.Context(), f)
	if err != nil {
		zap.L().Error("执行导入失败", zap.Error(err))
		response.Error(c, 500, "执行导入失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// isExcelFile 检查是否为Excel文件
func isExcelFile(filename string) bool {
	ext := filepath.Ext(filename)
	return ext == ".xlsx" || ext == ".xls"
}
