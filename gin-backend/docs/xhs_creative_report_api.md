# 小红书创意报表API接口文档

## 概述

基于页面截图识别，实现了小红书创意报表管理的完整API接口，支持多维度筛选、分页查询、统计分析和数据导出功能。

## 接口列表

### 1. 获取创意报表列表

**接口地址：** `GET /api/v1/xhs-creative-reports`

**功能描述：** 获取小红书创意报表列表，支持多维度筛选和分页

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| account_id | int | 否 | 账号ID | 123 |
| account_name | string | 否 | 账号名称（模糊匹配） | "测试账号" |
| campaign_name | string | 否 | 计划名称（模糊匹配） | "春季推广" |
| unit_name | string | 否 | 单元名称（模糊匹配） | "单元A" |
| title | string | 否 | 标题（模糊匹配） | "新品推荐" |
| pwd | string | 否 | 口令（模糊匹配） | "春季特惠" |
| start_date | string | 否 | 开始日期 YYYY-MM-DD | "2024-01-01" |
| end_date | string | 否 | 结束日期 YYYY-MM-DD | "2024-01-31" |
| placement | int | 否 | 广告类型 1:信息流 2:搜索 3:开屏 4:全站智投 7:视频内流 | 1 |
| aggregate_type | string | 否 | 聚合类型 daily:分日数据 summary:汇总数据 | "daily" |
| page | int | 是 | 页码，从1开始 | 1 |
| page_size | int | 是 | 每页数量，最大1000 | 20 |

**聚合类型说明：**
- **daily（分日数据）**：按日期+创意ID维度展示，每个创意每天一条记录，不进行聚合
- **summary（汇总数据）**：按创意ID维度聚合多日数据，每个创意只有一条汇总记录

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_name": "测试账号",
        "title": "新品推荐",
        "pwd": "春季特惠",
        "content_people": "张三",
        "pitcher_name": "李四",
        "campaign_name": "春季推广计划",
        "unit_name": "推广单元A",
        "creativity_id": "creative_001",
        "note_id": "note_001",
        "time": "2024-01-15T00:00:00Z",
        "placement": 1,
        "fee": 100.50,
        "impression": 10000,
        "click": 500,
        "ctr": 5.0,
        "acp": 0.20,
        "cpm": 10.05,
        "like": 100,
        "comment": 50,
        "collect": 30,
        "follow": 20,
        "share": 10,
        "interaction": 210,
        "cpi": 0.48,
        "goods_order": 15,
        "rgmv": 1500.00,
        "roi": 14.93,
        "success_goods_order": 12,
        "purchase_order_roi_7d": 11.94
      }
    ],
    "total": 1000,
    "page": 1,
    "size": 20
  }
}
```

### 2. 获取创意报表统计信息

**接口地址：** `GET /api/v1/xhs-creative-reports/stats`

**功能描述：** 获取小红书创意报表的统计信息，包括总数、总消费、总ROI等

**请求参数：** 与列表接口相同的筛选参数（除分页参数外）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_reports": 1000,
    "total_fee": 50000.00,
    "total_click": 250000,
    "total_roi": 12.5,
    "avg_ctr": 4.8,
    "avg_cpi": 0.45,
    "last_update_time": "2024-01-15 10:30:00"
  }
}
```

### 3. 导出创意报表数据

**接口地址：** `GET /api/v1/xhs-creative-reports/export`

**功能描述：** 导出小红书创意报表数据为Excel或CSV格式

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| format | string | 是 | 导出格式：xlsx 或 csv | "xlsx" |
| 其他参数 | - | 否 | 与列表接口相同的筛选参数 | - |

**限制说明：**
- 最大导出50,000条数据
- 时间范围不能超过90天
- 支持xlsx和csv两种格式

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "file_name": "xhs_creative_reports_20240115_103000.xlsx",
    "file_url": "/exports/xhs_creative_reports_20240115_103000.xlsx",
    "message": "导出成功"
  }
}
```

### 4. 获取创意报表详情

**接口地址：** `GET /api/v1/xhs-creative-reports/{id}`

**功能描述：** 根据ID获取小红书创意报表的详细信息

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | int | 是 | 报表ID | 123 |

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "account_id": 1,
    "account_name": "测试账号",
    "title": "新品推荐",
    "pwd": "春季特惠",
    "content_people": "张三",
    "pitcher_name": "李四",
    "campaign_id": "campaign_001",
    "campaign_name": "春季推广计划",
    "unit_id": "unit_001",
    "unit_name": "推广单元A",
    "creativity_id": "creative_001",
    "creativity_name": "新品推荐_春季特惠_张三/李四_2024_01_15_智投_10:51_3",
    "creativity_image": "https://example.com/image.jpg",
    "note_id": "note_001",
    "time": "2024-01-15T00:00:00Z",
    "placement": 1,
    "optimize_target": 1,
    "promotion_target": 1,
    "bidding_strategy": 1,
    "build_type": 1,
    "marketing_target": 1,
    "fee": 100.50,
    "impression": 10000,
    "click": 500,
    "ctr": 5.0,
    "acp": 0.20,
    "cpm": 10.05,
    "like": 100,
    "comment": 50,
    "collect": 30,
    "follow": 20,
    "share": 10,
    "interaction": 210,
    "cpi": 0.48,
    "goods_order": 15,
    "rgmv": 1500.00,
    "roi": 14.93,
    "success_goods_order": 12,
    "purchase_order_roi_7d": 11.94,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## 枚举值说明

### 广告类型 (placement)
- 1: 信息流
- 2: 搜索
- 3: 开屏
- 4: 全站智投
- 7: 视频内流

### 优化目标 (optimize_target)
- 1: 点击量
- 2: 转化量
- 3: 曝光量
- 4: 触达量

### 推广标的 (promotion_target)
- 1: 笔记
- 2: 直播间
- 3: 商品
- 4: 落地页

### 出价方式 (bidding_strategy)
- 1: 点击出价
- 2: 曝光出价
- 3: 转化出价

### 搭建类型 (build_type)
- 1: 手动搭建
- 2: 自动搭建

### 营销诉求 (marketing_target)
- 1: 品牌推广
- 4: 产品种草
- 9: 客资收集
- 16: 应用换端

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 1. 获取最近7天的分日报表数据
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?start_date=2024-01-08&end_date=2024-01-15&aggregate_type=daily&page=1&page_size=20"
```

### 2. 获取最近7天的汇总报表数据（按创意ID聚合）
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?start_date=2024-01-08&end_date=2024-01-15&aggregate_type=summary&page=1&page_size=20"
```

### 3. 按账号筛选并导出Excel
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports/export?account_id=123&format=xlsx"
```

### 4. 获取信息流广告的统计信息
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports/stats?placement=1"
```

### 5. 按标题和口令搜索分日数据
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?title=新品&pwd=特惠&aggregate_type=daily&page=1&page_size=10"
```

### 6. 获取某个创意的汇总数据
```bash
curl -X GET "http://localhost:8080/api/v1/xhs-creative-reports?title=新品推荐&aggregate_type=summary&page=1&page_size=1"
```

## 注意事项

1. **分页限制**：每页最大1000条数据，建议使用20-100条
2. **导出限制**：单次导出最大50,000条，时间范围不超过90天
3. **日期格式**：统一使用YYYY-MM-DD格式
4. **模糊匹配**：字符串类型的筛选条件支持模糊匹配
5. **性能优化**：建议添加适当的筛选条件以提高查询性能
6. **数据更新**：报表数据通过定时任务每日同步，建议查看last_update_time了解数据时效性
