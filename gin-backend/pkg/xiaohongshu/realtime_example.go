package xiaohongshu

import (
	"fmt"
	"log"
)

// RealtimeReportExampleBasic 基础实时报表使用示例
func RealtimeReportExampleBasic() {
	// 1. 创建配置
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	// 2. 创建客户端
	client := NewClient(config)

	// 3. 构建实时报表请求
	request := NewRealtimeReportBuilder(1234567890, "2024-01-01", "2024-01-01").
		WithPagination(1, 50).
		WithSort("fee", SortDesc).
		WithHourlyData(true).
		Build()

	// 4. 获取实时报表数据
	accessToken := "your_access_token"
	realtimeResp, err := client.GetCreativityRealtimeReport(accessToken, request)
	if err != nil {
		log.Printf("获取创意实时报表失败: %v", err)
		return
	}

	if !realtimeResp.Success {
		log.Printf("API返回错误: code=%d, msg=%s", realtimeResp.Code, realtimeResp.Msg)
		return
	}

	// 5. 处理实时报表数据
	fmt.Printf("实时报表数据获取成功:\n")
	fmt.Printf("  总记录数: %d\n", realtimeResp.Page.TotalCount)
	fmt.Printf("  当前页码: %d\n", realtimeResp.Page.PageIndex)
	fmt.Printf("  创意数据列表长度: %d\n", len(realtimeResp.CreativityDtos))

	// 打印汇总数据
	fmt.Printf("  汇总数据:\n")
	fmt.Printf("    总消费: %s\n", realtimeResp.TotalData.Fee)
	fmt.Printf("    总展现: %s\n", realtimeResp.TotalData.Impression)
	fmt.Printf("    总点击: %s\n", realtimeResp.TotalData.Click)
	fmt.Printf("    点击率: %s\n", realtimeResp.TotalData.Ctr)

	// 打印前几条创意数据
	for i, creativity := range realtimeResp.CreativityDtos {
		if i >= 3 { // 只打印前3条
			break
		}
		fmt.Printf("  创意 %d:\n", i+1)
		fmt.Printf("    创意名称: %s\n", creativity.BaseCreativity.CreativityName)
		fmt.Printf("    计划名称: %s\n", creativity.BaseCampaign.CampaignName)
		fmt.Printf("    单元名称: %s\n", creativity.BaseUnit.UnitName)
		fmt.Printf("    消费金额: %s\n", creativity.Data.Fee)
		fmt.Printf("    展现量: %s\n", creativity.Data.Impression)
		fmt.Printf("    点击量: %s\n", creativity.Data.Click)
		fmt.Printf("    点击率: %s\n", creativity.Data.Ctr)

		// 如果有小时数据，打印小时数据
		if len(creativity.HourlyData) > 0 {
			fmt.Printf("    小时数据条数: %d\n", len(creativity.HourlyData))
		}
	}
}

// RealtimeReportExampleWithFilters 带过滤条件的实时报表示例
func RealtimeReportExampleWithFilters() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)

	// 构建带过滤条件的实时报表请求
	request := NewRealtimeReportBuilder(1234567890, "2024-01-01", "2024-01-07").
		WithPlacementList([]int{PlacementFeed, PlacementSearch}).
		WithCreativityFilterState(CreativityFilterStateActive).
		WithCreativityAuditState(CreativityAuditStateApproved).
		WithProgrammaticList([]int{ProgrammaticAuto}).
		WithSort("fee", SortDesc).
		WithPagination(1, 100).
		Build()

	accessToken := "your_access_token"
	realtimeResp, err := client.GetCreativityRealtimeReport(accessToken, request)
	if err != nil {
		log.Printf("获取过滤实时报表失败: %v", err)
		return
	}

	fmt.Printf("过滤后的实时报表数据: %d 条记录\n", realtimeResp.Page.TotalCount)
	fmt.Printf("只显示信息流和搜索广告的有效创意数据\n")
}

// RealtimeReportExampleQuickMethods 快速方法示例
func RealtimeReportExampleQuickMethods() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"
	advertiserID := int64(1234567890)

	// 1. 获取今日实时报表
	todayRequest := CreateTodayRealtimeReport(advertiserID)
	todayResp, err := client.GetCreativityRealtimeReport(accessToken, todayRequest)
	if err != nil {
		log.Printf("获取今日实时报表失败: %v", err)
	} else {
		fmt.Printf("今日实时报表: 总消费 %s, 总展现 %s\n",
			todayResp.TotalData.Fee,
			todayResp.TotalData.Impression)
	}

	// 2. 获取昨日实时报表
	yesterdayRequest := CreateYesterdayRealtimeReport(advertiserID)
	yesterdayResp, err := client.GetCreativityRealtimeReport(accessToken, yesterdayRequest)
	if err != nil {
		log.Printf("获取昨日实时报表失败: %v", err)
	} else {
		fmt.Printf("昨日实时报表: %d 条记录\n", yesterdayResp.Page.TotalCount)
	}

	// 3. 获取最近7天实时报表
	last7DaysRequest := CreateLast7DaysRealtimeReport(advertiserID)
	last7DaysResp, err := client.GetCreativityRealtimeReport(accessToken, last7DaysRequest)
	if err != nil {
		log.Printf("获取最近7天实时报表失败: %v", err)
	} else {
		fmt.Printf("最近7天实时报表: %d 条记录\n", last7DaysResp.Page.TotalCount)
	}

	// 4. 获取信息流实时报表
	feedRequest := CreateFeedRealtimeReport(advertiserID, "2024-01-01", "2024-01-07")
	feedResp, err := client.GetCreativityRealtimeReport(accessToken, feedRequest)
	if err != nil {
		log.Printf("获取信息流实时报表失败: %v", err)
	} else {
		fmt.Printf("信息流实时报表: %d 条记录\n", feedResp.Page.TotalCount)
	}

	// 5. 获取搜索实时报表
	searchRequest := CreateSearchRealtimeReport(advertiserID, "2024-01-01", "2024-01-07")
	searchResp, err := client.GetCreativityRealtimeReport(accessToken, searchRequest)
	if err != nil {
		log.Printf("获取搜索实时报表失败: %v", err)
	} else {
		fmt.Printf("搜索实时报表: %d 条记录\n", searchResp.Page.TotalCount)
	}
}

// RealtimeReportExampleWithHourlyData 带小时数据的实时报表示例
func RealtimeReportExampleWithHourlyData() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"
	advertiserID := int64(1234567890)

	// 创建带小时数据的实时报表请求
	request := CreateRealtimeReportWithHourlyData(advertiserID, "2024-01-01", "2024-01-01")
	realtimeResp, err := client.GetCreativityRealtimeReport(accessToken, request)
	if err != nil {
		log.Printf("获取带小时数据的实时报表失败: %v", err)
		return
	}

	fmt.Printf("带小时数据的实时报表获取成功:\n")
	fmt.Printf("  总记录数: %d\n", realtimeResp.Page.TotalCount)

	// 分析小时数据
	for i, creativity := range realtimeResp.CreativityDtos {
		if i >= 2 { // 只分析前2条
			break
		}

		fmt.Printf("  创意 %d (%s):\n", i+1, creativity.BaseCreativity.CreativityName)
		fmt.Printf("    全天数据: 消费 %s, 展现 %s, 点击 %s\n",
			creativity.Data.Fee,
			creativity.Data.Impression,
			creativity.Data.Click)

		if len(creativity.HourlyData) > 0 {
			fmt.Printf("    小时数据 (%d 条):\n", len(creativity.HourlyData))
			for j, hourlyData := range creativity.HourlyData {
				if j >= 5 { // 只显示前5小时
					break
				}
				fmt.Printf("      小时 %d: 消费 %s, 展现 %s, 点击 %s\n",
					j+1,
					hourlyData.Fee,
					hourlyData.Impression,
					hourlyData.Click)
			}
		}
	}
}

// RealtimeReportExamplePagination 分页获取实时报表示例
func RealtimeReportExamplePagination() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"
	advertiserID := int64(1234567890)

	pageSize := 50
	pageNum := 1
	var allCreativities []CreativityDTO

	for {
		// 构建分页请求
		request := NewRealtimeReportBuilder(advertiserID, "2024-01-01", "2024-01-07").
			WithPagination(pageNum, pageSize).
			WithSort("fee", SortDesc).
			Build()

		// 获取当前页数据
		realtimeResp, err := client.GetCreativityRealtimeReport(accessToken, request)
		if err != nil {
			log.Printf("获取第%d页实时数据失败: %v", pageNum, err)
			break
		}

		if !realtimeResp.Success {
			log.Printf("API返回错误: code=%d, msg=%s", realtimeResp.Code, realtimeResp.Msg)
			break
		}

		// 添加到总数据中
		allCreativities = append(allCreativities, realtimeResp.CreativityDtos...)

		fmt.Printf("已获取第%d页实时数据，本页%d条记录\n", pageNum, len(realtimeResp.CreativityDtos))

		// 检查是否还有更多数据
		if len(realtimeResp.CreativityDtos) < pageSize {
			break // 最后一页
		}

		pageNum++
	}

	fmt.Printf("分页获取完成，总共获取 %d 条实时创意记录\n", len(allCreativities))
}

// RealtimeReportExampleMetricsAnalysis 指标分析示例
func RealtimeReportExampleMetricsAnalysis() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"

	// 获取实时报表数据
	request := CreateTodayRealtimeReport(1234567890)
	realtimeResp, err := client.GetCreativityRealtimeReport(accessToken, request)
	if err != nil {
		log.Printf("获取实时报表失败: %v", err)
		return
	}

	if !realtimeResp.Success {
		log.Printf("API返回错误: %v", realtimeResp.Msg)
		return
	}

	// 分析基础指标
	fmt.Println("=== 实时报表基础指标分析 ===")
	basicMetrics := GetRealtimeBasicMetrics()
	for _, metric := range basicMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 分析互动指标
	fmt.Println("\n=== 实时报表互动指标分析 ===")
	interactionMetrics := GetRealtimeInteractionMetrics()
	for _, metric := range interactionMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 分析转化指标
	fmt.Println("\n=== 实时报表转化指标分析 ===")
	conversionMetrics := GetRealtimeConversionMetrics()
	for _, metric := range conversionMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 分析直播间指标
	fmt.Println("\n=== 实时报表直播间指标分析 ===")
	liveMetrics := GetRealtimeLiveMetrics()
	for _, metric := range liveMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 打印实际数据示例
	if len(realtimeResp.CreativityDtos) > 0 {
		creativity := realtimeResp.CreativityDtos[0]
		fmt.Println("\n=== 实际实时数据示例 ===")
		fmt.Printf("创意名称: %s\n", creativity.BaseCreativity.CreativityName)
		fmt.Printf("计划名称: %s\n", creativity.BaseCampaign.CampaignName)
		fmt.Printf("单元名称: %s\n", creativity.BaseUnit.UnitName)
		fmt.Printf("消费金额: %s 元\n", creativity.Data.Fee)
		fmt.Printf("展现量: %s\n", creativity.Data.Impression)
		fmt.Printf("点击量: %s\n", creativity.Data.Click)
		fmt.Printf("点击率: %s%%\n", creativity.Data.Ctr)
		fmt.Printf("点赞量: %s\n", creativity.Data.Like)
		fmt.Printf("评论量: %s\n", creativity.Data.Comment)
		fmt.Printf("收藏量: %s\n", creativity.Data.Collect)
		fmt.Printf("创意状态: %d\n", creativity.BaseCreativity.CreativityFilterState)
		fmt.Printf("审核状态: %d\n", creativity.BaseCreativity.AuditStatus)
	}
}

// RealtimeReportExampleErrorHandling 错误处理示例
func RealtimeReportExampleErrorHandling() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)

	// 测试无效的访问令牌
	invalidToken := "invalid_token"
	request := CreateTodayRealtimeReport(1234567890)

	realtimeResp, err := client.GetCreativityRealtimeReport(invalidToken, request)
	if err != nil {
		fmt.Printf("网络或解析错误: %v\n", err)
		return
	}

	if !realtimeResp.Success {
		fmt.Printf("API业务错误: code=%d, msg=%s\n", realtimeResp.Code, realtimeResp.Msg)

		// 根据错误码进行不同处理
		switch realtimeResp.Code {
		case 40001:
			fmt.Println("处理建议: 访问令牌无效，请重新获取")
		case 40002:
			fmt.Println("处理建议: 访问令牌过期，请刷新令牌")
		case 40003:
			fmt.Println("处理建议: 权限不足，请检查广告主授权")
		default:
			fmt.Printf("处理建议: 其他错误，请查看错误信息: %s\n", realtimeResp.Msg)
		}
		return
	}

	fmt.Println("实时报表获取成功")
}
