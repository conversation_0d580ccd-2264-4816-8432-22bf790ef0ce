package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// GetAdAccountsReq 获取广告账号列表请求
type GetAdAccountsReq struct {
	Page                int    `form:"page" json:"page"`
	PageSize            int    `form:"page_size" json:"page_size"`
	AccountName         string `form:"account_name" json:"account_name"`           // 账号名称/ID联合查询
	Platform            int64  `form:"platform" json:"platform"`                  // 平台筛选
	AuthorizationStatus int8   `form:"authorization_status" json:"authorization_status"` // 授权状态筛选
	UsageStatus         int8   `form:"usage_status" json:"usage_status"`           // 使用状态筛选
	AccountType         int8   `form:"account_type" json:"account_type"`           // 账号类型筛选
	ParentId            int64  `form:"parent_id" json:"parent_id"`                 // 父账号ID筛选
}

// ToParam 转换为领域层参数
func (req GetAdAccountsReq) ToParam() domain.GetAdAccountsParam {
	return domain.GetAdAccountsParam{
		Page:                req.Page,
		PageSize:            req.PageSize,
		AccountName:         req.AccountName,
		Platform:            req.Platform,
		AuthorizationStatus: req.AuthorizationStatus,
		UsageStatus:         req.UsageStatus,
		AccountType:         req.AccountType,
		ParentId:            req.ParentId,
	}
}

// GetAdAccountsResp 获取广告账号列表响应
type GetAdAccountsResp struct {
	List  []AdAccountItem `json:"list"`
	Total int64           `json:"total"`
	Page  int             `json:"page"`
	Size  int             `json:"size"`
}

// FromResult 从领域层结果转换
func GetAdAccountsRespFromResult(result domain.GetAdAccountsResult) GetAdAccountsResp {
	items := make([]AdAccountItem, len(result.List))
	for i, entity := range result.List {
		items[i] = AdAccountItemFromEntity(entity)
	}
	return GetAdAccountsResp{
		List:  items,
		Total: result.Total,
		Page:  result.Page,
		Size:  result.Size,
	}
}

// AdAccountItem 广告账号信息项
type AdAccountItem struct {
	ID                  int64     `json:"id"`
	AccountType         int8      `json:"account_type"`
	AccountTypeName     string    `json:"account_type_name"`
	ParentId            int64     `json:"parent_id"`
	ParentAccountName   string    `json:"parent_account_name"`
	Platform            int64     `json:"platform"`
	PlatformName        string    `json:"platform_name"`
	AccountName         string    `json:"account_name"`
	PlatformAccountId   string    `json:"platform_account_id"`
	AuthorizationStatus int8      `json:"authorization_status"`
	AuthorizationStatusName string `json:"authorization_status_name"`
	Token               string    `json:"token,omitempty"` // 敏感信息，可选返回
	TokenExpireTime     time.Time `json:"token_expire_time"`
	UsageStatus         int8      `json:"usage_status"`
	UsageStatusName     string    `json:"usage_status_name"`
	AccountBalance      float64   `json:"account_balance"`
	Owner               string    `json:"owner"`
	LastSync            time.Time `json:"last_sync"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// AdAccountItemFromEntity 从领域实体转换为VO项
func AdAccountItemFromEntity(entity domain.AdAccountEntity) AdAccountItem {
	return AdAccountItem{
		ID:                      entity.ID,
		AccountType:             entity.AccountType,
		AccountTypeName:         entity.AccountTypeName,
		ParentId:                entity.ParentId,
		ParentAccountName:       entity.ParentAccountName,
		Platform:                entity.Platform,
		PlatformName:            entity.PlatformName,
		AccountName:             entity.AccountName,
		PlatformAccountId:       entity.PlatformAccountId,
		AuthorizationStatus:     entity.AuthorizationStatus,
		AuthorizationStatusName: entity.AuthorizationStatusName,
		Token:                   entity.Token,
		TokenExpireTime:         entity.TokenExpireTime,
		UsageStatus:             entity.UsageStatus,
		UsageStatusName:         entity.UsageStatusName,
		AccountBalance:          entity.AccountBalance,
		Owner:                   entity.Owner,
		LastSync:                entity.LastSync,
		CreatedAt:               entity.CreatedAt,
		UpdatedAt:               entity.UpdatedAt,
	}
}

// CreateAdAccountReq 创建广告账号请求
type CreateAdAccountReq struct {
	AccountType       int8    `json:"account_type" binding:"required,oneof=1 2"`        // 账号类型: 1-主账号 2-子账号
	ParentId          int64   `json:"parent_id"`                                        // 父账号ID（子账号时必填）
	Platform          int64   `json:"platform" binding:"required"`                     // 所属平台
	AccountName       string  `json:"account_name" binding:"required,min=1,max=100"`   // 账号名称
	PlatformAccountId string  `json:"platform_account_id" binding:"required,min=1,max=100"` // 平台账号ID
	Token             string  `json:"token"`                                            // token（可选）
	TokenExpireTime   *time.Time `json:"token_expire_time"`                            // token过期时间（可选）
	AccountBalance    float64 `json:"account_balance"`                                  // 账户余额
	Owner             string  `json:"owner" binding:"required,min=1,max=50"`           // 归属人员
}

// ToParam 转换为领域层参数
func (req CreateAdAccountReq) ToParam() domain.CreateAdAccountParam {
	param := domain.CreateAdAccountParam{
		AccountType:       req.AccountType,
		ParentId:          req.ParentId,
		Platform:          req.Platform,
		AccountName:       req.AccountName,
		PlatformAccountId: req.PlatformAccountId,
		Token:             req.Token,
		AccountBalance:    req.AccountBalance,
		Owner:             req.Owner,
	}
	if req.TokenExpireTime != nil {
		param.TokenExpireTime = *req.TokenExpireTime
	}
	return param
}

// CreateAdAccountResp 创建广告账号响应
type CreateAdAccountResp struct {
	ID int64 `json:"id"`
}

// FromResult 从领域层结果转换
func CreateAdAccountRespFromResult(result domain.CreateAdAccountResult) CreateAdAccountResp {
	return CreateAdAccountResp{
		ID: result.ID,
	}
}

// UpdateAdAccountReq 更新广告账号请求
type UpdateAdAccountReq struct {
	ID                int64      `json:"id" binding:"required"`
	AccountName       string     `json:"account_name" binding:"required,min=1,max=100"`
	PlatformAccountId string     `json:"platform_account_id" binding:"required,min=1,max=100"`
	Token             string     `json:"token"`
	TokenExpireTime   *time.Time `json:"token_expire_time"`
	UsageStatus       int8       `json:"usage_status" binding:"required,oneof=1 2"` // 使用状态
	AccountBalance    float64    `json:"account_balance"`
	Owner             string     `json:"owner" binding:"required,min=1,max=50"`
}

// ToParam 转换为领域层参数
func (req UpdateAdAccountReq) ToParam() domain.UpdateAdAccountParam {
	param := domain.UpdateAdAccountParam{
		ID:                req.ID,
		AccountName:       req.AccountName,
		PlatformAccountId: req.PlatformAccountId,
		Token:             req.Token,
		UsageStatus:       req.UsageStatus,
		AccountBalance:    req.AccountBalance,
		Owner:             req.Owner,
	}
	if req.TokenExpireTime != nil {
		param.TokenExpireTime = *req.TokenExpireTime
	}
	return param
}

// GetAdAccountOptionsResp 获取广告账号选项响应
type GetAdAccountOptionsResp struct {
	Platforms    []PlatformOptionItem    `json:"platforms"`
	AccountTypes []AccountTypeOptionItem `json:"account_types"`
	ParentAccounts []ParentAccountOptionItem `json:"parent_accounts"`
}

// FromResult 从领域层结果转换
func GetAdAccountOptionsRespFromResult(result domain.GetAdAccountOptionsResult) GetAdAccountOptionsResp {
	platforms := make([]PlatformOptionItem, len(result.Platforms))
	for i, platform := range result.Platforms {
		platforms[i] = PlatformOptionItem{
			Value: platform.Value,
			Label: platform.Label,
		}
	}

	accountTypes := make([]AccountTypeOptionItem, len(result.AccountTypes))
	for i, accountType := range result.AccountTypes {
		accountTypes[i] = AccountTypeOptionItem{
			Value: accountType.Value,
			Label: accountType.Label,
		}
	}

	parentAccounts := make([]ParentAccountOptionItem, len(result.ParentAccounts))
	for i, parentAccount := range result.ParentAccounts {
		parentAccounts[i] = ParentAccountOptionItem{
			Value: parentAccount.Value,
			Label: parentAccount.Label,
		}
	}

	return GetAdAccountOptionsResp{
		Platforms:      platforms,
		AccountTypes:   accountTypes,
		ParentAccounts: parentAccounts,
	}
}

// PlatformOptionItem 平台选项项
type PlatformOptionItem struct {
	Value int64  `json:"value"`
	Label string `json:"label"`
}

// AccountTypeOptionItem 账号类型选项项
type AccountTypeOptionItem struct {
	Value int8   `json:"value"`
	Label string `json:"label"`
}

// ParentAccountOptionItem 父账号选项项
type ParentAccountOptionItem struct {
	Value int64  `json:"value"`
	Label string `json:"label"`
}

// GetAdAccountDetailResp 获取广告账号详情响应
type GetAdAccountDetailResp struct {
	AdAccountItem
	SubAccounts []AdAccountItem `json:"sub_accounts,omitempty"` // 子账号列表（仅主账号返回）
}

// FromEntity 从领域实体转换
func GetAdAccountDetailRespFromEntity(entity domain.AdAccountEntity, subAccounts []domain.AdAccountEntity) GetAdAccountDetailResp {
	resp := GetAdAccountDetailResp{
		AdAccountItem: AdAccountItemFromEntity(entity),
	}

	if len(subAccounts) > 0 {
		resp.SubAccounts = make([]AdAccountItem, len(subAccounts))
		for i, subAccount := range subAccounts {
			resp.SubAccounts[i] = AdAccountItemFromEntity(subAccount)
		}
	}

	return resp
}
