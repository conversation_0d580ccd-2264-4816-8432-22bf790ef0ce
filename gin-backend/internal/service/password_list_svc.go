package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PasswordListService 口令列表服务
type PasswordListService struct {
	db *gorm.DB
}

// NewPasswordListService 创建口令列表服务
func NewPasswordListService() *PasswordListService {
	return &PasswordListService{
		db: global.DB,
	}
}

// GetPasswordList 获取口令列表
func (s *PasswordListService) GetPasswordList(ctx context.Context, param domain.PasswordListParam) (domain.PasswordListResult, error) {
	// 参数验证
	if err := domain.ValidatePasswordListParam(param); err != nil {
		return domain.PasswordListResult{}, err
	}

	// 判断是否为单日查询
	isSingleDay := domain.IsPasswordSingleDayQuery(param.StartDate, param.EndDate)

	var passwords []domain.PasswordListItem
	var total int64
	var err error

	if isSingleDay {
		// 单日查询：直接从password_stats表查询
		passwords, total, err = s.getPasswordListFromStats(ctx, param)
	} else {
		// 多日查询：从创意报表聚合数据
		passwords, total, err = s.getPasswordListFromReports(ctx, param)
	}

	if err != nil {
		return domain.PasswordListResult{}, err
	}

	return domain.PasswordListResult{
		List:  passwords,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// getPasswordListFromStats 从统计表获取口令列表（单日查询）
func (s *PasswordListService) getPasswordListFromStats(ctx context.Context, param domain.PasswordListParam) ([]domain.PasswordListItem, int64, error) {
	query := s.db.WithContext(ctx).Model(&model.PasswordStats{})

	// 添加筛选条件
	s.addPasswordFilterConditions(query, param)

	// 如果有日期条件，添加日期筛选
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("统计口令总数失败: %w", err)
	}

	// 分页查询
	var stats []model.PasswordStats
	offset := (param.Page - 1) * param.PageSize
	err := query.Order("consumption DESC, password_name ASC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&stats).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询口令统计数据失败: %w", err)
	}

	// 转换为列表项
	passwords := make([]domain.PasswordListItem, 0, len(stats))
	for _, stat := range stats {
		passwords = append(passwords, domain.PasswordListItem{
			PasswordName:       stat.Pwd,
			AccountName:        s.getAccountNameByID(ctx, stat.AccountId),
			Consumption:        stat.Consumption,
			ActualConsumption:  stat.ActualConsumption,
			Impressions:        stat.Impressions,
			ClickCount:         stat.ClickCount,
			ClickRate:          stat.ClickRate,
			SearchCount:        stat.SearchCount,
			NewOrdersToday:     stat.NewOrdersToday,
			TodayOrderCost:     stat.OrderCostToday,
			CumulativeOrders:   stat.TotalOrders,
			CumulativeIncome:   stat.TotalIncome,
			CumulativeRecovery: stat.TotalRecoveryRate,
			LastUpdate:         stat.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return passwords, total, nil
}

// getPasswordListFromReports 从创意报表聚合口令列表（多日查询）
func (s *PasswordListService) getPasswordListFromReports(ctx context.Context, param domain.PasswordListParam) ([]domain.PasswordListItem, int64, error) {
	// 构建聚合查询
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	s.addReportFilterConditions(query, param)

	// 构建聚合查询SQL
	selectFields := `
		pwd as password_name,
		MAX(account_name) as account_name,
		SUM(fee) as consumption,
		SUM(fee) as actual_consumption,
		SUM(impression) as impressions,
		SUM(click) as click_count,
		CASE 
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0 
		END as click_rate,
		0 as search_count,
		SUM(goods_order) as new_orders_today,
		CASE 
			WHEN SUM(goods_order) > 0 THEN (SUM(fee) / SUM(goods_order))
			ELSE 0 
		END as today_order_cost,
		SUM(success_goods_order) as cumulative_orders,
		SUM(rgmv) as cumulative_income,
		CASE 
			WHEN SUM(fee) > 0 THEN (SUM(rgmv) * 100.0 / SUM(fee))
			ELSE 0 
		END as cumulative_recovery,
		MAX(updated_at) as last_update
	`

	// 先统计总数（按口令分组后的数量）
	var totalCount int64
	countQuery := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})
	s.addReportFilterConditions(countQuery, param)

	err := countQuery.Select("COUNT(DISTINCT pwd)").Row().Scan(&totalCount)
	if err != nil {
		return nil, 0, fmt.Errorf("统计口令总数失败: %w", err)
	}

	// 分页查询聚合数据
	offset := (param.Page - 1) * param.PageSize

	type PasswordAggregateResult struct {
		PasswordName       string    `gorm:"column:password_name"`
		AccountName        string    `gorm:"column:account_name"`
		Consumption        float64   `gorm:"column:consumption"`
		ActualConsumption  float64   `gorm:"column:actual_consumption"`
		Impressions        int64     `gorm:"column:impressions"`
		ClickCount         int64     `gorm:"column:click_count"`
		ClickRate          float64   `gorm:"column:click_rate"`
		SearchCount        int64     `gorm:"column:search_count"`
		NewOrdersToday     int64     `gorm:"column:new_orders_today"`
		TodayOrderCost     float64   `gorm:"column:today_order_cost"`
		CumulativeOrders   int64     `gorm:"column:cumulative_orders"`
		CumulativeIncome   float64   `gorm:"column:cumulative_income"`
		CumulativeRecovery float64   `gorm:"column:cumulative_recovery"`
		LastUpdate         time.Time `gorm:"column:last_update"`
	}

	var results []PasswordAggregateResult
	err = query.Select(selectFields).
		Where("pwd IS NOT NULL AND pwd != ''"). // 过滤空口令
		Group("pwd").
		Order("consumption DESC, password_name ASC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&results).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询口令聚合数据失败: %w", err)
	}

	// 转换为列表项
	passwords := make([]domain.PasswordListItem, 0, len(results))
	for _, result := range results {
		passwords = append(passwords, domain.PasswordListItem{
			PasswordName:       result.PasswordName,
			AccountName:        result.AccountName,
			Consumption:        result.Consumption,
			ActualConsumption:  result.ActualConsumption,
			Impressions:        result.Impressions,
			ClickCount:         result.ClickCount,
			ClickRate:          result.ClickRate,
			SearchCount:        result.SearchCount,
			NewOrdersToday:     result.NewOrdersToday,
			TodayOrderCost:     result.TodayOrderCost,
			CumulativeOrders:   result.CumulativeOrders,
			CumulativeIncome:   result.CumulativeIncome,
			CumulativeRecovery: result.CumulativeRecovery,
			LastUpdate:         result.LastUpdate.Format("2006-01-02 15:04:05"),
		})
	}

	return passwords, totalCount, nil
}

// addPasswordFilterConditions 添加口令筛选条件（用于统计表查询）
func (s *PasswordListService) addPasswordFilterConditions(query *gorm.DB, param domain.PasswordListParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		// 通过账号名称查找账号ID
		var accountIDs []int64
		s.db.Model(&model.AdAccounts{}).
			Where("account_name LIKE ?", "%"+param.AccountName+"%").
			Pluck("id", &accountIDs)
		if len(accountIDs) > 0 {
			query.Where("account_id IN ?", accountIDs)
		} else {
			// 如果没有找到匹配的账号，添加一个不可能的条件
			query.Where("account_id = -1")
		}
	}
	if param.PasswordName != "" {
		query.Where("pwd LIKE ?", "%"+param.PasswordName+"%")
	}
}

// addReportFilterConditions 添加报表筛选条件（用于创意报表查询）
func (s *PasswordListService) addReportFilterConditions(query *gorm.DB, param domain.PasswordListParam) {
	if param.AccountID != nil {
		query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.PasswordName != "" {
		query.Where("pwd LIKE ?", "%"+param.PasswordName+"%")
	}
	if param.StartDate != nil {
		query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}
}

// getAccountNameByID 根据账号ID获取账号名称
func (s *PasswordListService) getAccountNameByID(ctx context.Context, accountID int64) string {
	var accountName string
	s.db.WithContext(ctx).Model(&model.AdAccounts{}).
		Where("id = ?", accountID).
		Pluck("account_name", &accountName)
	return accountName
}

// GetPasswordStats 获取口令统计信息
func (s *PasswordListService) GetPasswordStats(ctx context.Context, param domain.PasswordStatsParam) (domain.PasswordStatsResult, error) {
	// 参数验证
	if err := domain.ValidatePasswordStatsParam(param); err != nil {
		return domain.PasswordStatsResult{}, err
	}

	// 判断查询来源
	isSingleDay := domain.IsPasswordSingleDayQuery(param.StartDate, param.EndDate)

	if isSingleDay {
		return s.getPasswordStatsFromStats(ctx, param)
	} else {
		return s.getPasswordStatsFromReports(ctx, param)
	}
}

// getPasswordStatsFromStats 从统计表获取口令统计
func (s *PasswordListService) getPasswordStatsFromStats(ctx context.Context, param domain.PasswordStatsParam) (domain.PasswordStatsResult, error) {
	query := s.db.WithContext(ctx).Model(&model.PasswordStats{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		// 通过账号名称查找账号ID
		var accountIDs []int64
		s.db.Model(&model.AdAccounts{}).
			Where("account_name LIKE ?", "%"+param.AccountName+"%").
			Pluck("id", &accountIDs)
		if len(accountIDs) > 0 {
			query = query.Where("account_id IN ?", accountIDs)
		} else {
			query = query.Where("account_id = -1")
		}
	}
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	type StatsResult struct {
		TotalPasswords    int64     `gorm:"column:total_passwords"`
		TotalConsumption  float64   `gorm:"column:total_consumption"`
		TotalOrders       int64     `gorm:"column:total_orders"`
		TotalIncome       float64   `gorm:"column:total_income"`
		TotalRecoveryRate float64   `gorm:"column:total_recovery_rate"`
		AvgClickRate      float64   `gorm:"column:avg_click_rate"`
		LastUpdateTime    time.Time `gorm:"column:last_update_time"`
	}

	var result StatsResult
	err := query.Select(`
		COUNT(*) as total_passwords,
		SUM(consumption) as total_consumption,
		SUM(total_orders) as total_orders,
		SUM(total_income) as total_income,
		CASE
			WHEN SUM(consumption) > 0 THEN (SUM(total_income) * 100.0 / SUM(consumption))
			ELSE 0
		END as total_recovery_rate,
		CASE
			WHEN SUM(impressions) > 0 THEN (SUM(click_count) * 100.0 / SUM(impressions))
			ELSE 0
		END as avg_click_rate,
		MAX(updated_at) as last_update_time
	`).Row().Scan(
		&result.TotalPasswords,
		&result.TotalConsumption,
		&result.TotalOrders,
		&result.TotalIncome,
		&result.TotalRecoveryRate,
		&result.AvgClickRate,
		&result.LastUpdateTime,
	)

	if err != nil {
		return domain.PasswordStatsResult{}, fmt.Errorf("查询口令统计失败: %w", err)
	}

	return domain.PasswordStatsResult{
		TotalPasswords:    result.TotalPasswords,
		TotalConsumption:  result.TotalConsumption,
		TotalOrders:       result.TotalOrders,
		TotalIncome:       result.TotalIncome,
		TotalRecoveryRate: result.TotalRecoveryRate,
		AvgClickRate:      result.AvgClickRate,
		LastUpdateTime:    result.LastUpdateTime,
	}, nil
}

// getPasswordStatsFromReports 从创意报表获取口令统计
func (s *PasswordListService) getPasswordStatsFromReports(ctx context.Context, param domain.PasswordStatsParam) (domain.PasswordStatsResult, error) {
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query = query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}

	type StatsResult struct {
		TotalPasswords    int64     `gorm:"column:total_passwords"`
		TotalConsumption  float64   `gorm:"column:total_consumption"`
		TotalOrders       int64     `gorm:"column:total_orders"`
		TotalIncome       float64   `gorm:"column:total_income"`
		TotalRecoveryRate float64   `gorm:"column:total_recovery_rate"`
		AvgClickRate      float64   `gorm:"column:avg_click_rate"`
		LastUpdateTime    time.Time `gorm:"column:last_update_time"`
	}

	var result StatsResult
	err := query.Select(`
		COUNT(DISTINCT pwd) as total_passwords,
		SUM(fee) as total_consumption,
		SUM(success_goods_order) as total_orders,
		SUM(rgmv) as total_income,
		CASE
			WHEN SUM(fee) > 0 THEN (SUM(rgmv) * 100.0 / SUM(fee))
			ELSE 0
		END as total_recovery_rate,
		CASE
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0
		END as avg_click_rate,
		MAX(updated_at) as last_update_time
	`).Where("pwd IS NOT NULL AND pwd != ''").Row().Scan(
		&result.TotalPasswords,
		&result.TotalConsumption,
		&result.TotalOrders,
		&result.TotalIncome,
		&result.TotalRecoveryRate,
		&result.AvgClickRate,
		&result.LastUpdateTime,
	)

	if err != nil {
		return domain.PasswordStatsResult{}, fmt.Errorf("查询口令统计失败: %w", err)
	}

	return domain.PasswordStatsResult{
		TotalPasswords:    result.TotalPasswords,
		TotalConsumption:  result.TotalConsumption,
		TotalOrders:       result.TotalOrders,
		TotalIncome:       result.TotalIncome,
		TotalRecoveryRate: result.TotalRecoveryRate,
		AvgClickRate:      result.AvgClickRate,
		LastUpdateTime:    result.LastUpdateTime,
	}, nil
}

// ExportPasswordList 导出口令列表
func (s *PasswordListService) ExportPasswordList(ctx context.Context, param domain.PasswordExportParam) (domain.PasswordExportResult, error) {
	// 参数验证
	if err := domain.ValidatePasswordExportParam(param); err != nil {
		return domain.PasswordExportResult{}, err
	}

	// 判断是否为单日查询
	isSingleDay := domain.IsPasswordSingleDayQuery(param.StartDate, param.EndDate)

	var passwords []domain.PasswordListItem
	var err error

	if isSingleDay {
		// 单日查询：从统计表获取数据
		passwords, err = s.getExportDataFromStats(ctx, param)
	} else {
		// 多日查询：从创意报表聚合数据
		passwords, err = s.getExportDataFromReports(ctx, param)
	}

	if err != nil {
		return domain.PasswordExportResult{}, fmt.Errorf("查询导出数据失败: %w", err)
	}

	if len(passwords) == 0 {
		return domain.PasswordExportResult{}, fmt.Errorf("没有找到符合条件的数据")
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	timeRange := domain.GetPasswordTimeRangeDescription(param.StartDate, param.EndDate)
	fileName := fmt.Sprintf("password_list_%s_%s.%s", timeRange, timestamp, param.Format)

	// 根据格式导出文件
	var fileURL string
	switch param.Format {
	case "xlsx":
		fileURL, err = s.exportPasswordListToExcel(ctx, passwords, fileName, timeRange)
	case "csv":
		fileURL, err = s.exportPasswordListToCSV(ctx, passwords, fileName, timeRange)
	default:
		return domain.PasswordExportResult{}, fmt.Errorf("不支持的导出格式: %s", param.Format)
	}

	if err != nil {
		return domain.PasswordExportResult{}, fmt.Errorf("导出文件失败: %w", err)
	}

	return domain.PasswordExportResult{
		FileName: fileName,
		FileURL:  fileURL,
	}, nil
}

// getExportDataFromStats 从统计表获取导出数据
func (s *PasswordListService) getExportDataFromStats(ctx context.Context, param domain.PasswordExportParam) ([]domain.PasswordListItem, error) {
	query := s.db.WithContext(ctx).Model(&model.PasswordStats{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		// 通过账号名称查找账号ID
		var accountIDs []int64
		s.db.Model(&model.AdAccounts{}).
			Where("account_name LIKE ?", "%"+param.AccountName+"%").
			Pluck("id", &accountIDs)
		if len(accountIDs) > 0 {
			query = query.Where("account_id IN ?", accountIDs)
		} else {
			query = query.Where("account_id = -1")
		}
	}
	if param.PasswordName != "" {
		query = query.Where("pwd LIKE ?", "%"+param.PasswordName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(stat_date) = ?", param.StartDate.Format("2006-01-02"))
	}

	// 查询数据（限制最大导出数量）
	var stats []model.PasswordStats
	err := query.Order("consumption DESC, pwd ASC").
		Limit(10000). // 限制最大导出1万条
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询口令统计数据失败: %w", err)
	}

	// 转换为列表项
	passwords := make([]domain.PasswordListItem, 0, len(stats))
	for _, stat := range stats {
		passwords = append(passwords, domain.PasswordListItem{
			PasswordName:       stat.Pwd,
			AccountName:        s.getAccountNameByID(ctx, stat.AccountId),
			Consumption:        stat.Consumption,
			ActualConsumption:  stat.ActualConsumption,
			Impressions:        stat.Impressions,
			ClickCount:         stat.ClickCount,
			ClickRate:          stat.ClickRate,
			SearchCount:        stat.SearchCount,
			NewOrdersToday:     stat.NewOrdersToday,
			TodayOrderCost:     stat.OrderCostToday,
			CumulativeOrders:   stat.TotalOrders,
			CumulativeIncome:   stat.TotalIncome,
			CumulativeRecovery: stat.TotalRecoveryRate,
			LastUpdate:         stat.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return passwords, nil
}

// getExportDataFromReports 从创意报表聚合导出数据
func (s *PasswordListService) getExportDataFromReports(ctx context.Context, param domain.PasswordExportParam) ([]domain.PasswordListItem, error) {
	query := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{})

	// 添加筛选条件
	if param.AccountID != nil {
		query = query.Where("account_id = ?", *param.AccountID)
	}
	if param.AccountName != "" {
		query = query.Where("account_name LIKE ?", "%"+param.AccountName+"%")
	}
	if param.PasswordName != "" {
		query = query.Where("pwd LIKE ?", "%"+param.PasswordName+"%")
	}
	if param.StartDate != nil {
		query = query.Where("DATE(time) >= ?", param.StartDate.Format("2006-01-02"))
	}
	if param.EndDate != nil {
		query = query.Where("DATE(time) <= ?", param.EndDate.Format("2006-01-02"))
	}

	// 构建聚合查询SQL
	selectFields := `
		pwd as password_name,
		MAX(account_name) as account_name,
		SUM(fee) as consumption,
		SUM(fee) as actual_consumption,
		SUM(impression) as impressions,
		SUM(click) as click_count,
		CASE
			WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
			ELSE 0
		END as click_rate,
		0 as search_count,
		SUM(goods_order) as new_orders_today,
		CASE
			WHEN SUM(goods_order) > 0 THEN (SUM(fee) / SUM(goods_order))
			ELSE 0
		END as today_order_cost,
		SUM(success_goods_order) as cumulative_orders,
		SUM(rgmv) as cumulative_income,
		CASE
			WHEN SUM(fee) > 0 THEN (SUM(rgmv) * 100.0 / SUM(fee))
			ELSE 0
		END as cumulative_recovery,
		MAX(updated_at) as last_update
	`

	type PasswordAggregateResult struct {
		PasswordName       string    `gorm:"column:password_name"`
		AccountName        string    `gorm:"column:account_name"`
		Consumption        float64   `gorm:"column:consumption"`
		ActualConsumption  float64   `gorm:"column:actual_consumption"`
		Impressions        int64     `gorm:"column:impressions"`
		ClickCount         int64     `gorm:"column:click_count"`
		ClickRate          float64   `gorm:"column:click_rate"`
		SearchCount        int64     `gorm:"column:search_count"`
		NewOrdersToday     int64     `gorm:"column:new_orders_today"`
		TodayOrderCost     float64   `gorm:"column:today_order_cost"`
		CumulativeOrders   int64     `gorm:"column:cumulative_orders"`
		CumulativeIncome   float64   `gorm:"column:cumulative_income"`
		CumulativeRecovery float64   `gorm:"column:cumulative_recovery"`
		LastUpdate         time.Time `gorm:"column:last_update"`
	}

	var results []PasswordAggregateResult
	err := query.Select(selectFields).
		Where("pwd IS NOT NULL AND pwd != ''"). // 过滤空口令
		Group("pwd").
		Order("consumption DESC, password_name ASC").
		Limit(10000). // 限制最大导出1万条
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("查询口令聚合数据失败: %w", err)
	}

	// 转换为列表项
	passwords := make([]domain.PasswordListItem, 0, len(results))
	for _, result := range results {
		passwords = append(passwords, domain.PasswordListItem{
			PasswordName:       result.PasswordName,
			AccountName:        result.AccountName,
			Consumption:        result.Consumption,
			ActualConsumption:  result.ActualConsumption,
			Impressions:        result.Impressions,
			ClickCount:         result.ClickCount,
			ClickRate:          result.ClickRate,
			SearchCount:        result.SearchCount,
			NewOrdersToday:     result.NewOrdersToday,
			TodayOrderCost:     result.TodayOrderCost,
			CumulativeOrders:   result.CumulativeOrders,
			CumulativeIncome:   result.CumulativeIncome,
			CumulativeRecovery: result.CumulativeRecovery,
			LastUpdate:         result.LastUpdate.Format("2006-01-02 15:04:05"),
		})
	}

	return passwords, nil
}

// exportPasswordListToExcel 导出口令列表到Excel
func (s *PasswordListService) exportPasswordListToExcel(ctx context.Context, passwords []domain.PasswordListItem, fileName, timeRange string) (string, error) {
	// 这里可以实现Excel导出逻辑
	// 暂时返回一个模拟的文件URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	zap.L().Info("导出口令列表到Excel",
		zap.String("file_name", fileName),
		zap.String("time_range", timeRange),
		zap.Int("count", len(passwords)))

	// TODO: 实现实际的Excel导出逻辑
	// 可以使用 github.com/xuri/excelize/v2 库

	return fileURL, nil
}

// exportPasswordListToCSV 导出口令列表到CSV
func (s *PasswordListService) exportPasswordListToCSV(ctx context.Context, passwords []domain.PasswordListItem, fileName, timeRange string) (string, error) {
	// 这里可以实现CSV导出逻辑
	// 暂时返回一个模拟的文件URL
	fileURL := fmt.Sprintf("/exports/%s", fileName)

	zap.L().Info("导出口令列表到CSV",
		zap.String("file_name", fileName),
		zap.String("time_range", timeRange),
		zap.Int("count", len(passwords)))

	// TODO: 实现实际的CSV导出逻辑
	// 可以使用标准库的 encoding/csv

	return fileURL, nil
}
