package service

import (
	"strconv"

	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
)

// dashboardMediaService 仪表盘媒体服务
type dashboardMediaService struct {
	dashboard *DashboardService
}

// newDashboardMediaService 创建仪表盘媒体服务实例
func newDashboardMediaService(d *DashboardService) *dashboardMediaService {
	return &dashboardMediaService{
		dashboard: d,
	}
}

// GetAllMediaList 获取所有媒体列表
func (s *dashboardMediaService) GetAllMediaList() ([]struct {
	ID   string
	Name string
}, error) {
	var medias []model.Media
	err := s.dashboard.db.Select("id, name").
		Where("status = ?", "active").
		Order("id DESC").
		Find(&medias).Error
	if err != nil {
		return nil, err
	}

	var result []struct {
		ID   string
		Name string
	}

	for _, media := range medias {
		result = append(result, struct {
			ID   string
			Name string
		}{
			ID:   strconv.FormatUint(uint64(media.ID), 10),
			Name: media.Name,
		})
	}

	return result, nil
}

// GetProducts 获取产品列表
func (s *dashboardMediaService) GetProducts() (domain.ProductsEntity, error) {
	// 调用内部函数获取产品列表
	products, err := s.getProducts()
	if err != nil {
		return domain.ProductsEntity{}, err
	}

	// 转换为Domain实体
	domainList := make([]domain.ProductItem, 0, len(products))
	for _, item := range products {
		domainList = append(domainList, domain.ProductItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}

	return domain.ProductsEntity{
		List: domainList,
	}, nil
}

// getProducts 内部函数，从数据库获取产品列表
func (s *dashboardMediaService) getProducts() ([]struct {
	ID   string
	Name string
}, error) {
	var products []model.AdProduct
	var result []struct {
		ID   string
		Name string
	}

	err := s.dashboard.db.Select("id, name").
		Where("status = ?", "active").
		Order("id DESC").
		Find(&products).Error
	if err != nil {
		return result, err
	}

	// 转换为内部格式
	for _, product := range products {
		result = append(result, struct {
			ID   string
			Name string
		}{
			ID:   strconv.FormatUint(uint64(product.ID), 10),
			Name: product.Name,
		})
	}

	return result, nil
}

// GetMediaList 获取媒体列表
func (s *dashboardMediaService) GetMediaList(req domain.DashboardMediaListParam, userID int, userRole int) (domain.MediaListEntity, error) {
	// 构建查询
	query := s.dashboard.db.Model(&model.Media{}).
		Select("id, name").
		Where("status = ?", "active")

	// 根据合作类型过滤
	if req.Type != "" {
		query = query.Where("cooperation_type = ?", req.Type)
	}

	// 如果有用户ID过滤，查询该用户关联的媒体
	if req.UserID != "" && req.UserID != "0" {
		uid, _ := strconv.Atoi(req.UserID)
		if uid > 0 {
			// 查询用户关联的媒体IDs
			var mediaIDs []uint
			s.dashboard.db.Table("user_media_relation").
				Where("user_id = ?", uid).
				Pluck("media_id", &mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("id IN ?", mediaIDs)
			}
		}
	}

	// 执行查询
	var medias []model.Media
	if err := query.Order("id DESC").Find(&medias).Error; err != nil {
		return domain.MediaListEntity{}, err
	}

	// 转换为Domain实体
	var list []domain.MediaItem
	for _, media := range medias {
		list = append(list, domain.MediaItem{
			ID:   strconv.FormatUint(uint64(media.ID), 10),
			Name: media.Name,
		})
	}

	return domain.MediaListEntity{
		List: list,
	}, nil
}

// GetPlans 获取投放计划列表
func (s *dashboardMediaService) GetPlans(req domain.DashboardPlansParam, userID int, userRole int) (domain.PlansEntity, error) {
	// 构建查询
	query := s.dashboard.db.Table("ad_plans").
		Select("id, name").
		Where("status = ?", "active")

	// 如果有媒体账号ID，过滤该媒体账号下的计划
	if req.MediaAccountID != "" && req.MediaAccountID != "0" {
		mediaID, _ := strconv.Atoi(req.MediaAccountID)
		if mediaID > 0 {
			query = query.Where("media_id = ?", mediaID)
		}
	}

	// 执行查询
	type PlanResult struct {
		ID   uint
		Name string
	}
	var plans []PlanResult
	if err := query.Order("id DESC").Find(&plans).Error; err != nil {
		return domain.PlansEntity{}, err
	}

	// 转换为Domain实体
	var list []domain.DashboardPlanItem
	for _, plan := range plans {
		list = append(list, domain.DashboardPlanItem{
			ID:   strconv.FormatUint(uint64(plan.ID), 10),
			Name: plan.Name,
		})
	}

	return domain.PlansEntity{
		List: list,
	}, nil
}
