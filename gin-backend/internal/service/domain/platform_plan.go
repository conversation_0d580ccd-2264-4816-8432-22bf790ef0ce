package domain

import "time"

// MarketTarget 市场目标
type MarketTarget struct {
	Value string // 目标值
	Label string // 目标标签
}

// MarketTargetsParam 市场目标参数
type MarketTargetsParam struct {
	PlatformType string // 平台类型
}

// MarketTargetsResult 市场目标结果
type MarketTargetsResult struct {
	Targets []MarketTarget // 目标列表
}

// UpdatePlanTypeEntity 更新计划类型实体
type UpdatePlanTypeEntity struct {
	ID       int64  // 计划ID
	PlanType string // 计划类型
}

// PlatformPlanCreateParam 创建平台计划参数
type PlatformPlanCreateParam struct {
	AgentID          int64  // 代理商ID
	MediaID          int64  // 媒体ID
	PlatformType     string // 平台类型
	Name             string // 计划名称
	PlanType         string // 计划类型
	MarketTargetName string // 营销目标名称
	Remark           string // 备注
	DataID           string // 第三方数据ID
	PrincipalAccount string // 负责人账号
	PrincipalName    string // 负责人姓名
	SceneName        string // 场景名称
}

// PlatformPlanUpdateParam 更新平台计划参数
type PlatformPlanUpdateParam struct {
	ID               int64  // 计划ID
	Name             string // 计划名称
	PlanType         string // 计划类型
	MarketTargetName string // 营销目标名称
	Remark           string // 备注
	Status           int    // 状态
}

// PlatformPlanListParam 平台计划列表参数
type PlatformPlanListParam struct {
	Page             int    // 页码
	PageSize         int    // 每页数量
	Size             int    // 每页数量(兼容)
	PlatformType     string // 平台类型
	PlanName         string // 计划名称
	AgentID          int64  // 代理商ID
	MediaID          int64  // 媒体ID
	MarketTargetName string // 营销目标名称
	Keyword          string // 关键词
}

// PlatformPlanListResult 平台计划列表结果
type PlatformPlanListResult struct {
	Total int64              // 总数
	List  []PlatformPlanItem // 列表项
}

// PlatformPlanItem 平台计划列表项
type PlatformPlanItem struct {
	ID               int64     // 计划ID
	AgentID          int64     // 代理商ID
	MediaID          int64     // 媒体ID
	DataID           string    // 第三方数据ID
	Name             string    // 计划名称
	PlatformType     string    // 平台类型
	PlanType         string    // 计划类型
	MarketTargetName string    // 营销目标名称
	AgentName        string    // 代理商名称
	PrincipalAccount string    // 负责人账号
	PrincipalName    string    // 负责人姓名
	SceneName        string    // 场景名称
	Remark           string    // 备注
	MediaName        string    // 媒体名称
	CreatedAt        time.Time // 创建时间
	UpdatedAt        time.Time // 更新时间
}
