package service

import (
	"errors"
	"fmt"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// DepartmentService 部门服务实现
type DepartmentService struct {
	db *gorm.DB
}

// NewDepartmentService 创建部门服务实例
func NewDepartmentService() *DepartmentService {
	return &DepartmentService{
		db: global.DB,
	}
}

// GetDepartments 获取部门列表
func (s *DepartmentService) GetDepartments(param domain.GetDepartmentsParam) (domain.GetDepartmentsResult, error) {
	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}
	if param.PageSize > 100 {
		param.PageSize = 100
	}

	var departments []model.Department
	var total int64

	query := s.db.Model(&model.Department{})

	// 应用搜索条件
	if param.Keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ?", "%"+param.Keyword+"%", "%"+param.Keyword+"%")
	}
	if param.Status > 0 {
		query = query.Where("status = ?", param.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.GetDepartmentsResult{}, fmt.Errorf("获取部门总数失败: %w", err)
	}

	// 获取分页数据
	if err := query.Offset((param.Page - 1) * param.PageSize).
		Limit(param.PageSize).
		Order("sort_order ASC, id ASC").
		Find(&departments).Error; err != nil {
		return domain.GetDepartmentsResult{}, fmt.Errorf("获取部门列表失败: %w", err)
	}

	// 转换为domain实体
	list := make([]domain.DepartmentEntity, len(departments))
	for i, dept := range departments {
		list[i] = s.convertModelToDepartmentEntity(dept)
	}

	return domain.GetDepartmentsResult{
		List:  list,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// GetDepartmentTree 获取部门树形结构
func (s *DepartmentService) GetDepartmentTree() ([]domain.DepartmentTreeNode, error) {
	var departments []model.Department

	if err := s.db.Model(&model.Department{}).
		Order("sort_order ASC, id ASC").
		Find(&departments).Error; err != nil {
		return nil, fmt.Errorf("获取部门列表失败: %w", err)
	}

	// 转换为树节点结构
	tree := make([]domain.DepartmentTreeNode, len(departments))
	for i, dept := range departments {
		tree[i] = s.convertModelToDepartmentTreeNode(dept)
	}

	return tree, nil
}

// GetDepartmentOptions 获取部门选项
func (s *DepartmentService) GetDepartmentOptions(param domain.GetDepartmentOptionsParam) (domain.GetDepartmentOptionsResult, error) {
	var departments []model.Department

	query := s.db.Model(&model.Department{})
	if param.ExcludeID > 0 {
		query = query.Where("id != ?", param.ExcludeID)
	}

	if err := query.Order("sort_order ASC, id ASC").Find(&departments).Error; err != nil {
		return domain.GetDepartmentOptionsResult{}, fmt.Errorf("获取部门选项失败: %w", err)
	}

	// 转换为选项结构
	options := make([]domain.DepartmentOption, len(departments))
	for i, dept := range departments {
		options[i] = s.convertModelToDepartmentOption(dept)
	}

	return domain.GetDepartmentOptionsResult{
		Options: options,
	}, nil
}

// GetDepartmentByID 根据ID获取部门详情
func (s *DepartmentService) GetDepartmentByID(id int64) (domain.DepartmentEntity, error) {
	var department model.Department

	if err := s.db.First(&department, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.DepartmentEntity{}, fmt.Errorf("部门不存在")
		}
		return domain.DepartmentEntity{}, fmt.Errorf("获取部门详情失败: %w", err)
	}

	return s.convertModelToDepartmentEntity(department), nil
}

// CreateDepartment 创建部门
func (s *DepartmentService) CreateDepartment(entity domain.CreateDepartmentEntity) (domain.DepartmentEntity, error) {
	// 验证部门编码是否已存在
	var count int64
	if err := s.db.Model(&model.Department{}).Where("code = ?", entity.Code).Count(&count).Error; err != nil {
		return domain.DepartmentEntity{}, fmt.Errorf("检查部门编码失败: %w", err)
	}
	if count > 0 {
		return domain.DepartmentEntity{}, fmt.Errorf("部门编码已存在")
	}

	// 验证上级部门
	if entity.ParentID > 0 {
		if err := s.checkParentValid(entity.ParentID, 0); err != nil {
			return domain.DepartmentEntity{}, err
		}
	}

	// 验证部门负责人
	if entity.ManagerID > 0 {
		if err := s.validateManager(entity.ManagerID); err != nil {
			return domain.DepartmentEntity{}, err
		}
	}

	// 创建部门
	department := &model.Department{
		Name:        entity.Name,
		Code:        entity.Code,
		ParentID:    entity.ParentID,
		ManagerID:   entity.ManagerID,
		Description: entity.Description,
		SortOrder:   entity.SortOrder,
		Status:      entity.Status,
	}

	if err := s.db.Create(department).Error; err != nil {
		return domain.DepartmentEntity{}, fmt.Errorf("创建部门失败: %w", err)
	}

	// 获取创建后的完整信息
	return s.GetDepartmentByID(department.ID)
}

// UpdateDepartment 更新部门
func (s *DepartmentService) UpdateDepartment(id int64, entity domain.UpdateDepartmentEntity) (domain.DepartmentEntity, error) {
	// 检查部门是否存在
	var department model.Department
	if err := s.db.First(&department, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return domain.DepartmentEntity{}, fmt.Errorf("部门不存在")
		}
		return domain.DepartmentEntity{}, fmt.Errorf("获取部门信息失败: %w", err)
	}

	// 验证部门编码是否已存在（排除自己）
	var count int64
	if err := s.db.Model(&model.Department{}).Where("code = ? AND id != ?", entity.Code, id).Count(&count).Error; err != nil {
		return domain.DepartmentEntity{}, fmt.Errorf("检查部门编码失败: %w", err)
	}
	if count > 0 {
		return domain.DepartmentEntity{}, fmt.Errorf("部门编码已存在")
	}

	// 验证上级部门
	if entity.ParentID > 0 {
		if err := s.checkParentValid(entity.ParentID, id); err != nil {
			return domain.DepartmentEntity{}, err
		}
	}

	// 验证部门负责人
	if entity.ManagerID > 0 {
		if err := s.validateManager(entity.ManagerID); err != nil {
			return domain.DepartmentEntity{}, err
		}
	}

	// 更新部门
	department.Name = entity.Name
	department.Code = entity.Code
	department.ParentID = entity.ParentID
	department.ManagerID = entity.ManagerID
	department.Description = entity.Description
	department.SortOrder = entity.SortOrder
	department.Status = entity.Status

	if err := s.db.Save(&department).Error; err != nil {
		return domain.DepartmentEntity{}, fmt.Errorf("更新部门失败: %w", err)
	}

	// 获取更新后的完整信息
	return s.GetDepartmentByID(id)
}

// DeleteDepartment 删除部门
func (s *DepartmentService) DeleteDepartment(id uint64) error {
	// 检查部门是否存在
	var department model.Department
	if err := s.db.First(&department, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("部门不存在")
		}
		return fmt.Errorf("获取部门信息失败: %w", err)
	}

	// 检查是否有子部门
	var childrenCount int64
	if err := s.db.Model(&model.Department{}).Where("parent_id = ?", id).Count(&childrenCount).Error; err != nil {
		return fmt.Errorf("检查子部门失败: %w", err)
	}
	if childrenCount > 0 {
		return fmt.Errorf("该部门下有子部门，不能删除")
	}

	// 检查是否有用户关联
	var userCount int64
	if err := s.db.Model(&model.User{}).Where("department_id = ?", id).Count(&userCount).Error; err != nil {
		return fmt.Errorf("检查关联用户失败: %w", err)
	}
	if userCount > 0 {
		return fmt.Errorf("该部门下有用户，不能删除")
	}

	// 执行删除
	if err := s.db.Delete(&department).Error; err != nil {
		return fmt.Errorf("删除部门失败: %w", err)
	}

	return nil
}

// GetDepartmentStatistics 获取部门统计信息
func (s *DepartmentService) GetDepartmentStatistics() (map[string]int64, error) {
	stats := make(map[string]int64)

	// 获取部门总数
	var totalCount int64
	if err := s.db.Model(&model.Department{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("获取部门总数失败: %w", err)
	}
	stats["total"] = totalCount

	// 获取启用部门数
	var activeCount int64
	if err := s.db.Model(&model.Department{}).Where("status = ?", 1).Count(&activeCount).Error; err != nil {
		return nil, fmt.Errorf("获取启用部门数失败: %w", err)
	}
	stats["active"] = activeCount

	// 获取禁用部门数
	var inactiveCount int64
	if err := s.db.Model(&model.Department{}).Where("status = ?", 0).Count(&inactiveCount).Error; err != nil {
		return nil, fmt.Errorf("获取禁用部门数失败: %w", err)
	}
	stats["inactive"] = inactiveCount

	return stats, nil
}

// validateManager 验证部门负责人是否存在
func (s *DepartmentService) validateManager(managerID int64) error {
	var count int64
	if err := s.db.Model(&model.User{}).Where("id = ?", managerID).Count(&count).Error; err != nil {
		return fmt.Errorf("验证部门负责人失败: %w", err)
	}
	if count == 0 {
		return fmt.Errorf("部门负责人不存在")
	}
	return nil
}

// checkParentValid 检查上级部门是否合法
func (s *DepartmentService) checkParentValid(parentID, currentID int64) error {
	// 检查上级部门是否存在
	var parent model.Department
	if err := s.db.First(&parent, parentID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("上级部门不存在")
		}
		return fmt.Errorf("获取上级部门信息失败: %w", err)
	}

	// 如果是更新操作，检查上级部门不是自己
	if currentID > 0 && parentID == currentID {
		return fmt.Errorf("上级部门不能是自己")
	}

	// 检查上级部门不是自己的子部门（防止循环引用）
	if currentID > 0 {
		var tempParentID = parent.ParentID
		for tempParentID > 0 {
			if tempParentID == currentID {
				return fmt.Errorf("上级部门不能是自己的子部门")
			}

			var tempDept model.Department
			if err := s.db.Select("parent_id").First(&tempDept, tempParentID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					break
				}
				return fmt.Errorf("检查上级部门层级失败: %w", err)
			}
			tempParentID = tempDept.ParentID
		}
	}

	return nil
}

// convertModelToDepartmentEntity 将模型实体转换为领域实体
func (s *DepartmentService) convertModelToDepartmentEntity(dept model.Department) domain.DepartmentEntity {
	entity := domain.DepartmentEntity{
		ID:          dept.ID,
		Name:        dept.Name,
		Code:        dept.Code,
		ParentID:    dept.ParentID,
		Description: dept.Description,
		SortOrder:   dept.SortOrder,
		Status:      dept.Status,
		CreatedAt:   dept.CreatedAt,
		UpdatedAt:   dept.UpdatedAt,
		HasChildren: len(dept.Children) > 0,
	}

	// 填充关联信息
	if dept.Parent != nil {
		entity.ParentName = dept.Parent.Name
	}
	if dept.Manager != nil {
		entity.ManagerName = dept.Manager.RealName
		if entity.ManagerName == "" {
			entity.ManagerName = dept.Manager.Name
		}
	}

	// 递归处理子部门
	if len(dept.Children) > 0 {
		entity.Children = make([]domain.DepartmentEntity, len(dept.Children))
		for i, child := range dept.Children {
			entity.Children[i] = s.convertModelToDepartmentEntity(child)
		}
	}

	return entity
}

// convertModelToDepartmentTreeNode 将模型树节点转换为领域树节点
func (s *DepartmentService) convertModelToDepartmentTreeNode(dept model.Department) domain.DepartmentTreeNode {
	node := domain.DepartmentTreeNode{
		ID:       dept.ID,
		Name:     dept.Name,
		Code:     dept.Code,
		ParentID: dept.ParentID,
	}

	if len(dept.Children) > 0 {
		node.Children = make([]domain.DepartmentTreeNode, len(dept.Children))
		for i, child := range dept.Children {
			node.Children[i] = s.convertModelToDepartmentTreeNode(child)
		}
	}

	return node
}

// convertModelToDepartmentOption 将模型选项转换为领域选项
func (s *DepartmentService) convertModelToDepartmentOption(dept model.Department) domain.DepartmentOption {
	option := domain.DepartmentOption{
		ID:       dept.ID,
		Name:     dept.Name,
		Code:     dept.Code,
		ParentID: dept.ParentID,
	}

	if len(dept.Children) > 0 {
		option.Children = make([]domain.DepartmentOption, len(dept.Children))
		for i, child := range dept.Children {
			option.Children[i] = s.convertModelToDepartmentOption(child)
		}
	}

	return option
}
