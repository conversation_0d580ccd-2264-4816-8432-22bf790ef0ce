package promotion

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client 推广链接客户端
type Client struct {
	httpClient *http.Client
	baseURL    string
}

// ClientConfig 客户端配置
type ClientConfig struct {
	BaseURL string
	Timeout time.Duration
}

// NewClient 创建新的推广链接客户端
func NewClient(config *ClientConfig) *Client {
	timeout := 30 * time.Second
	baseURL := "http://adpro.biyingniao.com"

	if config != nil {
		if config.Timeout > 0 {
			timeout = config.Timeout
		}
		if config.BaseURL != "" {
			baseURL = config.BaseURL
		}
	}

	return &Client{
		httpClient: &http.Client{
			Timeout: timeout,
		},
		baseURL: baseURL,
	}
}

// PromotionLinkResponse API响应
type PromotionLinkResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// PromotionLinkResult 推广链接结果
type PromotionLinkResult struct {
	PromotionLink   string `json:"promotion_link"`
	PromotionQrcode string `json:"promotion_qrcode"`
}

// GeneratePromotionLink 生成推广链接
func (c *Client) GeneratePromotionLink(planID, productID uint64) (*PromotionLinkResult, error) {
	// 构建请求参数
	reqData := map[string]interface{}{
		"plan_id":    planID,
		"product_id": productID,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 发送POST请求
	url := fmt.Sprintf("%s/api/promotion/link", c.baseURL)
	resp, err := c.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("调用推广链接生成接口失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp PromotionLinkResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析推广链接响应失败: %v", err)
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("生成推广链接失败: %s", apiResp.Message)
	}

	if apiResp.Data == nil {
		return nil, fmt.Errorf("推广链接响应数据为空")
	}

	// 提取推广链接和二维码
	promotionLink, ok := apiResp.Data["promotion_link"].(string)
	if !ok {
		return nil, fmt.Errorf("推广链接格式错误")
	}

	promotionQrcode, ok := apiResp.Data["promotion_qrcode"].(string)
	if !ok {
		return nil, fmt.Errorf("推广二维码格式错误")
	}

	return &PromotionLinkResult{
		PromotionLink:   promotionLink,
		PromotionQrcode: promotionQrcode,
	}, nil
}
