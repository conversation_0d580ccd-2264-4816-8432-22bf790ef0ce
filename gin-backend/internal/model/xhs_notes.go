package model

import "time"

type NoteImageList []string

// XhsNotes 笔记基本信息结构
type XhsNotes struct {
	Id                     int64         // id
	CreatedAt              time.Time     // 创建时间
	UpdatedAt              time.Time     // 更新时间
	NoteID                 string        `json:"note_id"`                                             // 笔记ID
	Image                  string        `json:"image"`                                               // 笔记封面图
	Desc                   string        `json:"desc"`                                                // 笔记内容
	CreateTime             int64         `json:"create_time"`                                         // 创建时间(毫秒时间戳)
	Author                 string        `json:"author"`                                              // 作者名称
	AuthorImage            string        `json:"author_image"`                                        // 作者头像
	Status                 int           `json:"status"`                                              // 笔记状态 (0:正常, 1:不匹配, 2:非法, 3:删除等)
	NoteContentType        int           `json:"note_content_type"`                                   // 笔记类型 (1:图文, 2:视频)
	CooperateState         bool          `json:"cooperate_state"`                                     // 是否合作笔记
	Title                  string        `json:"title"`                                               // 笔记标题
	ImageList              NoteImageList `gorm:"column:image_list;serializer:json" json:"image_list"` // 封面图片列表
	CooperateComponentType int           `json:"cooperate_component_type"`                            // 合作组件类型 (1-6, 0表示无意义)
	CrowdCreationNote      bool          `json:"crowd_creation_note"`                                 // 是否为共创笔记
	ItemID                 string        `json:"item_id"`                                             // 商品ID (base_only=false时返回)
	ReadCount              int           `json:"read_count"`                                          // 阅读数
	ReadRate               string        `json:"read_rate"`                                           // 阅读率
	InteractCount          int           `json:"interact_count"`                                      // 互动数
	InteractRate           string        `json:"interact_rate"`                                       // 互动率
	HighQuality            int           `json:"high_quality"`                                        // 是否优质笔记 (0:否, 1:是)
	HighPotential          int           `json:"high_potential"`                                      // 是否高潜笔记 (0:否, 1:是)
	OutsideShopVisit       int           `json:"outside_shop_visit"`                                  // 站外进店量
	OutsideShopVisitRate   string        `json:"outside_shop_visitRate"`                              // 站外进店率
	Taxonomy1              string        `json:"taxonomy1"`                                           // 一级类目
	Taxonomy2              string        `json:"taxonomy2"`                                           // 二级类目
	Taxonomy3              string        `json:"taxonomy3"`                                           // 三级类目
	IsHitStrategy          int           `json:"is_hit_strategy"`                                     // 是否命中策略 (0:未命中, 1:命中)
	HitStrategyContent     string        `json:"hit_strategy_content"`                                // 命中策略内容
	WinHorseNote           bool          `json:"win_horse_note"`                                      // 是否为优胜笔记
	StaffTag               string        `json:"staff_tag"`                                           // 员工标签 (仅员工笔记)
	StaffArea              string        `json:"staff_area"`                                          // 地域 (仅员工笔记)
	NoteURL                string        `json:"note_url"`                                            // 笔记链接 (有效期两个月)
	HasShopCard            bool          `json:"has_shop_card"`                                       // 是否为商品笔记
}

func (XhsNotes) TableName() string {
	return "xhs_notes"
}
