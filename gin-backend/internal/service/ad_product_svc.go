package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// AdProductService 投放产品服务
type AdProductService struct {
	db *gorm.DB
}

// NewAdProductService 创建投放产品服务实例
func NewAdProductService() *AdProductService {
	return &AdProductService{
		db: global.DB,
	}
}

// GetList 获取投放产品列表
func (s *AdProductService) GetList(ctx context.Context, param domain.AdProductParam) (domain.AdProductListEntity, error) {
	var products []model.AdProduct
	var total int64

	query := s.db.WithContext(ctx).Model(&model.AdProduct{}).Where("deleted_at IS NULL")

	// 添加筛选条件
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Status != "" {
		query = query.Where("status = ?", param.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.AdProductListEntity{}, fmt.Errorf("获取投放产品总数失败: %w", err)
	}
	// 获取分页数据
	offset := (param.Page - 1) * param.Size
	if err := query.Offset(offset).Limit(param.Size).Order("id DESC").Find(&products).Error; err != nil {
		return domain.AdProductListEntity{}, fmt.Errorf("获取投放产品列表失败: %w", err)
	}

	// 转换为领域模型
	productInfos := make([]domain.AdProductInfo, 0, len(products))
	for _, product := range products {
		info := modelToDomainProductInfo(product)
		productInfos = append(productInfos, info)
	}

	return domain.AdProductListEntity{
		List:     productInfos,
		Total:    total,
		Page:     param.Page,
		PageSize: param.Size,
	}, nil
}

// modelToDomainProductInfo 辅助函数：将model.AdProduct转换为domain.AdProductInfo
func modelToDomainProductInfo(model model.AdProduct) domain.AdProductInfo {
	statusText := getStatusText(model.Status)
	return domain.AdProductInfo{
		ID:          model.ID,
		Code:        model.Code,
		Name:        model.Name,
		Image:       model.Image,
		Description: model.Description,
		Status:      model.Status,
		StatusText:  statusText,
	}
}

// getStatusText 获取状态文本
func getStatusText(status string) string {
	text, exists := domain.ProductStatusMap[status]
	if !exists {
		return "未知"
	}
	return text
}

// GetByID 根据ID获取投放产品详情
func (s *AdProductService) GetByID(ctx context.Context, id uint64) (domain.AdProductEntity, error) {
	var product model.AdProduct
	if err := s.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.AdProductEntity{}, fmt.Errorf("投放产品不存在")
		}
		return domain.AdProductEntity{}, fmt.Errorf("获取投放产品详情失败: %w", err)
	}

	return modelToDomainEntity(product), nil
}

// modelToDomainEntity 辅助函数：将model.AdProduct转换为domain.AdProductEntity
func modelToDomainEntity(model model.AdProduct) domain.AdProductEntity {
	var createdBy, updatedBy int64

	return domain.AdProductEntity{
		ID:          model.ID,
		Code:        model.Code,
		Name:        model.Name,
		Image:       model.Image,
		Description: model.Description,
		Status:      model.Status,
		CreatedBy:   createdBy,
		UpdatedBy:   updatedBy,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	}
}
