package model

import (
	"encoding/json"
	"time"
)

// Media 媒体模型 - 对应数据库ad_media表
type Media struct {
	ID        int64     `gorm:"primarykey;column:id"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	DeletedAt time.Time `gorm:"column:deleted_at"`

	Code              string  `gorm:"column:code;uniqueIndex;size:255"`              // 媒体编号
	AdAgentID         int64   `gorm:"column:ad_agent_id;index"`                      // 代理ID
	Name              string  `gorm:"column:name;uniqueIndex;size:255"`              // 媒体名称
	AuditStatus       string  `gorm:"column:audit_status;default:pending"`           // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	CooperationStatus string  `gorm:"column:cooperation_status;default:not_started"` // 合作状态：not_started-未开始,active-合作中,terminated-已终止
	CooperationType   string  `gorm:"column:cooperation_type;default:traffic"`       // 合作类型：traffic-流量采买,cps-CPS合作,dh-灯火投放
	Account           string  `gorm:"column:account;size:255"`                       // 账号
	Password          string  `gorm:"column:password;size:255"`                      // 密码（不返回给前端）
	LastLoginAt       string  `gorm:"column:last_login_at;type:longtext"`            // 最后登录时间
	Balance           float64 `gorm:"column:balance;default:0"`                      // 余额
	Types             string  `gorm:"column:types;type:json"`                        // 媒体类型（JSON存储）
	Industry          string  `gorm:"column:industry;size:255"`                      // 所属行业
	CustomIndustry    string  `gorm:"column:custom_industry;size:255"`               // 自定义行业
	DailyActivity     string  `gorm:"column:daily_activity;size:255"`                // 日活
	TransactionVolume string  `gorm:"column:transaction_volume;size:255"`            // 交易量
	RegionCodes       string  `gorm:"column:region_codes;type:json"`                 // 区域代码（JSON存储）
	CompanyName       string  `gorm:"column:company_name;size:255"`                  // 公司名称
	CompanyAddress    string  `gorm:"column:company_address;size:255"`               // 公司地址
	ContactName       string  `gorm:"column:contact_name;size:255"`                  // 联系人
	ContactPhone      string  `gorm:"column:contact_phone;size:255"`                 // 联系电话
	RejectReason      string  `gorm:"column:reject_reason;size:255"`                 // 拒绝原因
	UserID            int64   `gorm:"column:user_id;index"`                          // 用户ID
	Remark            string  `gorm:"column:remark;size:255"`                        // 备注
	CreatedBy         int64   `gorm:"column:created_by"`                             // 创建人ID
	UpdatedBy         int64   `gorm:"column:updated_by"`                             // 更新人ID
	InstanceID        string  `gorm:"column:instance_id;size:255;default:''"`        // 审批实例ID
	PlatformConfig    string  `gorm:"column:platform_config;type:json"`              // 平台配置信息
}

// TableName 指定表名
func (Media) TableName() string {
	return "ad_media"
}

// MediaTypes 媒体类型结构
type MediaTypes []string

// GetTypes 获取媒体类型列表
func (m *Media) GetTypes() (MediaTypes, error) {
	if m.Types == "" {
		return MediaTypes{}, nil
	}

	var types MediaTypes
	err := json.Unmarshal([]byte(m.Types), &types)
	if err != nil {
		return nil, err
	}
	return types, nil
}

// SetTypes 设置媒体类型列表
func (m *Media) SetTypes(types MediaTypes) error {
	if types == nil {
		m.Types = ""
		return nil
	}

	data, err := json.Marshal(types)
	if err != nil {
		return err
	}
	m.Types = string(data)
	return nil
}

// GetRegionCodes 获取地区编码列表
func (m *Media) GetRegionCodes() ([]string, error) {
	if m.RegionCodes == "" {
		return []string{}, nil
	}

	var regionCodes []string
	err := json.Unmarshal([]byte(m.RegionCodes), &regionCodes)
	if err != nil {
		return []string{}, err
	}

	return regionCodes, nil
}

// SetRegionCodes 设置地区编码列表
func (m *Media) SetRegionCodes(regionCodes []string) error {
	if regionCodes == nil {
		regionCodes = []string{}
	}

	data, err := json.Marshal(regionCodes)
	if err != nil {
		return err
	}
	m.RegionCodes = string(data)
	return nil
}

// MediaPlatformConfig 媒体平台配置结构
type MediaPlatformConfig struct {
	Platform string // 平台类型
	Info     any    // 平台配置信息（根据平台类型决定具体结构）
}

// DenghuoPlatformInfo 灯火平台配置信息
type DenghuoPlatformInfo struct {
	PID   string `json:"pid"`   // 项目ID（字符串类型避免精度丢失）
	Token string `json:"token"` // API Token
}

// XiaohongshuPlatformInfo 小红书平台配置信息
type XiaohongshuPlatformInfo struct {
	AppID     string `json:"appId"`     // 应用ID
	AppSecret string `json:"appSecret"` // 应用密钥
}

// GetPlatformConfig 获取平台配置
func (m *Media) GetPlatformConfig() (MediaPlatformConfig, error) {
	if m.PlatformConfig == "" {
		return MediaPlatformConfig{}, nil
	}

	var config = &MediaPlatformConfig{}
	err := json.Unmarshal([]byte(m.PlatformConfig), &config)
	if err != nil {
		return MediaPlatformConfig{}, err
	}

	// 根据平台类型，将info转换为具体的结构
	switch config.Platform {
	case "denghuoplus":
		var info DenghuoPlatformInfo
		if config.Info != nil {
			// 先将interface{}转为json，再解析为具体结构
			infoBytes, err := json.Marshal(config.Info)
			if err != nil {
				return MediaPlatformConfig{}, err
			}
			if err := json.Unmarshal(infoBytes, &info); err != nil {
				return MediaPlatformConfig{}, err
			}
			config.Info = info
		}
	case "xiaohongshu":
		var info XiaohongshuPlatformInfo
		if config.Info != nil {
			infoBytes, err := json.Marshal(config.Info)
			if err != nil {
				return MediaPlatformConfig{}, err
			}
			if err := json.Unmarshal(infoBytes, &info); err != nil {
				return MediaPlatformConfig{}, err
			}
			config.Info = info
		}
	}

	return *config, nil
}

// SetPlatformConfig 设置平台配置
func (m *Media) SetPlatformConfig(config *MediaPlatformConfig) error {
	if config == nil {
		m.PlatformConfig = ""
		return nil
	}

	// 确保数据按正确的结构序列化
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}
	m.PlatformConfig = string(data)
	return nil
}

// 媒体类型常量
const (
	MediaTypeWechat   = "wechat"   // 微信
	MediaTypeAlipay   = "alipay"   // 支付宝
	MediaTypeDouyin   = "douyin"   // 抖音
	MediaTypeKuaishou = "kuaishou" // 快手
	MediaTypeOther    = "other"    // 其他
)

// 所属行业常量
const (
	IndustryPayment  = "payment"  // 支付
	IndustryExpress  = "express"  // 快递
	IndustryInvoice  = "invoice"  // 开票
	IndustryParking  = "parking"  // 停车
	IndustryCharging = "charging" // 充电
	IndustryDevice   = "device"   // 设备
	IndustryOther    = "other"    // 其他
)

// 合作类型常量
const (
	CooperationTypeCPS      = "cps"     // CPS合作
	CooperationTypeTraffic  = "traffic" // 流量采买
	CooperationTypeDelivery = "dh"      // 灯火投放
)

// 媒体相关常量
const (
	// 审核状态
	MediaAuditStatusPending  = "pending"  // 待审核
	MediaAuditStatusApproved = "approved" // 已通过
	MediaAuditStatusRejected = "rejected" // 已拒绝

	// 合作状态
	MediaCooperationStatusNotStarted = "not_started" // 未开始
	MediaCooperationStatusActive     = "active"      // 合作中
	MediaCooperationStatusTerminated = "terminated"  // 已终止

	// 合作类型
	MediaCooperationTypeCPS      = "cps"     // CPS合作
	MediaCooperationTypeTraffic  = "traffic" // 流量采买
	MediaCooperationTypeDelivery = "dh"      // 灯火投放
)
