package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdAccountController 广告账号控制器
type AdAccountController struct {
	adAccountService *service.AdAccountService
}

// NewAdAccountController 创建广告账号控制器实例
func NewAdAccountController() *AdAccountController {
	return &AdAccountController{
		adAccountService: service.NewAdAccountService(),
	}
}

// GetAdAccounts 获取广告账号层级结构列表
// @Summary 获取广告账号层级结构列表
// @Description 获取广告账号层级结构列表，返回主账号下挂子账号的结构，支持分页和筛选
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param account_name query string false "账号名称/ID筛选"
// @Param platform query int false "平台筛选"
// @Param authorization_status query int false "授权状态筛选"
// @Param usage_status query int false "使用状态筛选"
// @Param account_type query int false "账号类型筛选（仅对主账号生效）"
// @Param parent_id query int false "父账号ID筛选（仅对主账号生效）"
// @Success 200 {object} response.Response{data=vo.GetAdAccountsHierarchyResp}
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts [get]
func (ac *AdAccountController) GetAdAccounts(c *gin.Context) {
	var req vo.GetAdAccountsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	// 调用层级结构方法，返回主账号下挂子账号的结构
	result, err := ac.adAccountService.GetAdAccountsHierarchy(c, param)
	if err != nil {
		response.ServerError(c, "获取广告账号列表失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.GetAdAccountsHierarchyRespFromResult(result)
	response.Success(c, resp)
}

// CreateAdAccount 创建广告账号
// @Summary 创建广告账号
// @Description 创建新的广告账号（主账号或子账号）
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param request body vo.CreateAdAccountReq true "创建广告账号请求"
// @Success 200 {object} response.Response{data=vo.CreateAdAccountResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts [post]
func (ac *AdAccountController) CreateAdAccount(c *gin.Context) {
	var req vo.CreateAdAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := ac.adAccountService.CreateAdAccount(c, param)
	if err != nil {
		response.ServerError(c, "创建广告账号失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.CreateAdAccountRespFromResult(result)
	response.Success(c, resp)
}

// UpdateAdAccount 更新广告账号
// @Summary 更新广告账号
// @Description 更新广告账号信息
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param id path uint64 true "账号ID"
// @Param request body vo.UpdateAdAccountReq true "更新广告账号请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/{id} [put]
func (ac *AdAccountController) UpdateAdAccount(c *gin.Context) {
	// 获取路径参数中的账号ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的账号ID")
		return
	}

	var req vo.UpdateAdAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 确保路径中的ID与请求体中的ID一致
	req.ID = id

	// 使用VO提供的转换方法
	param := req.ToParam()

	err = ac.adAccountService.UpdateAdAccount(c, param)
	if err != nil {
		response.ServerError(c, "更新广告账号失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// DeleteAdAccount 删除广告账号
// @Summary 删除广告账号
// @Description 删除广告账号（硬删除：真正删除数据）
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param id path uint64 true "账号ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/{id} [delete]
func (ac *AdAccountController) DeleteAdAccount(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的账号ID")
		return
	}

	err = ac.adAccountService.DeleteAdAccount(c, id)
	if err != nil {
		response.ServerError(c, "删除广告账号失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// GetAdAccountByID 获取广告账号详情
// @Summary 获取广告账号详情
// @Description 根据ID获取广告账号详情，主账号会包含子账号列表
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param id path uint64 true "账号ID"
// @Success 200 {object} response.Response{data=vo.GetAdAccountDetailResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/{id} [get]
func (ac *AdAccountController) GetAdAccountByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的账号ID")
		return
	}

	entity, subAccounts, err := ac.adAccountService.GetAdAccountByID(c, id)
	if err != nil {
		response.ServerError(c, "获取广告账号详情失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.GetAdAccountDetailRespFromEntity(entity, subAccounts)
	response.Success(c, resp)
}

// GetAdAccountOptions 获取广告账号选项
// @Summary 获取广告账号选项
// @Description 获取广告账号管理相关选项，包括平台、账号类型和父账号
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=vo.GetAdAccountOptionsResp}
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/options [get]
func (ac *AdAccountController) GetAdAccountOptions(c *gin.Context) {
	// 获取广告账号选项
	result, err := ac.adAccountService.GetAdAccountOptions(c)
	if err != nil {
		response.ServerError(c, "获取广告账号选项失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.GetAdAccountOptionsRespFromResult(result)
	response.Success(c, resp)
}

// CreateMasterAccount 创建主账号
// @Summary 创建主账号
// @Description 创建主账号的便捷接口
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param request body vo.CreateAdAccountReq true "创建主账号请求"
// @Success 200 {object} response.Response{data=vo.CreateAdAccountResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/master [post]
func (ac *AdAccountController) CreateMasterAccount(c *gin.Context) {
	var req vo.CreateAdAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 强制设置为主账号
	req.AccountType = 1
	req.ParentId = 0

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := ac.adAccountService.CreateAdAccount(c, param)
	if err != nil {
		response.ServerError(c, "创建主账号失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.CreateAdAccountRespFromResult(result)
	response.Success(c, resp)
}

// XiaohongshuAuth 小红书账号授权
// @Summary 小红书账号授权
// @Description 使用授权码为小红书广告账号进行授权
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param request body vo.XiaohongshuAuthReq true "小红书授权请求"
// @Success 200 {object} response.Response{data=vo.XiaohongshuAuthResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/xiaohongshu/auth [post]
func (ac *AdAccountController) XiaohongshuAuth(c *gin.Context) {
	var req vo.XiaohongshuAuthReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := ac.adAccountService.XiaohongshuAuth(c, param)
	if err != nil {
		response.ServerError(c, "小红书账号授权失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.XiaohongshuAuthRespFromResult(result)
	response.Success(c, resp)
}

// XiaohongshuRefreshToken 小红书刷新令牌
// @Summary 小红书刷新令牌
// @Description 刷新小红书广告账号的访问令牌
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param request body vo.XiaohongshuRefreshTokenReq true "小红书刷新令牌请求"
// @Success 200 {object} response.Response{data=vo.XiaohongshuRefreshTokenResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/xiaohongshu/refresh-token [post]
func (ac *AdAccountController) XiaohongshuRefreshToken(c *gin.Context) {
	var req vo.XiaohongshuRefreshTokenReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := ac.adAccountService.XiaohongshuRefreshToken(c, param)
	if err != nil {
		response.ServerError(c, "小红书刷新令牌失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.XiaohongshuRefreshTokenRespFromResult(result)
	response.Success(c, resp)
}

// CreateSubAccount 创建子账号
// @Summary 创建子账号
// @Description 创建子账号的便捷接口
// @Tags 广告账号管理
// @Accept json
// @Produce json
// @Param request body vo.CreateAdAccountReq true "创建子账号请求"
// @Success 200 {object} response.Response{data=vo.CreateAdAccountResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-accounts/sub [post]
func (ac *AdAccountController) CreateSubAccount(c *gin.Context) {
	var req vo.CreateAdAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 强制设置为子账号
	req.AccountType = 2

	// 验证父账号ID
	if req.ParentId <= 0 {
		response.InvalidParam(c, "创建子账号必须指定父账号ID")
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := ac.adAccountService.CreateAdAccount(c, param)
	if err != nil {
		response.ServerError(c, "创建子账号失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.CreateAdAccountRespFromResult(result)
	response.Success(c, resp)
}
