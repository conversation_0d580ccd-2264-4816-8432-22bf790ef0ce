package domain

import (
	"time"
)

// PlanRelateEntity 计划关联关系实体
// 用于表示投放计划与其他实体的关联关系
type PlanRelateEntity struct {
	ID         int64  // 关联关系ID
	PlanID     int64  // 计划ID
	RelateType string // 关联类型，字符串形式方便与前端交互
	RelateID   int64  // 关联对象ID
}

// AdSlotPlanEntity 广告位计划实体
// 用于表示广告位的投放计划信息
type AdSlotPlanEntity struct {
	ID             int64              // 计划ID
	Code           string             // 计划编码
	Name           string             // 计划名称
	UserID         int64              // 创建用户ID
	AdSlotID       int64              // 广告位ID
	StartDate      time.Time          // 开始日期
	EndDate        time.Time          // 结束日期
	AdCreativeID   int64              // 广告创意ID
	Budget         float64            // 总预算
	DailyBudget    float64            // 日预算
	Type           string             // 计划类型
	MaterialType   int                // 素材类型
	Status         int                // 状态
	AuditStatus    int                // 审核状态
	DeliveryStatus int                // 投放状态
	User           UserEntity         // 关联的用户信息
	Rels           []PlanRelateEntity // 关联关系列表
}

// CreateAdSlotPlanParam 创建广告位计划参数
// 用于表示创建广告位计划时的输入参数
type CreateAdSlotPlanParam struct {
	AdSlotID     int64     // 广告位ID
	StartDate    time.Time // 开始日期
	EndDate      time.Time // 结束日期
	Budget       float64   // 总预算
	DailyBudget  float64   // 日预算
	Name         string    // 计划名称
	Type         string    // 计划类型
	MaterialType int       // 素材类型
	Status       int       // 状态
	UserID       int64     // 创建用户ID
}

// UpdateAdSlotPlanParam 更新广告位计划参数
// 用于表示更新广告位计划时的输入参数
type UpdateAdSlotPlanParam struct {
	ID          int64     // 计划ID
	Name        string    // 计划名称
	Budget      float64   // 总预算
	DailyBudget float64   // 日预算
	StartDate   time.Time // 开始日期
	EndDate     time.Time // 结束日期
	Status      int       // 状态
}

// AdSlotPlanQueryParam 广告位计划查询参数
// 用于表示查询广告位计划列表时的过滤条件
type AdSlotPlanQueryParam struct {
	Page           int    // 页码
	Size           int    // 每页数量
	Code           string // 计划编码
	MediaID        int64  // 媒体ID
	AdSlotID       int64  // 广告位ID
	AuditStatus    string // 审核状态
	DeliveryStatus string // 投放状态
	Type           string // 计划类型
}

// PromotionLinkEntity 推广链接实体
// 用于表示推广链接的信息
type PromotionLinkEntity struct {
	PromotionLink   string // 推广链接
	PromotionQrcode string // 推广二维码
}

// MergeLinkEntity 融合链接实体
// 用于表示融合链接的信息
type MergeLinkEntity map[string]any

// 计划状态常量
const (
	// 审核状态（使用Plan前缀避免与其他模型冲突）
	PlanAuditStatusPending  = "pending"
	PlanAuditStatusApproved = "approved"
	PlanAuditStatusRejected = "rejected"

	// 投放状态
	DeliveryStatusInit        = "init"
	DeliveryStatusConfiguring = "configuring"
	DeliveryStatusWait        = "wait"
	DeliveryStatusRunning     = "running"
	DeliveryStatusStopped     = "stopped"
	DeliveryStatusStoppedAuto = "stopped_auto"
	DeliveryStatusCompleted   = "completed"

	// 投放策略
	DeliveryModeNormal = 1 // 正常投放
	DeliveryModeSpeed  = 2 // 加速投放
	DeliveryModeSlow   = 3 // 减速投放

	// 计划类型
	PlanTypeNormal = "normal" // 正式计划
	PlanTypeTest   = "test"   // 测试计划

	// 素材类型
	MaterialTypeImageText = 1 // 图文
	MaterialTypeVideo     = 2 // 视频

	// 关联类型
	RelateTypePlatform = 1 // 平台计划
	RelateTypeCreative = 2 // 创意
)

// 操作类型常量
const (
	ACTION_APPROVE              = "approve"              // 审核通过
	ACTION_REJECT               = "reject"               // 审核拒绝
	ACTION_UPDATE_DELIVERY_MODE = "update_delivery_mode" // 更新投放策略
)
