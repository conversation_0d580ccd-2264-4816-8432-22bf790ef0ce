package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupXHSCreativeReportRoutes 设置小红书创意报表路由
func SetupXHSCreativeReportRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewXHSCreativeReportController()

	// 小红书创意报表相关路由
	xhsReports := router.Group("/xhs-creative-reports")
	{
		// 获取报表列表
		xhsReports.GET("", ctrl.GetReportList)
		
		// 获取报表统计信息
		xhsReports.GET("/stats", ctrl.GetReportStats)
		
		// 导出报表数据
		xhsReports.GET("/export", ctrl.ExportReports)
		
		// 获取报表详情
		xhsReports.GET("/:id", ctrl.GetReportDetail)
	}
}

// RegisterXHSCreativeReportRoutes 注册小红书创意报表路由到主路由器
func RegisterXHSCreativeReportRoutes(r *gin.Engine) {
	// API v1 路由组
	v1 := r.Group("/api/v1")
	
	// 设置小红书创意报表路由
	SetupXHSCreativeReportRoutes(v1)
}
