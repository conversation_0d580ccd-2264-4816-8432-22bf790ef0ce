package report

import (
	"time"
)

// AdReportQueryRequest 广告数据报表查询请求
type AdReportQueryRequest struct {
	StartDate    string `json:"start_date"`            // 开始日期，格式：yyyy-MM-dd
	EndDate      string `json:"end_date"`              // 结束日期，格式：yyyy-MM-dd
	OuterId      string `json:"outer_id,omitempty"`    // 商户外部ID
	QueryType    string `json:"query_type,omitempty"`  // 查询类型
	Current      int    `json:"current,omitempty"`     // 分页参数，起始值从0开始
	PageSize     int    `json:"page_size,omitempty"`   // 每页大小，默认值20
	BizProduct   string `json:"biz_product,omitempty"` // 业务产品
	BizToken     string `json:"biz_token"`
	AlipayPid    string `json:"alipay_pid"`
	PrincipalTag string `json:"principal_tag"`
	AdLevel      string `json:"ad_level"`
}

// AdReportConversionDataDetail 广告转化数据详情
type AdReportConversionDataDetail struct {
	ConversionType     string `json:"conversion_type"`      // 转化事件类型
	ConversionTypeName string `json:"conversion_type_name"` // 转化事件类型名称
	ConversionResult   string `json:"conversion_result"`    // 转化事件结果
}

// Item 广告数据报表项
type Item struct {
	DataID                 string                         `json:"data_id"`                  // 根据ad_level不同会透出汇总数据的plan_id/group_id/creative_id/order_id
	BizDate                string                         `json:"biz_date"`                 // 数据汇总时间
	Impression             int64                          `json:"impression"`               // 展现量
	Click                  int64                          `json:"click"`                    // 点击量
	Cost                   int64                          `json:"cost"`                     // 消费金额，单位:分
	PlanName               string                         `json:"plan_name"`                // 计划名称
	PlanID                 string                         `json:"plan_id"`                  // 计划id
	GroupName              string                         `json:"group_name"`               // 单元名称
	GroupID                string                         `json:"group_id"`                 // 单元ID
	OrderName              string                         `json:"order_name"`               // 订单名称
	OrderID                string                         `json:"order_id"`                 // 订单id
	CreativeName           string                         `json:"creative_name"`            // 创意名称
	MarketTargetName       string                         `json:"market_target_name"`       // 营销目标名称
	SceneName              string                         `json:"scene_name"`               // 投放产品名称
	PrincipalAlipayAccount string                         `json:"principal_alipay_account"` // 商家账户
	PrincipalName          string                         `json:"principal_name"`           // 商家名称
	ConversionDataList     []AdReportConversionDataDetail `json:"conversion_data_list"`     // 投放转化数据
}

// AdReportQueryResponse 广告数据报表查询响应
type AdReportQueryResponse struct {
	Response struct {
		Code    string `json:"code"`
		Msg     string `json:"msg"`
		SubCode string `json:"sub_code"`
		SubMsg  string `json:"sub_msg"`
		Total   int    `json:"total"`
		Items   []Item `json:"data_list"`
	} `json:"alipay_data_dataservice_ad_reportdata_query_response"`
	Sign string `json:"sign"`
}

// NewAdReportQueryRequest 创建广告数据报表查询请求
func NewAdReportQueryRequest(startDate, endDate time.Time) *AdReportQueryRequest {
	return &AdReportQueryRequest{
		StartDate: startDate.Format("********"),
		EndDate:   endDate.Format("********"),
	}
}

// GetMethod 获取接口名称
func (r *AdReportQueryRequest) GetMethod() string {
	return "alipay.data.dataservice.ad.reportdata.query"
}
