package xiaohongshu

import (
	"time"
)

// ReportBuilder 报表请求构建器
type ReportBuilder struct {
	request *ReportRequest
}

// NewReportBuilder 创建报表请求构建器
func NewReportBuilder(advertiserID int64, startDate, endDate string) *ReportBuilder {
	return &ReportBuilder{
		request: &ReportRequest{
			AdvertiserID: advertiserID,
			StartDate:    startDate,
			EndDate:      endDate,
			PageNum:      1,
			PageSize:     20,
		},
	}
}

// WithTimeUnit 设置时间维度
func (rb *ReportBuilder) WithTimeUnit(timeUnit string) *ReportBuilder {
	rb.request.TimeUnit = timeUnit
	return rb
}

// WithPagination 设置分页参数
func (rb *ReportBuilder) WithPagination(pageNum, pageSize int) *ReportBuilder {
	rb.request.PageNum = pageNum
	rb.request.PageSize = pageSize
	return rb
}

// WithSort 设置排序
func (rb *ReportBuilder) WithSort(column, order string) *ReportBuilder {
	rb.request.SortColumn = column
	rb.request.Sort = order
	return rb
}

// WithFilters 设置过滤条件
func (rb *ReportBuilder) WithFilters(filters []Filter) *ReportBuilder {
	rb.request.Filters = filters
	return rb
}

// WithSplitColumns 设置细分条件
func (rb *ReportBuilder) WithSplitColumns(columns []string) *ReportBuilder {
	rb.request.SplitColumns = columns
	return rb
}

// WithMarketingTarget 设置营销目标过滤
func (rb *ReportBuilder) WithMarketingTarget(targets []int) *ReportBuilder {
	rb.request.MarketingTarget = targets
	return rb
}

// WithPlacement 设置广告类型过滤
func (rb *ReportBuilder) WithPlacement(placements []int) *ReportBuilder {
	rb.request.Placement = placements
	return rb
}

// WithDataCaliber 设置数据指标归因时间类型
func (rb *ReportBuilder) WithDataCaliber(caliber int) *ReportBuilder {
	rb.request.DataCaliber = caliber
	return rb
}

// WithNoteUserID 设置笔记作者ID
func (rb *ReportBuilder) WithNoteUserID(userID string) *ReportBuilder {
	rb.request.NoteUserID = userID
	return rb
}

// Build 构建请求
func (rb *ReportBuilder) Build() *ReportRequest {
	return rb.request
}

// 预定义的时间维度常量
const (
	TimeUnitDay     = "DAY"     // 分天
	TimeUnitHour    = "HOUR"    // 分时
	TimeUnitSummary = "SUMMARY" // 汇总
)

// 预定义的排序方式常量
const (
	SortAsc  = "asc"  // 升序
	SortDesc = "desc" // 降序
)

// 预定义的数据归因时间类型常量
const (
	DataCaliberClickTime      = 0 // 点击时间
	DataCaliberConversionTime = 1 // 转化时间
)

// CreateDateRangeReport 创建日期范围报表请求
func CreateDateRangeReport(advertiserID int64, days int) *ReportRequest {
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -days).Format("2006-01-02")

	return NewReportBuilder(advertiserID, startDate, endDate).
		WithTimeUnit(TimeUnitDay).
		WithPagination(1, 100).
		Build()
}

// CreateYesterdayReport 创建昨日报表请求
func CreateYesterdayReport(advertiserID int64) *ReportRequest {
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	return NewReportBuilder(advertiserID, yesterday, yesterday).
		WithTimeUnit(TimeUnitSummary).
		Build()
}

// CreateLast7DaysReport 创建最近7天报表请求
func CreateLast7DaysReport(advertiserID int64) *ReportRequest {
	return CreateDateRangeReport(advertiserID, 7)
}

// CreateLast30DaysReport 创建最近30天报表请求
func CreateLast30DaysReport(advertiserID int64) *ReportRequest {
	return CreateDateRangeReport(advertiserID, 30)
}

// CreateFilterByFee 创建按消费金额过滤的条件
func CreateFilterByFee(operator string, values []string) Filter {
	return Filter{
		Column:   "fee",
		Operator: operator,
		Values:   values,
	}
}

// CreateFilterByImpression 创建按展现量过滤的条件
func CreateFilterByImpression(operator string, values []string) Filter {
	return Filter{
		Column:   "impression",
		Operator: operator,
		Values:   values,
	}
}

// CreateFilterByClick 创建按点击量过滤的条件
func CreateFilterByClick(operator string, values []string) Filter {
	return Filter{
		Column:   "click",
		Operator: operator,
		Values:   values,
	}
}

// CreateFilterByCreativityID 创建按创意ID过滤的条件
func CreateFilterByCreativityID(creativityIDs []string) Filter {
	return Filter{
		Column:   "creativityId",
		Operator: "in",
		Values:   creativityIDs,
	}
}

// 预定义的过滤操作符常量
const (
	FilterOperatorGreaterThan = ">"  // 大于
	FilterOperatorLessThan    = "<"  // 小于
	FilterOperatorIn          = "in" // 包含
)

// 预定义的细分条件常量
var (
	SplitColumnsCampaign   = []string{"campaign_id", "campaign_name"}   // 按计划细分
	SplitColumnsUnit       = []string{"unit_id", "unit_name"}           // 按单元细分
	SplitColumnsCreativity = []string{"creativity_id", "creativity_name"} // 按创意细分
	SplitColumnsTime       = []string{"time"}                           // 按时间细分
	SplitColumnsPlacement  = []string{"placement"}                      // 按广告类型细分
)

// GetBasicMetrics 获取基础指标字段列表
func GetBasicMetrics() []string {
	return []string{
		"fee", "impression", "click", "ctr", "acp", "cpm",
	}
}

// GetInteractionMetrics 获取互动指标字段列表
func GetInteractionMetrics() []string {
	return []string{
		"like", "comment", "collect", "follow", "share", "interaction", "cpi",
	}
}

// GetConversionMetrics 获取转化指标字段列表
func GetConversionMetrics() []string {
	return []string{
		"goods_order", "goods_order_price", "rgmv", "roi",
		"success_goods_order", "click_order_cvr",
	}
}

// GetAllMetrics 获取所有常用指标字段列表
func GetAllMetrics() []string {
	var metrics []string
	metrics = append(metrics, GetBasicMetrics()...)
	metrics = append(metrics, GetInteractionMetrics()...)
	metrics = append(metrics, GetConversionMetrics()...)
	return metrics
}
