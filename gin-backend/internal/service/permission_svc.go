package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strings"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService 创建权限服务实例
func NewPermissionService() *PermissionService {
	return &PermissionService{
		db: global.DB,
	}
}

// GetUserDataScope 获取用户的数据权限范围
func (s *PermissionService) GetUserDataScope(param domain.DataScopeParam) (string, error) {

	// 特殊处理：管理员用户直接返回全部数据权限
	if param.UserID == 1 {
		return "all", nil
	}

	// 1. 获取用户信息
	var user model.User
	if err := s.db.Where("id = ?", param.UserID).First(&user).Error; err != nil {
		return "self", err // 默认返回个人数据权限
	}

	// 2. 获取用户角色
	if user.RoleID <= 0 {
		return "self", nil // 没有角色，只能看自己的数据
	}

	// 3. 获取角色权限
	var permissions []model.Permission
	if err := s.db.WithContext(context.Background()).
		Table("permissions p").
		Select("p.*").
		Joins("JOIN role_permissions rp ON p.id = rp.permission_id").
		Where("rp.role_id = ? AND p.status = ?", user.RoleID, 1).
		Order("p.module ASC, p.created_at DESC").
		Find(&permissions).Error; err != nil {
		return "self", err
	}

	// 4. 查找该模块的最高数据权限（只考虑数据权限，即包含":data:"的权限）
	maxScope := "self"
	var foundPermissions []string
	var dataPermissions []string

	for _, permission := range permissions {
		// 记录所有权限
		foundPermissions = append(foundPermissions, permission.Code)

		// 检查数据权限
		if permission.Module == param.Module && strings.Contains(permission.Code, ":data:") {
			dataPermissions = append(dataPermissions, fmt.Sprintf("%s(scope:%s)", permission.Code, permission.DataScope))

			// 权限优先级：all > dept > self
			switch permission.DataScope {
			case "all":
				return "all", nil // 找到最高权限，直接返回
			case "dept":
				if maxScope == "self" {
					maxScope = "dept"
				}
			}
		}
	}

	// 如果没有找到任何数据权限，记录详细信息
	if len(dataPermissions) == 0 {

		// 显示前10个权限作为参考
		samplePermissions := foundPermissions
		if len(samplePermissions) > 10 {
			samplePermissions = samplePermissions[:10]
		}
	}

	return maxScope, nil
}

// GetUserDepartment 获取用户部门
func (s *PermissionService) GetUserDepartment(param domain.UserDepartmentParam) (string, error) {
	var user model.User
	if err := s.db.Select("department").Where("id = ?", param.UserID).First(&user).Error; err != nil {
		return "", err
	}
	return user.Department, nil
}

// GetDepartmentUsers 获取部门下的所有用户ID
func (s *PermissionService) GetDepartmentUsers(param domain.DepartmentUsersParam) ([]int64, error) {
	var users []model.User
	if err := s.db.Select("id").Where("department = ?", param.Department).Find(&users).Error; err != nil {
		return nil, err
	}

	var userIDs []int64
	for _, user := range users {
		userIDs = append(userIDs, user.ID)
	}
	return userIDs, nil
}

// ApplyDataScopeFilter 应用数据权限过滤
func (s *PermissionService) ApplyDataScopeFilter(query *gorm.DB, userID int64, module string) (*gorm.DB, error) {
	// 获取用户数据权限范围
	scope, err := s.GetUserDataScope(domain.DataScopeParam{
		UserID: userID,
		Module: module,
	})
	if err != nil {
		return query, err
	}

	switch scope {
	case "all":
		// 全部数据，不添加过滤条件
		return query, nil
	case "dept":
		// 部门数据，需要获取部门下的所有用户
		department, err := s.GetUserDepartment(domain.UserDepartmentParam{
			UserID: userID,
		})
		if err != nil {
			return query.Where("user_id = ?", userID), nil // 出错时降级为个人数据
		}

		if department == "" {
			return query.Where("user_id = ?", userID), nil // 没有部门时只能看个人数据
		}

		departmentUsers, err := s.GetDepartmentUsers(domain.DepartmentUsersParam{
			Department: department,
		})
		if err != nil {
			return query.Where("user_id = ?", userID), nil // 出错时降级为个人数据
		}

		return query.Where("user_id IN ?", departmentUsers), nil
	case "self":
		// 个人数据
		return query.Where("user_id = ?", userID), nil
	default:
		// 默认个人数据
		return query.Where("user_id = ?", userID), nil
	}
}

// GetRolePermissions 获取角色权限
func (s *PermissionService) GetRolePermissions(ctx context.Context, param domain.PermissionParam) ([]domain.Permission, error) {
	var modelPermissions []model.Permission
	if err := s.db.Table("role_permissions rp").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id = ?", param.RoleID).
		Find(&modelPermissions).Error; err != nil {
		return nil, fmt.Errorf("获取角色权限失败: %w", err)
	}

	// 将模型实体转换为领域实体
	permissions := make([]domain.Permission, 0, len(modelPermissions))
	for _, p := range modelPermissions {
		permissions = append(permissions, domain.Permission{
			ID:          p.ID,
			Name:        p.Name,
			Code:        p.Code,
			Module:      p.Module,
			Type:        p.Type,
			DataScope:   p.DataScope,
			Description: p.Description,
			Status:      p.Status,
			CreatedAt:   p.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   p.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return permissions, nil
}
