package xiaohongshu

import (
	"fmt"
	"log"
)

// Example 使用示例
func Example() {
	// 1. 创建配置
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false, // 测试环境
	}

	// 2. 创建客户端
	client := NewClient(config)

	// 3. 获取访问令牌
	authCode := "d6a0b18531a2b9599a2c1e2361659c00"
	tokenResp, err := client.GetAccessToken(authCode)
	if err != nil {
		log.Printf("获取访问令牌失败: %v", err)
		return
	}

	fmt.Printf("获取访问令牌成功:\n")
	fmt.Printf("  用户ID: %s\n", tokenResp.Data.UserId)
	fmt.Printf("  访问令牌: %s\n", tokenResp.Data.AccessToken)
	fmt.Printf("  刷新令牌: %s\n", tokenResp.Data.RefreshToken)
	fmt.Printf("  访问令牌过期时间: %d秒\n", tokenResp.Data.AccessTokenExpiresIn)
	fmt.Printf("  刷新令牌过期时间: %d秒\n", tokenResp.Data.RefreshTokenExpiresIn)
	fmt.Printf("  广告主ID: %d\n", tokenResp.Data.AdvertiserId)

	// 4. 刷新访问令牌
	refreshTokenResp, err := client.RefreshToken(tokenResp.Data.RefreshToken)
	if err != nil {
		log.Printf("刷新访问令牌失败: %v", err)
		return
	}

	fmt.Printf("\n刷新访问令牌成功:\n")
	fmt.Printf("  新的访问令牌: %s\n", refreshTokenResp.Data.AccessToken)
	fmt.Printf("  新的刷新令牌: %s\n", refreshTokenResp.Data.RefreshToken)
}

// ExampleWithRealData 使用真实数据的示例
func ExampleWithRealData() {
	// 使用您提供的示例数据
	config := &Config{
		AppId:  "3",
		Secret: "1234abc",
		IsProd: true,
	}

	client := NewClient(config)

	// 获取访问令牌
	authCode := "d6a0b18531a2b9599a2c1e2361659c00"
	tokenResp, err := client.GetAccessToken(authCode)
	if err != nil {
		log.Printf("获取访问令牌失败: %v", err)
		return
	}

	if tokenResp.Success {
		fmt.Printf("成功获取令牌，广告主: %s\n", 
			tokenResp.Data.ApprovalAdvertisers[0].AdvertiserName)
	}
}
