package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AdAccountRebateService 账号返利比例服务
type AdAccountRebateService struct {
	db *gorm.DB
}

// NewAdAccountRebateService 创建账号返利比例服务
func NewAdAccountRebateService() *AdAccountRebateService {
	return &AdAccountRebateService{
		db: global.DB,
	}
}

// ChangeAccountRebate 变更账号返利比例
func (s *AdAccountRebateService) ChangeAccountRebate(ctx context.Context, param domain.AdAccountRebateChangeParam) error {
	// 参数验证
	if err := domain.ValidateAdAccountRebateChangeParam(param); err != nil {
		return err
	}

	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询当前返利比例
	var currentRebate model.AdAccountRebate
	err := tx.Where("account_id = ?", param.AccountId).First(&currentRebate).Error

	var oldRate *float64
	var changeType string

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 新增返利比例
			changeType = model.ChangeTypeCreate
		} else {
			tx.Rollback()
			return fmt.Errorf("查询当前返利比例失败: %w", err)
		}
	} else {
		// 更新返利比例
		changeType = model.ChangeTypeUpdate
		oldRate = &currentRebate.Rate
	}

	// 创建或更新返利比例记录
	rebate := model.AdAccountRebate{
		AccountId:     param.AccountId,
		Rate:          param.NewRate,
		EffectiveDate: param.EffectiveDate,
		UpdatedAt:     time.Now(),
	}

	if changeType == model.ChangeTypeCreate {
		rebate.CreatedAt = time.Now()
		if err := tx.Create(&rebate).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建返利比例失败: %w", err)
		}
	} else {
		if err := tx.Where("account_id = ?", param.AccountId).Updates(&rebate).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新返利比例失败: %w", err)
		}
	}

	// 记录变更历史
	history := model.AdAccountRebateHistory{
		AccountId:     param.AccountId,
		OldRate:       oldRate,
		NewRate:       param.NewRate,
		EffectiveDate: param.EffectiveDate,
		ChangeType:    changeType,
		ChangeReason:  param.ChangeReason,
		OperatorId:    param.OperatorId,
		OperatorName:  param.OperatorName,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("记录变更历史失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	zap.L().Info("账号返利比例变更成功",
		zap.Int64("account_id", param.AccountId),
		zap.Float64p("old_rate", oldRate),
		zap.Float64("new_rate", param.NewRate),
		zap.String("change_type", changeType),
		zap.String("operator", param.OperatorName))

	return nil
}

// GetAccountRebateHistory 获取账号返利比例变更记录
func (s *AdAccountRebateService) GetAccountRebateHistory(ctx context.Context, param domain.AdAccountRebateHistoryParam) (domain.AdAccountRebateHistoryResult, error) {
	// 参数验证
	if err := domain.ValidateAdAccountRebateHistoryParam(param); err != nil {
		return domain.AdAccountRebateHistoryResult{}, err
	}

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.AdAccountRebateHistory{})

	// 添加筛选条件
	s.addHistoryFilterConditions(query, param)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.AdAccountRebateHistoryResult{}, fmt.Errorf("统计记录总数失败: %w", err)
	}

	// 分页查询
	var histories []model.AdAccountRebateHistory
	offset := (param.Page - 1) * param.PageSize
	err := query.Order("created_at DESC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&histories).Error

	if err != nil {
		return domain.AdAccountRebateHistoryResult{}, fmt.Errorf("查询变更记录失败: %w", err)
	}

	// 转换为列表项
	items := make([]domain.AdAccountRebateHistoryItem, 0, len(histories))
	for _, history := range histories {
		// 获取账号名称
		accountName := s.getAccountNameByID(ctx, history.AccountId)

		items = append(items, domain.AdAccountRebateHistoryItem{
			ID:             history.ID,
			AccountId:      history.AccountId,
			AccountName:    accountName,
			OldRate:        history.OldRate,
			NewRate:        history.NewRate,
			RateChange:     domain.FormatRateChange(history.OldRate, history.NewRate),
			EffectiveDate:  history.EffectiveDate.Format("2006-01-02"),
			ChangeType:     history.ChangeType,
			ChangeTypeDesc: domain.GetChangeTypeDescription(history.ChangeType),
			ChangeReason:   history.ChangeReason,
			OperatorId:     history.OperatorId,
			OperatorName:   history.OperatorName,
			CreatedAt:      history.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return domain.AdAccountRebateHistoryResult{
		List:  items,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// GetAccountRebateList 获取账号返利比例列表
func (s *AdAccountRebateService) GetAccountRebateList(ctx context.Context, param domain.AdAccountRebateListParam) (domain.AdAccountRebateListResult, error) {
	// 参数验证
	if err := domain.ValidateAdAccountRebateListParam(param); err != nil {
		return domain.AdAccountRebateListResult{}, err
	}

	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.AdAccountRebate{})

	// 添加筛选条件
	s.addListFilterConditions(query, param)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return domain.AdAccountRebateListResult{}, fmt.Errorf("统计记录总数失败: %w", err)
	}

	// 分页查询
	var rebates []model.AdAccountRebate
	offset := (param.Page - 1) * param.PageSize
	err := query.Order("effective_date DESC, created_at DESC").
		Offset(offset).
		Limit(param.PageSize).
		Find(&rebates).Error

	if err != nil {
		return domain.AdAccountRebateListResult{}, fmt.Errorf("查询返利比例列表失败: %w", err)
	}

	// 转换为列表项
	items := make([]domain.AdAccountRebateListItem, 0, len(rebates))
	for _, rebate := range rebates {
		// 获取账号名称
		accountName := s.getAccountNameByID(ctx, rebate.AccountId)

		items = append(items, domain.AdAccountRebateListItem{
			ID:            rebate.ID,
			AccountId:     rebate.AccountId,
			AccountName:   accountName,
			Rate:          rebate.Rate,
			EffectiveDate: rebate.EffectiveDate.Format("2006-01-02"),
			CreatedAt:     rebate.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     rebate.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return domain.AdAccountRebateListResult{
		List:  items,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// GetAccountRebateStats 获取账号返利比例统计
func (s *AdAccountRebateService) GetAccountRebateStats(ctx context.Context) (domain.AdAccountRebateStatsResult, error) {
	var stats domain.AdAccountRebateStatsResult

	// 统计总账号数和有效账号数
	if err := s.db.WithContext(ctx).Model(&model.AdAccountRebate{}).Count(&stats.TotalAccounts).Error; err != nil {
		return stats, fmt.Errorf("统计总账号数失败: %w", err)
	}

	// 统计有效账号数（生效日期<=今天）
	today := time.Now().Format("2006-01-02")
	if err := s.db.WithContext(ctx).Model(&model.AdAccountRebate{}).
		Where("DATE(effective_date) <= ?", today).
		Count(&stats.ActiveAccounts).Error; err != nil {
		return stats, fmt.Errorf("统计有效账号数失败: %w", err)
	}

	// 统计平均、最高、最低返利比例
	type RateStats struct {
		AvgRate float64 `gorm:"column:avg_rate"`
		MaxRate float64 `gorm:"column:max_rate"`
		MinRate float64 `gorm:"column:min_rate"`
	}

	var rateStats RateStats
	err := s.db.WithContext(ctx).Model(&model.AdAccountRebate{}).
		Select("AVG(rate) as avg_rate, MAX(rate) as max_rate, MIN(rate) as min_rate").
		Where("DATE(effective_date) <= ?", today).
		Scan(&rateStats).Error

	if err != nil {
		return stats, fmt.Errorf("统计返利比例失败: %w", err)
	}

	stats.AverageRate = rateStats.AvgRate
	stats.MaxRate = rateStats.MaxRate
	stats.MinRate = rateStats.MinRate

	// 统计最近30天变更次数
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	if err := s.db.WithContext(ctx).Model(&model.AdAccountRebateHistory{}).
		Where("DATE(created_at) >= ?", thirtyDaysAgo).
		Count(&stats.RecentChanges).Error; err != nil {
		return stats, fmt.Errorf("统计最近变更次数失败: %w", err)
	}

	return stats, nil
}

// addHistoryFilterConditions 添加变更记录筛选条件
func (s *AdAccountRebateService) addHistoryFilterConditions(query *gorm.DB, param domain.AdAccountRebateHistoryParam) {
	if param.AccountId != nil {
		query.Where("account_id = ?", *param.AccountId)
	}
	if param.AccountName != "" {
		// 通过账号名称查找账号ID
		var accountIDs []int64
		s.db.Model(&model.AdAccounts{}).
			Where("account_name LIKE ?", "%"+param.AccountName+"%").
			Pluck("id", &accountIDs)
		if len(accountIDs) > 0 {
			query.Where("account_id IN ?", accountIDs)
		} else {
			query.Where("account_id = -1") // 没有匹配的账号
		}
	}
	if param.ChangeType != "" {
		query.Where("change_type = ?", param.ChangeType)
	}
	if param.OperatorName != "" {
		query.Where("operator_name LIKE ?", "%"+param.OperatorName+"%")
	}
	if param.StartDate != "" {
		query.Where("DATE(created_at) >= ?", param.StartDate)
	}
	if param.EndDate != "" {
		query.Where("DATE(created_at) <= ?", param.EndDate)
	}
}

// addListFilterConditions 添加列表筛选条件
func (s *AdAccountRebateService) addListFilterConditions(query *gorm.DB, param domain.AdAccountRebateListParam) {
	if param.AccountId != nil {
		query.Where("account_id = ?", *param.AccountId)
	}
	if param.AccountName != "" {
		// 通过账号名称查找账号ID
		var accountIDs []int64
		s.db.Model(&model.AdAccounts{}).
			Where("account_name LIKE ?", "%"+param.AccountName+"%").
			Pluck("id", &accountIDs)
		if len(accountIDs) > 0 {
			query.Where("account_id IN ?", accountIDs)
		} else {
			query.Where("account_id = -1") // 没有匹配的账号
		}
	}
}

// getAccountNameByID 根据账号ID获取账号名称
func (s *AdAccountRebateService) getAccountNameByID(ctx context.Context, accountID int64) string {
	var accountName string
	s.db.WithContext(ctx).Model(&model.AdAccounts{}).
		Where("id = ?", accountID).
		Pluck("account_name", &accountName)
	return accountName
}

// DeleteAccountRebate 删除账号返利比例
func (s *AdAccountRebateService) DeleteAccountRebate(ctx context.Context, accountID int64, operatorID *int64, operatorName, reason string) error {
	// 开始事务
	tx := s.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询当前返利比例
	var currentRebate model.AdAccountRebate
	err := tx.Where("account_id = ?", accountID).First(&currentRebate).Error
	if err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("账号返利比例不存在")
		}
		return fmt.Errorf("查询当前返利比例失败: %w", err)
	}

	// 删除返利比例记录
	if err := tx.Where("account_id = ?", accountID).Delete(&model.AdAccountRebate{}).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除返利比例失败: %w", err)
	}

	// 记录删除历史
	history := model.AdAccountRebateHistory{
		AccountId:     accountID,
		OldRate:       &currentRebate.Rate,
		NewRate:       0, // 删除时新比例设为0
		EffectiveDate: time.Now(),
		ChangeType:    model.ChangeTypeDelete,
		ChangeReason:  reason,
		OperatorId:    operatorID,
		OperatorName:  operatorName,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := tx.Create(&history).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("记录删除历史失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	zap.L().Info("账号返利比例删除成功",
		zap.Int64("account_id", accountID),
		zap.Float64("old_rate", currentRebate.Rate),
		zap.String("operator", operatorName))

	return nil
}

// GetAccountCurrentRebate 获取账号当前返利比例
func (s *AdAccountRebateService) GetAccountCurrentRebate(ctx context.Context, accountID int64) (*model.AdAccountRebate, error) {
	var rebate model.AdAccountRebate
	err := s.db.WithContext(ctx).Where("account_id = ?", accountID).First(&rebate).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有设置返利比例
		}
		return nil, fmt.Errorf("查询账号返利比例失败: %w", err)
	}
	return &rebate, nil
}
