package service

import (
	"fmt"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// dashboardStatsService 仪表盘统计服务
type dashboardStatsService struct {
	dashboard *DashboardService
}

// newDashboardStatsService 创建仪表盘统计服务实例
func newDashboardStatsService(d *DashboardService) *dashboardStatsService {
	return &dashboardStatsService{
		dashboard: d,
	}
}

// GetPlanStats 获取分计划统计数据
func (s *dashboardStatsService) GetPlanStats(req domain.DashboardPlanStatsParam, userID int, userRole int) (domain.PlanStatsEntity, error) {
	// 转换为内部结构体
	modelReq := &struct {
		StartDate string
		EndDate   string
		UserID    string
		MediaID   string
		PlanID    string
		ProductID string
		Type      string
		Category  string
	}{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		UserID:    req.UserID,
		MediaID:   req.MediaID,
		PlanID:    req.PlanID,
		ProductID: req.ProductID,
		Type:      req.Type,
		Category:  req.Category,
	}

	// 调用内部函数
	result, err := s.getPlanStats(modelReq, userID, userRole)
	if err != nil {
		return domain.PlanStatsEntity{}, err
	}

	// 转换为Domain实体
	domainList := make([]domain.PlanStatsItem, 0, len(result))
	for _, item := range result {
		domainList = append(domainList, domain.PlanStatsItem{
			PlanID:        item.PlanID,
			PlanCode:      item.PlanCode,
			MediaName:     item.MediaName,
			ProductID:     item.ProductID,
			Clicks:        item.Clicks,
			Cost:          item.Cost,
			Orders:        item.Orders,
			Revenue:       item.Revenue,
			Profit:        item.Profit,
			ROI:           item.ROI,
			SettledProfit: item.SettledProfit,
			PV:            item.PV,
			UV:            item.UV,
		})
	}

	return domain.PlanStatsEntity{
		List: domainList,
	}, nil
}

// getPlanStats 内部函数，获取分计划统计数据
func (s *dashboardStatsService) getPlanStats(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}, userID int, userRole int) ([]struct {
	PlanID        string
	PlanCode      string
	MediaName     string
	ProductID     string
	Clicks        int
	Cost          float64
	Orders        int
	Revenue       float64
	Profit        float64
	ROI           float64
	SettledProfit float64
	PV            int
	UV            int
}, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	if err != nil {
		return nil, err
	}

	// 构建查询
	query := s.dashboard.db.Table("ad_slot_plan_daily_stats as stats")
	query = query.Select(`
		stats.plan_id as plan_id,
		p.code as plan_code,
		m.name as media_name,
		stats.ad_product_id as product_id,
		SUM(stats.elm_click_pv) as clicks,
		SUM(stats.cost) as cost,
		SUM(stats.bd_orders) as orders,
		SUM(stats.bd_revenue) as revenue,
		SUM(stats.bd_profit) as profit,
		CASE WHEN SUM(stats.cost) > 0 THEN SUM(stats.bd_revenue) / SUM(stats.cost) ELSE 0 END as roi,
		SUM(stats.bd_settle_profit) as settled_profit,
		SUM(stats.elm_click_pv) as pv,
		SUM(stats.elm_click_uv) as uv
	`)
	query = query.Joins("LEFT JOIN ad_slot_plans p ON stats.plan_id = p.id")
	query = query.Joins("LEFT JOIN ad_media m ON stats.media_id = m.id")
	query = query.Where("stats.date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("stats.user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("stats.user_id = ?", userID)
	}

	// 添加过滤条件
	if req.UserID != "" && req.UserID != "0" {
		query = query.Where("stats.user_id = ?", req.UserID)
	}
	if req.MediaID != "" {
		query = query.Where("stats.media_id = ?", req.MediaID)
	}
	if req.PlanID != "" {
		query = query.Where("stats.plan_id = ?", req.PlanID)
	}
	if req.ProductID != "" {
		query = query.Where("stats.ad_product_id = ?", req.ProductID)
	}

	// 根据合作类型过滤
	if req.Type != "" {
		var cooperationType string
		switch req.Type {
		case "1":
			cooperationType = "traffic"
		case "2":
			cooperationType = "cps"
		case "3":
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int
			s.dashboard.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("stats.media_id IN (?)", mediaIDs)
			}
		}
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			s.dashboard.db.Model(&model.AdSlots{}).
				Where("type IN (?)", slotTypes).
				Select("id").
				Scan(&slotIDs)

			if len(slotIDs) > 0 {
				query = query.Where("stats.ad_slot_id IN (?)", slotIDs)
			}
		}
	}

	// 分组和排序
	query = query.Group("stats.plan_id").Order("profit DESC")

	// 执行查询
	var result []struct {
		PlanID        string
		PlanCode      string
		MediaName     string
		ProductID     string
		Clicks        int
		Cost          float64
		Orders        int
		Revenue       float64
		Profit        float64
		ROI           float64
		SettledProfit float64
		PV            int
		UV            int
	}

	err = query.Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetRegionData 获取地域数据
func (s *dashboardStatsService) GetRegionData(req domain.RegionParam, userID int64, userRole int) (domain.RegionEntity, error) {

	// 调用内部函数
	result, err := s.getRegionData(req, userID, userRole)
	if err != nil {
		return domain.RegionEntity{}, err
	}

	// 转换为Domain实体
	domainRegions := make([]domain.RegionDataItem, 0, len(result))
	for _, item := range result {
		domainRegions = append(domainRegions, domain.RegionDataItem{
			Name:  item.Name,
			Value: item.Value,
		})
	}

	return domain.RegionEntity{
		Regions: domainRegions,
	}, nil
}

// getRegionData 内部函数，获取地域数据
func (s *dashboardStatsService) getRegionData(req domain.RegionParam, userID int64, userRole int) ([]struct {
	Name  string
	Value float64
}, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	if err != nil {
		return nil, err
	}

	// 获取过滤后的计划ID列表
	planIDs, err := s.getPlanIDs(req, scope, departmentUsers, userID)
	if err != nil {
		return nil, err
	}

	if len(planIDs) == 0 {
		return []struct {
			Name  string
			Value float64
		}{}, nil
	}

	// 选择指标对应的字段
	var fieldName string
	var tableName string
	var timeField string

	switch req.Metric {
	case "orders":
		fieldName = "COUNT(*)"
		tableName = "ad_orders"
		timeField = "create_time"
	case "estimated_revenue", "estimated_commission":
		fieldName = "SUM(pre_commission)"
		tableName = "ad_orders"
		timeField = "create_time"
	case "settled_revenue", "settled_commission":
		fieldName = "SUM(commission)"
		tableName = "ad_orders"
		timeField = "receive_time"
	case "cost":
		fieldName = "SUM(cost)"
		tableName = "ad_slot_plan_daily_stats"
		timeField = "date"
	case "estimated_profit":
		fieldName = "SUM(bd_profit)"
		tableName = "ad_slot_plan_daily_stats"
		timeField = "date"
	case "settled_profit":
		fieldName = "SUM(bd_settle_profit)"
		tableName = "ad_slot_plan_daily_stats"
		timeField = "date"
	case "click_pv":
		fieldName = "SUM(elm_click_pv)"
		tableName = "ad_slot_plan_daily_stats"
		timeField = "date"
	case "click_uv":
		fieldName = "SUM(elm_click_uv)"
		tableName = "ad_slot_plan_daily_stats"
		timeField = "date"
	default:
		fieldName = "COUNT(*)"
		tableName = "ad_orders"
		timeField = "create_time"
	}

	// 构建查询
	var query *gorm.DB

	if tableName == "ad_orders" {
		query = s.dashboard.db.Table(tableName)
		query = query.Select("COALESCE(NULLIF(city, ''), '未知') as name, " + fieldName + " as value")
		query = query.Where("ad_plan_id IN (?)", planIDs)
		query = query.Where("leak = 0")
		query = query.Where("order_status IN (2,3,4)")
		query = query.Where(fmt.Sprintf("DATE(%s) BETWEEN ? AND ?", timeField), req.StartDate, req.EndDate)
		query = query.Group("city").Having("value > 0").Order("value DESC")
	} else {
		// 对于ad_slot_plan_daily_stats表的查询
		query = s.dashboard.db.Table(tableName)
		query = query.Select("'全部' as name, " + fieldName + " as value")
		query = query.Where("plan_id IN (?)", planIDs)
		query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	}

	// 执行查询
	var result []struct {
		Name  string
		Value float64
	}

	err = query.Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

// getPlanIDs 辅助函数，获取符合条件的计划ID列表
func (s *dashboardStatsService) getPlanIDs(req domain.RegionParam, scope string, departmentUsers []uint64, userID int64) ([]string, error) {
	query := s.dashboard.db.Table("ad_slot_plans")

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加过滤条件
	if req.UserID != "" && req.UserID != "0" {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.MediaID != "" {
		query = query.Where("media_id = ?", req.MediaID)
	}
	if req.PlanID != "" {
		query = query.Where("id = ?", req.PlanID)
	}
	if req.ProductID != "" {
		query = query.Where("ad_product_id = ?", req.ProductID)
	}

	// 根据合作类型过滤
	if req.Type != "" {
		var cooperationType string
		switch req.Type {
		case "1":
			cooperationType = "traffic"
		case "2":
			cooperationType = "cps"
		case "3":
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int
			s.dashboard.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("media_id IN (?)", mediaIDs)
			}
		}
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			s.dashboard.db.Model(&model.AdSlots{}).
				Where("type IN (?)", slotTypes).
				Select("id").
				Scan(&slotIDs)

			if len(slotIDs) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIDs)
			}
		}
	}

	// 执行查询
	var planIDs []string
	err := query.Pluck("id", &planIDs).Error
	if err != nil {
		return nil, err
	}

	return planIDs, nil
}

// GetOrderTypeData 获取订单类型数据
func (s *dashboardStatsService) GetOrderTypeData(req domain.OrderTypeParam, userID int, userRole int) (domain.OrderTypeEntity, error) {
	// 转换为内部结构体
	modelReq := &struct {
		StartDate string
		EndDate   string
		UserID    string
		MediaID   string
		PlanID    string
		ProductID string
		Type      string
		Category  string
		Metric    string
	}{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		UserID:    req.UserID,
		MediaID:   req.MediaID,
		PlanID:    req.PlanID,
		ProductID: req.ProductID,
		Type:      req.Type,
		Category:  req.Category,
		Metric:    req.Metric,
	}

	// 调用内部函数
	result, err := s.getOrderTypeData(modelReq, userID, userRole)
	if err != nil {
		return domain.OrderTypeEntity{}, err
	}

	// 转换为Domain实体
	domainOrderTypes := make([]domain.OrderTypeDataItem, 0, len(result))
	for _, item := range result {
		domainOrderTypes = append(domainOrderTypes, domain.OrderTypeDataItem{
			Name:  item.Name,
			Value: item.Value,
		})
	}

	return domain.OrderTypeEntity{
		OrderTypes: domainOrderTypes,
	}, nil
}

// getOrderTypeData 内部函数，获取订单类型数据
func (s *dashboardStatsService) getOrderTypeData(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
	Metric    string
}, userID int, userRole int) ([]struct {
	Name  string
	Value float64
}, error) {
	// 获取数据权限范围
	scope, departmentUsers, err := s.dashboard.getDataScopeInfo(int64(userID), "dashboard")
	if err != nil {
		return nil, err
	}

	// 选择指标对应的字段
	var field string
	switch req.Metric {
	case "orders":
		field = "SUM(bd_orders)"
	case "estimated_revenue", "estimated_commission":
		field = "SUM(bd_revenue)"
	case "settled_revenue", "settled_commission":
		field = "SUM(bd_settle_revenue)"
	case "cost":
		field = "SUM(cost)"
	case "estimated_profit":
		field = "SUM(bd_profit)"
	case "settled_profit":
		field = "SUM(bd_settle_profit)"
	default:
		field = "SUM(bd_orders)"
	}

	// 构建查询
	query := s.dashboard.db.Table("ad_slot_plan_daily_stats")
	query = query.Select("order_type as name, " + field + " as value")
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)
	query = query.Where("order_type IS NOT NULL AND order_type != ''")

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加过滤条件
	if req.UserID != "" && req.UserID != "0" {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.MediaID != "" {
		query = query.Where("media_id = ?", req.MediaID)
	}
	if req.PlanID != "" {
		query = query.Where("plan_id = ?", req.PlanID)
	}
	if req.ProductID != "" {
		query = query.Where("ad_product_id = ?", req.ProductID)
	}

	// 根据合作类型过滤
	if req.Type != "" {
		var cooperationType string
		switch req.Type {
		case "1":
			cooperationType = "traffic"
		case "2":
			cooperationType = "cps"
		case "3":
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int
			s.dashboard.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("media_id IN (?)", mediaIDs)
			}
		}
	}

	// 根据渠道分类添加筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp", "wechat_plugin"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			// 获取符合条件的广告位ID
			var slotIDs []int
			s.dashboard.db.Model(&model.AdSlots{}).
				Where("type IN (?)", slotTypes).
				Select("id").
				Scan(&slotIDs)

			if len(slotIDs) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIDs)
			}
		}
	}

	// 分组和排序
	query = query.Group("order_type").Order("value DESC")

	// 执行查询
	var result []struct {
		Name  string
		Value float64
	}

	err = query.Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}
