package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

// UserController 用户控制器
type UserController struct {
	userService *service.UserService
}

// NewUserController 创建用户控制器实例
func NewUserController() *UserController {
	return &UserController{
		userService: service.NewUserService(),
	}
}

// GetUsers 获取用户列表
// @Summary 获取用户列表
// @Description 获取用户列表，支持分页和筛选
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param name query string false "用户名筛选"
// @Param role query int false "角色筛选"
// @Param status query int false "状态筛选"
// @Param department query string false "部门筛选"
// @Param is_locked query int false "锁定状态筛选"
// @Success 200 {object} response.Response{data=vo.GetUsersResp}
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users [get]
func (uc *UserController) GetUsers(c *gin.Context) {
	var req vo.GetUsersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := uc.userService.GetUsers(c, param)
	if err != nil {
		response.ServerError(c, "获取用户列表失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.GetUsersRespFromResult(result)
	response.Success(c, resp)
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body vo.CreateUserReq true "创建用户请求"
// @Success 200 {object} response.Response{data=vo.CreateUserResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users [post]
func (uc *UserController) CreateUser(c *gin.Context) {
	var req vo.CreateUserReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	result, err := uc.userService.CreateUser(c, param)
	if err != nil {
		response.ServerError(c, "创建用户失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.CreateUserRespFromResult(result)
	response.Success(c, resp)
}

// UpdateUser 更新用户
// @Summary 更新用户
// @Description 更新用户信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Param request body vo.UpdateUserReq true "更新用户请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id} [put]
func (uc *UserController) UpdateUser(c *gin.Context) {
	// 获取路径参数中的用户ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	var req vo.UpdateUserReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 确保路径中的ID与请求体中的ID一致
	req.ID = id

	// 使用VO提供的转换方法
	param := req.ToParam()

	err = uc.userService.UpdateUser(c, param)
	if err != nil {
		response.ServerError(c, "更新用户失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户（硬删除：真正删除数据）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id} [delete]
func (uc *UserController) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	err = uc.userService.DeleteUser(c, id)
	if err != nil {
		response.ServerError(c, "删除用户失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// SetUserInactive 标记用户为离职状态
// @Summary 标记用户离职
// @Description 标记用户为离职状态（软删除）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id}/inactive [put]
func (uc *UserController) SetUserInactive(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	err = uc.userService.SetUserInactive(c, id)
	if err != nil {
		response.ServerError(c, "标记用户离职失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// LockUser 锁定用户
// @Summary 锁定用户
// @Description 锁定用户，阻止登录
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Param request body vo.LockUserReq true "锁定请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id}/lock [put]
func (uc *UserController) LockUser(c *gin.Context) {
	// 获取路径参数中的用户ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	var req vo.LockUserReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	err = uc.userService.LockUser(c, id, param)
	if err != nil {
		response.ServerError(c, "锁定用户失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// UnlockUser 解锁用户
// @Summary 解锁用户
// @Description 解锁用户，允许登录
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id}/unlock [put]
func (uc *UserController) UnlockUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	err = uc.userService.UnlockUser(c, id)
	if err != nil {
		response.ServerError(c, "解锁用户失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// ResetPassword 重置用户密码
// @Summary 重置用户密码
// @Description 重置用户密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path uint64 true "用户ID"
// @Param request body vo.ResetPasswordReq true "重置密码请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/{id}/password/reset [put]
func (uc *UserController) ResetPassword(c *gin.Context) {
	// 获取路径参数中的用户ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的用户ID")
		return
	}

	var req vo.ResetPasswordReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	param := req.ToParam()

	err = uc.userService.ResetPassword(c, id, param)
	if err != nil {
		response.ServerError(c, "重置用户密码失败: "+err.Error())
		return
	}

	response.Success(c, nil)
}

// GetUserOptions 获取用户选项
// @Summary 获取用户选项
// @Description 获取用户管理相关选项，包括角色、部门和上级
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=vo.GetUserOptionsResp}
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/options [get]
func (uc *UserController) GetUserOptions(c *gin.Context) {
	// 获取用户选项
	result, err := uc.userService.GetUserOptions(c)
	if err != nil {
		response.ServerError(c, "获取用户选项失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.GetUserOptionsRespFromResult(result)
	response.Success(c, resp)
}

// GetMediaUsers 获取媒介用户列表
// @Summary 获取媒介用户列表
// @Description 获取所有媒介角色的用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=vo.MediaUserListResp}
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/users/media [get]
func (uc *UserController) GetMediaUsers(c *gin.Context) {
	// 获取媒介用户列表
	result, err := uc.userService.GetMediaUsers(c)
	if err != nil {
		response.ServerError(c, "获取媒介用户列表失败: "+err.Error())
		return
	}

	// 使用VO提供的转换方法
	resp := vo.MediaUserListRespFromResult(result)
	response.Success(c, resp)
}
