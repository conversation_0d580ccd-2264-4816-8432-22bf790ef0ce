package domain

import "time"

// 工作模式常量
const (
	WorkModeTraffic = "traffic" // 流量采买模式
	WorkModeAd      = "ad"      // 广告投放模式
)

// 角色常量
const (
	RoleMedia    = 1 // 媒介
	RoleOperator = 2 // 运营
	RoleAdmin    = 3 // 管理层
	RoleTrader   = 4 // 投手
	RoleFinance  = 5 // 财务
	RoleTech     = 6 // 技术
	RoleProduct  = 7 // 产品
	RoleBI       = 8 // BI分析师
)

// 状态常量
const (
	StatusActive   = 1 // 在职
	StatusInactive = 2 // 离职
)

// 锁定状态常量
const (
	UnLocked = 0 // 未锁定
	Locked   = 1 // 已锁定
)

// UserEntity 用户实体
// 表示系统中的用户信息
type UserEntity struct {
	ID              int64     // 用户ID
	Name            string    // 用户名
	RealName        string    // 真实姓名
	Email           string    // 电子邮箱
	Phone           string    // 手机号码
	Department      string    // 部门编码
	DepartmentName  string    // 部门名称
	Position        string    // 职位
	SupervisorID    int64     // 上级主管ID
	SupervisorName  string    // 上级主管姓名
	Role            int       // 角色类型
	RoleID          int64     // 角色ID
	RoleName        string    // 角色名称
	Status          int       // 状态
	IsLocked        bool      // 是否锁定
	LockReason      string    // 锁定原因
	Remark          string    // 备注
	DefaultWorkMode string    // 默认工作模式
	LastLoginAt     time.Time // 最后登录时间
	LastLoginIP     string    // 最后登录IP
	CreatedAt       time.Time // 创建时间
	UpdatedAt       time.Time // 更新时间
}

// MediaUserEntity 媒体用户实体
type MediaUserEntity struct {
	ID       int64  // 用户ID
	Username string // 用户名
	RealName string // 真实姓名
}

// GetUsersParam 获取用户列表参数
type GetUsersParam struct {
	Page       int    // 页码
	PageSize   int    // 每页数量
	Name       string // 用户名筛选
	Role       int    // 角色筛选
	Status     int    // 状态筛选
	Department string // 部门筛选
	IsLocked   int    // 锁定状态筛选
}

// GetUsersResult 获取用户列表结果
type GetUsersResult struct {
	List  []UserEntity // 用户列表
	Total int64        // 总数
	Page  int          // 当前页码
	Size  int          // 每页大小
}

// UserCreateParam 创建用户参数
type UserCreateParam struct {
	Name         string // 用户名
	RealName     string // 真实姓名
	Email        string // 电子邮箱
	Phone        string // 手机号码
	Department   string // 部门编码
	Position     string // 职位
	SupervisorID int64  // 上级主管ID
	Role         int    // 角色类型
	RoleID       int64  // 角色ID
	Password     string // 密码
	Remark       string // 备注
}

// CreateUserResult 创建用户结果
type CreateUserResult struct {
	ID int64 // 用户ID
}

// UserUpdateParam 更新用户参数
type UserUpdateParam struct {
	ID           int64  // 用户ID
	Name         string // 用户名
	RealName     string // 真实姓名
	Email        string // 电子邮箱
	Phone        string // 手机号码
	Department   string // 部门编码
	Position     string // 职位
	SupervisorID int64  // 上级主管ID
	Role         int    // 角色类型
	RoleID       int64  // 角色ID
	Status       int    // 状态
	Remark       string // 备注
}

// LockUserParam 锁定用户参数
type LockUserParam struct {
	LockReason string // 锁定原因
}

// ResetPasswordParam 重置密码参数
type ResetPasswordParam struct {
	NewPassword string // 新密码
}

// UserRoleOption 用户角色选项
type UserRoleOption struct {
	Value int    // 角色值
	Label string // 角色名称
}

// UserOption 用户选项
type UserOption struct {
	Value int64  // 用户ID
	Label string // 用户名称
}

// GetUserOptionsResult 获取用户选项结果
type GetUserOptionsResult struct {
	Roles       []UserRoleOption   // 角色选项
	Departments []DepartmentOption // 部门选项
	Supervisors []UserOption       // 上级选项
}

// MediaUserListResult 媒体用户列表结果
type MediaUserListResult struct {
	List []MediaUserEntity // 媒体用户列表
}

// UserBasicInfo 用户基本信息
type UserBasicInfo struct {
	ID         int64  // 用户ID
	Name       string // 用户名
	RealName   string // 真实姓名
	Email      string // 电子邮箱
	Phone      string // 手机号码
	Role       int    // 角色类型
	Status     int    // 状态
	Department string // 部门编码
	Position   string // 职位
}

// GetRoleName 获取角色名称
func GetRoleName(role int) string {
	switch role {
	case RoleMedia:
		return "媒介"
	case RoleOperator:
		return "运营"
	case RoleAdmin:
		return "管理层"
	case RoleTrader:
		return "投手"
	case RoleFinance:
		return "财务"
	case RoleTech:
		return "技术"
	case RoleProduct:
		return "产品"
	case RoleBI:
		return "BI分析师"
	default:
		return "未知角色"
	}
}

// IsAdmin 判断用户是否是管理员
func IsAdmin(role int) bool {
	return role == RoleAdmin
}

// IsActive 判断用户是否处于正常状态
func IsActive(status, isLocked int) bool {
	return status == StatusActive && isLocked == UnLocked
}

// GetAvailableModes 获取用户可访问的工作模式列表
func GetAvailableModes(role int) []string {
	// 基于角色的判断逻辑
	modes := []string{}

	// 根据角色确定可访问的模式
	switch role {
	case RoleMedia, RoleOperator, RoleAdmin, RoleTrader, RoleFinance, RoleTech, RoleProduct, RoleBI:
		modes = append(modes, WorkModeTraffic) // 所有角色都可以访问流量采买模式
	}

	// 特定角色可以访问广告投放模式
	if role == RoleAdmin || role == RoleTech || role == RoleProduct || role == RoleBI {
		modes = append(modes, WorkModeAd)
	}

	return modes
}

// GetAvailableModesWithPermissions 基于权限获取用户可访问的工作模式列表
func GetAvailableModesWithPermissions(role int, permissions []Permission) []string {
	modes := []string{}

	// 检查是否有流量采买模式权限
	trafficAccessGranted := false
	for _, p := range permissions {
		if p.Code == "traffic:access" {
			trafficAccessGranted = true
			break
		}
	}

	// 检查是否有广告投放模式权限
	adAccessGranted := false
	for _, p := range permissions {
		if p.Code == "ad:access" {
			adAccessGranted = true
			break
		}
	}

	// 添加有权限访问的模式
	if trafficAccessGranted {
		modes = append(modes, WorkModeTraffic)
	}
	if adAccessGranted {
		modes = append(modes, WorkModeAd)
	}

	return modes
}

// CanAccessMode 判断用户是否可以访问指定工作模式
func CanAccessMode(role int, mode string) bool {
	modes := GetAvailableModes(role)
	for _, m := range modes {
		if m == mode {
			return true
		}
	}
	return false
}

// CanAccessModeWithPermissions 基于权限判断用户是否可以访问指定工作模式
func CanAccessModeWithPermissions(role int, mode string, permissions []Permission) bool {
	modes := GetAvailableModesWithPermissions(role, permissions)
	for _, m := range modes {
		if m == mode {
			return true
		}
	}
	return false
}

// ToBasicUserInfo 将用户实体转换为基本用户信息
func (u UserEntity) ToBasicUserInfo() UserBasicInfo {
	return UserBasicInfo{
		ID:         u.ID,
		Name:       u.Name,
		RealName:   u.RealName,
		Email:      u.Email,
		Phone:      u.Phone,
		Role:       u.Role,
		Status:     u.Status,
		Department: u.Department,
		Position:   u.Position,
	}
}
