# 小红书创意报表导出聚合类型支持

## 功能概述

小红书创意报表导出功能现已支持根据聚合类型选择不同的查询逻辑，提供两种数据视图：

1. **分日数据（daily）**：保持原始数据粒度，按日期+创意ID维度展示
2. **汇总数据（summary）**：按创意ID聚合多日数据，提供汇总视图

## 聚合类型说明

### 1. 分日数据（daily）
- **数据特点**：保持原始数据的时间粒度
- **适用场景**：需要查看每日数据变化趋势、进行时间序列分析
- **数据结构**：每个创意每天一条记录
- **排序方式**：按时间降序，创意ID升序

**示例数据**：
```
创意A - 2024-01-01 - 消费100元
创意A - 2024-01-02 - 消费120元
创意B - 2024-01-01 - 消费80元
创意B - 2024-01-02 - 消费90元
```

### 2. 汇总数据（summary）
- **数据特点**：按创意ID聚合多日数据
- **适用场景**：需要查看创意整体表现、进行创意对比分析
- **数据结构**：每个创意一条汇总记录
- **聚合逻辑**：
  - 数值字段：SUM（消费、点击、转化等）
  - 比率字段：AVG（点击率、转化率等）
  - 文本字段：MAX（取最新值）
  - 时间字段：MIN（取最早时间）

**示例数据**：
```
创意A - 汇总 - 总消费220元（100+120）
创意B - 汇总 - 总消费170元（80+90）
```

## API 使用方式

### 请求参数

在导出请求中添加 `aggregate_type` 参数：

```json
{
  "account_id": 123456,
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "aggregate_type": "daily",  // 或 "summary"
  "format": "xlsx"
}
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|--------|
| aggregate_type | string | 否 | 聚合类型：daily（分日）或 summary（汇总） | daily |

### API 示例

#### 导出分日数据
```bash
curl -X POST http://localhost:8080/api/v1/xhs-creative-reports/export \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "aggregate_type": "daily",
    "format": "xlsx"
  }'
```

#### 导出汇总数据
```bash
curl -X POST http://localhost:8080/api/v1/xhs-creative-reports/export \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "aggregate_type": "summary",
    "format": "xlsx"
  }'
```

## 文件命名规则

导出文件名会根据聚合类型自动调整：

- **分日数据**：`xhs_creative_reports_分日_20240123_143022.xlsx`
- **汇总数据**：`xhs_creative_reports_汇总_20240123_143022.xlsx`

## 数据字段聚合规则

### 基础信息字段（取最新值）
- account_id, account_name
- campaign_id, campaign_name
- unit_id, unit_name
- creativity_id, creativity_name
- title, pwd, content_people, pitcher_name

### 数值字段（求和）
- fee（消费）
- impression（展现）
- click（点击）
- like, comment, collect, follow, share（互动数据）
- goods_order, success_goods_order（订单数据）
- rgmv（GMV）

### 比率字段（平均值）
- ctr（点击率）
- acp（平均点击成本）
- cpm（千次曝光成本）
- cpi（平均互动成本）
- roi（投资回报率）
- purchase_order_roi_7d（7日ROI）

### 时间字段
- time：取最早时间（MIN）
- created_at, updated_at：取最新时间（MAX）

## 性能优化

### 1. 查询限制
- 最大导出数量：50,000条记录
- 时间范围限制：最多90天
- 自动索引优化：按时间和创意ID排序

### 2. 内存优化
- 分批处理大量数据
- 流式导出避免内存溢出
- 异步处理大文件导出

## 使用建议

### 1. 选择合适的聚合类型
- **日常监控**：使用分日数据，观察趋势变化
- **效果评估**：使用汇总数据，对比创意表现
- **报告制作**：根据报告需求选择合适类型

### 2. 时间范围设置
- **分日数据**：建议不超过30天，避免数据量过大
- **汇总数据**：可以设置较长时间范围，如90天

### 3. 筛选条件优化
- 合理使用账号、计划、单元等筛选条件
- 避免过于宽泛的查询条件

## 错误处理

### 常见错误及解决方案

1. **聚合类型错误**
   ```json
   {
     "code": 400,
     "message": "聚合类型只支持daily或summary"
   }
   ```

2. **时间范围过大**
   ```json
   {
     "code": 400,
     "message": "导出数据的时间范围不能超过90天"
   }
   ```

3. **数据量过大**
   ```json
   {
     "code": 400,
     "message": "查询结果超过最大导出限制，请缩小查询范围"
   }
   ```

## 扩展功能

### 未来可能的增强
1. **自定义聚合维度**：支持按计划、单元等维度聚合
2. **增量导出**：支持只导出新增或变更的数据
3. **定时导出**：支持定时自动导出报表
4. **数据压缩**：支持大文件的压缩导出

## 技术实现

### 核心方法
- `getDailyReportsForExport()`：分日数据查询
- `getSummaryReportsForExport()`：汇总数据查询
- `addFilterConditionsForExport()`：通用筛选条件

### 数据库优化
- 使用GROUP BY进行聚合查询
- 合理使用索引提升查询性能
- 限制查询结果集大小
