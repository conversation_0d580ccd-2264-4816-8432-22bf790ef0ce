package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// AdProductController 投放产品控制器
type AdProductController struct {
	adProductService *service.AdProductService
}

// NewAdProductController 创建投放产品控制器实例
func NewAdProductController() *AdProductController {
	return &AdProductController{
		adProductService: service.NewAdProductService(),
	}
}

// GetList 获取投放产品列表
// @Summary 获取投放产品列表
// @Description 分页获取投放产品列表
// @Tags 投放产品
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param name query string false "产品名称"
// @Param status query string false "产品状态"
// @Success 200 {object} response.Response{data=vo.AdProductListResp}
// @Failure 400 {object} response.Response
// @Router /api/ad-products/list [get]
func (c *AdProductController) GetList(ctx *gin.Context) {
	// 创建VO请求对象
	var request vo.AdProductListReq
	if err := ctx.ShouldBind(&request); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为领域层参数
	param := request.ToAdProductParam()

	// 调用service获取数据
	result, err := c.adProductService.GetList(ctx, param)
	if err != nil {
		response.Error(ctx, response.CodeServerError, "获取投放产品列表失败: "+err.Error())
		return
	}

	// 转换为VO响应
	listResponse := vo.FromDomainAdProductListEntity(result)
	response.Success(ctx, listResponse)
}
