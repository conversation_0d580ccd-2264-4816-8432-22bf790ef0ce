package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// AdPluginController 广告插件控制器
type AdPluginController struct {
	adPluginService *service.AdPluginService
}

// NewAdPluginController 创建广告插件控制器
func NewAdPluginController() *AdPluginController {
	return &AdPluginController{
		adPluginService: service.NewAdPluginService(),
	}
}

// GetAdPage 获取广告页面
func (ctrl *AdPluginController) GetAdPage(c *gin.Context) {
	var req vo.GetAdPageReq

	// 绑定请求参数
	if err := c.ShouldBind(&req); err != nil {
		response.InvalidParam(c, "参数格式错误："+err.Error())
		return
	}

	// 调用服务层方法获取广告页面
	result, err := ctrl.adPluginService.GetAdPage(c.Request.Context(), req.ToAdPageParam())
	if err != nil {
		response.Error(c, response.CodeError, "获取广告页面失败："+err.Error())
		return
	}

	response.Success(c, vo.GetAdPageResp{
		BackgroundImage: result.BackgroundImage,
		BackgroundLink:  result.BackgroundLink,
		DialogType:      result.DialogType,
		Dialog:          result.Dialog,
		DialogLinkInfo:  result.DialogLinkInfo,
	})
}

// GetAdLink 获取广告链接
func (ctrl *AdPluginController) GetAdLink(c *gin.Context) {
	var req vo.GetAdLinkReq

	// 绑定请求参数
	if err := c.ShouldBind(&req); err != nil {
		response.InvalidParam(c, "参数格式错误："+err.Error())
		return
	}

	// 处理请求
	result, err := ctrl.adPluginService.GetAdLinkV2(c.Request.Context(), req.ToAdLinkParam())
	if err != nil {
		response.Error(c, response.CodeError, "获取广告链接失败："+err.Error())
		return
	}

	// 转换为响应并返回
	response.Success(c, vo.GetAdLinkResp{
		ID:                result.ID,
		PromotionLinkInfo: result.PromotionLinkInfo,
		CreativeInfo:      result.CreativeInfo,
	})
}
