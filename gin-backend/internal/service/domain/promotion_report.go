package domain

// PromotionReportParam 推广报表查询参数
type PromotionReportParam struct {
	StartDate  string // 开始日期
	EndDate    string // 结束日期
	GroupID    int64  // 口令组ID
	CategoryID int64  // 分类ID
	Page       int    // 页码
	PageSize   int    // 每页数量
}

// PromotionReportEntity 推广报表实体
type PromotionReportEntity struct {
	ID         int64   // 报表ID
	Date       string  // 报表日期
	GroupID    int64   // 口令组ID
	GroupName  string  // 口令组名称
	CategoryID int64   // 分类ID
	Category   string  // 分类名称
	Cost       float64 // 成本
	Orders     int     // 订单数
	Commission float64 // 佣金
	Profit     float64 // 利润
	ROI        float64 // 投资回报率
	OrderRate  float64 // 订单率
	ProfitRate float64 // 利润率
}

// PromotionReportListResult 推广报表列表结果
type PromotionReportListResult struct {
	Total int64                   // 总数
	Items []PromotionReportEntity // 列表项
}

// PromotionReportSummary 推广报表汇总数据
type PromotionReportSummary struct {
	TotalCost       float64 // 总成本
	TotalOrders     int     // 总订单数
	TotalCommission float64 // 总佣金
	TotalProfit     float64 // 总利润
	TotalROI        float64 // 平均ROI
	TotalOrderRate  float64 // 平均订单率
	TotalProfitRate float64 // 平均利润率
	TotalGroupCount int     // 组数量
}

// PromotionReportCostListParam 推广报表费用列表查询参数
type PromotionReportCostListParam struct {
	Page       int    // 页码
	Size       int    // 每页数量
	GroupId    int64  // 口令组ID
	CategoryId int64  // 分类ID
	StartDate  string // 开始日期
	EndDate    string // 结束日期
}

// PromotionReportCostEntity 推广报表费用实体
type PromotionReportCostEntity struct {
	ID           int64   // ID
	GroupId      int64   // 口令组ID
	GroupName    string  // 口令组名称
	ReportDate   string  // 报表日期
	CategoryId   int64   // 分类ID
	CategoryName string  // 分类名称
	Cost         float64 // 成本
	UpdatedAt    string  // 更新时间
}

// PromotionReportCostListResult 推广报表费用列表结果
type PromotionReportCostListResult struct {
	List  []PromotionReportCostEntity // 列表项
	Page  int                         // 页码
	Size  int                         // 每页数量
	Total int64                       // 总数
}

// PromotionReportTaskEntity 任务实体
type PromotionReportTaskEntity struct {
	ID        int64  // 任务ID
	Name      string // 任务名称
	Status    string // 任务状态
	CreatedAt string // 创建时间
}

// ModelReportParam 模型报表查询参数
type ModelReportParam struct {
	ModelId    int64 // 模型ID
	CategoryId int64 // 分类ID
	GroupId    int64 // 口令组ID
}

// ModelReportEntity 模型报表实体
type ModelReportEntity struct {
	Date              string  // 日期
	TotalOrders       int     // 总订单数
	Cost              float64 // 成本
	TotalCommission   float64 // 总佣金
	NewOrders         int     // 新订单数
	DailyCost         float64 // 日成本
	AverageCommission float64 // 平均佣金
	PaybackDays       int     // 回本天数
}

// ModelReportResult 模型报表结果
type ModelReportResult struct {
	List []ModelReportEntity // 列表项
}
