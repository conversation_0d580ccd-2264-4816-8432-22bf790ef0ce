package service

import (
	"context"
	"encoding/json"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strconv"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AdPluginService 广告插件服务
type AdPluginService struct {
	db *gorm.DB
}

// NewAdPluginService 创建广告插件服务
func NewAdPluginService() *AdPluginService {
	return &AdPluginService{
		db: global.DB,
	}
}

// GetAdPage 获取广告页面
func (s *AdPluginService) GetAdPage(ctx context.Context, param domain.AdPageParam) (domain.AdPageEntity, error) {
	// 初始化响应
	res := domain.AdPageEntity{
		BackgroundImage: "",  // 硬编码的背景图URL
		BackgroundLink:  nil, // 硬编码的背景图链接
		DialogType:      1,   // 默认显示系统弹窗
		Dialog: map[string]any{
			"title":         "",
			"content":       "支付成功",
			"button":        "确定",
			"cancel_button": "",
		},
	}

	// 获取广告链接
	adLinkParam := domain.AdLinkParam{
		SlotID: param.SlotID,
		OpenID: param.OpenID,
	}
	adLink, err := s.GetAdLinkV2(ctx, adLinkParam)
	if err != nil {
		return domain.AdPageEntity{}, fmt.Errorf("获取广告链接失败：%w", err)
	}

	res.DialogLinkInfo = adLink

	if len(adLink.CreativeInfo) > 0 {
		res.Dialog = adLink.CreativeInfo
		res.DialogType = 2
	}

	return res, nil
}

// GetAdLinkV2 获取广告链接V2版本
func (s *AdPluginService) GetAdLinkV2(ctx context.Context, param domain.AdLinkParam) (domain.AdLinkEntity, error) {
	startTime := time.Now()
	zap.L().Info("开始处理广告链接请求", zap.Int64("slotId", param.SlotID))

	defer func() {
		zap.L().Info("广告链接请求处理完成",
			zap.Int64("slotId", param.SlotID),
			zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))
	}()

	zap.L().Debug("获取广告链接请求", zap.Int64("slotId", param.SlotID))

	res := domain.AdLinkEntity{}

	// 生成Redis缓存键
	plansKey := fmt.Sprintf("ad_plans:slot:%d", param.SlotID)
	callCountKey := fmt.Sprintf("ad_plans:slot:%d:call_count", param.SlotID)

	// 计算今天结束时间作为缓存过期时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int64(today.Sub(now).Seconds())
	if expireSeconds <= 0 {
		expireSeconds = 86400 // 如果已经是今天的最后时刻，设置为24小时
	}

	// 尝试从缓存获取广告计划列表
	var plans []domain.AdPlanCacheEntity
	var err error

	// 从Redis获取广告计划
	cache := global.Cache.Get(ctx, plansKey).String()

	zap.L().Info("Redis获取广告计划完成",
		zap.Int64("slotId", param.SlotID),
		zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))

	// 缓存不存在，从数据库读取并缓存
	if cache == "" {
		zap.L().Debug("缓存不存在，从数据库查询广告计划")

		// 查询条件：审核状态为通过，投放状态为投放中，按id升序
		var adPlans []domain.AdSlotPlanEntity
		// 注意: 这里的常量需要根据实际项目中的定义来调整
		const (
			PLAN_AUDIT_STATUS_APPROVED   = 2 // 审核通过
			PLAN_DELIVERY_STATUS_RUNNING = 2 // 投放中
		)

		err = s.db.Table("ad_slot_plans").
			Where("ad_slot_id = ?", param.SlotID).
			Where("audit_status = ?", PLAN_AUDIT_STATUS_APPROVED).      // 审核通过
			Where("delivery_status = ?", PLAN_DELIVERY_STATUS_RUNNING). // 投放中
			Order("id asc").
			Find(&adPlans).Error

		if err != nil {
			zap.L().Error("查询广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 如果没有符合条件的计划，直接返回空
		if len(adPlans) == 0 {
			zap.L().Info("没有找到符合条件的广告计划")
			return res, nil
		}

		// 获取所有计划的ID
		planIds := make([]int64, len(adPlans))
		for i, plan := range adPlans {
			planIds[i] = plan.ID
		}

		// 获取计划对应的推广链接
		var promotionZones []domain.PromotionZoneEntity
		// 直接查询所有计划的推广链接
		err = s.db.Table("promotion_zones").
			Select("plan_id, promotion_link, promotion_params").
			Where("plan_id IN ?", planIds).
			Order("plan_id asc").
			Order("id asc"). // 确保每个计划的推广位按ID升序排序
			Find(&promotionZones).Error

		if err != nil {
			zap.L().Error("查询推广链接失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 创建plan_id到promotion_link的映射，只保留每个计划的第一个链接
		promotionLinkMap := make(map[int64]string)
		for _, pz := range promotionZones {
			// 如果该计划ID还没有对应的链接，则保存这个链接（因为已经按ID排序，所以是最小ID的链接）
			if _, exists := promotionLinkMap[pz.PlanID]; !exists {
				promotionLinkMap[pz.PlanID] = pz.PromotionParams
			}
		}

		// 提取需要的计划信息
		plans = make([]domain.AdPlanCacheEntity, 0, len(adPlans))
		for _, plan := range adPlans {
			promotionLinkInfo, ok := promotionLinkMap[plan.ID]
			if !ok || promotionLinkInfo == "" {
				continue
			}
			var info map[string]any
			err = json.Unmarshal([]byte(promotionLinkInfo), &info)
			if err != nil {
				zap.L().Error("解析推广链接参数失败", zap.Error(err))
				continue
			}
			plans = append(plans, domain.AdPlanCacheEntity{
				ID:                plan.ID,
				PromotionLinkInfo: info,
				AdCreativeID:      plan.AdCreativeID,
				StartTime:         plan.StartDate,
				EndTime:           plan.EndDate,
			})
		}

		// 如果没有计划，直接返回空
		if len(plans) == 0 {
			zap.L().Info("没有可用的广告计划")
			return res, nil
		}

		// 序列化计划信息并存入Redis
		plansJson, err := json.Marshal(plans)
		if err != nil {
			zap.L().Error("序列化广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 设置缓存，过期时间到今天结束
		err = global.Cache.SetEx(ctx, plansKey, string(plansJson), time.Duration(expireSeconds)*time.Second).Err()
		if err != nil {
			zap.L().Error("缓存广告计划失败", zap.Error(err))
			// 继续执行，不影响返回结果
		}

		// 初始化调用次数为0
		err = global.Cache.SetEx(ctx, callCountKey, "0", time.Duration(expireSeconds)*time.Second).Err()
		if err != nil {
			zap.L().Error("初始化调用次数失败", zap.Error(err))
		}
	} else {
		// 从缓存解析广告计划
		err = json.Unmarshal([]byte(cache), &plans)
		if err != nil {
			zap.L().Error("解析缓存的广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}
	}

	// 处理计划，处理在当前时间生效的计划
	newPlans := make([]domain.AdPlanCacheEntity, 0, len(plans))
	for _, plan := range plans {
		if plan.StartTime.Before(now) && (plan.EndTime.IsZero() || plan.EndTime.After(now)) {
			newPlans = append(newPlans, plan)
		}
	}

	plans = newPlans

	// 如果没有计划，直接返回空
	if len(plans) == 0 {
		zap.L().Info("没有可用的广告计划")
		return res, nil
	}

	// 获取并递增调用次数
	callCount := 0
	callCountCache := global.Cache.Get(ctx, callCountKey).String()
	if err == nil && callCountCache != "" {
		callCount, _ = strconv.Atoi(callCountCache)
	}

	// 计算当前应该返回的计划索引
	index := callCount % len(plans)

	// 获取当前索引对应的计划
	plan := plans[index]
	res.ID = plan.ID
	res.PromotionLinkInfo = plan.PromotionLinkInfo

	// 如果计划有关联的创意ID，从创意缓存中获取创意信息
	if plan.AdCreativeID > 0 {
		creativeKey := fmt.Sprintf("ad_creative:%d", plan.AdCreativeID)
		creativeCache := global.Cache.Get(ctx, creativeKey).String()
		if err == nil && creativeCache != "" {
			var creativeInfo map[string]any
			err = json.Unmarshal([]byte(creativeCache), &creativeInfo)
			if err == nil {
				res.CreativeInfo = creativeInfo
			}

			zap.L().Info("Redis获取创意信息完成",
				zap.Int64("slotId", param.SlotID),
				zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))
		} else {
			// 缓存不存在或出错，从数据库查询创意信息
			var creative model.Creative
			err = s.db.Table("ad_creatives").
				Where("id = ?", plan.AdCreativeID).
				First(&creative).Error

			if err == nil {
				// 解析热区信息
				var hotAreas []map[string]any
				if creative.HotAreas != "" {
					err = json.Unmarshal([]byte(creative.HotAreas), &hotAreas)
					if err != nil {
						zap.L().Error("解析创意热区失败", zap.Error(err))
					}
				}

				creativeInfo := map[string]any{
					"id":               creative.ID,
					"name":             creative.Name,
					"hot_areas":        hotAreas,
					"image_url":        creative.ImageURL,
					"image_area":       creative.ImageArea,
					"background_color": creative.BackgroundColor,
				}

				res.CreativeInfo = creativeInfo

				// 序列化创意信息并存入Redis
				creativeJson, err := json.Marshal(creativeInfo)
				if err == nil {
					err = global.Cache.SetEx(ctx, creativeKey, string(creativeJson), time.Duration(expireSeconds)*time.Second).Err()
					if err != nil {
						zap.L().Error("缓存创意信息失败", zap.Error(err))
					}
				}
			} else {
				zap.L().Error("查询创意信息失败", zap.Error(err), zap.Int64("creativeId", plan.AdCreativeID))
			}
		}
	}

	// 递增调用次数
	err = global.Cache.IncrBy(ctx, callCountKey, 1).Err()
	if err != nil {
		zap.L().Error("递增调用次数失败", zap.Error(err))
	}

	return res, nil
}

// GetAdLink 获取广告链接
func (s *AdPluginService) GetAdLink(ctx context.Context, param domain.AdLinkParam) (domain.AdLinkEntity, error) {
	startTime := time.Now()
	zap.L().Info("开始处理广告链接请求",
		zap.Int64("slotId", param.SlotID),
		zap.String("openId", param.OpenID))

	defer func() {
		zap.L().Info("广告链接请求处理完成",
			zap.Int64("slotId", param.SlotID),
			zap.String("openId", param.OpenID),
			zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))
	}()

	zap.L().Debug("获取广告链接请求",
		zap.Int64("slotId", param.SlotID),
		zap.String("openId", param.OpenID))

	res := domain.AdLinkEntity{}

	// 生成Redis缓存键
	plansKey := fmt.Sprintf("ad_plans:slot:%d", param.SlotID)
	indexKey := fmt.Sprintf("ad_plans:slot:%d:index:%s", param.SlotID, param.OpenID)

	// 计算今天结束时间作为缓存过期时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int64(today.Sub(now).Seconds())
	if expireSeconds <= 0 {
		expireSeconds = 86400 // 如果已经是今天的最后时刻，设置为24小时
	}

	// 尝试从缓存获取广告计划列表
	var plans []domain.AdPlanCacheEntity

	var err error

	// 从Redis获取广告计划
	cache := global.Cache.Get(ctx, plansKey).String()

	zap.L().Info("Redis获取广告计划完成",
		zap.Int64("slotId", param.SlotID),
		zap.String("openId", param.OpenID),
		zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))

	// 缓存不存在，从数据库读取并缓存
	if cache == "" {
		zap.L().Debug("缓存不存在，从数据库查询广告计划")

		// 查询条件：审核状态为通过，投放状态为投放中，按id升序
		var adPlans []domain.AdSlotPlanEntity
		// 注意: 这里的常量需要根据实际项目中的定义来调整
		const (
			PLAN_AUDIT_STATUS_APPROVED   = 2 // 审核通过
			PLAN_DELIVERY_STATUS_RUNNING = 2 // 投放中
		)

		err = s.db.Table("ad_slot_plans").
			Where("ad_slot_id = ?", param.SlotID).
			Where("audit_status = ?", PLAN_AUDIT_STATUS_APPROVED).      // 审核通过
			Where("delivery_status = ?", PLAN_DELIVERY_STATUS_RUNNING). // 投放中
			Order("id asc").
			Find(&adPlans).Error

		if err != nil {
			zap.L().Error("查询广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 如果没有符合条件的计划，直接返回空
		if len(adPlans) == 0 {
			zap.L().Info("没有找到符合条件的广告计划")
			return res, nil
		}

		// 获取所有计划的ID
		planIds := make([]int64, len(adPlans))
		for i, plan := range adPlans {
			planIds[i] = plan.ID
		}

		// 获取计划对应的推广链接
		var promotionZones []domain.PromotionZoneEntity
		// 直接查询所有计划的推广链接
		err = s.db.Table("promotion_zones").
			Select("plan_id, promotion_link, promotion_params").
			Where("plan_id IN ?", planIds).
			Order("plan_id asc").
			Order("id asc"). // 确保每个计划的推广位按ID升序排序
			Find(&promotionZones).Error

		if err != nil {
			zap.L().Error("查询推广链接失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 创建plan_id到promotion_link的映射，只保留每个计划的第一个链接
		promotionLinkMap := make(map[int64]string)
		for _, pz := range promotionZones {
			// 如果该计划ID还没有对应的链接，则保存这个链接（因为已经按ID排序，所以是最小ID的链接）
			if _, exists := promotionLinkMap[pz.PlanID]; !exists {
				promotionLinkMap[pz.PlanID] = pz.PromotionParams
			}
		}

		// 提取需要的计划信息
		plans = make([]domain.AdPlanCacheEntity, 0, len(adPlans))
		for _, plan := range adPlans {
			promotionLinkInfo, ok := promotionLinkMap[plan.ID]
			if !ok || promotionLinkInfo == "" {
				continue
			}
			var info map[string]any
			err = json.Unmarshal([]byte(promotionLinkInfo), &info)
			if err != nil {
				zap.L().Error("解析推广链接参数失败", zap.Error(err))
				continue
			}
			plans = append(plans, domain.AdPlanCacheEntity{
				ID:                plan.ID,
				PromotionLinkInfo: info,
				AdCreativeID:      plan.AdCreativeID,
				StartTime:         plan.StartDate,
				EndTime:           plan.EndDate,
			})
		}

		// 如果没有计划，直接返回空
		if len(plans) == 0 {
			zap.L().Info("没有可用的广告计划")
			return res, nil
		}

		// 序列化计划信息并存入Redis
		plansJson, err := json.Marshal(plans)
		if err != nil {
			zap.L().Error("序列化广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}

		// 设置缓存，过期时间到今天结束
		err = global.Cache.SetEx(ctx, plansKey, string(plansJson), time.Duration(expireSeconds)*time.Second).Err()
		if err != nil {
			zap.L().Error("缓存广告计划失败", zap.Error(err))
			// 继续执行，不影响返回结果
		}
	} else {
		// 从缓存解析广告计划
		err = json.Unmarshal([]byte(cache), &plans)
		if err != nil {
			zap.L().Error("解析缓存的广告计划失败", zap.Error(err))
			return domain.AdLinkEntity{}, err
		}
	}

	// 处理计划，处理在当前时间生效的计划
	newPlans := make([]domain.AdPlanCacheEntity, 0, len(plans))
	for _, plan := range plans {
		if plan.StartTime.Before(now) && (plan.EndTime.IsZero() || plan.EndTime.After(now)) {
			newPlans = append(newPlans, plan)
		}
	}

	plans = newPlans

	// 如果没有计划，直接返回空
	if len(plans) == 0 {
		zap.L().Info("没有可用的广告计划")
		return res, nil
	}

	// 获取当前用户的轮询索引
	indexCache := global.Cache.Get(ctx, indexKey).String()

	zap.L().Info("Redis获取轮询索引完成",
		zap.Int64("slotId", param.SlotID),
		zap.String("openId", param.OpenID),
		zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))

	// 计算当前应该返回的计划索引
	index := 0
	if indexCache != "" {
		index, _ = strconv.Atoi(indexCache)
	}

	// 确保索引在有效范围内
	if index >= len(plans) {
		index = 0
	}

	// 获取当前索引对应的计划
	plan := plans[index]
	res.ID = plan.ID
	res.PromotionLinkInfo = plan.PromotionLinkInfo

	// 如果计划有关联的创意ID，从创意缓存中获取创意信息
	if plan.AdCreativeID > 0 {
		creativeKey := fmt.Sprintf("ad_creative:%d", plan.AdCreativeID)
		creativeCache := global.Cache.Get(ctx, creativeKey).String()
		if creativeCache != "" {
			var creativeInfo map[string]any
			err := json.Unmarshal([]byte(creativeCache), &creativeInfo)
			if err == nil {
				res.CreativeInfo = creativeInfo
			}

			zap.L().Info("Redis获取创意信息完成",
				zap.Int64("slotId", param.SlotID),
				zap.String("openId", param.OpenID),
				zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))
		} else {
			// 缓存不存在或出错，从数据库查询创意信息
			var creative model.Creative
			err := s.db.Table("ad_creatives").
				Where("id = ?", plan.AdCreativeID).
				First(&creative).Error

			if err == nil {
				// 解析热区信息
				var hotAreas []map[string]any
				if creative.HotAreas != "" {
					err = json.Unmarshal([]byte(creative.HotAreas), &hotAreas)
					if err != nil {
						zap.L().Error("解析创意热区失败", zap.Error(err))
					}
				}

				creativeInfo := map[string]any{
					"id":               creative.ID,
					"name":             creative.Name,
					"hot_areas":        hotAreas,
					"image_url":        creative.ImageURL,
					"image_area":       creative.ImageArea,
					"background_color": creative.BackgroundColor,
				}

				res.CreativeInfo = creativeInfo

				// 序列化创意信息并存入Redis
				creativeJson, err := json.Marshal(creativeInfo)
				if err == nil {
					err = global.Cache.SetEx(ctx, creativeKey, string(creativeJson), time.Duration(expireSeconds)*time.Second).Err()
					if err != nil {
						zap.L().Error("缓存创意信息失败", zap.Error(err))
					}
				}
			} else {
				zap.L().Error("查询创意信息失败", zap.Error(err))
			}
		}
	}

	// 更新轮询索引，为下次请求准备
	nextIndex := (index + 1) % len(plans)
	err = global.Cache.SetEx(ctx, indexKey, fmt.Sprintf("%d", nextIndex), time.Duration(expireSeconds)*time.Second).Err()
	if err != nil {
		zap.L().Error("更新轮询索引失败", zap.Error(err))
		// 继续执行，不影响返回结果
	}

	zap.L().Info("更新轮询索引完成",
		zap.Int64("slotId", param.SlotID),
		zap.String("openId", param.OpenID),
		zap.Int64("耗时(ms)", time.Since(startTime).Milliseconds()))

	zap.L().Debug("返回广告链接",
		zap.Any("promotionLinkInfo", res.PromotionLinkInfo),
		zap.Int64("planID", plan.ID),
		zap.Int("索引", index),
		zap.Int("新索引", nextIndex))

	return res, nil
}
