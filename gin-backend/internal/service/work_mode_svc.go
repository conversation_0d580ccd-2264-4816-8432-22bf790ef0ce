package service

import (
	"context"
	"errors"
	"fmt"
	"gin-backend/internal/service/domain"
)

// WorkModeService 工作模式服务
type WorkModeService struct{}

// NewWorkModeService 创建工作模式服务
func NewWorkModeService() *WorkModeService {
	return &WorkModeService{}
}

// GetUserModes 获取用户可用的工作模式
func (s *WorkModeService) GetUserModes(ctx context.Context, user domain.UserEntity) (domain.UserWorkModes, error) {
	// 初始化返回结果
	result := domain.UserWorkModes{
		DefaultMode: user.DefaultWorkMode,
		CurrentMode: user.DefaultWorkMode, // 使用DefaultWorkMode代替，因为User实体中可能没有CurrentWorkMode字段
		Modes:       make([]domain.WorkMode, 0),
	}

	// 检查用户是否可以访问流量主模式
	if domain.CanAccessMode(user.Role, domain.WorkModeTraffic) {
		result.Modes = append(result.Modes, domain.WorkMode{
			Code: domain.WorkModeTraffic,
			Name: "流量主模式",
		})
	}

	// 检查用户是否可以访问广告主模式
	if domain.CanAccessMode(user.Role, domain.WorkModeAd) {
		result.Modes = append(result.Modes, domain.WorkMode{
			Code: domain.WorkModeAd,
			Name: "广告主模式",
		})
	}

	// 如果用户没有可用模式
	if len(result.Modes) == 0 {
		return domain.UserWorkModes{}, errors.New("用户没有可用的工作模式")
	}

	return result, nil
}

// SwitchWorkMode 切换工作模式
func (s *WorkModeService) SwitchWorkMode(ctx context.Context, user domain.UserEntity, mode string) (domain.WorkModeMenus, error) {
	// 检查用户是否有权限访问该模式
	if !domain.CanAccessMode(user.Role, mode) {
		return domain.WorkModeMenus{}, fmt.Errorf("没有权限访问 %s 模式", mode)
	}

	// 根据不同模式返回不同的菜单和权限
	result := domain.WorkModeMenus{
		Mode:        mode,
		Permissions: make([]string, 0),
		Menus:       make([]domain.Menu, 0),
	}

	// 这里应该根据用户角色和工作模式查询对应的菜单和权限
	// 简化处理：添加一些示例权限和菜单
	if mode == domain.WorkModeTraffic {
		result.Permissions = []string{"traffic.view", "traffic.manage"}
		// 添加流量主模式菜单
		result.Menus = getTrafficMenus()
	} else if mode == domain.WorkModeAd {
		result.Permissions = []string{"ad.view", "ad.manage", "ad.report"}
		// 添加广告主模式菜单
		result.Menus = getAdMenus()
	}

	return result, nil
}

// CheckModePermission 检查用户是否有指定模式的权限
func (s *WorkModeService) CheckModePermission(ctx context.Context, user domain.UserEntity, mode string) (bool, error) {
	if mode == "" {
		return false, errors.New("模式不能为空")
	}
	return domain.CanAccessMode(user.Role, mode), nil
}

// 获取流量主模式菜单
func getTrafficMenus() []domain.Menu {
	// 示例菜单
	return []domain.Menu{
		{
			Path:      "/dashboard",
			Component: "Dashboard",
			Name:      "Dashboard",
			Meta: domain.Meta{
				Title:       "仪表盘",
				Icon:        "dashboard",
				Permissions: "traffic.view",
			},
		},
		{
			Path:      "/slots",
			Component: "Slots",
			Name:      "Slots",
			Meta: domain.Meta{
				Title:       "广告位",
				Icon:        "slot",
				Permissions: "traffic.manage",
			},
		},
	}
}

// 获取广告主模式菜单
func getAdMenus() []domain.Menu {
	// 示例菜单
	return []domain.Menu{
		{
			Path:      "/ad-dashboard",
			Component: "AdDashboard",
			Name:      "AdDashboard",
			Meta: domain.Meta{
				Title:       "广告主仪表盘",
				Icon:        "dashboard",
				Permissions: "ad.view",
			},
		},
		{
			Path:      "/campaigns",
			Component: "Campaigns",
			Name:      "Campaigns",
			Meta: domain.Meta{
				Title:       "广告计划",
				Icon:        "campaign",
				Permissions: "ad.manage",
			},
		},
		{
			Path:      "/reports",
			Component: "Reports",
			Name:      "Reports",
			Meta: domain.Meta{
				Title:       "数据报表",
				Icon:        "report",
				Permissions: "ad.report",
			},
		},
	}
}
