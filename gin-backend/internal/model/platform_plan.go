package model

import "time"

// PlatformPlan 平台计划
type PlatformPlan struct {
	ID               int64     `gorm:"primaryKey;autoIncrement"`                    // 平台计划ID
	AgentID          int64     `gorm:"column:agent_id;index"`                       // 代理商ID
	MediaID          int64     `gorm:"column:media_id;index"`                       // 媒体ID
	PlatformType     string    `gorm:"column:platform_type;type:varchar(50)"`       // 平台类型
	Remark           string    `gorm:"column:remark;type:text"`                     // 备注
	DataID           string    `gorm:"column:data_id;type:varchar(100);index"`      // 第三方数据ID
	Name             string    `gorm:"column:name;type:varchar(200)"`               // 计划名称
	PlanType         string    `gorm:"column:plan_type;type:varchar(20)"`           // 计划类型
	PrincipalAccount string    `gorm:"column:principal_account;type:varchar(100)"`  // 商家账户
	PrincipalName    string    `gorm:"column:principal_name;type:varchar(100)"`     // 商家名称
	MarketTargetName string    `gorm:"column:market_target_name;type:varchar(100)"` // 营销目标名称
	SceneName        string    `gorm:"column:scene_name;type:varchar(100)"`         // 场景名称
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime"`            // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime"`            // 更新时间

	// 关联模型
	Agent *Agent `gorm:"foreignKey:AgentID"` // 代理商
	Media *Media `gorm:"foreignKey:MediaID"` // 媒体
}

// TableName 表名
func (PlatformPlan) TableName() string {
	return "platform_plans"
}
