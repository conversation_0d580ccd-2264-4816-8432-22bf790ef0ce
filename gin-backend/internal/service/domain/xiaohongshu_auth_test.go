package domain

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateXiaohongshuAuthParam(t *testing.T) {
	tests := []struct {
		name      string
		param     XiaohongshuAuthParam
		expectErr bool
		errMsg    string
	}{
		{
			name: "有效的授权参数",
			param: XiaohongshuAuthParam{
				AccountID: 1,
				AuthCode:  "test_auth_code_123",
			},
			expectErr: false,
		},
		{
			name: "无效的账号ID",
			param: XiaohongshuAuthParam{
				AccountID: 0,
				AuthCode:  "test_auth_code_123",
			},
			expectErr: true,
			errMsg:    "广告账号ID必须大于0",
		},
		{
			name: "负数账号ID",
			param: XiaohongshuAuthParam{
				AccountID: -1,
				AuthCode:  "test_auth_code_123",
			},
			expectErr: true,
			errMsg:    "广告账号ID必须大于0",
		},
		{
			name: "缺少授权码",
			param: XiaohongshuAuthParam{
				AccountID: 1,
				AuthCode:  "",
			},
			expectErr: true,
			errMsg:    "授权码不能为空",
		},
		{
			name: "空白授权码",
			param: XiaohongshuAuthParam{
				AccountID: 1,
				AuthCode:  "   ",
			},
			expectErr: false, // 这里只检查是否为空字符串，不检查空白字符
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateXiaohongshuAuthParam(tt.param)
			if tt.expectErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateXiaohongshuRefreshTokenParam(t *testing.T) {
	tests := []struct {
		name      string
		param     XiaohongshuRefreshTokenParam
		expectErr bool
		errMsg    string
	}{
		{
			name: "有效的刷新令牌参数",
			param: XiaohongshuRefreshTokenParam{
				AccountID:    1,
			},
			expectErr: false,
		},
		{
			name: "无效的账号ID",
			param: XiaohongshuRefreshTokenParam{
				AccountID:    0,
			},
			expectErr: true,
			errMsg:    "广告账号ID必须大于0",
		},
		{
			name: "负数账号ID",
			param: XiaohongshuRefreshTokenParam{
				AccountID:    -1,
			},
			expectErr: true,
			errMsg:    "广告账号ID必须大于0",
		},
		{
			name: "缺少刷新令牌",
			param: XiaohongshuRefreshTokenParam{
				AccountID:    1,
			},
			expectErr: true,
			errMsg:    "刷新令牌不能为空",
		},
		{
			name: "空白刷新令牌",
			param: XiaohongshuRefreshTokenParam{
				AccountID:    1,
			},
			expectErr: false, // 这里只检查是否为空字符串，不检查空白字符
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateXiaohongshuRefreshTokenParam(tt.param)
			if tt.expectErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestXiaohongshuAuthParam_Validation(t *testing.T) {
	// 测试边界值
	t.Run("最小有效账号ID", func(t *testing.T) {
		param := XiaohongshuAuthParam{
			AccountID: 1,
			AuthCode:  "valid_code",
		}
		err := ValidateXiaohongshuAuthParam(param)
		assert.NoError(t, err)
	})

	t.Run("大账号ID", func(t *testing.T) {
		param := XiaohongshuAuthParam{
			AccountID: 9223372036854775807, // int64最大值
			AuthCode:  "valid_code",
		}
		err := ValidateXiaohongshuAuthParam(param)
		assert.NoError(t, err)
	})

	t.Run("长授权码", func(t *testing.T) {
		longCode := ""
		for i := 0; i < 1000; i++ {
			longCode += "a"
		}
		param := XiaohongshuAuthParam{
			AccountID: 1,
			AuthCode:  longCode,
		}
		err := ValidateXiaohongshuAuthParam(param)
		assert.NoError(t, err)
	})
}

func TestXiaohongshuRefreshTokenParam_Validation(t *testing.T) {
	// 测试边界值
	t.Run("最小有效账号ID", func(t *testing.T) {
		param := XiaohongshuRefreshTokenParam{
			AccountID:    1,
		}
		err := ValidateXiaohongshuRefreshTokenParam(param)
		assert.NoError(t, err)
	})

	t.Run("大账号ID", func(t *testing.T) {
		param := XiaohongshuRefreshTokenParam{
			AccountID:    9223372036854775807, // int64最大值
		}
		err := ValidateXiaohongshuRefreshTokenParam(param)
		assert.NoError(t, err)
	})

	t.Run("长刷新令牌", func(t *testing.T) {
		longToken := ""
		for i := 0; i < 1000; i++ {
			longToken += "a"
		}
		param := XiaohongshuRefreshTokenParam{
			AccountID:    1,
		}
		err := ValidateXiaohongshuRefreshTokenParam(param)
		assert.NoError(t, err)
	})
}

func TestXiaohongshuAuthResult_Structure(t *testing.T) {
	// 测试结果结构体的字段
	result := XiaohongshuAuthResult{
		AccessToken:           "access_token_123",
		RefreshToken:          "refresh_token_456",
		AccessTokenExpiresIn:  3600,
		RefreshTokenExpiresIn: 7200,
		AdvertiserID:          123456,
		AdvertiserName:        "测试广告主",
	}

	assert.Equal(t, "access_token_123", result.AccessToken)
	assert.Equal(t, "refresh_token_456", result.RefreshToken)
	assert.Equal(t, int64(3600), result.AccessTokenExpiresIn)
	assert.Equal(t, int64(7200), result.RefreshTokenExpiresIn)
	assert.Equal(t, int64(123456), result.AdvertiserID)
	assert.Equal(t, "测试广告主", result.AdvertiserName)
}

func TestXiaohongshuRefreshTokenResult_Structure(t *testing.T) {
	// 测试刷新令牌结果结构体的字段
	result := XiaohongshuRefreshTokenResult{
		AccessToken:           "new_access_token_123",
		RefreshToken:          "new_refresh_token_456",
		AccessTokenExpiresIn:  3600,
		RefreshTokenExpiresIn: 7200,
	}

	assert.Equal(t, "new_access_token_123", result.AccessToken)
	assert.Equal(t, "new_refresh_token_456", result.RefreshToken)
	assert.Equal(t, int64(3600), result.AccessTokenExpiresIn)
	assert.Equal(t, int64(7200), result.RefreshTokenExpiresIn)
}

func TestXiaohongshuAuth_EmptyValues(t *testing.T) {
	// 测试空值情况
	t.Run("空授权参数", func(t *testing.T) {
		param := XiaohongshuAuthParam{}
		err := ValidateXiaohongshuAuthParam(param)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "广告账号ID必须大于0")
	})

	t.Run("空刷新令牌参数", func(t *testing.T) {
		param := XiaohongshuRefreshTokenParam{}
		err := ValidateXiaohongshuRefreshTokenParam(param)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "广告账号ID必须大于0")
	})
}

func TestXiaohongshuAuth_SpecialCharacters(t *testing.T) {
	// 测试特殊字符
	t.Run("授权码包含特殊字符", func(t *testing.T) {
		param := XiaohongshuAuthParam{
			AccountID: 1,
			AuthCode:  "auth_code_with_!@#$%^&*()_+-={}[]|\\:;\"'<>?,./",
		}
		err := ValidateXiaohongshuAuthParam(param)
		assert.NoError(t, err)
	})

	t.Run("刷新令牌包含特殊字符", func(t *testing.T) {
		param := XiaohongshuRefreshTokenParam{
			AccountID:    1,
		}
		err := ValidateXiaohongshuRefreshTokenParam(param)
		assert.NoError(t, err)
	})
}
