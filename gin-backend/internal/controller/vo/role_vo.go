package vo

import "gin-backend/internal/service/domain"

// GetRolesReq 获取角色列表请求
type GetRolesReq struct {
	Page     int    `json:"page" form:"page"`           // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
	Name     string `json:"name" form:"name"`           // 角色名称
	Code     string `json:"code" form:"code"`           // 角色编码
	Status   int    `json:"status" form:"status"`       // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域参数
func (req GetRolesReq) ToDomain() domain.RoleListParam {
	return domain.RoleListParam{
		Page:     req.Page,
		PageSize: req.PageSize,
		Name:     req.Name,
		Code:     req.Code,
		Status:   req.Status,
	}
}

// GetRolesResp 获取角色列表响应
type GetRolesResp struct {
	List  []RoleItem `json:"list"`  // 角色列表
	Total int64      `json:"total"` // 总记录数
	Page  int        `json:"page"`  // 当前页码
	Size  int        `json:"size"`  // 每页数量
}

// FromDomainRoleList 从领域实体转换为角色列表响应
func FromDomainRoleList(entity domain.RoleListEntity, page, size int) GetRolesResp {
	items := make([]RoleItem, 0, len(entity.List))
	for _, e := range entity.List {
		items = append(items, RoleItem{
			ID:          e.ID,
			Name:        e.Name,
			Code:        e.Code,
			Description: e.Description,
			Status:      e.Status,
			IsSystem:    e.IsSystem,
			CreatedAt:   e.CreatedAt,
			UpdatedAt:   e.UpdatedAt,
		})
	}
	return GetRolesResp{
		List:  items,
		Total: entity.Total,
		Page:  page,
		Size:  size,
	}
}

// RoleItem 角色详情项
type RoleItem struct {
	ID              int64  `json:"id"`               // 角色ID
	Name            string `json:"name"`             // 角色名称
	Code            string `json:"code"`             // 角色编码
	Description     string `json:"description"`      // 角色描述
	SortOrder       int    `json:"sort_order"`       // 排序
	Status          int    `json:"status"`           // 状态：0-禁用 1-启用
	IsSystem        int    `json:"is_system"`        // 是否系统角色：0-否 1-是
	PermissionCount int    `json:"permission_count"` // 权限数量
	UserCount       int    `json:"user_count"`       // 用户数量
	CreatedAt       string `json:"created_at"`       // 创建时间
	UpdatedAt       string `json:"updated_at"`       // 更新时间
}

// FromDomainRole 从领域实体转换为单个角色项
func FromDomainRole(entity domain.RoleEntity) RoleItem {
	return RoleItem{
		ID:          entity.ID,
		Name:        entity.Name,
		Code:        entity.Code,
		Description: entity.Description,
		SortOrder:   0, // 领域层没有此字段
		Status:      entity.Status,
		IsSystem:    entity.IsSystem,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}

// CreateRoleReq 创建角色请求
type CreateRoleReq struct {
	Name        string `json:"name" binding:"required,min=1,max=50"` // 角色名称
	Code        string `json:"code" binding:"required,min=1,max=50"` // 角色编码
	Description string `json:"description" binding:"max=500"`        // 角色描述
	SortOrder   int    `json:"sort_order"`                           // 排序
	Status      int    `json:"status" binding:"oneof=0 1"`           // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域实体
func (req CreateRoleReq) ToDomain() domain.RoleEntity {
	return domain.RoleEntity{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Status:      req.Status,
	}
}

// UpdateRoleReq 更新角色请求
type UpdateRoleReq struct {
	Name        string `json:"name" binding:"required,min=1,max=50"` // 角色名称
	Code        string `json:"code" binding:"required,min=1,max=50"` // 角色编码
	Description string `json:"description" binding:"max=500"`        // 角色描述
	SortOrder   int    `json:"sort_order"`                           // 排序
	Status      int    `json:"status" binding:"oneof=0 1"`           // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域实体
func (req UpdateRoleReq) ToDomain() domain.RoleEntity {
	return domain.RoleEntity{
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Status:      req.Status,
	}
}

// GetPermissionsReq 获取权限列表请求
type GetPermissionsReq struct {
	Page      int    `json:"page" form:"page"`             // 页码
	PageSize  int    `json:"page_size" form:"page_size"`   // 每页数量
	Name      string `json:"name" form:"name"`             // 权限名称
	Code      string `json:"code" form:"code"`             // 权限编码
	Module    string `json:"module" form:"module"`         // 模块
	Type      string `json:"type" form:"type"`             // 类型：function-功能权限 data-数据权限
	DataScope string `json:"data_scope" form:"data_scope"` // 数据范围：all-全部 dept-部门 self-个人
	Status    int    `json:"status" form:"status"`         // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域参数
func (req GetPermissionsReq) ToDomain() domain.PermissionListParam {
	return domain.PermissionListParam{
		Page:      req.Page,
		PageSize:  req.PageSize,
		Name:      req.Name,
		Code:      req.Code,
		Module:    req.Module,
		Type:      req.Type,
		DataScope: req.DataScope,
		Status:    req.Status,
	}
}

// GetPermissionsResp 获取权限列表响应
type GetPermissionsResp struct {
	List  []PermissionItem `json:"list"`  // 权限列表
	Total int64            `json:"total"` // 总记录数
	Page  int              `json:"page"`  // 当前页码
	Size  int              `json:"size"`  // 每页数量
}

// FromDomainPermissionList 从领域实体转换为权限列表响应
func FromDomainPermissionList(entity domain.PermissionListEntity, page, size int) GetPermissionsResp {
	items := make([]PermissionItem, 0, len(entity.List))
	for _, e := range entity.List {
		items = append(items, PermissionItem{
			ID:          e.ID,
			Name:        e.Name,
			Code:        e.Code,
			Module:      e.Module,
			Type:        e.Type,
			Description: e.Description,
			DataScope:   e.DataScope,
			Status:      e.Status,
			CreatedAt:   e.CreatedAt,
			UpdatedAt:   e.UpdatedAt,
		})
	}
	return GetPermissionsResp{
		List:  items,
		Total: entity.Total,
		Page:  page,
		Size:  size,
	}
}

// PermissionItem 权限详情项
type PermissionItem struct {
	ID          int64  `json:"id"`          // 权限ID
	Name        string `json:"name"`        // 权限名称
	Code        string `json:"code"`        // 权限编码
	Module      string `json:"module"`      // 模块
	Type        string `json:"type"`        // 类型：function-功能权限 data-数据权限
	Description string `json:"description"` // 权限描述
	DataScope   string `json:"data_scope"`  // 数据范围：all-全部 dept-部门 self-个人
	Status      int    `json:"status"`      // 状态：0-禁用 1-启用
	CreatedAt   string `json:"created_at"`  // 创建时间
	UpdatedAt   string `json:"updated_at"`  // 更新时间
}

// FromDomainPermission 从领域实体转换单个权限
func FromDomainPermission(entity domain.PermissionEntity) PermissionItem {
	return PermissionItem{
		ID:          entity.ID,
		Name:        entity.Name,
		Code:        entity.Code,
		Module:      entity.Module,
		Type:        entity.Type,
		Description: entity.Description,
		DataScope:   entity.DataScope,
		Status:      entity.Status,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}

// CreatePermissionReq 创建权限请求
type CreatePermissionReq struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`              // 权限名称
	Code        string `json:"code" binding:"required,min=1,max=100"`              // 权限编码
	Module      string `json:"module" binding:"required,min=1,max=50"`             // 模块
	Type        string `json:"type" binding:"required,oneof=function data"`        // 类型：function-功能权限 data-数据权限
	Description string `json:"description" binding:"max=500"`                      // 权限描述
	DataScope   string `json:"data_scope" binding:"omitempty,oneof=all dept self"` // 数据范围：all-全部 dept-部门 self-个人
	Status      int    `json:"status" binding:"oneof=0 1"`                         // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域实体
func (req CreatePermissionReq) ToDomain() domain.PermissionEntity {
	return domain.PermissionEntity{
		Name:        req.Name,
		Code:        req.Code,
		Module:      req.Module,
		Type:        req.Type,
		Description: req.Description,
		DataScope:   req.DataScope,
		Status:      req.Status,
	}
}

// UpdatePermissionReq 更新权限请求
type UpdatePermissionReq struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`              // 权限名称
	Code        string `json:"code" binding:"required,min=1,max=100"`              // 权限编码
	Module      string `json:"module" binding:"required,min=1,max=50"`             // 模块
	Type        string `json:"type" binding:"required,oneof=function data"`        // 类型：function-功能权限 data-数据权限
	Description string `json:"description" binding:"max=500"`                      // 权限描述
	DataScope   string `json:"data_scope" binding:"omitempty,oneof=all dept self"` // 数据范围：all-全部 dept-部门 self-个人
	Status      int    `json:"status" binding:"oneof=0 1"`                         // 状态：0-禁用 1-启用
}

// ToDomain 转换为领域实体
func (req UpdatePermissionReq) ToDomain() domain.PermissionEntity {
	return domain.PermissionEntity{
		Name:        req.Name,
		Code:        req.Code,
		Module:      req.Module,
		Type:        req.Type,
		Description: req.Description,
		DataScope:   req.DataScope,
		Status:      req.Status,
	}
}

// GetRolePermissionsReq 获取角色权限请求
type GetRolePermissionsReq struct {
	RoleID int64 `json:"role_id" form:"role_id" binding:"required"` // 角色ID
}

// GetRolePermissionsResp 获取角色权限响应
type GetRolePermissionsResp struct {
	RoleID      int64                  `json:"role_id"`     // 角色ID
	RoleName    string                 `json:"role_name"`   // 角色名称
	Permissions []PermissionItem       `json:"permissions"` // 权限列表
	Modules     []PermissionModuleItem `json:"modules"`     // 模块列表
}

// FromDomainRolePermissions 从领域实体转换角色权限信息
func FromDomainRolePermissions(roleID int64, roleName string, permissions []domain.PermissionEntity, modules map[string][]domain.PermissionEntity) GetRolePermissionsResp {
	permItems := make([]PermissionItem, 0, len(permissions))
	for _, p := range permissions {
		permItems = append(permItems, FromDomainPermission(p))
	}

	moduleItems := make([]PermissionModuleItem, 0, len(modules))
	for module, perms := range modules {
		permsByModule := make([]PermissionItem, 0, len(perms))
		for _, p := range perms {
			permsByModule = append(permsByModule, FromDomainPermission(p))
		}
		moduleItems = append(moduleItems, PermissionModuleItem{
			Module:      module,
			ModuleName:  getModuleName(module),
			Permissions: permsByModule,
		})
	}

	return GetRolePermissionsResp{
		RoleID:      roleID,
		RoleName:    roleName,
		Permissions: permItems,
		Modules:     moduleItems,
	}
}

// getModuleName 获取模块名称
func getModuleName(module string) string {
	moduleNames := map[string]string{
		"system":    "系统管理",
		"dashboard": "数据概览",
		"user":      "用户管理",
		"role":      "角色管理",
		"media":     "媒体管理",
		"creative":  "创意管理",
		"plan":      "计划管理",
		"report":    "报表管理",
		// 可以根据需要添加更多模块名称映射
	}

	if name, ok := moduleNames[module]; ok {
		return name
	}
	return module
}

// PermissionModuleItem 权限模块项
type PermissionModuleItem struct {
	Module      string           `json:"module"`      // 模块编码
	ModuleName  string           `json:"module_name"` // 模块名称
	Permissions []PermissionItem `json:"permissions"` // 权限列表
}

// AssignPermissionsReq 分配权限请求
type AssignPermissionsReq struct {
	RoleID        int64   `json:"role_id" binding:"required"`        // 角色ID
	PermissionIDs []int64 `json:"permission_ids" binding:"required"` // 权限ID列表
}

// ToDomain 转换为领域实体
func (req AssignPermissionsReq) ToDomain() domain.AssignPermissionEntity {
	return domain.AssignPermissionEntity{
		RoleID:        req.RoleID,
		PermissionIDs: req.PermissionIDs,
	}
}

// RevokePermissionsReq 撤销权限请求
type RevokePermissionsReq struct {
	RoleID        int64   `json:"role_id" binding:"required"`        // 角色ID
	PermissionIDs []int64 `json:"permission_ids" binding:"required"` // 权限ID列表
}

// ToDomain 转换为领域实体
func (req RevokePermissionsReq) ToDomain() domain.RevokePermissionEntity {
	return domain.RevokePermissionEntity{
		RoleID:        req.RoleID,
		PermissionIDs: req.PermissionIDs,
	}
}

// GetRoleOptionsResp 获取角色选项响应
type GetRoleOptionsResp struct {
	Roles []RoleOptionItem `json:"roles"` // 角色选项列表
}

// FromDomainRoleOptions 从领域实体转换为角色选项响应
func FromDomainRoleOptions(entity domain.RoleOptionsEntity) GetRoleOptionsResp {
	items := make([]RoleOptionItem, 0, len(entity.Options))
	for _, e := range entity.Options {
		items = append(items, RoleOptionItem{
			ID:   e.ID,
			Name: e.Name,
		})
	}
	return GetRoleOptionsResp{
		Roles: items,
	}
}

// RoleOptionItem 角色选项项
type RoleOptionItem struct {
	ID   int64  `json:"id"`   // 角色ID
	Name string `json:"name"` // 角色名称
}

// GetPermissionModulesResp 获取权限模块响应
type GetPermissionModulesResp struct {
	Modules []string `json:"modules"` // 模块列表
}

// FromDomainPermissionModules 从领域实体转换为权限模块响应
func FromDomainPermissionModules(entity domain.PermissionModulesEntity) GetPermissionModulesResp {
	modules := make([]string, 0, len(entity.Modules))
	for _, m := range entity.Modules {
		modules = append(modules, m.Name)
	}
	return GetPermissionModulesResp{
		Modules: modules,
	}
}

// GetPermissionLogsReq 获取权限日志请求
type GetPermissionLogsReq struct {
	Page         int    `json:"page" form:"page"`                   // 页码
	PageSize     int    `json:"page_size" form:"page_size"`         // 每页数量
	RoleID       int64  `json:"role_id" form:"role_id"`             // 角色ID
	OperatorID   int64  `json:"operator_id" form:"operator_id"`     // 操作人ID
	Action       string `json:"action" form:"action"`               // 操作类型：assign-分配 revoke-撤销
	PermissionID int64  `json:"permission_id" form:"permission_id"` // 权限ID
}

// ToDomain 转换为领域参数
func (req GetPermissionLogsReq) ToDomain() domain.PermissionLogListParam {
	return domain.PermissionLogListParam{
		Page:         req.Page,
		PageSize:     req.PageSize,
		RoleID:       req.RoleID,
		OperatorID:   req.OperatorID,
		Action:       req.Action,
		PermissionID: req.PermissionID,
	}
}

// GetPermissionLogsResp 获取权限日志响应
type GetPermissionLogsResp struct {
	List  []PermissionLogItem `json:"list"`  // 日志列表
	Total int64               `json:"total"` // 总记录数
	Page  int                 `json:"page"`  // 当前页码
	Size  int                 `json:"size"`  // 每页数量
}

// FromDomainPermissionLogList 从领域实体转换为权限日志列表响应
func FromDomainPermissionLogList(entity domain.PermissionLogListEntity, page, size int) GetPermissionLogsResp {
	items := make([]PermissionLogItem, 0, len(entity.List))
	for _, e := range entity.List {
		items = append(items, PermissionLogItem{
			ID:             e.ID,
			OperatorID:     e.OperatorID,
			OperatorName:   e.OperatorName,
			RoleID:         e.RoleID,
			RoleName:       e.RoleName,
			Action:         e.Action,
			ActionName:     getActionName(e.Action),
			PermissionID:   e.PermissionID,
			PermissionName: e.Permission,
			PermissionCode: "", // 领域实体中缺少此字段
			CreatedAt:      e.CreatedAt,
		})
	}
	return GetPermissionLogsResp{
		List:  items,
		Total: entity.Total,
		Page:  page,
		Size:  size,
	}
}

// getActionName 获取操作名称
func getActionName(action string) string {
	switch action {
	case "assign":
		return "分配权限"
	case "revoke":
		return "撤销权限"
	default:
		return action
	}
}

// PermissionLogItem 权限日志详情项
type PermissionLogItem struct {
	ID             int64  `json:"id"`              // 日志ID
	OperatorID     int64  `json:"operator_id"`     // 操作人ID
	OperatorName   string `json:"operator_name"`   // 操作人名称
	RoleID         int64  `json:"role_id"`         // 角色ID
	RoleName       string `json:"role_name"`       // 角色名称
	Action         string `json:"action"`          // 操作类型：assign-分配 revoke-撤销
	ActionName     string `json:"action_name"`     // 操作名称
	PermissionID   int64  `json:"permission_id"`   // 权限ID
	PermissionName string `json:"permission_name"` // 权限名称
	PermissionCode string `json:"permission_code"` // 权限编码
	CreatedAt      string `json:"created_at"`      // 创建时间
}

// CheckUserPermissionReq 检查用户权限请求
type CheckUserPermissionReq struct {
	UserID     int64  `json:"user_id" binding:"required"`    // 用户ID
	Permission string `json:"permission" binding:"required"` // 权限编码
}

// CheckUserPermissionResp 检查用户权限响应
type CheckUserPermissionResp struct {
	HasPermission bool `json:"has_permission"` // 是否有权限
}

// GetUserPermissionsReq 获取用户权限请求
type GetUserPermissionsReq struct {
	UserID int64 `json:"user_id" form:"user_id" binding:"required"` // 用户ID
}

// GetUserPermissionsResp 获取用户权限响应
type GetUserPermissionsResp struct {
	UserID      int64                  `json:"user_id"`     // 用户ID
	UserName    string                 `json:"user_name"`   // 用户名
	Roles       []RoleOptionItem       `json:"roles"`       // 角色列表
	Permissions []PermissionItem       `json:"permissions"` // 权限列表
	Modules     []PermissionModuleItem `json:"modules"`     // 模块列表
}

// FromDomainUserPermissions 从领域实体转换为用户权限响应
func FromDomainUserPermissions(userID int64, userName string, roles []domain.RoleEntity, permissions []domain.PermissionEntity, modules map[string][]domain.PermissionEntity) GetUserPermissionsResp {
	roleItems := make([]RoleOptionItem, 0, len(roles))
	for _, r := range roles {
		roleItems = append(roleItems, RoleOptionItem{
			ID:   r.ID,
			Name: r.Name,
		})
	}

	permItems := make([]PermissionItem, 0, len(permissions))
	for _, p := range permissions {
		permItems = append(permItems, FromDomainPermission(p))
	}

	moduleItems := make([]PermissionModuleItem, 0, len(modules))
	for module, perms := range modules {
		permsByModule := make([]PermissionItem, 0, len(perms))
		for _, p := range perms {
			permsByModule = append(permsByModule, FromDomainPermission(p))
		}
		moduleItems = append(moduleItems, PermissionModuleItem{
			Module:      module,
			ModuleName:  getModuleName(module),
			Permissions: permsByModule,
		})
	}

	return GetUserPermissionsResp{
		UserID:      userID,
		UserName:    userName,
		Roles:       roleItems,
		Permissions: permItems,
		Modules:     moduleItems,
	}
}
