package domain

import (
	"errors"
	"gin-backend/internal/model"
	"time"
)

// XHSCreativeReportListParam 小红书创意报表列表查询参数
type XHSCreativeReportListParam struct {
	// 筛选条件
	AccountID    *int64     // 账号ID
	AccountName  string     // 账号名称
	CampaignName string     // 计划名称
	UnitName     string     // 单元名称
	Title        string     // 标题
	Pwd          string     // 口令
	StartDate    *time.Time // 开始日期
	EndDate      *time.Time // 结束日期
	Placement    *int8      // 广告类型
	
	// 分页参数
	Page     int // 页码，从1开始
	PageSize int // 每页数量
}

// Validate 验证参数
func (p XHSCreativeReportListParam) Validate() error {
	if p.Page < 1 {
		return errors.New("页码必须大于0")
	}
	if p.PageSize < 1 || p.PageSize > 1000 {
		return errors.New("每页数量必须在1-1000之间")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// XHSCreativeReportListResult 小红书创意报表列表查询结果
type XHSCreativeReportListResult struct {
	List  []model.XHSCreativeReports // 报表列表
	Total int64                      // 总数量
	Page  int                        // 当前页码
	Size  int                        // 每页数量
}

// XHSCreativeReportExportParam 小红书创意报表导出参数
type XHSCreativeReportExportParam struct {
	// 筛选条件
	AccountID    *int64     // 账号ID
	AccountName  string     // 账号名称
	CampaignName string     // 计划名称
	UnitName     string     // 单元名称
	Title        string     // 标题
	Pwd          string     // 口令
	StartDate    *time.Time // 开始日期
	EndDate      *time.Time // 结束日期
	Placement    *int8      // 广告类型
	
	// 导出参数
	Format string // 导出格式：xlsx 或 csv
}

// Validate 验证参数
func (p XHSCreativeReportExportParam) Validate() error {
	if p.Format != "xlsx" && p.Format != "csv" {
		return errors.New("导出格式只支持xlsx或csv")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	// 限制导出数据的时间范围，避免数据量过大
	if p.StartDate != nil && p.EndDate != nil {
		duration := p.EndDate.Sub(*p.StartDate)
		if duration > 90*24*time.Hour {
			return errors.New("导出数据的时间范围不能超过90天")
		}
	}
	return nil
}

// XHSCreativeReportExportResult 小红书创意报表导出结果
type XHSCreativeReportExportResult struct {
	FileName string // 文件名
	FileURL  string // 文件下载URL
}

// XHSCreativeReportStatsParam 小红书创意报表统计参数
type XHSCreativeReportStatsParam struct {
	// 筛选条件
	AccountID    *int64     // 账号ID
	AccountName  string     // 账号名称
	CampaignName string     // 计划名称
	UnitName     string     // 单元名称
	Title        string     // 标题
	Pwd          string     // 口令
	StartDate    *time.Time // 开始日期
	EndDate      *time.Time // 结束日期
	Placement    *int8      // 广告类型
}

// Validate 验证参数
func (p XHSCreativeReportStatsParam) Validate() error {
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// XHSCreativeReportStatsResult 小红书创意报表统计结果
type XHSCreativeReportStatsResult struct {
	TotalReports   int64     // 总报表数量
	TotalFee       float64   // 总消费
	TotalClick     int64     // 总点击量
	TotalROI       float64   // 总ROI
	AvgCTR         float64   // 平均点击率
	AvgCPI         float64   // 平均互动成本
	LastUpdateTime time.Time // 最后更新时间
}

// ValidateXHSCreativeReportListParam 验证小红书创意报表列表查询参数
func ValidateXHSCreativeReportListParam(param XHSCreativeReportListParam) error {
	return param.Validate()
}

// ValidateXHSCreativeReportExportParam 验证小红书创意报表导出参数
func ValidateXHSCreativeReportExportParam(param XHSCreativeReportExportParam) error {
	return param.Validate()
}

// ValidateXHSCreativeReportStatsParam 验证小红书创意报表统计参数
func ValidateXHSCreativeReportStatsParam(param XHSCreativeReportStatsParam) error {
	return param.Validate()
}

// GetPlacementName 获取广告类型名称
func GetPlacementName(placement int8) string {
	switch placement {
	case 1:
		return "信息流"
	case 2:
		return "搜索"
	case 3:
		return "开屏"
	case 4:
		return "全站智投"
	case 7:
		return "视频内流"
	default:
		return "未知"
	}
}

// GetOptimizeTargetName 获取优化目标名称
func GetOptimizeTargetName(target int8) string {
	switch target {
	case 1:
		return "点击量"
	case 2:
		return "转化量"
	case 3:
		return "曝光量"
	case 4:
		return "触达量"
	default:
		return "未知"
	}
}

// GetPromotionTargetName 获取推广标的名称
func GetPromotionTargetName(target int8) string {
	switch target {
	case 1:
		return "笔记"
	case 2:
		return "直播间"
	case 3:
		return "商品"
	case 4:
		return "落地页"
	default:
		return "未知"
	}
}

// GetBiddingStrategyName 获取出价方式名称
func GetBiddingStrategyName(strategy int8) string {
	switch strategy {
	case 1:
		return "点击出价"
	case 2:
		return "曝光出价"
	case 3:
		return "转化出价"
	default:
		return "未知"
	}
}

// GetBuildTypeName 获取搭建类型名称
func GetBuildTypeName(buildType int8) string {
	switch buildType {
	case 1:
		return "手动搭建"
	case 2:
		return "自动搭建"
	default:
		return "未知"
	}
}

// GetMarketingTargetName 获取营销诉求名称
func GetMarketingTargetName(target int8) string {
	switch target {
	case 1:
		return "品牌推广"
	case 4:
		return "产品种草"
	case 9:
		return "客资收集"
	case 16:
		return "应用换端"
	default:
		return "未知"
	}
}
