package controller

import (
	"net/http"
	"strconv"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// DepartmentController 部门控制器
type DepartmentController struct {
	departmentService *service.DepartmentService
}

// NewDepartmentController 创建部门控制器实例
func NewDepartmentController() *DepartmentController {
	return &DepartmentController{
		departmentService: service.NewDepartmentService(),
	}
}

// GetDepartments 获取部门列表
// @Summary 获取部门列表
// @Description 获取部门列表，支持分页和筛选
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param keyword query string false "关键词搜索（部门名称/编码）"
// @Param status query int false "状态筛选（0:禁用 1:启用）"
// @Param parent_id query int false "上级部门ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} response.Response{data=vo.GetDepartmentsResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/departments [get]
func (c *DepartmentController) GetDepartments(ctx *gin.Context) {
	var req vo.GetDepartmentsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 转换为domain参数
	param := domain.GetDepartmentsParam{
		Keyword:  req.Keyword,
		Status:   req.Status,
		ParentID: req.ParentID,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 调用service
	result, err := c.departmentService.GetDepartments(param)
	if err != nil {
		response.ServerError(ctx, "获取部门列表失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := vo.GetDepartmentsResp{
		Total: result.Total,
		Page:  result.Page,
		Size:  result.Size,
		List:  make([]vo.DepartmentDetailResp, len(result.List)),
	}

	// 转换实体列表
	for i, dept := range result.List {
		voResult.List[i] = convertToVoDepartmentDetail(dept)
	}

	response.Success(ctx, voResult)
}

// GetDepartmentTree 获取部门树形结构
// @Summary 获取部门树形结构
// @Description 获取部门树形结构，用于树形展示
// @Tags 部门管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.DepartmentTreeNodeResp}
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/tree [get]
func (c *DepartmentController) GetDepartmentTree(ctx *gin.Context) {
	result, err := c.departmentService.GetDepartmentTree()
	if err != nil {
		response.ServerError(ctx, "获取部门树失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := make([]vo.DepartmentTreeNodeResp, len(result))
	for i, node := range result {
		voResult[i] = convertToVoDepartmentTreeNode(node)
	}

	response.Success(ctx, voResult)
}

// GetDepartmentOptions 获取部门选项
// @Summary 获取部门选项
// @Description 获取部门选项，用于下拉选择
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param exclude_id query int false "排除的部门ID"
// @Success 200 {object} response.Response{data=vo.GetDepartmentOptionsResp}
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/options [get]
func (c *DepartmentController) GetDepartmentOptions(ctx *gin.Context) {
	var req vo.GetDepartmentOptionsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 转换为domain参数
	param := domain.GetDepartmentOptionsParam{
		ExcludeID: req.ExcludeID,
	}

	// 调用service
	result, err := c.departmentService.GetDepartmentOptions(param)
	if err != nil {
		response.ServerError(ctx, "获取部门选项失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := vo.GetDepartmentOptionsResp{
		Options: make([]vo.DepartmentOptionResp, len(result.Options)),
	}

	// 转换选项列表
	for i, opt := range result.Options {
		voResult.Options[i] = convertToVoDepartmentOption(opt)
	}

	response.Success(ctx, voResult)
}

// GetDepartmentByID 根据ID获取部门详情
// @Summary 根据ID获取部门详情
// @Description 根据ID获取部门详情
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param id path int true "部门ID"
// @Success 200 {object} response.Response{data=vo.DepartmentDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/{id} [get]
func (c *DepartmentController) GetDepartmentByID(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的部门ID")
		return
	}

	result, err := c.departmentService.GetDepartmentByID(int64(id))
	if err != nil {
		if err.Error() == "部门不存在" {
			response.NotFound(ctx, err.Error())
			return
		}
		response.ServerError(ctx, "获取部门详情失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := convertToVoDepartmentDetail(result)

	response.Success(ctx, voResult)
}

// CreateDepartment 创建部门
// @Summary 创建部门
// @Description 创建新的部门
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param request body vo.CreateDepartmentReq true "创建部门请求"
// @Success 201 {object} response.Response{data=vo.DepartmentDetailResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/departments [post]
func (c *DepartmentController) CreateDepartment(ctx *gin.Context) {
	var req vo.CreateDepartmentReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := domain.CreateDepartmentEntity{
		Name:        req.Name,
		Code:        req.Code,
		ParentID:    req.ParentID,
		ManagerID:   req.ManagerID,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		Status:      req.Status,
	}

	result, err := c.departmentService.CreateDepartment(entity)
	if err != nil {
		response.ServerError(ctx, "创建部门失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := convertToVoDepartmentDetail(result)

	ctx.JSON(http.StatusCreated, response.Response{
		Code:    0,
		Message: "创建成功",
		Data:    voResult,
	})
}

// UpdateDepartment 更新部门
// @Summary 更新部门
// @Description 更新部门信息
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param id path int true "部门ID"
// @Param request body vo.UpdateDepartmentReq true "更新部门请求"
// @Success 200 {object} response.Response{data=vo.DepartmentDetailResp}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/{id} [put]
func (c *DepartmentController) UpdateDepartment(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的部门ID")
		return
	}

	var req vo.UpdateDepartmentReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 转换为domain实体
	entity := domain.UpdateDepartmentEntity{
		Name:        req.Name,
		Code:        req.Code,
		ParentID:    req.ParentID,
		ManagerID:   req.ManagerID,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		Status:      req.Status,
	}

	result, err := c.departmentService.UpdateDepartment(int64(id), entity)
	if err != nil {
		if err.Error() == "部门不存在" {
			response.NotFound(ctx, err.Error())
			return
		}
		response.ServerError(ctx, "更新部门失败: "+err.Error())
		return
	}

	// 转换为vo响应
	voResult := convertToVoDepartmentDetail(result)

	response.Success(ctx, voResult)
}

// DeleteDepartment 删除部门
// @Summary 删除部门
// @Description 删除部门（软删除）
// @Tags 部门管理
// @Accept json
// @Produce json
// @Param id path int true "部门ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/{id} [delete]
func (c *DepartmentController) DeleteDepartment(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的部门ID")
		return
	}

	err = c.departmentService.DeleteDepartment(id)
	if err != nil {
		if err.Error() == "部门不存在" {
			response.NotFound(ctx, err.Error())
			return
		}
		response.ServerError(ctx, "删除部门失败: "+err.Error())
		return
	}

	response.Success(ctx, "删除成功")
}

// GetDepartmentStatistics 获取部门统计信息
// @Summary 获取部门统计信息
// @Description 获取部门统计信息
// @Tags 部门管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]int64}
// @Failure 500 {object} response.Response
// @Router /api/v1/departments/statistics [get]
func (c *DepartmentController) GetDepartmentStatistics(ctx *gin.Context) {
	result, err := c.departmentService.GetDepartmentStatistics()
	if err != nil {
		response.ServerError(ctx, "获取部门统计信息失败: "+err.Error())
		return
	}

	response.Success(ctx, result)
}

// 转换函数 - domain到vo
// convertToVoDepartmentDetail 将领域模型实体转换为VO层的部门详情响应对象
func convertToVoDepartmentDetail(entity domain.DepartmentEntity) vo.DepartmentDetailResp {
	detail := vo.DepartmentDetailResp{
		ID:          entity.ID,
		Name:        entity.Name,
		Code:        entity.Code,
		ParentID:    entity.ParentID,
		ParentName:  entity.ParentName,
		ManagerID:   entity.ManagerID,
		ManagerName: entity.ManagerName,
		Description: entity.Description,
		SortOrder:   entity.SortOrder,
		Status:      entity.Status,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
		HasChildren: entity.HasChildren,
	}

	if len(entity.Children) > 0 {
		detail.Children = make([]vo.DepartmentDetailResp, len(entity.Children))
		for i, child := range entity.Children {
			detail.Children[i] = convertToVoDepartmentDetail(child)
		}
	}

	return detail
}

// convertToVoDepartmentTreeNode 将领域模型树节点转换为VO层的部门树节点响应对象
func convertToVoDepartmentTreeNode(node domain.DepartmentTreeNode) vo.DepartmentTreeNodeResp {
	treeNode := vo.DepartmentTreeNodeResp{
		ID:       node.ID,
		Name:     node.Name,
		Code:     node.Code,
		ParentID: node.ParentID,
	}

	if len(node.Children) > 0 {
		treeNode.Children = make([]vo.DepartmentTreeNodeResp, len(node.Children))
		for i, child := range node.Children {
			treeNode.Children[i] = convertToVoDepartmentTreeNode(child)
		}
	}

	return treeNode
}

// convertToVoDepartmentOption 将领域模型选项转换为VO层的部门选项响应对象
func convertToVoDepartmentOption(opt domain.DepartmentOption) vo.DepartmentOptionResp {
	option := vo.DepartmentOptionResp{
		ID:       opt.ID,
		Name:     opt.Name,
		Code:     opt.Code,
		ParentID: opt.ParentID,
	}

	if len(opt.Children) > 0 {
		option.Children = make([]vo.DepartmentOptionResp, len(opt.Children))
		for i, child := range opt.Children {
			option.Children[i] = convertToVoDepartmentOption(child)
		}
	}

	return option
}
