package service

import (
	"context"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/pkg/xiaohongshu"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// XHSCreativeReportService 小红书创意报表服务
type XHSCreativeReportService struct {
	db *gorm.DB
}

// NewXHSCreativeReportService 创建小红书创意报表服务
func NewXHSCreativeReportService() *XHSCreativeReportService {
	return &XHSCreativeReportService{
		db: global.DB,
	}
}

// SyncYesterdayReports 同步昨天的创意报表数据
func (s *XHSCreativeReportService) SyncYesterdayReports(ctx context.Context) error {
	yesterday := time.Now().AddDate(0, 0, -1)
	return s.SyncReportsByDate(ctx, yesterday)
}

// SyncReportsByDate 同步指定日期的创意报表数据
func (s *XHSCreativeReportService) SyncReportsByDate(ctx context.Context, date time.Time) error {
	zap.L().Info("开始同步小红书创意报表数据", zap.String("date", date.Format("2006-01-02")))

	// 获取所有已授权的小红书账号
	accounts, err := s.getAuthorizedXHSAccounts(ctx)
	if err != nil {
		return fmt.Errorf("获取已授权账号失败: %w", err)
	}

	if len(accounts) == 0 {
		zap.L().Info("没有找到已授权的小红书账号，跳过同步")
		return nil
	}

	// 创建小红书客户端
	xhsConfig := &xiaohongshu.Config{
		AppId:  config.AppConfig.Xiaohongshu.AppId,
		Secret: config.AppConfig.Xiaohongshu.Secret,
		IsProd: config.AppConfig.Xiaohongshu.IsProd,
	}
	client := xiaohongshu.NewClient(xhsConfig)

	// 为每个账号同步数据
	successCount := 0
	errorCount := 0

	for _, account := range accounts {
		err := s.syncAccountReports(ctx, client, account, date)
		if err != nil {
			zap.L().Error("同步账号报表数据失败",
				zap.Int64("account_id", account.ID),
				zap.String("account_name", account.AccountName),
				zap.Error(err))
			errorCount++
		} else {
			successCount++
		}
	}

	zap.L().Info("小红书创意报表数据同步完成",
		zap.String("date", date.Format("2006-01-02")),
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))

	return nil
}

// getAuthorizedXHSAccounts 获取已授权的小红书账号
func (s *XHSCreativeReportService) getAuthorizedXHSAccounts(ctx context.Context) ([]model.AdAccounts, error) {
	var accounts []model.AdAccounts

	err := s.db.WithContext(ctx).Where(
		"platform = ? AND authorization_status = ? AND usage_status = ? AND token != '' AND token_expire_time > ?",
		1, // 小红书平台
		2, // 已授权
		1, // 启用
		time.Now(),
	).Find(&accounts).Error

	if err != nil {
		return nil, err
	}

	return accounts, nil
}

// syncAccountReports 同步单个账号的报表数据
func (s *XHSCreativeReportService) syncAccountReports(ctx context.Context, client *xiaohongshu.Client, account model.AdAccounts, date time.Time) error {
	dateStr := date.Format("2006-01-02")

	// 检查是否已经同步过该日期的数据
	exists, err := s.checkReportExists(ctx, account.ID, date)
	if err != nil {
		return fmt.Errorf("检查报表数据是否存在失败: %w", err)
	}

	if exists {
		zap.L().Info("该账号该日期的数据已存在，跳过同步",
			zap.Int64("account_id", account.ID),
			zap.String("date", dateStr))
		return nil
	}

	// 构建报表请求
	advertiserID, err := strconv.ParseInt(account.PlatformAccountId, 10, 64)
	if err != nil {
		return fmt.Errorf("解析广告主ID失败: %w", err)
	}

	request := xiaohongshu.NewReportBuilder(advertiserID, dateStr, dateStr).
		WithTimeUnit(xiaohongshu.TimeUnitSummary).
		WithPagination(1, 500). // 每页500条
		Build()

	// 分页获取所有数据
	allReports := make([]*model.XHSCreativeReports, 0)
	pageNum := 1

	for {
		request.PageNum = pageNum

		// 调用小红书API
		resp, err := client.GetCreativeReport(account.Token, request)
		if err != nil {
			return fmt.Errorf("调用小红书API失败: %w", err)
		}

		if !resp.Success {
			return fmt.Errorf("小红书API返回错误: code=%d, msg=%s", resp.Code, resp.Msg)
		}

		// 转换数据格式
		reports, err := s.convertToReportModels(account, resp.Data.DataList, date)
		if err != nil {
			return fmt.Errorf("转换数据格式失败: %w", err)
		}

		allReports = append(allReports, reports...)

		// 检查是否还有更多数据
		if len(resp.Data.DataList) < request.PageSize {
			break
		}

		pageNum++
	}

	// 批量保存到数据库
	if len(allReports) > 0 {
		err = s.batchSaveReports(ctx, allReports)
		if err != nil {
			return fmt.Errorf("批量保存报表数据失败: %w", err)
		}

		zap.L().Info("成功同步账号报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("account_name", account.AccountName),
			zap.String("date", dateStr),
			zap.Int("count", len(allReports)))
	} else {
		zap.L().Info("该账号该日期无报表数据",
			zap.Int64("account_id", account.ID),
			zap.String("date", dateStr))
	}

	return nil
}

// checkReportExists 检查报表数据是否已存在
func (s *XHSCreativeReportService) checkReportExists(ctx context.Context, accountID int64, date time.Time) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&model.XHSCreativeReports{}).
		Where("account_id = ? AND DATE(time) = ?", accountID, date.Format("2006-01-02")).
		Count(&count).Error

	return count > 0, err
}

// convertToReportModels 转换API响应数据为数据库模型
func (s *XHSCreativeReportService) convertToReportModels(account model.AdAccounts, dataList []xiaohongshu.ReportData, date time.Time) ([]*model.XHSCreativeReports, error) {
	reports := make([]*model.XHSCreativeReports, 0, len(dataList))

	for _, data := range dataList {
		// 标题_口令词_内容姓名/投手姓名_日期_时间_智投_10:51_3
		creativityNames := strings.Split(data.CreativityName, "_")
		var (
			pwd           string
			title         string
			contentPeople string
			pitcherName   string
		)

		if len(creativityNames) == 8 {
			pwd = creativityNames[1]
			title = creativityNames[0]
			peoples := strings.Split(creativityNames[2], "/")
			if len(peoples) == 2 {
				contentPeople = peoples[0]
				pitcherName = peoples[1]
			}

		}
		report := &model.XHSCreativeReports{
			Id:            0,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
			AccountId:     account.ID,
			AccountName:   account.AccountName,
			Pwd:           pwd,
			Title:         title,
			ContentPeople: contentPeople,
			PitcherName:   pitcherName,
			// 业务字段
			CampaignID:                  data.CampaignID,
			CampaignName:                data.CampaignName,
			UnitID:                      data.UnitID,
			UnitName:                    data.UnitName,
			CreativityID:                data.CreativityID,
			CreativityName:              data.CreativityName,
			CreativityImage:             data.CreativityImage,
			NoteID:                      data.NoteID,
			Time:                        date,
			Placement:                   0,
			OptimizeTarget:              0,
			PromotionTarget:             0,
			BiddingStrategy:             0,
			BuildType:                   0,
			MarketingTarget:             0,
			PageID:                      data.PageID,
			ItemID:                      data.ItemID,
			LiveRedID:                   data.LiveRedID,
			CountryName:                 data.CountryName,
			Province:                    data.Province,
			City:                        data.City,
			NoteUserID:                  data.NoteUserID,
			Fee:                         0,
			Impression:                  0,
			Click:                       0,
			CTR:                         0,
			ACP:                         0,
			CPM:                         0,
			Like:                        0,
			Comment:                     0,
			Collect:                     0,
			Follow:                      0,
			Share:                       0,
			Interaction:                 0,
			CPI:                         0,
			ActionButtonClick:           0,
			ActionButtonCTR:             0,
			Screenshot:                  0,
			PicSave:                     0,
			ReservePV:                   0,
			ClkLiveEntryPV:              0,
			ClkLiveEntryPVCost:          0,
			ClkLiveAvgViewTime:          0,
			ClkLiveAllFollow:            0,
			ClkLive5sEntryPV:            0,
			ClkLive5sEntryUVCost:        0,
			ClkLiveComment:              0,
			SearchCmtClick:              0,
			SearchCmtClickCVR:           0,
			SearchCmtAfterRead:          0,
			SearchCmtAfterReadAvg:       0,
			IUserNum:                    0,
			TIUserNum:                   0,
			IUserPrice:                  0,
			TIUserPrice:                 0,
			GoodsVisit:                  0,
			GoodsVisitPrice:             0,
			SellerVisit:                 0,
			SellerVisitPrice:            0,
			ShoppingCartAdd:             0,
			AddCartPrice:                0,
			PresaleOrderNum7D:           0,
			PresaleOrderGMV7D:           0,
			GoodsOrder:                  0,
			GoodsOrderPrice:             0,
			RGMV:                        0,
			ROI:                         0,
			SuccessGoodsOrder:           0,
			ClickOrderCVR:               0,
			PurchaseOrderPrice7D:        0,
			PurchaseOrderGMV7D:          0,
			PurchaseOrderROI7D:          0,
			ClkLiveRoomOrderNum:         0,
			LiveAverageOrderCost:        0,
			ClkLiveRoomRGMV:             0,
			ClkLiveRoomROI:              0,
			Leads:                       0,
			LeadsCPL:                    0,
			LandingPageVisit:            0,
			LeadsButtonImpression:       0,
			ValidLeads:                  0,
			ValidLeadsCPL:               0,
			LeadsCVR:                    0,
			PhoneCallCnt:                0,
			PhoneCallSuccCnt:            0,
			WechatCopyCnt:               0,
			WechatCopySuccCnt:           0,
			IdentityCertiCnt:            0,
			CommodityBuyCnt:             0,
			MessageUser:                 0,
			Message:                     0,
			MessageConsult:              0,
			MessageFstReplyTimeAvg:      0,
			InitiativeMessage:           0,
			MessageConsultCPL:           0,
			InitiativeMessageCPL:        0,
			MsgLeadsNum:                 0,
			MsgLeadsCost:                0,
			ExternalGoodsVisit7:         0,
			ExternalGoodsVisitPrice7:    0,
			ExternalGoodsVisitRate7:     0,
			ExternalGoodsOrder7:         0,
			ExternalRGMV7:               0,
			ExternalGoodsOrderPrice7:    0,
			ExternalGoodsOrderRate7:     0,
			ExternalROI7:                0,
			ExternalGoodsOrder15:        0,
			ExternalRGMV15:              0,
			ExternalGoodsOrderPrice15:   0,
			ExternalGoodsOrderRate15:    0,
			ExternalROI15:               0,
			ExternalGoodsOrder30:        0,
			ExternalRGMV30:              0,
			ExternalGoodsOrderPrice30:   0,
			ExternalGoodsOrderRate30:    0,
			ExternalROI30:               0,
			ExternalLeads:               0,
			ExternalLeadsCPL:            0,
			WordAvgLocation:             0,
			WordImpressionRankFirst:     0,
			WordImpressionRateFirst:     0,
			WordImpressionRankThird:     0,
			WordImpressionRateThird:     0,
			WordClickRankFirst:          0,
			WordClickRateFirst:          0,
			WordClickRateThird:          0,
			WordClickRankThird:          0,
			WordImpressionRankAll:       0,
			WordImpressionRateAll:       0,
			WordClickRankAll:            0,
			WordClickRateAll:            0,
			InvokeAppOpenCnt:            0,
			InvokeAppOpenCost:           0,
			InvokeAppEnterStoreCnt:      0,
			InvokeAppEnterStoreCost:     0,
			InvokeAppEngagementCnt:      0,
			InvokeAppEngagementCost:     0,
			InvokeAppPaymentCnt:         0,
			InvokeAppPaymentCost:        0,
			SearchInvokeButtonClickCnt:  0,
			SearchInvokeButtonClickCost: 0,
			InvokeAppPaymentROI:         0,
			InvokeAppPaymentAmount:      0,
			InvokeAppPaymentUnitPrice:   0,
			JDActiveUserNum:             0,
			JDActiveUserNumCVR:          0,
			JDActiveUserNumCPL:          0,
			AppDownloadButtonClickCnt:   0,
			AppDownloadButtonClickCTR:   0,
			AppDownloadButtonClickCost:  0,
			AppActivateCnt:              0,
			AppActivateCost:             0,
			AppActivateCTR:              0,
			AppRegisterCnt:              0,
			AppRegisterCost:             0,
			AppRegisterCTR:              0,
			FirstAppPayCnt:              0,
			FirstAppPayCost:             0,
			FirstAppPayCTR:              0,
			CurrentAppPayCnt:            0,
			CurrentAppPayCost:           0,
			AppKeyActionCnt:             0,
			AppKeyActionCost:            0,
			AppKeyActionCTR:             0,
			AppPayCnt7D:                 0,
			AppPayCost7D:                0,
			AppPayAmount:                0,
			AppPayROI:                   0,
			AppActivateAmount1D:         0,
			AppActivateAmount3D:         0,
			AppActivateAmount7D:         0,
			AppActivateAmount1DROI:      0,
			AppActivateAmount3DROI:      0,
			AppActivateAmount7DROI:      0,
			Retention1DCnt:              0,
			Retention3DCnt:              0,
			Retention7DCnt:              0,
			AddWechatCount:              0,
			AddWechatCost:               0,
			AddWechatSucCount:           0,
			AddWechatSucCost:            0,
			WechatTalkCount:             0,
			WechatTalkCost:              0,
			ShopPoiClickNum:             0,
			ShopPoiPagePV:               0,
			ShopPoiPageVisitPrice:       0,
			ShopPoiPageNavigateClick:    0,
		}

		// 转换枚举字段
		if placement, err := s.parseIntField(data.Placement); err == nil {
			report.Placement = int8(placement)
		}
		if optimizeTarget, err := s.parseIntField(data.OptimizeTarget); err == nil {
			report.OptimizeTarget = int8(optimizeTarget)
		}
		if promotionTarget, err := s.parseIntField(data.PromotionTarget); err == nil {
			report.PromotionTarget = int8(promotionTarget)
		}
		if biddingStrategy, err := s.parseIntField(data.BiddingStrategy); err == nil {
			report.BiddingStrategy = int8(biddingStrategy)
		}
		if buildType, err := s.parseIntField(data.BuildType); err == nil {
			report.BuildType = int8(buildType)
		}
		if marketingTarget, err := s.parseIntField(data.MarketingTarget); err == nil {
			report.MarketingTarget = int8(marketingTarget)
		}

		// 转换基础指标
		report.Fee, _ = s.parseFloatField(data.Fee)
		report.Impression, _ = s.parseIntField(data.Impression)
		report.Click, _ = s.parseIntField(data.Click)
		report.CTR, _ = s.parseFloatField(data.CTR)
		report.ACP, _ = s.parseFloatField(data.ACP)
		report.CPM, _ = s.parseFloatField(data.CPM)

		// 转换互动指标
		report.Like, _ = s.parseIntField(data.Like)
		report.Comment, _ = s.parseIntField(data.Comment)
		report.Collect, _ = s.parseIntField(data.Collect)
		report.Follow, _ = s.parseIntField(data.Follow)
		report.Share, _ = s.parseIntField(data.Share)
		report.Interaction, _ = s.parseIntField(data.Interaction)
		report.CPI, _ = s.parseFloatField(data.CPI)
		report.ActionButtonClick, _ = s.parseIntField(data.ActionButtonClick)
		report.ActionButtonCTR, _ = s.parseFloatField(data.ActionButtonCTR)
		report.Screenshot, _ = s.parseIntField(data.Screenshot)
		report.PicSave, _ = s.parseIntField(data.PicSave)
		report.ReservePV, _ = s.parseIntField(data.ReservePV)

		// 转换电商指标
		report.GoodsVisit, _ = s.parseIntField(data.GoodsVisit)
		report.GoodsVisitPrice, _ = s.parseFloatField(data.GoodsVisitPrice)
		report.SellerVisit, _ = s.parseIntField(data.SellerVisit)
		report.SellerVisitPrice, _ = s.parseFloatField(data.SellerVisitPrice)
		report.ShoppingCartAdd, _ = s.parseIntField(data.ShoppingCartAdd)
		report.AddCartPrice, _ = s.parseFloatField(data.AddCartPrice)

		// 转换直播间互动指标
		report.ClkLiveEntryPV, _ = s.parseIntField(data.ClkLiveEntryPV)
		report.ClkLiveEntryPVCost, _ = s.parseFloatField(data.ClkLiveEntryPVCost)
		// 直播间停留时长转换为秒（原始数据可能是分钟）
		if avgViewTime, err := s.parseFloatField(data.ClkLiveAvgViewTime); err == nil {
			report.ClkLiveAvgViewTime = int32(avgViewTime * 60) // 转换为秒
		}
		report.ClkLiveAllFollow, _ = s.parseIntField(data.ClkLiveAllFollow)
		report.ClkLive5sEntryPV, _ = s.parseIntField(data.ClkLive5sEntryPV)
		report.ClkLive5sEntryUVCost, _ = s.parseFloatField(data.ClkLive5sEntryUVCost)
		report.ClkLiveComment, _ = s.parseIntField(data.ClkLiveComment)

		// 转换笔记种草指标
		report.SearchCmtClick, _ = s.parseIntField(data.SearchCmtClick)
		report.SearchCmtClickCVR, _ = s.parseFloatField(data.SearchCmtClickCVR)
		report.SearchCmtAfterRead, _ = s.parseIntField(data.SearchCmtAfterRead)
		report.SearchCmtAfterReadAvg, _ = s.parseFloatField(data.SearchCmtAfterReadAvg)
		report.IUserNum, _ = s.parseIntField(data.IUserNum)
		report.TIUserNum, _ = s.parseIntField(data.TIUserNum)
		report.IUserPrice, _ = s.parseFloatField(data.IUserPrice)
		report.TIUserPrice, _ = s.parseFloatField(data.TIUserPrice)

		// 转换7日转化指标
		report.PresaleOrderNum7D, _ = s.parseIntField(data.PresaleOrderNum7D)
		report.PresaleOrderGMV7D, _ = s.parseFloatField(data.PresaleOrderGMV7D)
		report.GoodsOrder, _ = s.parseIntField(data.GoodsOrder)
		report.GoodsOrderPrice, _ = s.parseFloatField(data.GoodsOrderPrice)
		report.RGMV, _ = s.parseFloatField(data.RGMV)
		report.ROI, _ = s.parseFloatField(data.ROI)
		report.SuccessGoodsOrder, _ = s.parseIntField(data.SuccessGoodsOrder)
		report.ClickOrderCVR, _ = s.parseFloatField(data.ClickOrderCVR)
		report.PurchaseOrderPrice7D, _ = s.parseFloatField(data.PurchaseOrderPrice7D)
		report.PurchaseOrderGMV7D, _ = s.parseFloatField(data.PurchaseOrderGMV7D)
		report.PurchaseOrderROI7D, _ = s.parseFloatField(data.PurchaseOrderROI7D)

		// 转换直播间转化指标
		report.ClkLiveRoomOrderNum, _ = s.parseIntField(data.ClkLiveRoomOrderNum)
		report.LiveAverageOrderCost, _ = s.parseFloatField(data.LiveAverageOrderCost)
		report.ClkLiveRoomRGMV, _ = s.parseFloatField(data.ClkLiveRoomRGMV)
		report.ClkLiveRoomROI, _ = s.parseFloatField(data.ClkLiveRoomROI)

		// 转换销售线索指标
		report.Leads, _ = s.parseIntField(data.Leads)
		report.LeadsCPL, _ = s.parseFloatField(data.LeadsCPL)
		report.LandingPageVisit, _ = s.parseIntField(data.LandingPageVisit)
		report.LeadsButtonImpression, _ = s.parseIntField(data.LeadsButtonImpression)
		report.ValidLeads, _ = s.parseIntField(data.ValidLeads)
		report.ValidLeadsCPL, _ = s.parseFloatField(data.ValidLeadsCPL)
		report.LeadsCVR, _ = s.parseFloatField(data.LeadsCVR)
		report.PhoneCallCnt, _ = s.parseIntField(data.PhoneCallCnt)
		report.PhoneCallSuccCnt, _ = s.parseIntField(data.PhoneCallSuccCnt)
		report.WechatCopyCnt, _ = s.parseIntField(data.WechatCopyCnt)
		report.WechatCopySuccCnt, _ = s.parseIntField(data.WechatCopySuccCnt)
		report.IdentityCertiCnt, _ = s.parseIntField(data.IdentityCertiCnt)
		report.CommodityBuyCnt, _ = s.parseIntField(data.CommodityBuyCnt)

		// 转换私信营销指标
		report.MessageUser, _ = s.parseIntField(data.MessageUser)
		report.Message, _ = s.parseIntField(data.Message)
		report.MessageConsult, _ = s.parseIntField(data.MessageConsult)
		// 平均响应时长转换为秒（原始数据可能是分钟）
		if replyTime, err := s.parseFloatField(data.MessageFstReplyTimeAvg); err == nil {
			report.MessageFstReplyTimeAvg = int32(replyTime * 60) // 转换为秒
		}
		report.InitiativeMessage, _ = s.parseIntField(data.InitiativeMessage)
		report.MessageConsultCPL, _ = s.parseFloatField(data.MessageConsultCPL)
		report.InitiativeMessageCPL, _ = s.parseFloatField(data.InitiativeMessageCPL)
		report.MsgLeadsNum, _ = s.parseIntField(data.MsgLeadsNum)
		report.MsgLeadsCost, _ = s.parseFloatField(data.MsgLeadsCost)

		// 转换行业商品销量指标
		report.ExternalGoodsVisit7, _ = s.parseIntField(data.ExternalGoodsVisit7)
		report.ExternalGoodsVisitPrice7, _ = s.parseFloatField(data.ExternalGoodsVisitPrice7)
		report.ExternalGoodsVisitRate7, _ = s.parseFloatField(data.ExternalGoodsVisitRate7)
		report.ExternalGoodsOrder7, _ = s.parseIntField(data.ExternalGoodsOrder7)
		report.ExternalRGMV7, _ = s.parseFloatField(data.ExternalRGMV7)
		report.ExternalGoodsOrderPrice7, _ = s.parseFloatField(data.ExternalGoodsOrderPrice7)
		report.ExternalGoodsOrderRate7, _ = s.parseFloatField(data.ExternalGoodsOrderRate7)
		report.ExternalROI7, _ = s.parseFloatField(data.ExternalROI7)
		report.ExternalGoodsOrder15, _ = s.parseIntField(data.ExternalGoodsOrder15)
		report.ExternalRGMV15, _ = s.parseFloatField(data.ExternalRGMV15)
		report.ExternalGoodsOrderPrice15, _ = s.parseFloatField(data.ExternalGoodsOrderPrice15)
		report.ExternalGoodsOrderRate15, _ = s.parseFloatField(data.ExternalGoodsOrderRate15)
		report.ExternalROI15, _ = s.parseFloatField(data.ExternalROI15)
		report.ExternalGoodsOrder30, _ = s.parseIntField(data.ExternalGoodsOrder30)
		report.ExternalRGMV30, _ = s.parseFloatField(data.ExternalRGMV30)
		report.ExternalGoodsOrderPrice30, _ = s.parseFloatField(data.ExternalGoodsOrderPrice30)
		report.ExternalGoodsOrderRate30, _ = s.parseFloatField(data.ExternalGoodsOrderRate30)
		report.ExternalROI30, _ = s.parseFloatField(data.ExternalROI30)

		// 转换外链专属指标
		report.ExternalLeads, _ = s.parseIntField(data.ExternalLeads)
		report.ExternalLeadsCPL, _ = s.parseFloatField(data.ExternalLeadsCPL)

		// 转换关键词指标
		report.WordAvgLocation, _ = s.parseFloatField(data.WordAvgLocation)
		report.WordImpressionRankFirst, _ = s.parseIntField(data.WordImpressionRankFirst)
		report.WordImpressionRateFirst, _ = s.parseFloatField(data.WordImpressionRateFirst)
		report.WordImpressionRankThird, _ = s.parseIntField(data.WordImpressionRankThird)
		report.WordImpressionRateThird, _ = s.parseFloatField(data.WordImpressionRateThird)
		report.WordClickRankFirst, _ = s.parseIntField(data.WordClickRankFirst)
		report.WordClickRateFirst, _ = s.parseFloatField(data.WordClickRateFirst)
		report.WordClickRateThird, _ = s.parseFloatField(data.WordClickRateThird)
		report.WordClickRankThird, _ = s.parseIntField(data.WordClickRankThird)
		report.WordImpressionRankAll, _ = s.parseIntField(data.WordImpressionRankAll)
		report.WordImpressionRateAll, _ = s.parseFloatField(data.WordImpressionRateAll)
		report.WordClickRankAll, _ = s.parseIntField(data.WordClickRankAll)
		report.WordClickRateAll, _ = s.parseFloatField(data.WordClickRateAll)

		// 转换APP内转化数据指标
		report.InvokeAppOpenCnt, _ = s.parseIntField(data.InvokeAppOpenCnt)
		report.InvokeAppOpenCost, _ = s.parseFloatField(data.InvokeAppOpenCost)
		report.InvokeAppEnterStoreCnt, _ = s.parseIntField(data.InvokeAppEnterStoreCnt)
		report.InvokeAppEnterStoreCost, _ = s.parseFloatField(data.InvokeAppEnterStoreCost)
		report.InvokeAppEngagementCnt, _ = s.parseIntField(data.InvokeAppEngagementCnt)
		report.InvokeAppEngagementCost, _ = s.parseFloatField(data.InvokeAppEngagementCost)
		report.InvokeAppPaymentCnt, _ = s.parseIntField(data.InvokeAppPaymentCnt)
		report.InvokeAppPaymentCost, _ = s.parseFloatField(data.InvokeAppPaymentCost)
		report.SearchInvokeButtonClickCnt, _ = s.parseIntField(data.SearchInvokeButtonClickCnt)
		report.SearchInvokeButtonClickCost, _ = s.parseFloatField(data.SearchInvokeButtonClickCost)
		report.InvokeAppPaymentROI, _ = s.parseFloatField(data.InvokeAppPaymentROI)
		report.InvokeAppPaymentAmount, _ = s.parseFloatField(data.InvokeAppPaymentAmount)
		report.InvokeAppPaymentUnitPrice, _ = s.parseFloatField(data.InvokeAppPaymentUnitPrice)

		// 转换京东站外店铺行为指标
		report.JDActiveUserNum, _ = s.parseIntField(data.JDActiveUserNum)
		report.JDActiveUserNumCVR, _ = s.parseFloatField(data.JDActiveUserNumCVR)
		report.JDActiveUserNumCPL, _ = s.parseFloatField(data.JDActiveUserNumCPL)

		// 转换应用下载指标
		report.AppDownloadButtonClickCnt, _ = s.parseIntField(data.AppDownloadButtonClickCnt)
		report.AppDownloadButtonClickCTR, _ = s.parseFloatField(data.AppDownloadButtonClickCTR)
		report.AppDownloadButtonClickCost, _ = s.parseFloatField(data.AppDownloadButtonClickCost)
		report.AppActivateCnt, _ = s.parseIntField(data.AppActivateCnt)
		report.AppActivateCost, _ = s.parseFloatField(data.AppActivateCost)
		report.AppActivateCTR, _ = s.parseFloatField(data.AppActivateCTR)
		report.AppRegisterCnt, _ = s.parseIntField(data.AppRegisterCnt)
		report.AppRegisterCost, _ = s.parseFloatField(data.AppRegisterCost)
		report.AppRegisterCTR, _ = s.parseFloatField(data.AppRegisterCTR)
		report.FirstAppPayCnt, _ = s.parseIntField(data.FirstAppPayCnt)
		report.FirstAppPayCost, _ = s.parseFloatField(data.FirstAppPayCost)
		report.FirstAppPayCTR, _ = s.parseFloatField(data.FirstAppPayCTR)
		report.CurrentAppPayCnt, _ = s.parseIntField(data.CurrentAppPayCnt)
		report.CurrentAppPayCost, _ = s.parseFloatField(data.CurrentAppPayCost)
		report.AppKeyActionCnt, _ = s.parseIntField(data.AppKeyActionCnt)
		report.AppKeyActionCost, _ = s.parseFloatField(data.AppKeyActionCost)
		report.AppKeyActionCTR, _ = s.parseFloatField(data.AppKeyActionCTR)
		report.AppPayCnt7D, _ = s.parseIntField(data.AppPayCnt7D)
		report.AppPayCost7D, _ = s.parseFloatField(data.AppPayCost7D)
		report.AppPayAmount, _ = s.parseFloatField(data.AppPayAmount)
		report.AppPayROI, _ = s.parseFloatField(data.AppPayROI)
		report.AppActivateAmount1D, _ = s.parseFloatField(data.AppActivateAmount1D)
		report.AppActivateAmount3D, _ = s.parseFloatField(data.AppActivateAmount3D)
		report.AppActivateAmount7D, _ = s.parseFloatField(data.AppActivateAmount7D)
		report.AppActivateAmount1DROI, _ = s.parseFloatField(data.AppActivateAmount1DROI)
		report.AppActivateAmount3DROI, _ = s.parseFloatField(data.AppActivateAmount3DROI)
		report.AppActivateAmount7DROI, _ = s.parseFloatField(data.AppActivateAmount7DROI)
		report.Retention1DCnt, _ = s.parseIntField(data.Retention1DCnt)
		report.Retention3DCnt, _ = s.parseIntField(data.Retention3DCnt)
		report.Retention7DCnt, _ = s.parseIntField(data.Retention7DCnt)

		// 转换企微营销指标
		report.AddWechatCount, _ = s.parseIntField(data.AddWechatCount)
		report.AddWechatCost, _ = s.parseFloatField(data.AddWechatCost)
		report.AddWechatSucCount, _ = s.parseIntField(data.AddWechatSucCount)
		report.AddWechatSucCost, _ = s.parseFloatField(data.AddWechatSucCost)
		report.WechatTalkCount, _ = s.parseIntField(data.WechatTalkCount)
		report.WechatTalkCost, _ = s.parseFloatField(data.WechatTalkCost)

		// 转换门店营销指标
		report.ShopPoiClickNum, _ = s.parseIntField(data.ShopPoiClickNum)
		report.ShopPoiPagePV, _ = s.parseIntField(data.ShopPoiPagePV)
		report.ShopPoiPageVisitPrice, _ = s.parseFloatField(data.ShopPoiPageVisitPrice)
		report.ShopPoiPageNavigateClick, _ = s.parseIntField(data.ShopPoiPageNavigateClick)

		reports = append(reports, report)
	}

	return reports, nil
}

// parseIntField 解析字符串为int64
func (s *XHSCreativeReportService) parseIntField(value string) (int64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseInt(strings.TrimSpace(value), 10, 64)
}

// parseFloatField 解析字符串为float64
func (s *XHSCreativeReportService) parseFloatField(value string) (float64, error) {
	if value == "" || value == "-" {
		return 0, nil
	}
	return strconv.ParseFloat(strings.TrimSpace(value), 64)
}

// batchSaveReports 批量保存报表数据
func (s *XHSCreativeReportService) batchSaveReports(ctx context.Context, reports []*model.XHSCreativeReports) error {
	// 使用事务批量插入
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 分批插入，每批1000条
		batchSize := 1000
		for i := 0; i < len(reports); i += batchSize {
			end := i + batchSize
			if end > len(reports) {
				end = len(reports)
			}

			batch := reports[i:end]
			if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
				return err
			}
		}
		return nil
	})
}
