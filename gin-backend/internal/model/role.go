package model

import (
	"time"
)

// Role 角色实体
type Role struct {
	ID          int64     `gorm:"primaryKey;autoIncrement;comment:角色ID"`
	Name        string    `gorm:"type:varchar(50);not null;comment:角色名称"`
	Code        string    `gorm:"type:varchar(50);not null;uniqueIndex;comment:角色编码"`
	Description string    `gorm:"type:text;comment:角色描述"`
	SortOrder   int       `gorm:"default:0;comment:排序"`
	Status      int       `gorm:"default:1;comment:状态：1启用 0禁用"`
	IsSystem    int       `gorm:"default:0;comment:是否系统角色：1是 0否"`
	CreatedAt   time.Time `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime;comment:更新时间"`

	// 关联字段
	Permissions []Permission `gorm:"many2many:role_permissions;"`
}

// Permission 权限实体
type Permission struct {
	ID          int64     `gorm:"primaryKey;autoIncrement;comment:权限ID"`
	Name        string    `gorm:"type:varchar(100);not null;comment:权限名称"`
	Code        string    `gorm:"type:varchar(100);not null;uniqueIndex;comment:权限编码"`
	Module      string    `gorm:"type:varchar(50);not null;comment:所属模块"`
	Type        string    `gorm:"type:varchar(20);default:'function';comment:权限类型：function-功能权限，data-数据权限"`
	Description string    `gorm:"type:text;comment:权限描述"`
	DataScope   string    `gorm:"type:varchar(20);comment:数据范围：all-全部，dept-部门，self-个人"`
	Status      int       `gorm:"default:1;comment:状态：1启用 0禁用"`
	CreatedAt   time.Time `gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime;comment:更新时间"`

	// 关联字段
	Roles []Role `gorm:"many2many:role_permissions;"`
}

// RolePermission 角色权限关联
type RolePermission struct {
	ID           int64     `gorm:"primaryKey;autoIncrement;comment:关联ID"`
	RoleID       int64     `gorm:"not null;comment:角色ID"`
	PermissionID int64     `gorm:"not null;comment:权限ID"`
	CreatedAt    time.Time `gorm:"autoCreateTime;comment:创建时间"`

	// 关联字段
	Role       Role       `gorm:"foreignKey:RoleID"`
	Permission Permission `gorm:"foreignKey:PermissionID"`
}

// PermissionLog 权限操作日志
type PermissionLog struct {
	ID           int64     `gorm:"primaryKey;autoIncrement;comment:日志ID"`
	OperatorID   int64     `gorm:"not null;comment:操作人ID"`
	RoleID       int64     `gorm:"not null;comment:角色ID"`
	Action       string    `gorm:"type:varchar(50);not null;comment:操作类型"`
	PermissionID int64     `gorm:"not null;comment:权限ID"`
	CreatedAt    time.Time `gorm:"autoCreateTime;comment:创建时间"`

	// 关联字段
	Operator   User       `gorm:"foreignKey:OperatorID"`
	Role       Role       `gorm:"foreignKey:RoleID"`
	Permission Permission `gorm:"foreignKey:PermissionID"`
}

// TableName 指定表名
func (Role) TableName() string {
	return "roles"
}

// TableName 指定表名
func (Permission) TableName() string {
	return "permissions"
}

// TableName 指定表名
func (RolePermission) TableName() string {
	return "role_permissions"
}

// TableName 指定表名
func (PermissionLog) TableName() string {
	return "permission_logs"
}
