package controller

import (
	"strconv"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// ModelController 模型管理控制器
type ModelController struct {
	modelService      *service.ModelService
	permissionService *service.PermissionService
	roleService       *service.RoleService
}

// NewModelController 创建模型管理控制器实例
func NewModelController() *ModelController {
	return &ModelController{
		modelService:      service.NewModelService(),
		permissionService: service.NewPermissionService(),
		roleService:       service.NewRoleService(),
	}
}

// Create 创建模型
// @Summary 创建模型
// @Description 创建新的模型
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param request body vo.ModelCreateReq true "创建模型请求"
// @Success 200 {object} response.Response{data=vo.ModelCreateResp} "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/create [post]
func (c *ModelController) Create(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:create"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.ModelCreateReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为domain层实体
	modelEntity := domain.ModelEntity{
		ModelName: req.ModelName,
		CreatorID: userEntity.ID,
	}

	// 创建模型
	result, err := c.modelService.CreateModel(modelEntity)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为VO层响应
	resp := vo.ModelCreateResp{
		ID: result.ID,
	}

	response.Success(ctx, resp)
}

// Update 更新模型
// @Summary 更新模型
// @Description 更新模型信息
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param request body vo.ModelUpdateReq true "更新模型请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/update [post]
func (c *ModelController) Update(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:edit"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.ModelUpdateReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为domain层实体
	modelEntity := domain.ModelEntity{
		ID:        req.ID,
		ModelName: req.ModelName,
		CreatorID: userEntity.ID,
	}

	// 更新模型
	err := c.modelService.UpdateModel(modelEntity)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Delete 删除模型
// @Summary 删除模型
// @Description 删除指定模型
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param request body vo.ModelDeleteReq true "删除模型请求"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/delete [post]
func (c *ModelController) Delete(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:delete"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.ModelDeleteReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 删除模型
	err := c.modelService.DeleteModel(req.ID)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// List 获取模型列表
// @Summary 获取模型列表
// @Description 获取模型列表，支持分页和搜索
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(20)
// @Param modelName query string false "模型名称（模糊搜索）"
// @Success 200 {object} response.Response{data=vo.ModelListResp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/list [get]
func (c *ModelController) List(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.ModelListReq

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为domain层参数
	param := req.ToModelParam()

	// 获取模型列表
	result, err := c.modelService.GetModelList(param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为VO层响应
	resp := vo.FromModelListResult(result)

	response.Success(ctx, resp)
}

// Import 导入模型
// @Summary 导入模型
// @Description 通过Excel文件导入模型及其衰减数据
// @Tags 模型管理
// @Accept multipart/form-data
// @Produce json
// @Param modelName formData string true "模型名称"
// @Param file formData file true "Excel文件"
// @Success 200 {object} response.Response "导入成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/import [post]
func (c *ModelController) Import(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:import"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 获取表单数据
	var req vo.ModelImportReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 获取文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.InvalidParam(ctx, "请选择要导入的文件")
		return
	}

	// 构建domain层参数
	importParam := req.ToModelImportParam(file, userEntity.ID)

	// 导入模型
	err = c.modelService.ImportModel(importParam)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "导入成功"})
}

// GetDetail 获取模型详情
// @Summary 获取模型详情
// @Description 根据ID获取模型详细信息
// @Tags 模型管理
// @Accept json
// @Produce json
// @Param id path string true "模型ID"
// @Success 200 {object} response.Response{data=vo.ModelDetailResp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "模型不存在"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/models/{id} [get]
func (c *ModelController) GetDetail(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:model:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 获取路径参数
	idStr := ctx.Param("id")
	if idStr == "" {
		response.InvalidParam(ctx, "请提供模型ID")
		return
	}

	// 转换为数字
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "模型ID格式错误")
		return
	}

	// 获取模型详情
	modelDetail, err := c.modelService.GetModelDetail(id)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为VO层响应
	resp := vo.FromModelDetailResult(modelDetail)

	response.Success(ctx, resp)
}
