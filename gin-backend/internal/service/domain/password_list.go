package domain

import (
	"errors"
	"time"
)

// PasswordListParam 口令列表查询参数
type PasswordListParam struct {
	// 筛选条件
	AccountID    *int64     // 账号ID
	AccountName  string     // 账号名称（模糊查询）
	PasswordName string     // 口令名称（模糊查询）
	StartDate    *time.Time // 开始日期
	EndDate      *time.Time // 结束日期

	// 分页参数
	Page     int // 页码，从1开始
	PageSize int // 每页数量
}

// Validate 验证参数
func (p PasswordListParam) Validate() error {
	if p.Page < 1 {
		return errors.New("页码必须大于0")
	}
	if p.PageSize < 1 || p.PageSize > 1000 {
		return errors.New("每页数量必须在1-1000之间")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// PasswordListItem 口令列表项
type PasswordListItem struct {
	PasswordName        string  `json:"password_name"`         // 口令名称
	AccountName         string  `json:"account_name"`          // 账号名称
	Consumption         float64 `json:"consumption"`           // 消费
	ActualConsumption   float64 `json:"actual_consumption"`    // 实际消费
	Impressions         int64   `json:"impressions"`           // 展现量
	ClickCount          int64   `json:"click_count"`           // 点击量
	ClickRate           float64 `json:"click_rate"`            // 点击率（%）
	SearchCount         int64   `json:"search_count"`          // 搜索人数
	NewOrdersToday      int64   `json:"new_orders_today"`      // 今日新订单
	TodayOrderCost      float64 `json:"today_order_cost"`      // 今日订单成本
	CumulativeOrders    int64   `json:"cumulative_orders"`     // 累计订单
	CumulativeIncome    float64 `json:"cumulative_income"`     // 累计收入
	CumulativeRecovery  float64 `json:"cumulative_recovery"`   // 累计回收
	LastUpdate          string  `json:"last_update"`           // 最后更新时间
}

// PasswordListResult 口令列表查询结果
type PasswordListResult struct {
	List  []PasswordListItem `json:"list"`  // 口令列表
	Total int64              `json:"total"` // 总数量
	Page  int                `json:"page"`  // 当前页码
	Size  int                `json:"size"`  // 每页数量
}

// PasswordExportParam 口令列表导出参数
type PasswordExportParam struct {
	// 筛选条件
	AccountID    *int64     // 账号ID
	AccountName  string     // 账号名称（模糊查询）
	PasswordName string     // 口令名称（模糊查询）
	StartDate    *time.Time // 开始日期
	EndDate      *time.Time // 结束日期

	// 导出参数
	Format string // 导出格式：xlsx 或 csv
}

// Validate 验证导出参数
func (p PasswordExportParam) Validate() error {
	if p.Format != "xlsx" && p.Format != "csv" {
		return errors.New("导出格式只支持xlsx或csv")
	}
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	// 限制导出数据的时间范围，避免数据量过大
	if p.StartDate != nil && p.EndDate != nil {
		duration := p.EndDate.Sub(*p.StartDate)
		if duration > 365*24*time.Hour {
			return errors.New("导出数据的时间范围不能超过365天")
		}
	}
	return nil
}

// PasswordExportResult 口令列表导出结果
type PasswordExportResult struct {
	FileName string // 文件名
	FileURL  string // 文件下载URL
}

// PasswordStatsParam 口令统计参数
type PasswordStatsParam struct {
	// 筛选条件
	AccountID   *int64     // 账号ID
	AccountName string     // 账号名称
	StartDate   *time.Time // 开始日期
	EndDate     *time.Time // 结束日期
}

// Validate 验证统计参数
func (p PasswordStatsParam) Validate() error {
	if p.StartDate != nil && p.EndDate != nil && p.StartDate.After(*p.EndDate) {
		return errors.New("开始日期不能晚于结束日期")
	}
	return nil
}

// PasswordStatsResult 口令统计结果
type PasswordStatsResult struct {
	TotalPasswords      int64     `json:"total_passwords"`       // 总口令数量
	TotalConsumption    float64   `json:"total_consumption"`     // 总消费
	TotalOrders         int64     `json:"total_orders"`          // 总订单数
	TotalIncome         float64   `json:"total_income"`          // 总收入
	TotalRecoveryRate   float64   `json:"total_recovery_rate"`   // 总回收率
	AvgClickRate        float64   `json:"avg_click_rate"`        // 平均点击率
	LastUpdateTime      time.Time `json:"last_update_time"`      // 最后更新时间
}

// ValidatePasswordListParam 验证口令列表查询参数
func ValidatePasswordListParam(param PasswordListParam) error {
	return param.Validate()
}

// ValidatePasswordExportParam 验证口令列表导出参数
func ValidatePasswordExportParam(param PasswordExportParam) error {
	return param.Validate()
}

// ValidatePasswordStatsParam 验证口令统计参数
func ValidatePasswordStatsParam(param PasswordStatsParam) error {
	return param.Validate()
}

// GetPasswordTimeRangeDescription 获取口令时间范围描述
func GetPasswordTimeRangeDescription(startDate, endDate *time.Time) string {
	if startDate == nil && endDate == nil {
		return "全部时间"
	}
	if startDate != nil && endDate != nil {
		if startDate.Format("2006-01-02") == endDate.Format("2006-01-02") {
			return startDate.Format("2006-01-02") + "（单日）"
		}
		return startDate.Format("2006-01-02") + " 至 " + endDate.Format("2006-01-02")
	}
	if startDate != nil {
		return "从 " + startDate.Format("2006-01-02") + " 开始"
	}
	if endDate != nil {
		return "到 " + endDate.Format("2006-01-02") + " 结束"
	}
	return "未知时间范围"
}

// IsPasswordDateRangeValid 检查口令日期范围是否有效
func IsPasswordDateRangeValid(startDate, endDate *time.Time) bool {
	if startDate == nil || endDate == nil {
		return true
	}
	return !startDate.After(*endDate)
}

// GetPasswordDateRangeDays 获取口令日期范围的天数
func GetPasswordDateRangeDays(startDate, endDate *time.Time) int {
	if startDate == nil || endDate == nil {
		return 0
	}
	duration := endDate.Sub(*startDate)
	return int(duration.Hours()/24) + 1
}

// IsPasswordSingleDayQuery 判断是否为单日查询
func IsPasswordSingleDayQuery(startDate, endDate *time.Time) bool {
	if startDate == nil || endDate == nil {
		return false
	}
	return startDate.Format("2006-01-02") == endDate.Format("2006-01-02")
}
