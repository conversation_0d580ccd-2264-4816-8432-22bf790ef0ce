package controller

import (
	"fmt"
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AgentController 代理控制器
type AgentController struct {
	agentService      *service.AgentService
	authService       *service.AuthService
	permissionService *service.PermissionService
}

// NewAgentController 创建代理控制器实例
func NewAgentController() *AgentController {
	return &AgentController{
		agentService:      service.NewAgentService(),
		authService:       service.NewAuthService(),
		permissionService: service.NewPermissionService(),
	}
}

// checkPermission 检查权限的辅助函数
func (ctrl *AgentController) checkPermission(c *gin.Context, permission string) error {
	userID, exists := c.Get("user_id")
	if !exists {
		return fmt.Errorf("用户未登录")
	}

	uid, ok := userID.(uint64)
	if !ok {
		return fmt.Errorf("用户ID格式错误")
	}

	// 获取用户信息
	user, err := ctrl.authService.ValidateUser(c.Request.Context(), uid)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 简单的权限检查逻辑（可以根据实际需求扩展）
	// 这里暂时使用基于角色的权限检查
	if user.Role == domain.RoleAdmin {
		return nil // 管理员拥有所有权限
	}

	// 根据权限和角色进行检查
	switch permission {
	case "agent:list", "agent:detail", "agent:view":
		// 所有角色都可以查看
		return nil
	case "agent:create", "agent:edit", "agent:delete":
		// 只有管理员和运营可以操作
		if user.Role == domain.RoleOperator {
			return nil
		}
		return fmt.Errorf("权限不足")
	case "agent:audit":
		// 只有管理员和运营可以审核
		if user.Role == domain.RoleOperator {
			return nil
		}
		return fmt.Errorf("权限不足")
	default:
		return fmt.Errorf("未知权限: %s", permission)
	}
}

// logUserAction 记录用户操作日志
func (ctrl *AgentController) logUserAction(c *gin.Context, userID uint64, module, action, details string) {
	logrus.WithFields(logrus.Fields{
		"user_id": userID,
		"module":  module,
		"action":  action,
		"details": details,
		"ip":      c.ClientIP(),
		"path":    c.Request.URL.Path,
	}).Info("用户操作日志")
}

// getRoleString 获取角色字符串
func getRoleString(role int) string {
	switch role {
	case domain.RoleAdmin:
		return "admin"
	case domain.RoleOperator:
		return "operator"
	default:
		return "user"
	}
}

// parseAgentID 解析代理ID，支持查询参数和路径参数两种方式
func (ctrl *AgentController) parseAgentID(c *gin.Context) (int64, error) {
	var idStr string

	// 优先从路径参数获取（RESTful风格）
	idStr = c.Param("id")

	// 如果路径参数为空，从查询参数获取（兼容前端API）
	if idStr == "" {
		idStr = c.Query("id")
	}

	// 记录调试信息
	logrus.WithFields(logrus.Fields{
		"path":     c.Request.URL.Path,
		"id_param": idStr,
		"method":   c.Request.Method,
		"query":    c.Request.URL.RawQuery,
	}).Debug("解析代理ID")

	if idStr == "" {
		return 0, fmt.Errorf("代理ID不能为空")
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("代理ID格式错误，期望数字格式，实际接收: '%s'", idStr)
	}

	if id == 0 {
		return 0, fmt.Errorf("代理ID不能为0")
	}

	return int64(id), nil
}

// GetList 获取代理列表
// @Summary 获取代理列表
// @Description 支持按名称、类型、审核状态筛选，支持分页，支持数据权限控制
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param name query string false "代理名称"
// @Param type query string false "代理类型：traffic-流量采买,delivery-投放"
// @Param audit_status query string false "审核状态：pending-审核中,approved-已通过,rejected-已拒绝"
// @Param media_id query uint64 false "媒介ID"
// @Param page query int true "页码"
// @Param page_size query int true "每页数量"
// @Success 200 {object} response.Response{data=vo.AgentListResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/list [get]
func (ctrl *AgentController) GetList(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:list"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析请求参数
	var req vo.AgentListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 转换参数并调用服务
	domainParam := req.ToDomain()
	result, err := ctrl.agentService.GetList(c.Request.Context(), userEntity, domainParam)
	if err != nil {
		response.Error(c, response.CodeServerError, "获取代理列表失败: "+err.Error())
		return
	}

	// 转换响应
	var respVO vo.AgentListResp
	respVO.FromDomain(result)

	// 返回成功响应
	response.Success(c, respVO)
}

// GetDetail 获取代理详情
// @Summary 获取代理详情
// @Description 根据代理ID获取详细信息，支持数据权限控制
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param id query uint64 true "代理ID"
// @Success 200 {object} response.Response{data=vo.AgentItem}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/detail [get]
func (ctrl *AgentController) GetDetail(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:detail"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析ID参数
	id, err := ctrl.parseAgentID(c)
	if err != nil {
		response.Error(c, response.CodeInvalidParam, err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 调用服务
	agentInfo, err := ctrl.agentService.GetByID(c.Request.Context(), userEntity, id)
	if err != nil {
		response.Error(c, response.CodeServerError, "获取代理详情失败: "+err.Error())
		return
	}

	// 转换为VO
	var respVO vo.AgentItem
	respVO.FromDomain(agentInfo)

	// 返回成功响应
	response.Success(c, respVO)
}

// Create 创建代理
// @Summary 创建代理
// @Description 创建新的代理
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentCreateReq true "代理信息"
// @Success 200 {object} response.Response{data=vo.AgentCreateResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/create [post]
func (ctrl *AgentController) Create(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:create"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析请求参数
	var req vo.AgentCreateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 转换为领域模型
	agentEntity := req.ToDomain()

	// 调用服务创建代理
	id, err := ctrl.agentService.Create(c.Request.Context(), userEntity, agentEntity)
	if err != nil {
		response.Error(c, response.CodeServerError, "创建代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, vo.AgentCreateResp{ID: id})
}

// Update 更新代理
// @Summary 更新代理
// @Description 更新代理信息
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentUpdateReq true "代理信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/update [put]
func (ctrl *AgentController) Update(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:update"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析请求参数
	var req vo.AgentUpdateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 转换为领域模型
	agentEntity := req.ToDomain()

	// 调用服务更新代理
	if err := ctrl.agentService.Update(c.Request.Context(), userEntity, agentEntity); err != nil {
		response.Error(c, response.CodeServerError, "更新代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}

// Delete 删除代理
// @Summary 删除代理
// @Description 根据代理ID删除代理
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param id query uint64 true "代理ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/delete [delete]
func (ctrl *AgentController) Delete(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:delete"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析ID参数
	id, err := ctrl.parseAgentID(c)
	if err != nil {
		response.Error(c, response.CodeInvalidParam, err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 调用服务删除代理
	if err := ctrl.agentService.Delete(c.Request.Context(), userEntity, id); err != nil {
		response.Error(c, response.CodeServerError, "删除代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}

// Audit 审核代理
// @Summary 审核代理
// @Description 审核代理信息
// @Tags 代理管理
// @Accept json
// @Produce json
// @Param data body vo.AgentAuditReq true "审核信息"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/agents/audit [post]
func (ctrl *AgentController) Audit(c *gin.Context) {
	// 检查权限
	if err := ctrl.checkPermission(c, "agent:audit"); err != nil {
		response.Error(c, response.CodeForbidden, err.Error())
		return
	}

	// 解析请求参数
	var req vo.AgentAuditReq
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, response.CodeInvalidParam, "invalid request: "+err.Error())
		return
	}

	// 获取用户信息
	userEntity := middleware.GetCurrentUser(c)
	if userEntity.ID <= 0 {
		response.Error(c, response.CodeUnauthorized, "获取用户信息失败")
		return
	}

	// 转换为领域模型
	auditEntity := req.ToDomain()

	// 调用服务审核代理
	if err := ctrl.agentService.Audit(c.Request.Context(), userEntity, auditEntity); err != nil {
		response.Error(c, response.CodeServerError, "审核代理失败: "+err.Error())
		return
	}

	// 返回成功响应
	response.Success(c, nil)
}
