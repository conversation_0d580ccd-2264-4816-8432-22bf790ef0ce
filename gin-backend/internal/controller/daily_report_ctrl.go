package controller

import (
	"net/http"
	"net/url"
	"time"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// DailyReportController 日报表控制器
type DailyReportController struct {
	dailyReportService *service.DailyReportService
}

// NewDailyReportController 创建日报表控制器
func NewDailyReportController() *DailyReportController {
	return &DailyReportController{
		dailyReportService: service.NewDailyReportService(),
	}
}

// GetDailyReport 获取日报表汇总数据
// @Summary 获取日报表汇总数据
// @Description 根据时间范围获取日报表汇总统计数据，支持媒体筛选
// @Tags 数据报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期" example("2024-01-01")
// @Param end_date query string true "结束日期" example("2024-01-31")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {object} response.Response{data=vo.DailyReportResp} "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/daily-report [get]
// @Security ApiKeyAuth
func (c *DailyReportController) GetDailyReport(ctx *gin.Context) {
	var req vo.DailyReportReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}
	userEntity := middleware.GetCurrentUser(ctx)

	// 转换请求参数到domain层参数
	param := req.ToDailyReportParam(userEntity.ID)

	// 调用服务获取数据
	reportData, err := c.dailyReportService.GetDailyReport(param)
	if err != nil {
		response.ServerError(ctx, "获取日报表数据失败："+err.Error())
		return
	}

	// 使用VO层的转换方法
	reportResp := vo.FromDailyReportEntity(reportData, req.StartDate, req.EndDate)

	response.Success(ctx, reportResp)
}

// GetDailyDetail 获取日报表详情数据
// @Summary 获取日报表详情数据
// @Description 根据指定日期获取详细的媒体级别数据
// @Tags 数据报表
// @Accept json
// @Produce json
// @Param date query string true "查询日期" example("2024-01-01")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {object} response.Response{data=vo.DailyDetailResp} "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/daily-detail [get]
// @Security ApiKeyAuth
func (c *DailyReportController) GetDailyDetail(ctx *gin.Context) {
	var req vo.DailyDetailReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}

	// 验证日期格式 - 支持多种日期格式
	var dateStr string
	if len(req.Date) >= 10 {
		// 取前10位作为日期部分
		dateStr = req.Date[:10]
	} else {
		dateStr = req.Date
	}

	_, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		response.InvalidParam(ctx, "日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	// 使用处理后的日期字符串
	req.Date = dateStr

	// 权限检查
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 转换请求参数到domain层参数
	param := req.ToDailyDetailParam(userID.(int64))

	// 调用服务获取详情数据
	detailData, err := c.dailyReportService.GetDailyDetail(param)
	if err != nil {
		response.ServerError(ctx, "获取日报表详情失败："+err.Error())
		return
	}

	// 使用VO层的转换方法
	detailResp := vo.FromDailyDetailEntity(detailData)

	response.Success(ctx, detailResp)
}

// ExportDailyReport 导出日报表数据
// @Summary 导出日报表数据
// @Description 导出指定时间范围的日报表数据为Excel
// @Tags 数据报表
// @Accept json
// @Produce octet-stream
// @Param start_date query string true "开始日期" example("2024-01-01")
// @Param end_date query string true "结束日期" example("2024-01-31")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {file} file "Excel文件"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/daily-report/export [get]
// @Security ApiKeyAuth
func (c *DailyReportController) ExportDailyReport(ctx *gin.Context) {
	var req vo.DailyReportReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}

	// 验证日期格式
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		response.InvalidParam(ctx, "开始日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		response.InvalidParam(ctx, "结束日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	// 验证日期范围
	if endDate.Before(startDate) {
		response.InvalidParam(ctx, "结束日期不能早于开始日期")
		return
	}

	// 限制查询范围不超过90天
	if endDate.Sub(startDate).Hours() > 90*24 {
		response.InvalidParam(ctx, "查询范围不能超过90天")
		return
	}

	// 权限检查
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 转换请求参数到domain层参数
	param := req.ToDailyReportParam(userID.(int64))

	// 调用服务导出数据
	excelData, filename, err := c.dailyReportService.ExportDailyReport(param)
	if err != nil {
		response.ServerError(ctx, "导出日报表失败："+err.Error())
		return
	}

	// 设置响应头
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename="+url.QueryEscape(filename))
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelData)
}

// ============= 周报表相关接口 =============

// GetWeeklyReport 获取周报表数据
// @Summary 获取周报表数据
// @Description 根据时间范围获取周报表统计数据，支持媒体筛选
// @Tags 数据报表
// @Accept json
// @Produce json
// @Param start_date query string true "开始日期" example("2024-01-01")
// @Param end_date query string true "结束日期" example("2024-01-31")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {object} response.Response{data=vo.WeeklyReportResp} "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/weekly-report [get]
// @Security ApiKeyAuth
func (c *DailyReportController) GetWeeklyReport(ctx *gin.Context) {
	var req vo.WeeklyReportReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}

	// 验证日期格式
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		response.InvalidParam(ctx, "开始日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		response.InvalidParam(ctx, "结束日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	// 验证日期范围
	if endDate.Before(startDate) {
		response.InvalidParam(ctx, "结束日期不能早于开始日期")
		return
	}

	// 权限检查
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 转换请求参数到domain层参数
	param := req.ToWeeklyReportParam(userID.(int64))

	// 调用服务获取数据
	reportData, err := c.dailyReportService.GetWeeklyReport(param)
	if err != nil {
		response.ServerError(ctx, "获取周报表数据失败："+err.Error())
		return
	}

	// 使用VO层的转换方法
	weeklyResp := vo.FromWeeklyReportEntity(reportData, req.StartDate, req.EndDate)

	response.Success(ctx, weeklyResp)
}

// GetWeeklyDetail 获取周报表详情
// @Summary 获取周报表详情
// @Description 获取指定周的详细数据，包含媒体维度和日期维度的数据
// @Tags 数据报表
// @Accept json
// @Produce json
// @Param year_week query string true "年周" example("202401")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {object} response.Response{data=vo.WeeklyDetailResp} "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/weekly-detail [get]
// @Security ApiKeyAuth
func (c *DailyReportController) GetWeeklyDetail(ctx *gin.Context) {
	var req vo.WeeklyDetailReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}

	// 验证年周格式
	if len(req.YearWeek) != 6 {
		response.InvalidParam(ctx, "年周格式错误，应为YYYYWW格式，如202401")
		return
	}

	// 权限检查
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 转换请求参数到domain层参数
	param := req.ToWeeklyDetailParam(userID.(int64))

	// 调用服务获取详情数据
	detailData, err := c.dailyReportService.GetWeeklyDetail(param)
	if err != nil {
		response.ServerError(ctx, "获取周报表详情失败："+err.Error())
		return
	}

	// 使用VO层的转换方法
	weeklyDetailResp := vo.FromWeeklyDetailEntity(detailData)

	response.Success(ctx, weeklyDetailResp)
}

// ExportWeeklyReport 导出周报表
// @Summary 导出周报表数据
// @Description 导出指定时间范围的周报表数据为Excel
// @Tags 数据报表
// @Accept json
// @Produce octet-stream
// @Param start_date query string true "开始日期" example("2024-01-01")
// @Param end_date query string true "结束日期" example("2024-01-31")
// @Param media_id query string false "媒体ID" example("1")
// @Success 200 {file} file "Excel文件"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/reports/weekly-report/export [get]
// @Security ApiKeyAuth
func (c *DailyReportController) ExportWeeklyReport(ctx *gin.Context) {
	var req vo.WeeklyReportReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数验证失败")
		return
	}

	// 验证日期格式
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		response.InvalidParam(ctx, "开始日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		response.InvalidParam(ctx, "结束日期格式错误，日期格式应为YYYY-MM-DD")
		return
	}

	// 验证日期范围
	if endDate.Before(startDate) {
		response.InvalidParam(ctx, "结束日期不能早于开始日期")
		return
	}

	// 权限检查
	userID, exists := ctx.Get("user_id")
	if !exists {
		response.Unauthorized(ctx, "用户未登录")
		return
	}

	// 转换请求参数到domain层参数
	param := req.ToWeeklyReportParam(userID.(int64))

	// 调用服务导出数据
	excelData, filename, err := c.dailyReportService.ExportWeeklyReport(param)
	if err != nil {
		response.ServerError(ctx, "导出周报表失败："+err.Error())
		return
	}

	// 设置文件下载
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Disposition", "attachment; filename="+url.QueryEscape(filename))
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelData)
}
