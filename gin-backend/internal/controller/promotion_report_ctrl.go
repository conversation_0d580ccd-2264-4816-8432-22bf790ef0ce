package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

type PromotionReportController struct {
	service *service.PromotionReportService
}

func NewPromotionReportController() *PromotionReportController {
	return &PromotionReportController{service: service.NewPromotionReportService()}
}

// GetCostList 获取费用列表
// @Summary 获取费用列表
// @Description 获取推广费用列表
// @Tags 推广报表
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param group_id query int false "口令组ID"
// @Param category_id query int false "分类ID"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Success 200 {object} response.Response{data=vo.PromotionReportCostListResp}
// @Router /api/promotion-reports/costs [get]
func (c *PromotionReportController) GetCostList(ctx *gin.Context) {
	var req vo.PromotionReportCostListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误")
		return
	}

	// 转换为domain参数并调用service层
	result, err := c.service.GetCostList(ctx, req.ToDomainPromotionReportCostParam())
	if err != nil {
		response.ServerError(ctx, "获取费用列表失败")
		return
	}

	// 使用VO层转换方法生成响应
	response.Success(ctx, vo.NewPromotionReportCostListRespFromDomain(result))
}

// UpdateCost 更新费用
// @Summary 更新费用
// @Description 更新推广费用
// @Tags 推广报表
// @Accept json
// @Produce json
// @Param data body vo.UpdateCostReq true "更新费用请求"
// @Success 200 {object} response.Response
// @Router /api/promotion-reports/costs [put]
func (c *PromotionReportController) UpdateCost(ctx *gin.Context) {
	var req vo.UpdateCostReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误")
		return
	}

	// 调用服务执行更新
	err := c.service.UpdateCost(ctx, req.GroupId, req.ReportDate, req.Cost)
	if err != nil {
		response.ServerError(ctx, "更新费用失败: "+err.Error())
		return
	}

	response.Success(ctx, nil)
}

// ImportCost 导入费用
// @Summary 导入费用
// @Description 导入推广费用
// @Tags 推广报表
// @Accept multipart/form-data
// @Produce json
// @Param category_id formData int true "分类ID"
// @Param file formData file true "Excel文件"
// @Success 200 {object} response.Response
// @Router /api/promotion-reports/costs/import [post]
func (c *PromotionReportController) ImportCost(ctx *gin.Context) {
	var req vo.ImportCostReq
	if err := ctx.ShouldBind(&req); err != nil {
		response.InvalidParam(ctx, "参数错误")
		return
	}

	file, err := ctx.FormFile("file")
	if err != nil {
		response.InvalidParam(ctx, "文件获取失败")
		return
	}

	// 验证文件类型
	ext := filepath.Ext(file.Filename)
	if ext != ".xlsx" && ext != ".xls" {
		response.InvalidParam(ctx, "只支持.xlsx或.xls格式文件")
		return
	}

	// 调用service执行导入
	success, err := c.service.ImportCost(ctx, req.CategoryId, file)
	if err != nil {
		response.ServerError(ctx, "导入失败: "+err.Error())
		return
	}

	if success {
		response.Success(ctx, nil)
	} else {
		response.ServerError(ctx, "导入任务已提交，但可能存在问题，请检查任务状态")
	}
}

// GetTaskList 获取任务列表
// @Summary 获取任务列表
// @Description 获取导入任务列表
// @Tags 推广报表
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]vo.TaskListItem}
// @Router /api/promotion-reports/tasks [get]
func (c *PromotionReportController) GetTaskList(ctx *gin.Context) {
	tasks, err := c.service.GetTaskList(ctx)
	if err != nil {
		response.ServerError(ctx, "获取任务列表失败")
		return
	}

	// 转换为VO响应
	items := make([]vo.TaskListItem, 0, len(tasks))
	for _, task := range tasks {
		items = append(items, vo.NewTaskListItemFromDomain(task))
	}

	response.Success(ctx, items)
}

// GetModelReport 获取模型报表
// @Summary 获取模型报表
// @Description 获取模型报表数据
// @Tags 推广报表
// @Accept json
// @Produce json
// @Param model_id query int true "模型ID"
// @Param category_id query int false "分类ID"
// @Param group_id query int false "口令组ID"
// @Success 200 {object} response.Response{data=vo.ModelReportResp}
// @Router /api/v1/delivery/reports/list [get]
func (c *PromotionReportController) GetModelReport(ctx *gin.Context) {
	var req vo.ModelReportReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误")
		return
	}

	// 转换为domain参数并调用service层
	result, err := c.service.GetModelReport(ctx, req.ToDomainModelReportParam())
	if err != nil {
		response.ServerError(ctx, "获取模型报表失败: "+err.Error())
		return
	}

	// 使用VO层转换方法生成响应
	response.Success(ctx, vo.NewModelReportRespFromDomain(*result))
}
