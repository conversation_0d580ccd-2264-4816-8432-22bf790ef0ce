package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"gorm.io/gorm"
)

// AdAccountService 广告账号服务实现
type AdAccountService struct {
	db *gorm.DB
}

// NewAdAccountService 创建广告账号服务实例
func NewAdAccountService() *AdAccountService {
	return &AdAccountService{
		db: global.DB,
	}
}

// GetAdAccounts 获取广告账号列表
func (s *AdAccountService) GetAdAccounts(ctx context.Context, param domain.GetAdAccountsParam) (domain.GetAdAccountsResult, error) {
	// 参数校验和默认值设置
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 || param.PageSize > 100 {
		param.PageSize = 20
	}

	// 计算偏移量
	offset := (param.Page - 1) * param.PageSize

	// 获取广告账号列表
	var accounts []model.AdAccounts
	var total int64

	query := s.db.WithContext(ctx).Model(&model.AdAccounts{})

	// 应用筛选条件
	if param.AccountName != "" {
		// 账号名称/ID联合查询
		query = query.Where("account_name LIKE ? OR platform_account_id LIKE ?", 
			"%"+param.AccountName+"%", "%"+param.AccountName+"%")
	}
	if param.Platform > 0 {
		query = query.Where("platform = ?", param.Platform)
	}
	if param.AuthorizationStatus >= 0 {
		query = query.Where("authorization_status = ?", param.AuthorizationStatus)
	}
	if param.UsageStatus > 0 {
		query = query.Where("usage_status = ?", param.UsageStatus)
	}
	if param.AccountType > 0 {
		query = query.Where("account_type = ?", param.AccountType)
	}
	if param.ParentId > 0 {
		query = query.Where("parent_id = ?", param.ParentId)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return domain.GetAdAccountsResult{}, fmt.Errorf("获取广告账号列表失败: %w", err)
	}

	// 分页查询
	err := query.Offset(offset).Limit(param.PageSize).Order("created_at DESC").Find(&accounts).Error
	if err != nil {
		return domain.GetAdAccountsResult{}, fmt.Errorf("获取广告账号列表失败: %w", err)
	}

	// 转换为响应格式并填充关联信息
	accountEntities := make([]domain.AdAccountEntity, len(accounts))
	for i, account := range accounts {
		entity := s.modelToEntity(account)
		
		// 填充父账号名称
		if account.ParentId > 0 {
			var parentAccount model.AdAccounts
			if err := s.db.WithContext(ctx).Where("id = ?", account.ParentId).First(&parentAccount).Error; err == nil {
				entity.ParentAccountName = parentAccount.AccountName
			}
		}

		accountEntities[i] = entity
	}

	return domain.GetAdAccountsResult{
		List:  accountEntities,
		Total: total,
		Page:  param.Page,
		Size:  param.PageSize,
	}, nil
}

// CreateAdAccount 创建广告账号
func (s *AdAccountService) CreateAdAccount(ctx context.Context, param domain.CreateAdAccountParam) (domain.CreateAdAccountResult, error) {
	// 验证参数
	if err := domain.ValidateCreateAdAccountParam(param); err != nil {
		return domain.CreateAdAccountResult{}, err
	}

	// 检查平台账号ID是否已存在
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.AdAccounts{}).
		Where("platform_account_id = ? AND platform = ?", param.PlatformAccountId, param.Platform).
		Count(&count).Error; err != nil {
		return domain.CreateAdAccountResult{}, fmt.Errorf("检查平台账号ID失败: %w", err)
	}
	if count > 0 {
		return domain.CreateAdAccountResult{}, fmt.Errorf("该平台账号ID已存在")
	}

	// 如果是子账号，验证父账号是否存在且为主账号
	if param.AccountType == domain.AccountTypeSub {
		var parentAccount model.AdAccounts
		if err := s.db.WithContext(ctx).Where("id = ?", param.ParentId).First(&parentAccount).Error; err != nil {
			return domain.CreateAdAccountResult{}, fmt.Errorf("指定的父账号不存在")
		}
		if parentAccount.AccountType != domain.AccountTypeMaster {
			return domain.CreateAdAccountResult{}, fmt.Errorf("父账号必须是主账号")
		}
		if parentAccount.UsageStatus != domain.UsageStatusEnabled {
			return domain.CreateAdAccountResult{}, fmt.Errorf("父账号已被禁用")
		}
	}

	// 创建广告账号对象
	now := time.Now()
	account := model.AdAccounts{
		AccountType:         param.AccountType,
		ParentId:            param.ParentId,
		Platform:            param.Platform,
		AccountName:         param.AccountName,
		PlatformAccountId:   param.PlatformAccountId,
		AuthorizationStatus: domain.AuthStatusUnauthorized, // 默认未授权
		Token:               param.Token,
		TokenExpireTime:     param.TokenExpireTime,
		UsageStatus:         domain.UsageStatusEnabled, // 默认启用
		AccountBalance:      param.AccountBalance,
		Owner:               param.Owner,
		LastSync:            now,
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	// 如果提供了token，设置为已授权
	if param.Token != "" {
		account.AuthorizationStatus = domain.AuthStatusAuthorized
	}

	// 创建账号
	if err := s.db.WithContext(ctx).Create(&account).Error; err != nil {
		return domain.CreateAdAccountResult{}, fmt.Errorf("创建广告账号失败: %w", err)
	}

	return domain.CreateAdAccountResult{
		ID: account.ID,
	}, nil
}

// UpdateAdAccount 更新广告账号
func (s *AdAccountService) UpdateAdAccount(ctx context.Context, param domain.UpdateAdAccountParam) error {
	// 验证参数
	if err := domain.ValidateUpdateAdAccountParam(param); err != nil {
		return err
	}

	// 检查账号是否存在
	var account model.AdAccounts
	if err := s.db.WithContext(ctx).Where("id = ?", param.ID).First(&account).Error; err != nil {
		return fmt.Errorf("广告账号不存在: %w", err)
	}

	// 检查平台账号ID是否已被其他账号使用
	var count int64
	if err := s.db.WithContext(ctx).Model(&model.AdAccounts{}).
		Where("platform_account_id = ? AND platform = ? AND id != ?", 
			param.PlatformAccountId, account.Platform, param.ID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("检查平台账号ID失败: %w", err)
	}
	if count > 0 {
		return fmt.Errorf("该平台账号ID已被其他账号使用")
	}

	// 更新账号信息
	updates := map[string]interface{}{
		"account_name":         param.AccountName,
		"platform_account_id":  param.PlatformAccountId,
		"token":                param.Token,
		"token_expire_time":    param.TokenExpireTime,
		"usage_status":         param.UsageStatus,
		"account_balance":      param.AccountBalance,
		"owner":                param.Owner,
		"updated_at":           time.Now(),
	}

	// 根据token更新授权状态
	if param.Token != "" {
		updates["authorization_status"] = domain.AuthStatusAuthorized
	} else {
		updates["authorization_status"] = domain.AuthStatusUnauthorized
	}

	if err := s.db.WithContext(ctx).Model(&account).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新广告账号失败: %w", err)
	}

	return nil
}

// DeleteAdAccount 删除广告账号
func (s *AdAccountService) DeleteAdAccount(ctx context.Context, id int64) error {
	// 检查账号是否存在
	var account model.AdAccounts
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&account).Error; err != nil {
		return fmt.Errorf("广告账号不存在: %w", err)
	}

	// 如果是主账号，检查是否有子账号
	if account.AccountType == domain.AccountTypeMaster {
		var subAccountCount int64
		if err := s.db.WithContext(ctx).Model(&model.AdAccounts{}).
			Where("parent_id = ?", id).Count(&subAccountCount).Error; err != nil {
			return fmt.Errorf("检查子账号关系失败: %w", err)
		}
		if subAccountCount > 0 {
			return fmt.Errorf("该主账号下还有%d个子账号，请先删除子账号", subAccountCount)
		}
	}

	// 删除账号
	if err := s.db.WithContext(ctx).Delete(&account).Error; err != nil {
		return fmt.Errorf("删除广告账号失败: %w", err)
	}

	return nil
}

// GetAdAccountByID 根据ID获取广告账号详情
func (s *AdAccountService) GetAdAccountByID(ctx context.Context, id int64) (domain.AdAccountEntity, []domain.AdAccountEntity, error) {
	// 获取主账号信息
	var account model.AdAccounts
	if err := s.db.WithContext(ctx).Where("id = ?", id).First(&account).Error; err != nil {
		return domain.AdAccountEntity{}, nil, fmt.Errorf("广告账号不存在: %w", err)
	}

	entity := s.modelToEntity(account)

	// 填充父账号名称
	if account.ParentId > 0 {
		var parentAccount model.AdAccounts
		if err := s.db.WithContext(ctx).Where("id = ?", account.ParentId).First(&parentAccount).Error; err == nil {
			entity.ParentAccountName = parentAccount.AccountName
		}
	}

	// 如果是主账号，获取子账号列表
	var subAccountEntities []domain.AdAccountEntity
	if account.AccountType == domain.AccountTypeMaster {
		var subAccounts []model.AdAccounts
		if err := s.db.WithContext(ctx).Where("parent_id = ?", id).
			Order("created_at DESC").Find(&subAccounts).Error; err == nil {
			subAccountEntities = make([]domain.AdAccountEntity, len(subAccounts))
			for i, subAccount := range subAccounts {
				subAccountEntities[i] = s.modelToEntity(subAccount)
			}
		}
	}

	return entity, subAccountEntities, nil
}

// GetAdAccountOptions 获取广告账号选项
func (s *AdAccountService) GetAdAccountOptions(ctx context.Context) (domain.GetAdAccountOptionsResult, error) {
	// 平台选项
	platforms := []domain.PlatformOption{
		{Value: domain.PlatformXiaohongshu, Label: domain.GetPlatformName(domain.PlatformXiaohongshu)},
	}

	// 账号类型选项
	accountTypes := []domain.AccountTypeOption{
		{Value: domain.AccountTypeMaster, Label: domain.GetAccountTypeName(domain.AccountTypeMaster)},
		{Value: domain.AccountTypeSub, Label: domain.GetAccountTypeName(domain.AccountTypeSub)},
	}

	// 父账号选项（只包含启用的主账号）
	var parentAccounts []model.AdAccounts
	if err := s.db.WithContext(ctx).
		Select("id, account_name").
		Where("account_type = ? AND usage_status = ?", domain.AccountTypeMaster, domain.UsageStatusEnabled).
		Order("account_name").
		Find(&parentAccounts).Error; err != nil {
		return domain.GetAdAccountOptionsResult{}, fmt.Errorf("获取父账号列表失败: %w", err)
	}

	parentAccountOptions := make([]domain.ParentAccountOption, len(parentAccounts))
	for i, account := range parentAccounts {
		parentAccountOptions[i] = domain.ParentAccountOption{
			Value: account.ID,
			Label: account.AccountName,
		}
	}

	return domain.GetAdAccountOptionsResult{
		Platforms:      platforms,
		AccountTypes:   accountTypes,
		ParentAccounts: parentAccountOptions,
	}, nil
}

// modelToEntity 将模型转换为实体
func (s *AdAccountService) modelToEntity(account model.AdAccounts) domain.AdAccountEntity {
	return domain.AdAccountEntity{
		ID:                      account.ID,
		AccountType:             account.AccountType,
		AccountTypeName:         domain.GetAccountTypeName(account.AccountType),
		ParentId:                account.ParentId,
		Platform:                account.Platform,
		PlatformName:            domain.GetPlatformName(account.Platform),
		AccountName:             account.AccountName,
		PlatformAccountId:       account.PlatformAccountId,
		AuthorizationStatus:     account.AuthorizationStatus,
		AuthorizationStatusName: domain.GetAuthorizationStatusName(account.AuthorizationStatus),
		Token:                   account.Token,
		TokenExpireTime:         account.TokenExpireTime,
		UsageStatus:             account.UsageStatus,
		UsageStatusName:         domain.GetUsageStatusName(account.UsageStatus),
		AccountBalance:          account.AccountBalance,
		Owner:                   account.Owner,
		LastSync:                account.LastSync,
		CreatedAt:               account.CreatedAt,
		UpdatedAt:               account.UpdatedAt,
	}
}
