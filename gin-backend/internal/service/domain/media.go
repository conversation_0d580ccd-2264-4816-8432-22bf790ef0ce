package domain

import "time"

// MediaListParam 媒体列表查询参数
type MediaListParam struct {
	Name              string   // 媒体名称
	UserID            int64    // 用户ID
	Types             []string // 媒体类型列表
	Industry          string   // 行业
	AuditStatus       string   // 审核状态
	CooperationStatus string   // 合作状态
	CooperationType   string   // 合作类型
	Page              int      // 页码
	PageSize          int      // 每页数量
}

// MediaEntity 媒体实体
type MediaEntity struct {
	ID                int64               // 媒体ID
	Code              string              // 媒体编号
	AdAgentID         int64               // 代理ID
	Name              string              // 媒体名称
	AuditStatus       string              // 审核状态
	CooperationStatus string              // 合作状态
	CooperationType   string              // 合作类型
	Account           string              // 账号
	LastLoginAt       string              // 最后登录时间
	Balance           float64             // 余额
	Types             []string            // 媒体类型列表
	Industry          string              // 行业
	CustomIndustry    string              // 自定义行业
	DailyActivity     string              // 日活
	TransactionVolume string              // 交易量
	RegionCodes       []string            // 地区编码列表
	CompanyName       string              // 公司名称
	CompanyAddress    string              // 公司地址
	ContactName       string              // 联系人
	ContactPhone      string              // 联系电话
	RejectReason      string              // 拒绝原因
	UserID            int64               // 用户ID
	Remark            string              // 备注
	PlatformConfig    MediaPlatformConfig // 平台配置
	CreatedAt         time.Time           // 创建时间
	UpdatedAt         time.Time           // 更新时间
}

// MediaListResult 媒体列表结果
type MediaListResult struct {
	List     []MediaEntity // 媒体列表
	Total    int64         // 总数量
	Page     int           // 当前页码
	PageSize int           // 每页数量
}

// MediaPlatformConfig 媒体平台配置
type MediaPlatformConfig struct {
	Platform string // 平台类型
	Info     any    // 平台配置信息
}

// DenghuoPlatformInfo 灯火平台配置信息
type DenghuoPlatformInfo struct {
	PID   string // 项目ID
	Token string // API Token
}

// XiaohongshuPlatformInfo 小红书平台配置信息
type XiaohongshuPlatformInfo struct {
	AppID     string // 应用ID
	AppSecret string // 应用密钥
}

// MediaCreateEntity 创建媒体实体
type MediaCreateEntity struct {
	Name              string              // 媒体名称
	Types             []string            // 媒体类型列表
	Industry          string              // 行业
	CustomIndustry    string              // 自定义行业
	DailyActivity     string              // 日活
	TransactionVolume string              // 交易量
	RegionCodes       []string            // 地区编码列表
	CompanyName       string              // 公司名称
	CompanyAddress    string              // 公司地址
	ContactName       string              // 联系人
	ContactPhone      string              // 联系电话
	CooperationType   string              // 合作类型
	Account           string              // 账号
	Password          string              // 密码
	PlatformConfig    MediaPlatformConfig // 平台配置
	AdAgentID         int64               // 代理ID
	Remark            string              // 备注
}

// MediaCreateResult 创建媒体结果
type MediaCreateResult struct {
	ID int64 // 媒体ID
}

// MediaUpdateEntity 更新媒体实体
type MediaUpdateEntity struct {
	ID                int64               // 媒体ID
	Name              string              // 媒体名称
	Types             []string            // 媒体类型列表
	Industry          string              // 行业
	CustomIndustry    string              // 自定义行业
	DailyActivity     string              // 日活
	TransactionVolume string              // 交易量
	RegionCodes       []string            // 地区编码列表
	CompanyName       string              // 公司名称
	CompanyAddress    string              // 公司地址
	ContactName       string              // 联系人
	ContactPhone      string              // 联系电话
	CooperationType   string              // 合作类型
	Account           string              // 账号
	Password          string              // 密码
	PlatformConfig    MediaPlatformConfig // 平台配置
	AdAgentID         int64               // 代理ID
	Remark            string              // 备注
}

// MediaAuditEntity 审核媒体实体
type MediaAuditEntity struct {
	ID           int64  // 媒体ID
	AuditStatus  string // 审核状态
	RejectReason string // 拒绝原因
	Remark       string // 备注
}

// MediaSelectOption 媒体选择器选项
type MediaSelectOption struct {
	ID   int64  // 媒体ID
	Name string // 媒体名称
}
