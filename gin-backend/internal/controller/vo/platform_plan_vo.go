package vo

import (
	"time"

	"gin-backend/internal/service/domain"
)

// 注意：本文件包含平台计划相关的所有VO类型定义和转换方法

// CreatePlatformPlanReq 创建平台计划请求
type CreatePlatformPlanReq struct {
	AgentID          int64  `json:"agent_id" form:"agent_id" binding:"required"`           // 代理ID
	MediaID          int64  `json:"media_id" form:"media_id" binding:"required"`           // 媒体ID
	PlatformType     string `json:"platform_type" form:"platform_type" binding:"required"` // 平台类型
	Name             string `json:"name" form:"name" binding:"required"`                   // 计划名称
	PlanType         string `json:"plan_type" form:"plan_type" binding:"required"`         // 计划类型
	MarketTargetName string `json:"market_target_name" form:"market_target_name"`          // 营销目标名称
	Remark           string `json:"remark" form:"remark"`                                  // 备注
	DataID           string `json:"data_id" form:"data_id"`                                // 数据ID
	PrincipalAccount string `json:"principal_account" form:"principal_account"`            // 负责人账号
	PrincipalName    string `json:"principal_name" form:"principal_name"`                  // 负责人名称
	SceneName        string `json:"scene_name" form:"scene_name"`                          // 场景名称
}

// UpdatePlatformPlanReq 更新平台计划请求
type UpdatePlatformPlanReq struct {
	ID       int64  `json:"id" form:"id" binding:"required"` // 计划ID
	Name     string `json:"name" form:"name"`                // 计划名称
	PlanType string `json:"plan_type" form:"plan_type"`      // 计划类型
	Remark   string `json:"remark" form:"remark"`            // 备注
}

// PlatformPlanDetailReq 获取平台计划详情请求
type PlatformPlanDetailReq struct {
	ID int64 `form:"id" json:"id" binding:"required"` // 计划ID
}

// PlatformPlanUpdateTypeReq 更新平台计划类型请求
type PlatformPlanUpdateTypeReq struct {
	ID       int64  `json:"id" binding:"required"`       // 计划ID
	PlanType string `json:"planType" binding:"required"` // 计划类型
}

// PlatformPlanDeleteReq 删除平台计划请求
type PlatformPlanDeleteReq struct {
	ID int64 `json:"id" form:"id" binding:"required"` // 计划ID
}

// MarketTargetItem 市场目标详情项
type MarketTargetItem struct {
	Value string `json:"value"` // 值
	Label string `json:"label"` // 标签
}

// MarketTargetsDetailReq 市场目标详情请求
type MarketTargetsDetailReq struct {
	PlatformType string `form:"platform_type" binding:"required"` // 平台类型
}

// MarketTargetsDetailResp 市场目标详情响应
type MarketTargetsDetailResp struct {
	Targets []MarketTargetItem `json:"targets"` // 目标列表
}

// PlatformPlanResp 平台计划响应
type PlatformPlanResp struct {
	ID               int64     `json:"id"`                 // 计划ID
	AgentID          int64     `json:"agent_id"`           // 代理ID
	MediaID          int64     `json:"media_id"`           // 媒体ID
	PlatformType     string    `json:"platform_type"`      // 平台类型
	Name             string    `json:"name"`               // 计划名称
	PlanType         string    `json:"plan_type"`          // 计划类型
	MarketTargetName string    `json:"market_target_name"` // 营销目标名称
	Remark           string    `json:"remark"`             // 备注
	DataID           string    `json:"data_id"`            // 数据ID
	PrincipalAccount string    `json:"principal_account"`  // 负责人账号
	PrincipalName    string    `json:"principal_name"`     // 负责人名称
	SceneName        string    `json:"scene_name"`         // 场景名称
	CreatedAt        time.Time `json:"created_at"`         // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`         // 更新时间
}

// PlatformPlanListReq 平台计划列表请求
type PlatformPlanListReq struct {
	Page             int    `json:"page" form:"page"`                         // 页码
	PageSize         int    `json:"pageSize" form:"pageSize"`                 // 每页数量
	PlatformType     string `json:"platformType" form:"platformType"`         // 平台类型
	PlanName         string `json:"planName" form:"planName"`                 // 计划名称
	AgentID          int64  `json:"agentId" form:"agentId"`                   // 代理ID
	MediaID          int64  `json:"mediaId" form:"mediaId"`                   // 媒体ID
	MarketTargetName string `json:"marketTargetName" form:"marketTargetName"` // 营销目标名称
	Keyword          string `json:"keyword" form:"keyword"`                   // 关键词
}

// PlatformPlanListResp 平台计划列表响应
type PlatformPlanListResp struct {
	Total int64              `json:"total"` // 总数量
	List  []PlatformPlanResp `json:"list"`  // 计划列表
}

// MarketTargetsResp 营销目标列表响应
type MarketTargetsResp struct {
	Targets []string `json:"targets"` // 营销目标列表
}

// UpdatePlatformPlanTypeReq 更新平台计划类型请求
type UpdatePlatformPlanTypeReq struct {
	PlanType string `json:"plan_type" form:"plan_type" binding:"required,oneof=image_text video"` // 计划类型
}

// PlatformPlanCreateResp 创建平台计划响应
type PlatformPlanCreateResp struct {
	ID int64 `json:"id"` // 计划ID
}

// ========== 转换方法 ==========

// ToPlatformPlanDetailParam 将平台计划详情请求转换为领域参数
func (req PlatformPlanDetailReq) ToPlatformPlanDetailParam() int64 {
	return req.ID
}

// ToPlatformPlanUpdateTypeParam 将更新平台计划类型请求转换为领域参数
func (req PlatformPlanUpdateTypeReq) ToPlatformPlanUpdateTypeParam() domain.UpdatePlanTypeEntity {
	return domain.UpdatePlanTypeEntity{
		ID:       req.ID,
		PlanType: req.PlanType,
	}
}

// ToMarketTargetsDetailParam 将市场目标详情请求转换为领域参数
func (req MarketTargetsDetailReq) ToMarketTargetsDetailParam() domain.MarketTargetsParam {
	return domain.MarketTargetsParam{
		PlatformType: req.PlatformType,
	}
}

// ToPlatformPlanUpdateParam 将更新平台计划请求转换为领域参数
func (req *UpdatePlatformPlanReq) ToPlatformPlanUpdateParam() domain.PlatformPlanUpdateParam {
	return domain.PlatformPlanUpdateParam{
		ID:       req.ID,
		Name:     req.Name,
		PlanType: req.PlanType,
		Remark:   req.Remark,
	}
}

// ToPlatformPlanQueryParam 将平台计划列表请求转换为领域查询参数
func (req *PlatformPlanListReq) ToPlatformPlanQueryParam() domain.PlatformPlanListParam {
	pageSize := req.PageSize
	if pageSize == 0 {
		pageSize = 10 // 默认每页10条
	}

	return domain.PlatformPlanListParam{
		Page:             req.Page,
		PageSize:         pageSize,
		PlatformType:     req.PlatformType,
		PlanName:         req.PlanName,
		AgentID:          req.AgentID,
		MediaID:          req.MediaID,
		MarketTargetName: req.MarketTargetName,
		Keyword:          req.Keyword,
	}
}

// FromPlatformPlanItems 从领域实体列表转换为响应
func FromPlatformPlanItems(total int64, items []domain.PlatformPlanItem) PlatformPlanListResp {
	list := make([]PlatformPlanResp, 0, len(items))
	for _, item := range items {
		list = append(list, PlatformPlanResp{
			ID:               item.ID,
			AgentID:          item.AgentID,
			MediaID:          item.MediaID,
			DataID:           item.DataID,
			Name:             item.Name,
			PlatformType:     item.PlatformType,
			PlanType:         item.PlanType,
			MarketTargetName: item.MarketTargetName,
			CreatedAt:        item.CreatedAt,
			UpdatedAt:        item.UpdatedAt,
		})
	}

	return PlatformPlanListResp{
		Total: total,
		List:  list,
	}
}

// FromMarketTargetsDetail 从领域实体转换为市场目标详情响应
func FromMarketTargetsDetail(targets []domain.MarketTarget) MarketTargetsDetailResp {
	result := make([]MarketTargetItem, 0, len(targets))
	for _, target := range targets {
		result = append(result, MarketTargetItem{
			Value: target.Value,
			Label: target.Label,
		})
	}

	return MarketTargetsDetailResp{
		Targets: result,
	}
}
