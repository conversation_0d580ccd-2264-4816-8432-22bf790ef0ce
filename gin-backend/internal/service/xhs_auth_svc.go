package service

import (
	"context"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/service/domain"

	"go.uber.org/zap"
)

// XHSAuthService 小红书授权服务
type XHSAuthService struct {
	config *config.XiaohongshuConfig
}

// NewXHSAuthService 创建小红书授权服务
func NewXHSAuthService() *XHSAuthService {
	return &XHSAuthService{
		config: &config.AppConfig.Xiaohongshu,
	}
}

// GetAuthUrl 获取小红书授权链接
func (s *XHSAuthService) GetAuthUrl(ctx context.Context) (domain.GetXHSAuthUrlResult, error) {
	zap.L().Info("开始生成小红书授权链接")

	// 检查配置
	if s.config.AppId == "" {
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("小红书应用ID未配置")
	}
	if s.config.RedirectUri == "" {
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("小红书重定向URI未配置")
	}

	// 生成状态参数
	state := domain.GenerateXHSAuthState()

	// 获取默认授权范围
	scopes := domain.GetDefaultXHSAuthScopes()

	// 构建参数
	param := domain.GetXHSAuthUrlParam{
		AppId:       s.config.AppId,
		RedirectUri: s.config.RedirectUri,
		Scopes:      scopes,
		State:       state,
	}

	// 构建授权链接
	authUrl, err := domain.BuildXHSAuthUrl(param)
	if err != nil {
		zap.L().Error("构建小红书授权链接失败", zap.Error(err))
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("构建授权链接失败: %w", err)
	}

	result := domain.GetXHSAuthUrlResult{
		AuthUrl:     authUrl,
		AppId:       param.AppId,
		RedirectUri: param.RedirectUri,
		Scopes:      param.Scopes,
		State:       param.State,
	}

	zap.L().Info("小红书授权链接生成成功",
		zap.String("auth_url", authUrl),
		zap.String("app_id", param.AppId),
		zap.String("redirect_uri", param.RedirectUri),
		zap.String("state", param.State))

	return result, nil
}

// GetAuthUrlWithCustomParams 获取自定义参数的小红书授权链接
func (s *XHSAuthService) GetAuthUrlWithCustomParams(ctx context.Context, customScopes []domain.XHSAuthScope, customState string) (domain.GetXHSAuthUrlResult, error) {
	zap.L().Info("开始生成自定义参数的小红书授权链接",
		zap.Any("custom_scopes", customScopes),
		zap.String("custom_state", customState))

	// 检查配置
	if s.config.AppId == "" {
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("小红书应用ID未配置")
	}
	if s.config.RedirectUri == "" {
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("小红书重定向URI未配置")
	}

	// 使用自定义参数或默认值
	scopes := customScopes
	if len(scopes) == 0 {
		scopes = domain.GetDefaultXHSAuthScopes()
	}

	state := customState
	if state == "" {
		state = domain.GenerateXHSAuthState()
	}

	// 构建参数
	param := domain.GetXHSAuthUrlParam{
		AppId:       s.config.AppId,
		RedirectUri: s.config.RedirectUri,
		Scopes:      scopes,
		State:       state,
	}

	// 构建授权链接
	authUrl, err := domain.BuildXHSAuthUrl(param)
	if err != nil {
		zap.L().Error("构建自定义小红书授权链接失败", zap.Error(err))
		return domain.GetXHSAuthUrlResult{}, fmt.Errorf("构建授权链接失败: %w", err)
	}

	result := domain.GetXHSAuthUrlResult{
		AuthUrl:     authUrl,
		AppId:       param.AppId,
		RedirectUri: param.RedirectUri,
		Scopes:      param.Scopes,
		State:       param.State,
	}

	zap.L().Info("自定义小红书授权链接生成成功",
		zap.String("auth_url", authUrl),
		zap.String("app_id", param.AppId),
		zap.String("redirect_uri", param.RedirectUri),
		zap.String("state", param.State))

	return result, nil
}

// HandleAuthCallback 处理小红书授权回调
func (s *XHSAuthService) HandleAuthCallback(ctx context.Context, param domain.XHSAuthCallbackParam) error {
	zap.L().Info("开始处理小红书授权回调",
		zap.String("code", param.Code),
		zap.String("state", param.State),
		zap.String("error", param.Error))

	// 验证回调参数
	if err := domain.ValidateXHSAuthCallbackParam(param); err != nil {
		zap.L().Error("小红书授权回调参数验证失败", zap.Error(err))
		return fmt.Errorf("回调参数验证失败: %w", err)
	}

	// 验证状态参数
	if !domain.ValidateXHSAuthState(param.State) {
		zap.L().Error("小红书授权状态参数验证失败", zap.String("state", param.State))
		return fmt.Errorf("状态参数验证失败")
	}

	zap.L().Info("小红书授权回调处理成功",
		zap.String("code", param.Code),
		zap.String("state", param.State))

	// 这里可以继续处理授权码，获取访问令牌等
	// 暂时只做参数验证和日志记录

	return nil
}

// GetAccessToken 获取小红书访问令牌
func (s *XHSAuthService) GetAccessToken(ctx context.Context, code string) (domain.XHSAuthTokenResult, error) {
	zap.L().Info("开始获取小红书访问令牌", zap.String("code", code))

	// 构建获取令牌参数
	param := domain.XHSAuthTokenParam{
		AppId:       s.config.AppId,
		Secret:      s.config.Secret,
		Code:        code,
		RedirectUri: s.config.RedirectUri,
	}

	// 验证参数
	if err := domain.ValidateXHSAuthTokenParam(param); err != nil {
		zap.L().Error("获取小红书访问令牌参数验证失败", zap.Error(err))
		return domain.XHSAuthTokenResult{}, fmt.Errorf("参数验证失败: %w", err)
	}

	// TODO: 这里应该调用小红书API获取访问令牌
	// 暂时返回模拟数据
	result := domain.XHSAuthTokenResult{
		AccessToken:  "mock_access_token_" + code,
		RefreshToken: "mock_refresh_token_" + code,
		ExpiresIn:    7200, // 2小时
		TokenType:    "Bearer",
		Scope:        domain.FormatXHSAuthScopes(domain.GetDefaultXHSAuthScopes()),
	}

	zap.L().Info("小红书访问令牌获取成功",
		zap.String("access_token", result.AccessToken),
		zap.String("token_type", result.TokenType),
		zap.Int64("expires_in", result.ExpiresIn))

	return result, nil
}

// ValidateConfig 验证小红书配置
func (s *XHSAuthService) ValidateConfig() error {
	if s.config.AppId == "" {
		return fmt.Errorf("小红书应用ID未配置")
	}
	if s.config.Secret == "" {
		return fmt.Errorf("小红书应用密钥未配置")
	}
	if s.config.RedirectUri == "" {
		return fmt.Errorf("小红书重定向URI未配置")
	}
	return nil
}

// GetConfig 获取小红书配置信息（脱敏）
func (s *XHSAuthService) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"app_id":       s.config.AppId,
		"redirect_uri": s.config.RedirectUri,
		"is_prod":      s.config.IsProd,
		"secret":       "***", // 脱敏处理
	}
}

// GetSupportedScopes 获取支持的授权范围
func (s *XHSAuthService) GetSupportedScopes() []map[string]interface{} {
	scopes := []map[string]interface{}{
		{
			"scope":       string(domain.ScopeReportService),
			"name":        "报表服务",
			"description": "获取广告报表数据的权限",
		},
		{
			"scope":       string(domain.ScopeAdQuery),
			"name":        "广告查询",
			"description": "查询广告信息的权限",
		},
		{
			"scope":       string(domain.ScopeAdManage),
			"name":        "广告管理",
			"description": "管理广告的权限",
		},
		{
			"scope":       string(domain.ScopeAccountManage),
			"name":        "账户管理",
			"description": "管理广告账户的权限",
		},
	}
	return scopes
}
