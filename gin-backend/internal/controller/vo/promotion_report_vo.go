package vo

import "gin-backend/internal/service/domain"

// PromotionReportListReq 推广报表列表请求参数
type PromotionReportListReq struct {
	StartDate  string `form:"start_date" json:"start_date" `
	EndDate    string `form:"end_date" json:"end_date"`
	GroupID    int64  `form:"group_id" json:"group_id"`
	CategoryID int64  `form:"category_id" json:"category_id"`
	Page       int    `form:"page" json:"page" `
	PageSize   int    `form:"page_size" json:"page_size"`
}

// PromotionReportItem 推广报表列表项
type PromotionReportItem struct {
	ID              int64   `json:"id"`
	ReportDate      string  `json:"report_date"`
	GroupID         int64   `json:"group_id"`
	GroupName       string  `json:"group_name"`
	CategoryID      int64   `json:"category_id"`
	Cost            float64 `json:"cost"`
	TotalOrders     int     `json:"total_orders"`
	TotalCommission float64 `json:"total_commission"`
}

// PromotionReportGroupItem 推广报表分组项
type PromotionReportGroupItem struct {
	ID              int64   `json:"id"`
	ReportDate      string  `json:"report_date"`
	GroupID         int64   `json:"group_id"`
	GroupName       string  `json:"group_name"`
	CategoryID      int64   `json:"category_id"`
	Category        string  `json:"category"`
	Cost            float64 `json:"cost"`
	TotalOrders     int     `json:"total_orders"`
	TotalCommission float64 `json:"total_commission"`
}

// PromotionReportListResp 推广报表列表响应
type PromotionReportListResp struct {
	Total int64                 `json:"total"`
	Items []PromotionReportItem `json:"items"`
}

// PromotionReportSummaryResp 推广报表摘要响应
type PromotionReportSummaryResp struct {
	TotalCost   float64 `json:"total_cost"`
	TotalOrders int     `json:"total_orders"`
	AverageROI  float64 `json:"average_roi"`
}

// PromotionReportCostListReq 推广报表费用列表请求参数
type PromotionReportCostListReq struct {
	Page       int    `form:"page" json:"page"`
	Size       int    `form:"size" json:"size"`
	GroupId    int64  `form:"group_id" json:"group_id"`
	CategoryId int64  `form:"category_id" json:"category_id"`
	StartDate  string `form:"start_date" json:"start_date"`
	EndDate    string `form:"end_date" json:"end_date"`
}

// PromotionReportCostItem 推广报表费用列表项
type PromotionReportCostItem struct {
	ID           int64   `json:"id"`
	GroupId      int64   `json:"group_id"`
	GroupName    string  `json:"group_name"`
	ReportDate   string  `json:"report_date"`
	CategoryId   int64   `json:"category_id"`
	CategoryName string  `json:"category_name"`
	Cost         float64 `json:"cost"`
	UpdatedAt    string  `json:"updated_at"`
}

// PromotionReportCostListResp 推广报表费用列表响应
type PromotionReportCostListResp struct {
	Total int64                     `json:"total"`
	List  []PromotionReportCostItem `json:"list"`
}

// TaskListItem 任务列表项
type TaskListItem struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Status    string `json:"status"`
	CreatedAt string `json:"created_at"`
}

// UpdateCostReq 更新费用请求参数
type UpdateCostReq struct {
	ReportDate string  `json:"report_date" binding:"required"`
	GroupId    int64   `json:"group_id" binding:"required"`
	Cost       float64 `json:"cost" binding:"required"`
}

// ImportCostReq 导入费用请求参数
type ImportCostReq struct {
	CategoryId int64 `form:"category_id" binding:"required"`
}

// ModelReportReq 模型报表请求参数
type ModelReportReq struct {
	ModelId    int64 `form:"model_id" binding:"required"`
	CategoryId int64 `form:"category_id"`
	GroupId    int64 `form:"group_id"`
}

// ModelReportItem 模型报表项
type ModelReportItem struct {
	Date              string  `json:"date"`
	TotalOrders       int     `json:"total_orders"`
	Cost              float64 `json:"cost"`
	TotalCommission   float64 `json:"total_commission"`
	NewOrders         int     `json:"new_orders"`
	DailyCost         float64 `json:"daily_cost"`
	AverageCommission float64 `json:"average_commission"`
	PaybackDays       int     `json:"payback_days"`
}

// ModelReportResp 模型报表响应
type ModelReportResp struct {
	List []ModelReportItem `json:"list"`
}

// 转换方法：从VO到Domain

// ToDomainPromotionReportParam 将PromotionReportListReq转换为domain.PromotionReportParam
func (req PromotionReportListReq) ToDomainPromotionReportParam() domain.PromotionReportParam {
	page := 1
	size := 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.PageSize > 0 {
		size = req.PageSize
	}
	return domain.PromotionReportParam{
		StartDate:  req.StartDate,
		EndDate:    req.EndDate,
		GroupID:    req.GroupID,
		CategoryID: req.CategoryID,
		Page:       page,
		PageSize:   size,
	}
}

// ToDomainPromotionReportCostParam 将PromotionReportCostListReq转换为domain.PromotionReportCostListParam
func (req PromotionReportCostListReq) ToDomainPromotionReportCostParam() domain.PromotionReportCostListParam {
	page := 1
	size := 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.Size > 0 {
		size = req.Size
	}
	return domain.PromotionReportCostListParam{
		Page:       page,
		Size:       size,
		GroupId:    req.GroupId,
		CategoryId: req.CategoryId,
		StartDate:  req.StartDate,
		EndDate:    req.EndDate,
	}
}

// ToDomainModelReportParam 将ModelReportReq转换为domain.ModelReportParam
func (req ModelReportReq) ToDomainModelReportParam() domain.ModelReportParam {
	return domain.ModelReportParam{
		ModelId:    req.ModelId,
		CategoryId: req.CategoryId,
		GroupId:    req.GroupId,
	}
}

// 转换方法：从Domain到VO

// NewPromotionReportItemFromDomain 从domain.PromotionReportEntity创建PromotionReportItem
func NewPromotionReportItemFromDomain(entity domain.PromotionReportEntity) PromotionReportItem {
	return PromotionReportItem{
		ID:              entity.ID,
		ReportDate:      entity.ReportDate,
		GroupID:         entity.GroupID,
		GroupName:       entity.GroupName,
		CategoryID:      entity.CategoryID,
		Cost:            entity.Cost,
		TotalOrders:     entity.TotalOrders,
		TotalCommission: entity.TotalCommission,
	}
}

// NewPromotionReportListRespFromDomain 从domain.PromotionReportListResult创建PromotionReportListResp
func NewPromotionReportListRespFromDomain(result domain.PromotionReportListResult) PromotionReportListResp {
	items := make([]PromotionReportItem, 0, len(result.Items))
	for _, item := range result.Items {
		items = append(items, NewPromotionReportItemFromDomain(item))
	}
	return PromotionReportListResp{
		Total: result.Total,
		Items: items,
	}
}

// NewPromotionReportGroupItemFromDomain 从domain.PromotionReportEntity创建PromotionReportGroupItem
func NewPromotionReportGroupItemFromDomain(entity domain.PromotionReportEntity) PromotionReportGroupItem {
	return PromotionReportGroupItem{
		ID:              entity.ID,
		ReportDate:      entity.ReportDate,
		GroupID:         entity.GroupID,
		GroupName:       entity.GroupName,
		CategoryID:      entity.CategoryID,
		Category:        entity.Category,
		Cost:            entity.Cost,
		TotalOrders:     entity.TotalOrders,
		TotalCommission: entity.TotalCommission,
	}
}

// NewPromotionReportSummaryRespFromDomain 从domain.PromotionReportSummary创建PromotionReportSummaryResp
func NewPromotionReportSummaryRespFromDomain(summary domain.PromotionReportSummary) PromotionReportSummaryResp {
	return PromotionReportSummaryResp{
		TotalCost:   summary.TotalCost,
		TotalOrders: summary.TotalOrders,
		AverageROI:  summary.TotalROI,
	}
}

// NewPromotionReportCostItemFromDomain 从domain.PromotionReportCostEntity创建PromotionReportCostItem
func NewPromotionReportCostItemFromDomain(entity domain.PromotionReportCostEntity) PromotionReportCostItem {
	return PromotionReportCostItem{
		ID:           entity.ID,
		GroupId:      entity.GroupId,
		GroupName:    entity.GroupName,
		ReportDate:   entity.ReportDate,
		CategoryId:   entity.CategoryId,
		CategoryName: entity.CategoryName,
		Cost:         entity.Cost,
		UpdatedAt:    entity.UpdatedAt,
	}
}

// NewPromotionReportCostListRespFromDomain 从domain.PromotionReportCostListResult创建PromotionReportCostListResp
func NewPromotionReportCostListRespFromDomain(result domain.PromotionReportCostListResult) PromotionReportCostListResp {
	items := make([]PromotionReportCostItem, 0, len(result.List))
	for _, item := range result.List {
		items = append(items, NewPromotionReportCostItemFromDomain(item))
	}
	return PromotionReportCostListResp{
		Total: result.Total,
		List:  items,
	}
}

// NewTaskListItemFromDomain 从domain.PromotionReportTaskEntity创建TaskListItem
func NewTaskListItemFromDomain(entity domain.PromotionReportTaskEntity) TaskListItem {
	return TaskListItem{
		ID:        entity.ID,
		Name:      entity.Name,
		Status:    entity.Status,
		CreatedAt: entity.CreatedAt,
	}
}

// NewModelReportItemFromDomain 从domain.ModelReportEntity创建ModelReportItem
func NewModelReportItemFromDomain(entity domain.ModelReportEntity) ModelReportItem {
	return ModelReportItem{
		Date:              entity.Date,
		TotalOrders:       entity.TotalOrders,
		Cost:              entity.Cost,
		TotalCommission:   entity.TotalCommission,
		NewOrders:         entity.NewOrders,
		DailyCost:         entity.DailyCost,
		AverageCommission: entity.AverageCommission,
		PaybackDays:       entity.PaybackDays,
	}
}

// NewModelReportRespFromDomain 从domain.ModelReportResult创建ModelReportResp
func NewModelReportRespFromDomain(result domain.ModelReportResult) ModelReportResp {
	items := make([]ModelReportItem, 0, len(result.List))
	for _, item := range result.List {
		items = append(items, NewModelReportItemFromDomain(item))
	}
	return ModelReportResp{
		List: items,
	}
}
