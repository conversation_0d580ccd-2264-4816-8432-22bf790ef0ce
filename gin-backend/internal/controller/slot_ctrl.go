package controller

import (
	"fmt"
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// SlotController 资源位控制器
type SlotController struct {
	slotService       *service.SlotService
	permissionService *service.PermissionService
	roleService       *service.RoleService
}

// NewSlotController 创建资源位控制器实例
func NewSlotController(permissionService *service.PermissionService, roleService *service.RoleService) *SlotController {
	return &SlotController{
		slotService:       service.NewSlotService(),
		permissionService: permissionService,
		roleService:       roleService,
	}
}

// checkPermission 检查用户权限
func (c *SlotController) checkPermission(ctx *gin.Context, permissionCode string) (domain.UserEntity, error) {
	// 从上下文中获取用户实体
	userEntity := middleware.GetCurrentUser(ctx)
	if userEntity.ID == 0 {
		return domain.UserEntity{}, fmt.Errorf("用户未登录")
	}

	hasPermission, err := c.roleService.CheckUserPermission(ctx, userEntity.ID, permissionCode)
	if err != nil {
		return domain.UserEntity{}, fmt.Errorf("权限检查失败: %w", err)
	}

	if !hasPermission {
		return domain.UserEntity{}, fmt.Errorf("权限不足")
	}

	return userEntity, nil
}

// GetSlotList 获取资源位列表
// @Summary 获取资源位列表
// @Description 获取资源位列表，支持分页和筛选
// @Tags 资源位管理
// @Accept json
// @Produce json
// @Param name query string false "资源位名称"
// @Param type query string false "资源位类型"
// @Param audit_status query string false "审核状态"
// @Param media_id query int false "媒体ID"
// @Param user_id query int false "用户ID"
// @Param page query int false "页码"
// @Param size query int false "每页数量"
// @Success 200 {object} response.Response{data=vo.SlotListResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/slots/list [get]
// @Security Bearer
func (c *SlotController) GetSlotList(ctx *gin.Context) {
	// 检查权限
	userInfo, err := c.checkPermission(ctx, "slot:view")
	if err != nil {
		if err.Error() == "用户未登录" {
			response.Error(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Error(ctx, http.StatusForbidden, err.Error())
		}
		return
	}

	// 绑定查询参数
	var req vo.SlotListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	result, err := c.slotService.GetSlotList(ctx, req.ToSlotListParam(), userInfo)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 转换为 vo 对象
	resp := vo.FromDomainResult(result)
	response.Success(ctx, resp)
}

// CreateSlot 创建资源位
// @Summary 创建资源位
// @Description 创建新的资源位
// @Tags 资源位管理
// @Accept json
// @Produce json
// @Param slot body vo.SlotCreateReq true "资源位信息"
// @Success 200 {object} response.Response{data=vo.SlotCreateResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/slots/create [post]
// @Security Bearer
func (c *SlotController) CreateSlot(ctx *gin.Context) {
	// 检查权限
	userInfo, err := c.checkPermission(ctx, "slot:create")
	if err != nil {
		if err.Error() == "用户未登录" {
			response.Error(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Error(ctx, http.StatusForbidden, err.Error())
		}
		return
	}

	// 绑定请求参数
	var req vo.SlotCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	id, err := c.slotService.CreateSlot(ctx, req.ToCreateEntity(), userInfo)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 返回结果
	resp := vo.SlotCreateResp{
		ID: id,
	}

	response.Success(ctx, resp)
}

// UpdateSlot 更新资源位
// @Summary 更新资源位
// @Description 更新资源位信息
// @Tags 资源位管理
// @Accept json
// @Produce json
// @Param slot body vo.SlotUpdateReq true "资源位信息"
// @Success 200 {object} response.Response{data=vo.SlotUpdateResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/slots/update [put]
// @Security Bearer
func (c *SlotController) UpdateSlot(ctx *gin.Context) {
	// 检查权限
	userInfo, err := c.checkPermission(ctx, "slot:edit")
	if err != nil {
		if err.Error() == "用户未登录" {
			response.Error(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Error(ctx, http.StatusForbidden, err.Error())
		}
		return
	}

	// 绑定请求参数
	var req vo.SlotUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	id, err := c.slotService.UpdateSlot(ctx, req.ToUpdateEntity(), userInfo)
	if err != nil {
		// 根据错误类型返回不同的状态码
		if err.Error() == "无权限访问此资源位" || err.Error() == "无权限修改此资源位" {
			response.Error(ctx, http.StatusForbidden, err.Error())
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 返回结果
	resp := vo.SlotUpdateResp{
		ID: id,
	}

	response.Success(ctx, resp)
}

// DeleteSlot 删除资源位
// @Summary 删除资源位
// @Description 删除指定的资源位
// @Tags 资源位管理
// @Accept json
// @Produce json
// @Param id query int true "资源位ID"
// @Success 200 {object} response.Response{data=vo.SlotDeleteResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/slots/delete [delete]
// @Security Bearer
func (c *SlotController) DeleteSlot(ctx *gin.Context) {
	// 检查权限
	userInfo, err := c.checkPermission(ctx, "slot:delete")
	if err != nil {
		if err.Error() == "用户未登录" {
			response.Error(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Error(ctx, http.StatusForbidden, err.Error())
		}
		return
	}

	// 获取资源位ID
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "资源位ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "无效的资源位ID")
		return
	}

	// 调用服务层
	id, err = c.slotService.DeleteSlot(ctx, id, userInfo)
	if err != nil {
		// 根据错误类型返回不同的状态码
		if err.Error() == "无权限访问此资源位" || err.Error() == "无权限删除此资源位" {
			response.Error(ctx, http.StatusForbidden, err.Error())
			return
		}
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 返回结果
	resp := vo.SlotDeleteResp{
		ID: id,
	}

	response.Success(ctx, resp)
}

// AuditSlot 审核资源位
// @Summary 审核资源位
// @Description 审核资源位（管理员权限）
// @Tags 资源位管理
// @Accept json
// @Produce json
// @Param audit body vo.SlotAuditReq true "审核信息"
// @Success 200 {object} response.Response{data=vo.SlotAuditResp}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 403 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/slots/audit [put]
// @Security Bearer
func (c *SlotController) AuditSlot(ctx *gin.Context) {
	// 检查权限
	userInfo, err := c.checkPermission(ctx, "slot:audit")
	if err != nil {
		if err.Error() == "用户未登录" {
			response.Error(ctx, http.StatusUnauthorized, err.Error())
		} else {
			response.Error(ctx, http.StatusForbidden, err.Error())
		}
		return
	}

	// 绑定请求参数
	var req vo.SlotAuditReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	id, err := c.slotService.AuditSlot(ctx, req.ToAuditEntity(), userInfo)
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, err.Error())
		return
	}

	// 返回结果
	resp := vo.SlotAuditResp{
		ID: id,
	}

	response.Success(ctx, resp)
}
