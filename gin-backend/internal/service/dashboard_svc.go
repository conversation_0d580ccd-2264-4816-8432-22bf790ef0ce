package service

import (
	"context"
	"strconv"

	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"

	"gorm.io/gorm"
)

// DashboardService 仪表盘服务
type DashboardService struct {
	db                *gorm.DB
	permissionService *PermissionService
	userService       *UserService
}

// NewDashboardService 创建仪表盘服务实例
func NewDashboardService() *DashboardService {
	return &DashboardService{
		db:                global.DB,
		permissionService: NewPermissionService(),
		userService:       NewUserService(),
	}
}

// GetMetrics 获取仪表盘指标数据
func (s *DashboardService) GetMetrics(req domain.DashboardMetricsParam, userID int, userRole int) (domain.DashboardMetricsEntity, error) {
	return newDashboardMetricsService(s).GetMetrics(req, userID, userRole)
}

// GetAllMediaList 获取所有媒体列表
func (s *DashboardService) GetAllMediaList() ([]struct {
	ID   string
	Name string
}, error) {
	return newDashboardMediaService(s).GetAllMediaList()
}

// GetFilterOptions 获取筛选选项
func (s *DashboardService) GetFilterOptions() (domain.FilterOptionsEntity, error) {
	return newDashboardFilterService(s).GetFilterOptions()
}

// GetProducts 获取产品列表
func (s *DashboardService) GetProducts() (domain.ProductsEntity, error) {
	return newDashboardMediaService(s).GetProducts()
}

// GetTrendData 获取趋势数据
func (s *DashboardService) GetTrendData(req domain.DashboardTrendParam, userID int, userRole int) (domain.TrendEntity, error) {
	return newDashboardTrendService(s).GetTrendData(req, userID, userRole)
}

// GetPlanStats 获取分计划统计数据
func (s *DashboardService) GetPlanStats(req domain.DashboardPlanStatsParam, userID int, userRole int) (domain.PlanStatsEntity, error) {
	return newDashboardStatsService(s).GetPlanStats(req, userID, userRole)
}

// GetMediaList 获取媒体列表
func (s *DashboardService) GetMediaList(req domain.DashboardMediaListParam, userID int, userRole int) (domain.MediaListEntity, error) {
	return newDashboardMediaService(s).GetMediaList(req, userID, userRole)
}

// GetPlans 获取投放计划列表
func (s *DashboardService) GetPlans(req domain.DashboardPlansParam, userID int, userRole int) (domain.PlansEntity, error) {
	return newDashboardMediaService(s).GetPlans(req, userID, userRole)
}

// GetRegionData 获取地域数据
func (s *DashboardService) GetRegionData(req domain.RegionParam, userID int64, userRole int) (domain.RegionEntity, error) {
	return newDashboardStatsService(s).GetRegionData(req, userID, userRole)
}

// GetOrderTypeData 获取订单类型数据
func (s *DashboardService) GetOrderTypeData(req domain.OrderTypeParam, userID int, userRole int) (domain.OrderTypeEntity, error) {
	return newDashboardStatsService(s).GetOrderTypeData(req, userID, userRole)
}

// GetDynamicFilterOptions 获取动态筛选选项
func (s *DashboardService) GetDynamicFilterOptions(req domain.DynamicFilterParam, userID int, userRole int) (domain.DynamicFilterEntity, error) {
	return newDashboardFilterService(s).GetDynamicFilterOptions(req, userID, userRole)
}

// GetUserPermissionDebugInfo 获取用户权限调试信息
func (s *DashboardService) GetUserPermissionDebugInfo(userID int64) (string, []uint64, error) {
	return s.getDataScopeInfo(userID, "dashboard")
}

// getDataScopeInfo 获取数据权限范围信息
func (s *DashboardService) getDataScopeInfo(userID int64, module string) (string, []uint64, error) {
	// 获取数据权限范围
	scope := "all" // 默认所有数据权限
	var departmentUsers []uint64

	// 获取用户角色信息
	user, err := s.userService.GetByID(context.Background(), userID)
	if err != nil {
		return scope, departmentUsers, err
	}

	// 如果是管理员角色，返回所有权限
	if user.Role == 2 || user.Role == 3 {
		return "all", nil, nil
	}

	// 获取用户所在部门的用户
	if user.Department != "" {
		var users []struct {
			ID uint64
		}
		err := s.db.Table("users").
			Where("department = ?", user.Department).
			Select("id").
			Scan(&users).Error

		if err == nil && len(users) > 0 {
			for _, user := range users {
				departmentUsers = append(departmentUsers, user.ID)
			}
			scope = "department"
		} else {
			scope = "personal"
		}
	} else {
		scope = "personal"
	}

	return scope, departmentUsers, nil
}

// ValidateQueryParams 验证查询参数
func (s *DashboardService) ValidateQueryParams(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}) error {
	// 验证类型参数
	if req.Type != "" && req.Type != "1" && req.Type != "2" && req.Type != "3" {
		return nil
	}

	// 验证渠道分类
	if req.Category != "" && req.Category != "alipay" && req.Category != "wechat" && req.Category != "other" {
		return nil
	}

	return nil
}

// GetDailyStatsWithScope 获取带权限控制的每日统计数据
func (s *DashboardService) GetDailyStatsWithScope(req *struct {
	StartDate string
	EndDate   string
	UserID    string
	MediaID   string
	PlanID    string
	ProductID string
	Type      string
	Category  string
}, userID int, userRole int, scope string, departmentUsers []uint64) (domain.DailyStatsData, error) {
	// 构建查询
	query := s.db.Model(&model.AdSlotPlanDailyStats{})
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 应用权限过滤
	if scope == "department" && len(departmentUsers) > 0 {
		query = query.Where("user_id IN (?)", departmentUsers)
	} else if scope == "personal" {
		query = query.Where("user_id = ?", userID)
	}

	// 添加其他过滤条件
	if req.UserID != "" && req.UserID != "0" {
		query = query.Where("user_id = ?", req.UserID)
	}
	if req.MediaID != "" {
		query = query.Where("media_id = ?", req.MediaID)
	}
	if req.PlanID != "" {
		query = query.Where("plan_id = ?", req.PlanID)
	}
	if req.ProductID != "" {
		query = query.Where("product_id = ?", req.ProductID)
	}

	// 根据合作类型过滤
	if req.Type != "" {
		var cooperationType string
		switch req.Type {
		case "1":
			cooperationType = "traffic"
		case "2":
			cooperationType = "cps"
		case "3":
			cooperationType = "dh"
		}

		if cooperationType != "" {
			// 获取对应合作类型的媒体ID
			var mediaIDs []int
			s.db.Raw("SELECT id FROM ad_media WHERE cooperation_type = ?", cooperationType).
				Scan(&mediaIDs)

			if len(mediaIDs) > 0 {
				query = query.Where("media_id IN (?)", mediaIDs)
			}
		}
	}

	// 聚合查询
	var result struct {
		Orders           int     `json:"orders"`
		EstimatedRevenue float64 `json:"estimated_revenue"`
		SettledRevenue   float64 `json:"settled_revenue"`
		Cost             float64 `json:"cost"`
		EstimatedProfit  float64 `json:"estimated_profit"`
		SettledProfit    float64 `json:"settled_profit"`
		ClickPV          int     `json:"click_pv"`
		ClickUV          int     `json:"click_uv"`
	}

	err := query.Select(`
		SUM(bd_orders) as orders,
		SUM(bd_revenue) as estimated_revenue,
		SUM(bd_settle_revenue) as settled_revenue,
		SUM(cost) as cost,
		SUM(bd_profit) as estimated_profit,
		SUM(bd_settle_profit) as settled_profit,
		SUM(elm_click_pv) as click_pv,
		SUM(elm_click_uv) as click_uv
	`).Scan(&result).Error

	if err != nil {
		return domain.DailyStatsData{}, err
	}

	return domain.DailyStatsData{
		Orders:           result.Orders,
		EstimatedRevenue: result.EstimatedRevenue,
		SettledRevenue:   result.SettledRevenue,
		Cost:             result.Cost,
		EstimatedProfit:  result.EstimatedProfit,
		SettledProfit:    result.SettledProfit,
		ClickPV:          result.ClickPV,
		ClickUV:          result.ClickUV,
	}, nil
}

// GetProductList 获取产品列表
func (s *DashboardService) GetProductList() ([]struct {
	ID   string
	Name string
}, error) {
	var products []model.AdProduct
	var result []struct {
		ID   string
		Name string
	}

	err := s.db.Select("id, name").
		Where("status = ?", "active").
		Order("id DESC").
		Find(&products).Error
	if err != nil {
		return result, err
	}

	// 转换为内部格式
	for _, product := range products {
		result = append(result, struct {
			ID   string
			Name string
		}{
			ID:   strconv.FormatUint(uint64(product.ID), 10),
			Name: product.Name,
		})
	}

	return result, nil
}

// 以下是原始函数，维持不变
// ... 其他所有原始函数 ...
