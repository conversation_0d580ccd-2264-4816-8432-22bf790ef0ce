package bootstrap

import (
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDB 初始化数据库，返回关闭函数和数据库连接
func InitDB() (func() error, error) {
	// 初始化数据库连接
	db, err := CreateDB()
	if err != nil {
		return nil, err
	}

	global.RegisterDB(db)

	// 返回一个关闭函数和数据库连接
	return func() error {
		// CloseDB 关闭数据库连接
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}, nil
}

// CreateDB 初始化数据库连接
func CreateDB() (*gorm.DB, error) {
	cfg := config.AppConfig.Database

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.User,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.DBName,
		cfg.Charset,
	)

	var err error
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的sql.DB实例进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}

	// 设置连接池
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	return db, nil
}
