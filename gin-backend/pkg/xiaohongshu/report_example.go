package xiaohongshu

import (
	"fmt"
	"log"
)

// ReportExampleBasic 基础报表使用示例
func ReportExampleBasic() {
	// 1. 创建配置
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	// 2. 创建客户端
	client := NewClient(config)

	// 3. 构建报表请求
	request := NewReportBuilder(1234567890, "2024-01-01", "2024-01-07").
		WithTimeUnit(TimeUnitDay).
		WithPagination(1, 50).
		WithSort("fee", SortDesc).
		Build()

	// 4. 获取报表数据
	accessToken := "your_access_token"
	reportResp, err := client.GetCreativeReport(accessToken, request)
	if err != nil {
		log.Printf("获取创意报表失败: %v", err)
		return
	}

	if !reportResp.Success {
		log.Printf("API返回错误: code=%d, msg=%s", reportResp.Code, reportResp.Msg)
		return
	}

	// 5. 处理报表数据
	fmt.Printf("报表数据获取成功:\n")
	fmt.Printf("  总记录数: %d\n", reportResp.Data.TotalCount)
	fmt.Printf("  数据列表长度: %d\n", len(reportResp.Data.DataList))

	// 打印前几条数据
	for i, data := range reportResp.Data.DataList {
		if i >= 3 { // 只打印前3条
			break
		}
		fmt.Printf("  数据 %d:\n", i+1)
		fmt.Printf("    计划名称: %s\n", data.CampaignName)
		fmt.Printf("    消费金额: %s\n", data.Fee)
		fmt.Printf("    展现量: %s\n", data.Impression)
		fmt.Printf("    点击量: %s\n", data.Click)
		fmt.Printf("    点击率: %s\n", data.CTR)
	}

	// 打印汇总数据
	fmt.Printf("  汇总数据:\n")
	fmt.Printf("    总消费: %s\n", reportResp.Data.AggregationData.Fee)
	fmt.Printf("    总展现: %s\n", reportResp.Data.AggregationData.Impression)
	fmt.Printf("    总点击: %s\n", reportResp.Data.AggregationData.Click)
}

// ReportExampleWithFilters 带过滤条件的报表示例
func ReportExampleWithFilters() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)

	// 创建过滤条件
	filters := []Filter{
		CreateFilterByFee(FilterOperatorGreaterThan, []string{"100"}),     // 消费大于100元
		CreateFilterByClick(FilterOperatorGreaterThan, []string{"10"}),    // 点击量大于10
		CreateFilterByCreativityID([]string{"123456", "789012"}),          // 指定创意ID
	}

	// 构建报表请求
	request := NewReportBuilder(1234567890, "2024-01-01", "2024-01-31").
		WithTimeUnit(TimeUnitSummary).
		WithFilters(filters).
		WithSplitColumns(SplitColumnsCreativity).
		WithSort("fee", SortDesc).
		WithPagination(1, 100).
		Build()

	accessToken := "your_access_token"
	reportResp, err := client.GetCreativeReport(accessToken, request)
	if err != nil {
		log.Printf("获取过滤报表失败: %v", err)
		return
	}

	fmt.Printf("过滤后的报表数据: %d 条记录\n", reportResp.Data.TotalCount)
}

// ReportExampleQuickMethods 快速方法示例
func ReportExampleQuickMethods() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"
	advertiserID := int64(1234567890)

	// 1. 获取昨日报表
	yesterdayRequest := CreateYesterdayReport(advertiserID)
	yesterdayResp, err := client.GetCreativeReport(accessToken, yesterdayRequest)
	if err != nil {
		log.Printf("获取昨日报表失败: %v", err)
	} else {
		fmt.Printf("昨日报表: 总消费 %s, 总展现 %s\n",
			yesterdayResp.Data.AggregationData.Fee,
			yesterdayResp.Data.AggregationData.Impression)
	}

	// 2. 获取最近7天报表
	last7DaysRequest := CreateLast7DaysReport(advertiserID)
	last7DaysResp, err := client.GetCreativeReport(accessToken, last7DaysRequest)
	if err != nil {
		log.Printf("获取最近7天报表失败: %v", err)
	} else {
		fmt.Printf("最近7天报表: %d 条记录\n", last7DaysResp.Data.TotalCount)
	}

	// 3. 获取最近30天报表
	last30DaysRequest := CreateLast30DaysReport(advertiserID)
	last30DaysResp, err := client.GetCreativeReport(accessToken, last30DaysRequest)
	if err != nil {
		log.Printf("获取最近30天报表失败: %v", err)
	} else {
		fmt.Printf("最近30天报表: %d 条记录\n", last30DaysResp.Data.TotalCount)
	}
}

// ReportExamplePagination 分页获取示例
func ReportExamplePagination() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"
	advertiserID := int64(1234567890)

	pageSize := 50
	pageNum := 1
	var allData []ReportData

	for {
		// 构建分页请求
		request := NewReportBuilder(advertiserID, "2024-01-01", "2024-01-31").
			WithTimeUnit(TimeUnitDay).
			WithPagination(pageNum, pageSize).
			WithSort("fee", SortDesc).
			Build()

		// 获取当前页数据
		reportResp, err := client.GetCreativeReport(accessToken, request)
		if err != nil {
			log.Printf("获取第%d页数据失败: %v", pageNum, err)
			break
		}

		if !reportResp.Success {
			log.Printf("API返回错误: code=%d, msg=%s", reportResp.Code, reportResp.Msg)
			break
		}

		// 添加到总数据中
		allData = append(allData, reportResp.Data.DataList...)

		fmt.Printf("已获取第%d页数据，本页%d条记录\n", pageNum, len(reportResp.Data.DataList))

		// 检查是否还有更多数据
		if len(reportResp.Data.DataList) < pageSize {
			break // 最后一页
		}

		pageNum++
	}

	fmt.Printf("分页获取完成，总共获取 %d 条记录\n", len(allData))
}

// ReportExampleMetricsAnalysis 指标分析示例
func ReportExampleMetricsAnalysis() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)
	accessToken := "your_access_token"

	// 获取报表数据
	request := CreateLast7DaysReport(1234567890)
	reportResp, err := client.GetCreativeReport(accessToken, request)
	if err != nil {
		log.Printf("获取报表失败: %v", err)
		return
	}

	if !reportResp.Success {
		log.Printf("API返回错误: %v", reportResp.Msg)
		return
	}

	// 分析基础指标
	fmt.Println("=== 基础指标分析 ===")
	basicMetrics := GetBasicMetrics()
	for _, metric := range basicMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 分析互动指标
	fmt.Println("\n=== 互动指标分析 ===")
	interactionMetrics := GetInteractionMetrics()
	for _, metric := range interactionMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 分析转化指标
	fmt.Println("\n=== 转化指标分析 ===")
	conversionMetrics := GetConversionMetrics()
	for _, metric := range conversionMetrics {
		fmt.Printf("指标: %s\n", metric)
	}

	// 打印实际数据示例
	if len(reportResp.Data.DataList) > 0 {
		data := reportResp.Data.DataList[0]
		fmt.Println("\n=== 实际数据示例 ===")
		fmt.Printf("计划名称: %s\n", data.CampaignName)
		fmt.Printf("消费金额: %s 元\n", data.Fee)
		fmt.Printf("展现量: %s\n", data.Impression)
		fmt.Printf("点击量: %s\n", data.Click)
		fmt.Printf("点击率: %s%%\n", data.CTR)
		fmt.Printf("点赞量: %s\n", data.Like)
		fmt.Printf("评论量: %s\n", data.Comment)
		fmt.Printf("收藏量: %s\n", data.Collect)
	}
}

// ReportExampleErrorHandling 错误处理示例
func ReportExampleErrorHandling() {
	config := &Config{
		AppId:  "your_app_id",
		Secret: "your_secret",
		IsProd: false,
	}

	client := NewClient(config)

	// 测试无效的访问令牌
	invalidToken := "invalid_token"
	request := CreateYesterdayReport(1234567890)

	reportResp, err := client.GetCreativeReport(invalidToken, request)
	if err != nil {
		fmt.Printf("网络或解析错误: %v\n", err)
		return
	}

	if !reportResp.Success {
		fmt.Printf("API业务错误: code=%d, msg=%s\n", reportResp.Code, reportResp.Msg)
		
		// 根据错误码进行不同处理
		switch reportResp.Code {
		case 40001:
			fmt.Println("处理建议: 访问令牌无效，请重新获取")
		case 40002:
			fmt.Println("处理建议: 访问令牌过期，请刷新令牌")
		case 40003:
			fmt.Println("处理建议: 权限不足，请检查广告主授权")
		default:
			fmt.Printf("处理建议: 其他错误，请查看错误信息: %s\n", reportResp.Msg)
		}
		return
	}

	fmt.Println("报表获取成功")
}
