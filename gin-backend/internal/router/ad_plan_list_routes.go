package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupAdPlanListRoutes 设置计划列表路由
func SetupAdPlanListRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewAdPlanListController()

	// 计划列表相关路由
	adPlans := router.Group("/ad-plans")
	{
		// 获取计划列表
		adPlans.GET("", ctrl.GetPlanList)
		
		// 获取计划统计信息
		adPlans.GET("/stats", ctrl.GetPlanStats)
		
		// 导出计划列表
		adPlans.POST("/export", ctrl.ExportPlanList)
		
		// 获取计划详情
		adPlans.GET("/:plan_id", ctrl.GetPlanDetail)
		
		// 获取账号列表（用于筛选）
		adPlans.GET("/accounts", ctrl.GetAccountList)
	}
}
