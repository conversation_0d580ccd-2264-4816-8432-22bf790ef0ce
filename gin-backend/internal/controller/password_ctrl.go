package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PasswordController struct {
	passwordService *service.PasswordService
}

func NewPasswordController() *PasswordController {
	return &PasswordController{
		passwordService: service.NewPasswordService(),
	}
}

// Create 创建口令组及口令
// @Summary 创建口令组及口令
// @Description 创建口令组及其关联的口令
// @Tags 口令管理
// @Accept json
// @Produce json
// @Param request body vo.PasswordCreateReq true "创建请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/password/create [post]
func (c *PasswordController) Create(ctx *gin.Context) {
	var req vo.PasswordCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用转换方法将VO转为domain实体
	entity := req.ToPasswordEntity()

	result, err := c.passwordService.CreateWithGroup(ctx.Request.Context(), entity)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 使用转换方法将domain结果转为VO
	respData := vo.FromPasswordActionResult(result)

	response.Success(ctx, respData)
}

// List 获取口令组列表
// @Summary 获取口令组列表
// @Description 获取口令组列表，支持分页和筛选
// @Tags 口令管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(20)
// @Param size query int false "每页数量(兼容参数)" default(20)
// @Param groupName query string false "组名称(模糊搜索)"
// @Param categoryId query int false "分类ID"
// @Success 200 {object} response.Response{data=vo.PasswordListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/password/list [get]
func (c *PasswordController) List(ctx *gin.Context) {
	var req vo.PasswordListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用转换方法将VO转为domain参数
	param := req.ToPasswordQueryParam()

	result, err := c.passwordService.List(ctx.Request.Context(), param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 使用转换方法将domain结果转为VO
	respData := vo.FromPasswordResult(result)

	response.Success(ctx, respData)
}

// Update 更新口令组
// @Summary 更新口令组
// @Description 更新口令组及其关联的口令
// @Tags 口令管理
// @Accept json
// @Produce json
// @Param id path int true "口令组ID"
// @Param request body vo.PasswordUpdateReq true "更新请求"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/password/{id} [put]
func (c *PasswordController) Update(ctx *gin.Context) {
	// 获取ID参数
	idStr := ctx.Param("id")
	idUint64, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的口令组ID")
		return
	}
	id := uint(idUint64)

	var req vo.PasswordUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用转换方法将VO转为domain实体
	entity := req.ToPasswordEntity()

	result, err := c.passwordService.Update(ctx.Request.Context(), id, entity)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 使用转换方法将domain结果转为VO
	respData := vo.FromPasswordActionResultToUpdateResp(result)

	response.Success(ctx, respData)
}

// Delete 删除口令组
// @Summary 删除口令组
// @Description 删除口令组及其关联的口令
// @Tags 口令管理
// @Produce json
// @Param id path int true "口令组ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/password/{id} [delete]
func (c *PasswordController) Delete(ctx *gin.Context) {
	// 获取ID参数
	idStr := ctx.Param("id")
	idUint64, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的口令组ID")
		return
	}
	id := uint(idUint64)

	result, err := c.passwordService.Delete(ctx.Request.Context(), id)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 使用转换方法将domain结果转为VO
	respData := vo.FromPasswordActionResultToDeleteResp(result)

	response.Success(ctx, respData)
}

// OriList 获取原始口令列表
// @Summary 获取原始口令列表
// @Description 获取原始口令列表
// @Tags 口令管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(20)
// @Param categoryId query []int false "分类ID数组"
// @Success 200 {object} response.Response{data=vo.PasswordOriListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/password/oriList [get]
func (c *PasswordController) OriList(ctx *gin.Context) {
	var req vo.PasswordOriListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, "参数错误: "+err.Error())
		return
	}

	// 使用转换方法将VO转为domain参数
	param := req.ToPasswordOriQueryParam()

	result, err := c.passwordService.OriList(ctx.Request.Context(), param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 使用转换方法将domain结果转为VO
	respData := vo.FromPasswordOriResult(result)

	response.Success(ctx, respData)
}
