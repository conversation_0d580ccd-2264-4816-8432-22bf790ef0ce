package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupXHSAuthRoutes 设置小红书授权路由
func SetupXHSAuthRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewXHSAuthController()

	// 小红书授权相关路由
	xhsAuth := router.Group("/xhs/auth")
	{
		// 获取授权链接
		xhsAuth.GET("/url", ctrl.GetAuthUrlSimple)    // GET方式，使用默认参数
		xhsAuth.POST("/url", ctrl.GetAuthUrl)         // POST方式，支持自定义参数
		
		// 授权回调处理
		xhsAuth.GET("/callback", ctrl.HandleAuthCallback)
		
		// 配置相关
		xhsAuth.GET("/config", ctrl.GetAuthConfig)       // 获取配置信息
		xhsAuth.GET("/validate", ctrl.ValidateAuthConfig) // 验证配置
		
		// 授权范围相关
		xhsAuth.GET("/scopes", ctrl.GetSupportedScopes) // 获取支持的授权范围
	}
}
