package model

import (
	"time"
)

// XHSRealtimeReports 小红书创意层级实时报表数据模型
type XHSRealtimeReports struct {
	Id        int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime" json:"updated_at"`

	// 账号信息
	AccountID   int64  `gorm:"not null;index:idx_account_time" json:"account_id"`   // 账号ID
	AccountName string `gorm:"size:255;not null" json:"account_name"`              // 账号名称

	// 业务字段
	CampaignID     string `gorm:"size:100;not null;index:idx_campaign" json:"campaign_id"`     // 计划ID
	CampaignName   string `gorm:"size:255;not null" json:"campaign_name"`                      // 计划名称
	UnitID         string `gorm:"size:100;not null;index:idx_unit" json:"unit_id"`             // 单元ID
	UnitName       string `gorm:"size:255;not null" json:"unit_name"`                          // 单元名称
	CreativityID   string `gorm:"size:100;not null;index:idx_creativity" json:"creativity_id"` // 创意ID
	CreativityName string `gorm:"size:500;not null" json:"creativity_name"`                    // 创意名称
	NoteID         string `gorm:"size:100" json:"note_id"`                                     // 笔记ID
	
	// 解析后的业务字段
	Title         string `gorm:"size:255" json:"title"`          // 标题
	Pwd           string `gorm:"size:255" json:"pwd"`            // 口令
	ContentPeople string `gorm:"size:100" json:"content_people"` // 内容人员
	PitcherName   string `gorm:"size:100" json:"pitcher_name"`   // 投手姓名

	// 创意属性
	CreativityImage string `gorm:"size:500" json:"creativity_image"` // 创意图片URL
	PageID          string `gorm:"size:100" json:"page_id"`          // 落地页ID
	ItemID          string `gorm:"size:100" json:"item_id"`          // 商品ID
	LiveRedID       string `gorm:"size:100" json:"live_red_id"`      // 直播间ID

	// 地域信息
	CountryName string `gorm:"size:100" json:"country_name"` // 国家
	Province    string `gorm:"size:100" json:"province"`     // 省份
	City        string `gorm:"size:100" json:"city"`         // 城市
	NoteUserID  string `gorm:"size:100" json:"note_user_id"` // 笔记作者ID

	// 枚举字段
	Placement       int8 `gorm:"not null;index:idx_placement" json:"placement"`        // 广告类型
	OptimizeTarget  int8 `gorm:"default:0" json:"optimize_target"`                     // 优化目标
	PromotionTarget int8 `gorm:"default:0" json:"promotion_target"`                    // 推广标的
	BiddingStrategy int8 `gorm:"default:0" json:"bidding_strategy"`                    // 出价方式
	BuildType       int8 `gorm:"default:0" json:"build_type"`                          // 搭建类型
	MarketingTarget int8 `gorm:"default:0" json:"marketing_target"`                    // 营销诉求

	// 统计时间（实时数据的时间戳）
	Time time.Time `gorm:"not null;index:idx_account_time" json:"time"`

	// 基础指标
	Fee        float64 `gorm:"type:decimal(15,2);default:0" json:"fee"`        // 消费金额
	Impression int64   `gorm:"default:0" json:"impression"`                    // 展现量
	Click      int64   `gorm:"default:0" json:"click"`                         // 点击量
	CTR        float64 `gorm:"type:decimal(10,4);default:0" json:"ctr"`        // 点击率
	ACP        float64 `gorm:"type:decimal(10,4);default:0" json:"acp"`        // 平均点击成本
	CPM        float64 `gorm:"type:decimal(10,4);default:0" json:"cpm"`        // 千次曝光成本

	// 互动指标
	Like              int64   `gorm:"default:0" json:"like"`                // 点赞量
	Comment           int64   `gorm:"default:0" json:"comment"`             // 评论量
	Collect           int64   `gorm:"default:0" json:"collect"`             // 收藏量
	Follow            int64   `gorm:"default:0" json:"follow"`              // 关注量
	Share             int64   `gorm:"default:0" json:"share"`               // 分享量
	Interaction       int64   `gorm:"default:0" json:"interaction"`         // 互动量
	CPI               float64 `gorm:"type:decimal(10,4);default:0" json:"cpi"` // 平均互动成本
	ActionButtonClick int64   `gorm:"default:0" json:"action_button_click"` // 行动按钮点击量
	ActionButtonCTR   float64 `gorm:"type:decimal(10,4);default:0" json:"action_button_ctr"` // 行动按钮点击率
	Screenshot        int64   `gorm:"default:0" json:"screenshot"`          // 截图次数
	PicSave           int64   `gorm:"default:0" json:"pic_save"`            // 保存图片次数
	ReservePV         int64   `gorm:"default:0" json:"reserve_pv"`          // 预告组件点击量

	// 直播间互动指标
	ClkLiveEntryPV      int64   `gorm:"default:0" json:"clk_live_entry_pv"`       // 直播间观看次数
	ClkLiveEntryPVCost  float64 `gorm:"type:decimal(10,4);default:0" json:"clk_live_entry_pv_cost"` // 直播间观看成本
	ClkLiveAvgViewTime  int32   `gorm:"default:0" json:"clk_live_avg_view_time"`  // 直播间平均停留时长(秒)
	ClkLiveAllFollow    int64   `gorm:"default:0" json:"clk_live_all_follow"`     // 直播间新增粉丝量
	ClkLive5sEntryPV    int64   `gorm:"default:0" json:"clk_live_5s_entry_pv"`    // 直播间有效观看次数
	ClkLive5sEntryUVCost float64 `gorm:"type:decimal(10,4);default:0" json:"clk_live_5s_entry_uv_cost"` // 直播间有效观看成本
	ClkLiveComment      int64   `gorm:"default:0" json:"clk_live_comment"`        // 直播间评论次数

	// 笔记种草指标
	SearchCmtClick        int64   `gorm:"default:0" json:"search_cmt_click"`         // 搜索组件点击量
	SearchCmtClickCVR     float64 `gorm:"type:decimal(10,4);default:0" json:"search_cmt_click_cvr"` // 搜索组件点击转化率
	SearchCmtAfterRead    int64   `gorm:"default:0" json:"search_cmt_after_read"`    // 搜后阅读量
	SearchCmtAfterReadAvg float64 `gorm:"type:decimal(10,4);default:0" json:"search_cmt_after_read_avg"` // 平均搜后阅读笔记篇数
	IUserNum              int64   `gorm:"default:0" json:"i_user_num"`               // 新增种草人群
	TIUserNum             int64   `gorm:"default:0" json:"ti_user_num"`              // 新增深度种草人群
	IUserPrice            float64 `gorm:"type:decimal(10,4);default:0" json:"i_user_price"` // 新增种草人群成本
	TIUserPrice           float64 `gorm:"type:decimal(10,4);default:0" json:"ti_user_price"` // 新增深度种草人群成本

	// 电商转化指标 - 购买兴趣
	GoodsVisit       int64   `gorm:"default:0" json:"goods_visit"`        // 进店访问量
	GoodsVisitPrice  float64 `gorm:"type:decimal(10,4);default:0" json:"goods_visit_price"` // 进店访问成本
	SellerVisit      int64   `gorm:"default:0" json:"seller_visit"`       // 商品访客量
	SellerVisitPrice float64 `gorm:"type:decimal(10,4);default:0" json:"seller_visit_price"` // 商品访客成本
	ShoppingCartAdd  int64   `gorm:"default:0" json:"shopping_cart_add"`  // 商品加购量
	AddCartPrice     float64 `gorm:"type:decimal(10,4);default:0" json:"add_cart_price"` // 商品加购成本

	// 电商转化指标 - 7日转化
	PresaleOrderNum7D    int64   `gorm:"default:0" json:"presale_order_num_7d"`     // 7日预售订单量
	PresaleOrderGMV7D    float64 `gorm:"type:decimal(15,2);default:0" json:"presale_order_gmv_7d"` // 7日预售订单金额
	GoodsOrder           int64   `gorm:"default:0" json:"goods_order"`              // 7日下单订单量
	GoodsOrderPrice      float64 `gorm:"type:decimal(10,4);default:0" json:"goods_order_price"` // 7日下单订单成本
	RGMV                 float64 `gorm:"type:decimal(15,2);default:0" json:"rgmv"`  // 7日下单金额
	ROI                  float64 `gorm:"type:decimal(10,4);default:0" json:"roi"`   // 7日下单ROI
	SuccessGoodsOrder    int64   `gorm:"default:0" json:"success_goods_order"`      // 7日支付订单量
	ClickOrderCVR        float64 `gorm:"type:decimal(10,4);default:0" json:"click_order_cvr"` // 7日支付转化率
	PurchaseOrderPrice7D float64 `gorm:"type:decimal(10,4);default:0" json:"purchase_order_price_7d"` // 7日支付订单成本
	PurchaseOrderGMV7D   float64 `gorm:"type:decimal(15,2);default:0" json:"purchase_order_gmv_7d"` // 7日支付金额
	PurchaseOrderROI7D   float64 `gorm:"type:decimal(10,4);default:0" json:"purchase_order_roi_7d"` // 7日支付ROI

	// 销售线索指标
	Leads                 int64   `gorm:"default:0" json:"leads"`                   // 表单提交量
	LeadsCPL              float64 `gorm:"type:decimal(10,4);default:0" json:"leads_cpl"` // 表单成本
	LandingPageVisit      int64   `gorm:"default:0" json:"landing_page_visit"`      // 落地页访问量
	LeadsButtonImpression int64   `gorm:"default:0" json:"leads_button_impression"` // 表单按钮曝光量
	ValidLeads            int64   `gorm:"default:0" json:"valid_leads"`             // 有效表单
	ValidLeadsCPL         float64 `gorm:"type:decimal(10,4);default:0" json:"valid_leads_cpl"` // 有效表单成本
	LeadsCVR              float64 `gorm:"type:decimal(10,4);default:0" json:"leads_cvr"` // 表单转化率
	PhoneCallCnt          int64   `gorm:"default:0" json:"phone_call_cnt"`          // 电话拨打
	PhoneCallSuccCnt      int64   `gorm:"default:0" json:"phone_call_succ_cnt"`     // 电话接通
	WechatCopyCnt         int64   `gorm:"default:0" json:"wechat_copy_cnt"`         // 微信复制
	WechatCopySuccCnt     int64   `gorm:"default:0" json:"wechat_copy_succ_cnt"`    // 微信加为好友
	IdentityCertiCnt      int64   `gorm:"default:0" json:"identity_certi_cnt"`      // 身份认证
	CommodityBuyCnt       int64   `gorm:"default:0" json:"commodity_buy_cnt"`       // 商品购买

	// 私信营销指标
	MessageUser            int64   `gorm:"default:0" json:"message_user"`             // 私信咨询人数
	Message                int64   `gorm:"default:0" json:"message"`                  // 私信咨询条数
	MessageConsult         int64   `gorm:"default:0" json:"message_consult"`          // 私信咨询数
	MessageFstReplyTimeAvg int32   `gorm:"default:0" json:"message_fst_reply_time_avg"` // 平均响应时长(秒)
	InitiativeMessage      int64   `gorm:"default:0" json:"initiative_message"`       // 私信开口数
	MessageConsultCPL      float64 `gorm:"type:decimal(10,4);default:0" json:"message_consult_cpl"` // 私信咨询成本
	InitiativeMessageCPL   float64 `gorm:"type:decimal(10,4);default:0" json:"initiative_message_cpl"` // 私信开口成本
	MsgLeadsNum            int64   `gorm:"default:0" json:"msg_leads_num"`            // 私信留资数
	MsgLeadsCost           float64 `gorm:"type:decimal(10,4);default:0" json:"msg_leads_cost"` // 私信留资成本
}

// TableName 指定表名
func (XHSRealtimeReports) TableName() string {
	return "xhs_realtime_reports"
}
