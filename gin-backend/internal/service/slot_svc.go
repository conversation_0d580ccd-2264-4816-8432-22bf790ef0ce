package service

import (
	"context"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"time"

	"gorm.io/gorm"
)

// SlotService 资源位服务
type SlotService struct {
	db               *gorm.DB
	dataScopeService *DataScopeService
}

// NewSlotService 创建资源位服务实例
func NewSlotService() *SlotService {
	return &SlotService{
		db:               global.DB,
		dataScopeService: NewDataScopeService(),
	}
}

// GetSlotList 获取资源位列表
func (s *SlotService) GetSlotList(ctx context.Context, param domain.SlotListParam, userInfo domain.UserEntity) (domain.SlotListResult, error) {
	var result domain.SlotListResult
	var slots []model.Slot
	var total int64

	// 构建查询条件
	query := s.db.Model(&model.Slot{}).Where("deleted_at IS NULL")

	// 添加查询条件
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Type != "" {
		query = query.Where("type = ?", param.Type)
	}
	if param.AuditStatus != "" {
		query = query.Where("audit_status = ?", param.AuditStatus)
	}
	if param.MediaID != 0 {
		query = query.Where("media_id = ?", param.MediaID)
	}

	// 数据权限控制
	if userInfo.Role != 3 {
		query = query.Where("user_id = ?", userInfo.ID)
	}
	if param.UserID != 0 {
		query = query.Where("user_id = ?", param.UserID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return result, err
	}

	// 分页查询
	offset := (param.Page - 1) * param.Size
	if err := query.Offset(offset).Limit(param.Size).Order("id DESC").Find(&slots).Error; err != nil {
		return result, err
	}

	// 构建结果
	slotEntities := make([]domain.SlotEntity, 0, len(slots))
	for _, slot := range slots {
		createdAt := ""
		updatedAt := ""
		if !slot.CreatedAt.IsZero() {
			createdAt = slot.CreatedAt.Format("2006-01-02 15:04:05")
		}
		if !slot.UpdatedAt.IsZero() {
			updatedAt = slot.UpdatedAt.Format("2006-01-02 15:04:05")
		}

		// 获取创建人和更新人名称
		var createdBy, updatedBy string
		if slot.CreatedBy != 0 {
			var creator model.User
			if err := s.db.Model(&model.User{}).Where("id = ?", slot.CreatedBy).First(&creator).Error; err == nil {
				createdBy = creator.RealName
			}
		}
		if slot.UpdatedBy != 0 {
			var updater model.User
			if err := s.db.Model(&model.User{}).Where("id = ?", slot.UpdatedBy).First(&updater).Error; err == nil {
				updatedBy = updater.RealName
			}
		}

		slotEntities = append(slotEntities, domain.SlotEntity{
			ID:            slot.ID,
			Code:          slot.Code,
			Name:          slot.Name,
			Type:          slot.Type,
			TypeText:      domain.GetTypeText(slot.Type),
			MediaID:       slot.MediaID,
			MediaName:     slot.MediaName,
			UserID:        slot.UserID,
			Description:   slot.Remark,
			ScreenshotURL: slot.ScreenshotURL,
			VideoURL:      slot.VideoURL,
			AuditStatus:   slot.AuditStatus,
			RejectReason:  slot.RejectReason,
			LeakRate:      slot.LeakRate,
			DiscountRate:  slot.DiscountRate,
			Remark:        slot.Remark,
			CreatedAt:     createdAt,
			CreatedBy:     createdBy,
			UpdatedAt:     updatedAt,
			UpdatedBy:     updatedBy,
		})
	}

	result.Total = total
	result.List = slotEntities
	return result, nil
}

// CreateSlot 创建资源位
func (s *SlotService) CreateSlot(ctx context.Context, entity domain.SlotCreateEntity, userInfo domain.UserEntity) (int64, error) {

	// 生成资源位编号
	code := s.generateSlotCode()

	// 验证媒体是否存在
	var media model.Media
	if err := s.db.Where("id = ?", entity.MediaID).First(&media).Error; err != nil {
		return 0, fmt.Errorf("媒体不存在")
	}

	// 创建资源位
	slot := model.Slot{
		Code:          code,
		Name:          entity.Name,
		Type:          entity.Type,
		UserID:        userInfo.ID,
		MediaID:       entity.MediaID,
		MediaName:     media.Name,
		ScreenshotURL: entity.ScreenshotURL,
		VideoURL:      entity.VideoURL,
		AuditStatus:   domain.SlotAuditStatusPending, // 默认待审核
		LeakRate:      entity.LeakRate,
		DiscountRate:  entity.DiscountRate,
		Remark:        entity.Remark,
	}

	// 设置创建人和更新人
	now := time.Now()
	userID := userInfo.ID
	slot.CreatedBy = userID
	slot.UpdatedBy = userID
	slot.CreatedAt = now
	slot.UpdatedAt = now

	// 保存到数据库
	if err := s.db.Create(&slot).Error; err != nil {
		return 0, err
	}

	return slot.ID, nil
}

// UpdateSlot 更新资源位
func (s *SlotService) UpdateSlot(ctx context.Context, entity domain.SlotUpdateEntity, userInfo domain.UserEntity) (int64, error) {

	// 检查资源位是否存在
	var slot model.Slot
	if err := s.db.Where("id = ?", entity.ID).First(&slot).Error; err != nil {
		return 0, fmt.Errorf("资源位不存在")
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, entity.ID, userInfo, "self"); err != nil {
		return 0, err
	}

	// 验证媒体是否存在
	var media model.Media
	if entity.MediaID != slot.MediaID {
		if err := s.db.Where("id = ?", entity.MediaID).First(&media).Error; err != nil {
			return 0, fmt.Errorf("媒体不存在")
		}
	} else {
		media.Name = slot.MediaName
	}

	// 更新资源位
	updates := map[string]interface{}{
		"name":           entity.Name,
		"type":           entity.Type,
		"media_id":       entity.MediaID,
		"media_name":     media.Name,
		"screenshot_url": entity.ScreenshotURL,
		"video_url":      entity.VideoURL,
		"leak_rate":      entity.LeakRate,
		"discount_rate":  entity.DiscountRate,
		"remark":         entity.Remark,
		"updated_by":     userInfo.ID,
		"updated_at":     time.Now(),
	}

	if err := s.db.Model(&slot).Updates(updates).Error; err != nil {
		return 0, err
	}

	return entity.ID, nil
}

// DeleteSlot 删除资源位
func (s *SlotService) DeleteSlot(ctx context.Context, id int64, userInfo domain.UserEntity) (int64, error) {

	// 检查资源位是否存在
	var slot model.Slot
	if err := s.db.Where("id = ?", id).First(&slot).Error; err != nil {
		return 0, fmt.Errorf("资源位不存在")
	}

	// 检查数据权限
	if err := s.checkDataPermission(ctx, id, userInfo, "self"); err != nil {
		return 0, err
	}

	// 软删除
	now := time.Now()
	if err := s.db.Model(&slot).Update("deleted_at", &now).Error; err != nil {
		return 0, err
	}

	return id, nil
}

// AuditSlot 审核资源位
func (s *SlotService) AuditSlot(ctx context.Context, entity domain.SlotAuditEntity, userInfo domain.UserEntity) (int64, error) {

	// 检查审核状态是否有效
	if !s.isValidAuditStatus(entity.AuditStatus) {
		return 0, fmt.Errorf("无效的审核状态")
	}

	// 检查资源位是否存在
	var slot model.Slot
	if err := s.db.Where("id = ?", entity.ID).First(&slot).Error; err != nil {
		return 0, fmt.Errorf("资源位不存在")
	}

	// 更新审核状态
	updates := map[string]interface{}{
		"audit_status":  entity.AuditStatus,
		"reject_reason": entity.RejectReason,
		"updated_by":    userInfo.ID,
		"updated_at":    time.Now(),
	}

	if err := s.db.Model(&slot).Updates(updates).Error; err != nil {
		return 0, err
	}

	return entity.ID, nil
}

// isValidSlotType 验证资源位类型是否有效
func (s *SlotService) isValidSlotType(slotType string) bool {
	validTypes := map[string]bool{
		domain.SlotTypeWechatH5:     true,
		domain.SlotTypeWechatMP:     true,
		domain.SlotTypeWechatPlugin: true,
		domain.SlotTypeAlipayMP:     true,
		domain.SlotTypeAlipayH5:     true,
		domain.SlotTypeOther:        true,
	}
	return validTypes[slotType]
}

// isValidAuditStatus 验证审核状态是否有效
func (s *SlotService) isValidAuditStatus(status string) bool {
	validStatus := map[string]bool{
		domain.SlotAuditStatusPending:  true,
		domain.SlotAuditStatusApproved: true,
		domain.SlotAuditStatusRejected: true,
	}
	return validStatus[status]
}

// checkDataPermission 检查数据权限
func (s *SlotService) checkDataPermission(ctx context.Context, id int64, userInfo domain.UserEntity, dataScope string) error {
	var count int64
	query := s.db.Model(&model.Slot{}).Where("id = ?", id)

	// 根据数据权限添加查询条件
	switch dataScope {
	case "self":
		query = query.Where("user_id = ?", userInfo.ID)
	case "dept":
		// 这里可以根据部门信息添加查询条件
		// 暂时使用用户ID作为筛选条件
		query = query.Where("user_id = ?", userInfo.ID)
	case "all":
		// 不添加额外条件，管理员可以查看所有数据
		if userInfo.Role != 3 {
			query = query.Where("user_id = ?", userInfo.ID)
		}
	default:
		query = query.Where("user_id = ?", userInfo.ID)
	}

	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("检查数据权限失败: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("无权限访问此资源位")
	}

	return nil
}

// generateSlotCode 生成资源位编号
func (s *SlotService) generateSlotCode() string {
	var count int64
	s.db.Model(&model.Slot{}).Count(&count)
	return fmt.Sprintf("SLOT%06d", count+1)
}
