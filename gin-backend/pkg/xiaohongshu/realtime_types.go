package xiaohongshu

// ==================== 创意层级实时报表相关类型 ====================

// CreativityRealtimeReportRequest 创意层级实时报表请求参数
type CreativityRealtimeReportRequest struct {
	AdvertiserID              int64  `json:"advertiser_id"`                          // 广告主ID（必填）
	PageNum                   int    `json:"page_num,omitempty"`                     // 页数（默认1）
	PageSize                  int    `json:"page_size,omitempty"`                    // 页大小（默认20，最大100）
	StartDate                 string `json:"start_date"`                             // 开始时间，格式yyyy-MM-dd（必填）
	EndDate                   string `json:"end_date"`                               // 结束时间，格式yyyy-MM-dd（必填）
	SortColumn                string `json:"sort_column,omitempty"`                  // 排序字段
	Sort                      string `json:"sort,omitempty"`                         // 升降序：asc升序，desc降序
	PlacementList             []int  `json:"placement_list,omitempty"`               // 广告类型：1信息流，2搜索，7视频流
	CreativityFilterState     int    `json:"creativity_filter_state,omitempty"`      // 创意状态过滤
	CreativityCreateBeginTime string `json:"creativity_create_begin_time,omitempty"` // 创意创建时间范围开始
	CreativityCreateEndTime   string `json:"creativity_create_end_time,omitempty"`   // 创意创建时间范围结束
	ConversionType            int    `json:"conversion_type,omitempty"`              // 创意类型
	ProgrammaticList          []int  `json:"programmatic_list,omitempty"`            // 创意组合方式
	CreativityAuditState      int    `json:"creativity_audit_state,omitempty"`       // 创意审核状态
	Name                      string `json:"name,omitempty"`                         // 搜索创意名称
	ID                        int    `json:"id,omitempty"`                           // 搜索创意ID
	DataCaliber               int    `json:"data_caliber,omitempty"`                 // 数据指标归因时间类型
	NeedHourlyData            bool   `json:"need_hourly_data,omitempty"`             // 是否拉取小时数据
}

// PageRespDTO 分页响应结构体
type PageRespDTO struct {
	PageIndex  int `json:"page_index"`  // 当前页码
	TotalCount int `json:"total_count"` // 总数量
}

// BaseCreativityDTO 创意属性信息
type BaseCreativityDTO struct {
	CreativityID          int64  `json:"creativity_id"`           // 创意ID
	CreativityName        string `json:"creativity_name"`         // 创意名称
	CreativityFilterState int    `json:"creativity_filter_state"` // 创意状态
	CreativityCreateTime  string `json:"creativity_create_time"`  // 创意创建时间
	CreativityEnable      int    `json:"creativity_enable"`       // 创意启停状态
	AuditStatus           int    `json:"audit_status"`            // 审核状态
	UnitID                int64  `json:"unit_id"`                 // 单元ID
	Programmatic          int    `json:"programmatic"`            // 创意组合类型
	NoteID                string `json:"note_id"`                 // 笔记ID
	CreativityType        int    `json:"creativity_type"`         // 创意类型
}

// BaseCampaignDTO 计划属性信息
type BaseCampaignDTO struct {
	CampaignID              int64  `json:"campaign_id"`                // 计划ID
	CampaignName            string `json:"campaign_name"`              // 计划名称
	CampaignFilterState     int    `json:"campaign_filter_state"`      // 计划状态
	CampaignCreateTime      string `json:"campaign_create_time"`       // 计划创建时间
	CampaignEnable          int    `json:"campaign_enable"`            // 计划启停状态
	MarketingTarget         int    `json:"marketing_target"`           // 营销诉求
	Placement               int    `json:"placement"`                  // 广告类型
	OptimizeTarget          int    `json:"optimize_target"`            // 推广目标
	PromotionTarget         int    `json:"promotion_target"`           // 投放标的
	BiddingStrategy         int    `json:"bidding_strategy"`           // 出价方式
	ConstraintType          int    `json:"constraint_type"`            // 成本控制类型
	ConstraintValue         int    `json:"constraint_value"`           // 成本控制值
	LimitDayBudget          int    `json:"limit_day_budget"`           // 预算类型
	OriginCampaignDayBudget int    `json:"origin_campaign_day_budget"` // 计划日预算
	BudgetState             int    `json:"budget_state"`               // 预算状态
	SmartSwitch             int    `json:"smart_switch"`               // 是否节假日预算上调
	PacingMode              int    `json:"pacing_mode"`                // 投放速率
	StartTime               string `json:"start_time"`                 // 计划开始时间
	ExpireTime              string `json:"expire_time"`                // 计划结束时间
	TimePeriod              string `json:"time_period"`                // 时段
	TimePeriodType          int    `json:"time_period_type"`           // 推广时段类型
	BuildType               int    `json:"build_type"`                 // 搭建方式
	FeedFlag                int    `json:"feed_flag"`                  // 是否搜索追投信息流
	SearchFlag              int    `json:"search_flag"`                // 是否信息流快投搜索
	MigrationStatus         int    `json:"migration_status"`           // 专业号平台计划迁移状态
}

// BaseUnitDTO 单元属性信息
type BaseUnitDTO struct {
	UnitID          int64  `json:"unit_id"`           // 单元ID
	UnitName        string `json:"unit_name"`         // 单元名称
	UnitFilterState int    `json:"unit_filter_state"` // 单元状态
	UnitCreateTime  string `json:"unit_create_time"`  // 单元创建时间
	UnitEnable      int    `json:"unit_enable"`       // 单元启停状态
	CampaignID      int64  `json:"campaign_id"`       // 计划ID
	EventBid        int    `json:"event_bid"`         // 出价
}

// DataReportDTO 数据报告指标
type DataReportDTO struct {
	// 基础指标
	Fee        string `json:"fee"`        // 消费（元）
	Impression string `json:"impression"` // 展现量
	Click      string `json:"click"`      // 点击量
	Ctr        string `json:"ctr"`        // 点击率
	Acp        string `json:"acp"`        // 平均点击成本
	Cpm        string `json:"cpm"`        // 平均千次曝光成本

	// 互动指标
	Like              string `json:"like"`                // 点赞量
	Comment           string `json:"comment"`             // 评论量
	Collect           string `json:"collect"`             // 收藏量
	Follow            string `json:"follow"`              // 关注量
	Share             string `json:"share"`               // 分享量
	Interaction       string `json:"interaction"`         // 互动量
	Cpi               string `json:"cpi"`                 // 平均互动成本
	ActionButtonClick string `json:"action_button_click"` // 行动按钮点击量
	ActionButtonCtr   string `json:"action_button_ctr"`   // 行动按钮点击率
	Screenshot        string `json:"screenshot"`          // 截图次数
	PicSave           string `json:"pic_save"`            // 保存图片次数
	ReservePv         string `json:"reserve_pv"`          // 预告组件点击

	// 直播间互动指标
	ClkLiveEntryPv       string `json:"clk_live_entry_pv"`         // 直播间观看次数
	ClkLiveEntryPvCost   string `json:"clk_live_entry_pv_cost"`    // 直播间观看成本
	ClkLiveAvgViewTime   string `json:"clk_live_avg_view_time"`    // 直播间人均停留时长(分钟)
	ClkLiveAllFollow     string `json:"clk_live_all_follow"`       // 直播间新增粉丝量
	ClkLive5sEntryPv     string `json:"clk_live_5s_entry_pv"`      // 直播间有效观看次数
	ClkLive5sEntryUvCost string `json:"clk_live_5s_entry_uv_cost"` // 直播间有效观看成本
	ClkLiveComment       string `json:"clk_live_comment"`          // 直播间评论次数

	// 笔记种草指标
	SearchCmtClick        string `json:"search_cmt_click"`          // 搜索组件点击量
	SearchCmtClickCvr     string `json:"search_cmt_click_cvr"`      // 搜索组件点击转化率
	SearchCmtAfterRead    string `json:"search_cmt_after_read"`     // 搜后阅读量
	SearchCmtAfterReadAvg string `json:"search_cmt_after_read_avg"` // 平均搜后阅读笔记篇数
	IUserNum              string `json:"i_user_num"`                // 新增种草人群
	TiUserNum             string `json:"ti_user_num"`               // 新增深度种草人群
	IUserPrice            string `json:"i_user_price"`              // 新增种草人群成本
	TiUserPrice           string `json:"ti_user_price"`             // 新增深度种草人群成本

	// 电商转化指标/购买兴趣
	GoodsVisit       string `json:"goods_visit"`        // 进店访问量
	GoodsVisitPrice  string `json:"goods_visit_price"`  // 进店访问成本
	SellerVisit      string `json:"seller_visit"`       // 商品访客量
	SellerVisitPrice string `json:"seller_visit_price"` // 商品访客成本
	ShoppingCartAdd  string `json:"shopping_cart_add"`  // 商品加购量
	AddCartPrice     string `json:"add_cart_price"`     // 商品加购成本

	// 电商转化指标/7日转化
	PresaleOrderNum7d    string `json:"presale_order_num_7d"`    // 7日预售订单量
	PresaleOrderGmv7d    string `json:"presale_order_gmv_7d"`    // 7日预售订单金额
	GoodsOrder           string `json:"goods_order"`             // 7日下单订单量
	GoodsOrderPrice      string `json:"goods_order_price"`       // 7日下单订单成本
	Rgmv                 string `json:"rgmv"`                    // 7日下单金额
	Roi                  string `json:"roi"`                     // 7日下单ROI
	SuccessGoodsOrder    string `json:"success_goods_order"`     // 7日支付订单量
	ClickOrderCvr        string `json:"click_order_cvr"`         // 7日支付转化率
	PurchaseOrderPrice7d string `json:"purchase_order_price_7d"` // 7日支付订单成本
	PurchaseOrderGmv7d   string `json:"purchase_order_gmv_7d"`   // 7日支付金额
	PurchaseOrderRoi7d   string `json:"purchase_order_roi_7d"`   // 7日支付ROI

	// 电商转化指标/直播间转化
	ClkLiveRoomOrderNum  string `json:"clk_live_room_order_num"` // 直播间支付订单量
	LiveAverageOrderCost string `json:"live_average_order_cost"` // 直播间支付订单成本
	ClkLiveRoomRgmv      string `json:"clk_live_room_rgmv"`      // 直播间支付金额
	ClkLiveRoomRoi       string `json:"clk_live_room_roi"`       // 直播间支付ROI

	// 销售线索指标
	Leads                 string `json:"leads"`                   // 表单提交量
	LeadsCpl              string `json:"leads_cpl"`               // 表单成本
	LandingPageVisit      string `json:"landing_page_visit"`      // 落地页访问量(行为时间)
	LeadsButtonImpression string `json:"leads_button_impression"` // 表单按钮曝光量(行为时间)
	ValidLeads            string `json:"valid_leads"`             // 有效表单
	ValidLeadsCpl         string `json:"valid_leads_cpl"`         // 有效表单成本
	LeadsCvr              string `json:"leads_cvr"`               // 表单转化率
	PhoneCallCnt          string `json:"phone_call_cnt"`          // 电话拨打
	PhoneCallSuccCnt      string `json:"phone_call_succ_cnt"`     // 电话接通
	WechatCopyCnt         string `json:"wechat_copy_cnt"`         // 微信复制
	WechatCopySuccCnt     string `json:"wechat_copy_succ_cnt"`    // 微信加为好友
	IdentityCertiCnt      string `json:"identity_certi_cnt"`      // 身份认证
	CommodityBuyCnt       string `json:"commodity_buy_cnt"`       // 商品购买

	// 私信营销指标
	MessageUser            string `json:"message_user"`               // 私信咨询人数
	Message                string `json:"message"`                    // 私信咨询条数
	MessageConsult         string `json:"message_consult"`            // 私信咨询数
	MessageFstReplyTimeAvg string `json:"message_fst_reply_time_avg"` // 平均响应时长(分)
	InitiativeMessage      string `json:"initiative_message"`         // 私信开口数
	MessageConsultCpl      string `json:"message_consult_cpl"`        // 私信咨询成本
	InitiativeMessageCpl   string `json:"initiative_message_cpl"`     // 私信开口成本
	MsgLeadsNum            string `json:"msg_leads_num"`              // 私信留资数
	MsgLeadsCost           string `json:"msg_leads_cost"`             // 私信留资成本

	// 行业商品销量指标
	ExternalGoodsVisit7       string `json:"external_goods_visit_7"`        // 行业商品点击量(7日)
	ExternalGoodsVisitPrice7  string `json:"external_goods_visit_price_7"`  // 行业商品点击成本(7日)
	ExternalGoodsVisitRate7   string `json:"external_goods_visit_rate_7"`   // 行业商品点击转化率(7日)
	ExternalGoodsOrder7       string `json:"external_goods_order_7"`        // 行业商品成交订单量(7日)
	ExternalRgmv7             string `json:"external_rgmv_7"`               // 行业商品GMV(7日)
	ExternalGoodsOrderPrice7  string `json:"external_goods_order_price_7"`  // 行业商品成交订单成本(7日)
	ExternalGoodsOrderRate7   string `json:"external_goods_order_rate_7"`   // 行业商品成交订单转化率(7日)
	ExternalRoi7              string `json:"external_roi_7"`                // 行业商品ROI(7日)
	ExternalGoodsOrder15      string `json:"external_goods_order_15"`       // 行业商品成交订单量(15日)
	ExternalRgmv15            string `json:"external_rgmv_15"`              // 行业商品GMV(15日)
	ExternalGoodsOrderPrice15 string `json:"external_goods_order_price_15"` // 行业商品成交订单成本(15日)
	ExternalGoodsOrderRate15  string `json:"external_goods_order_rate_15"`  // 行业商品成交订单转化率(15日)
	ExternalRoi15             string `json:"external_roi_15"`               // 行业商品ROI(15日)
	ExternalGoodsOrder30      string `json:"external_goods_order_30"`       // 行业商品成交订单量(30日)
	ExternalRgmv30            string `json:"external_rgmv_30"`              // 行业商品GMV(30日)
	ExternalGoodsOrderPrice30 string `json:"external_goods_order_price_30"` // 行业商品成交订单成本(30日)
	ExternalGoodsOrderRate30  string `json:"external_goods_order_rate_30"`  // 行业商品成交订单转化率(30日)
	ExternalRoi30             string `json:"external_roi_30"`               // 行业商品ROI(30日)

	// 外链专属指标
	ExternalLeads    string `json:"external_leads"`     // 外链转化数
	ExternalLeadsCpl string `json:"external_leads_cpl"` // 平均外链转化成本

	// 关键词指标
	WordAvgLocation         string `json:"word_avg_location"`          // 平均位次
	WordImpressionRankFirst string `json:"word_impression_rank_first"` // 首位曝光排名
	WordImpressionRateFirst string `json:"word_impression_rate_first"` // 首位曝光占比
	WordImpressionRankThird string `json:"word_impression_rank_third"` // 前三位曝光排名
	WordImpressionRateThird string `json:"word_impression_rate_third"` // 前三位曝光占比
	WordClickRankFirst      string `json:"word_click_rank_first"`      // 首位点击排名
	WordClickRateFirst      string `json:"word_click_rate_first"`      // 首位点击占比
	WordClickRateThird      string `json:"word_click_rate_third"`      // 前三位点击占比
	WordClickRankThird      string `json:"word_click_rank_third"`      // 前三位点击排名
	WordImpressionRankAll   string `json:"word_impression_rank_all"`   // 全坑位曝光排名
	WordImpressionRateAll   string `json:"word_impression_rate_all"`   // 全坑位曝光占比
	WordClickRankAll        string `json:"word_click_rank_all"`        // 全坑位点击排名
	WordClickRateAll        string `json:"word_click_rate_all"`        // 全坑位点击占比

	// APP内转化数据指标
	InvokeAppOpenCnt            string `json:"invoke_app_open_cnt"`             // APP打开量(唤起)
	InvokeAppOpenCost           string `json:"invoke_app_open_cost"`            // APP打开成本(唤起)
	InvokeAppEnterStoreCnt      string `json:"invoke_app_enter_store_cnt"`      // APP进店量(唤起)
	InvokeAppEnterStoreCost     string `json:"invoke_app_enter_store_cost"`     // APP进店成本(唤起)
	InvokeAppEngagementCnt      string `json:"invoke_app_engagement_cnt"`       // APP互动量(唤起)
	InvokeAppEngagementCost     string `json:"invoke_app_engagement_cost"`      // APP互动成本(唤起)
	InvokeAppPaymentCnt         string `json:"invoke_app_payment_cnt"`          // APP支付次数(唤起)
	InvokeAppPaymentCost        string `json:"invoke_app_payment_cost"`         // APP订单支付成本(唤起)
	SearchInvokeButtonClickCnt  string `json:"search_invoke_button_click_cnt"`  // APP打开按钮点击量(唤起)
	SearchInvokeButtonClickCost string `json:"search_invoke_button_click_cost"` // APP打开按钮点击成本(唤起)
	InvokeAppPaymentRoi         string `json:"invoke_app_payment_roi"`          // APP支付ROI(唤起)
	InvokeAppPaymentAmount      string `json:"invoke_app_payment_amount"`       // APP支付金额(唤起)
	InvokeAppPaymentUnitPrice   string `json:"invoke_app_payment_unit_price"`   // APP支付单价(唤起)

	// 京东站外店铺行为指标
	JdActiveUserNum    string `json:"jd_active_user_num"`     // 京东站外活跃行为量
	JdActiveUserNumCvr string `json:"jd_active_user_num_cvr"` // 京东站外转化率
	JdActiveUserNumCpl string `json:"jd_active_user_num_cpl"` // 京东站外转化成本

	// 应用下载指标
	AppDownloadButtonClickCnt  string `json:"app_download_button_click_cnt"`  // APP下载按钮点击
	AppDownloadButtonClickCtr  string `json:"app_download_button_click_ctr"`  // APP下载按钮点击率
	AppDownloadButtonClickCost string `json:"app_download_button_click_cost"` // APP下载按钮点击成本
	AppActivateCnt             string `json:"app_activate_cnt"`               // 激活数
	AppActivateCost            string `json:"app_activate_cost"`              // 激活成本
	AppActivateCtr             string `json:"app_activate_ctr"`               // 激活率
	AppRegisterCnt             string `json:"app_register_cnt"`               // 注册数
	AppRegisterCost            string `json:"app_register_cost"`              // 注册成本
	AppRegisterCtr             string `json:"app_register_ctr"`               // 注册率
	FirstAppPayCnt             string `json:"first_app_pay_cnt"`              // 首次付费数
	FirstAppPayCost            string `json:"first_app_pay_cost"`             // 首次付费成本
	FirstAppPayCtr             string `json:"first_app_pay_ctr"`              // 首次付费率
	CurrentAppPayCnt           string `json:"current_app_pay_cnt"`            // 当日付费次数
	CurrentAppPayCost          string `json:"current_app_pay_cost"`           // 当日付费成本
	AppKeyActionCnt            string `json:"app_key_action_cnt"`             // 关键行为数
	AppKeyActionCost           string `json:"app_key_action_cost"`            // 关键行为成本
	AppKeyActionCtr            string `json:"app_key_action_ctr"`             // 关键行为率
	AppPayCnt7d                string `json:"app_pay_cnt_7d"`                 // 7日付费次数
	AppPayCost7d               string `json:"app_pay_cost_7d"`                // 7日付费成本
	AppPayAmount               string `json:"app_pay_amount"`                 // 付费金额
	AppPayRoi                  string `json:"app_pay_roi"`                    // 付费ROI
	AppActivateAmount1d        string `json:"app_activate_amount_1d"`         // 当日LTV
	AppActivateAmount3d        string `json:"app_activate_amount_3d"`         // 三日LTV
	AppActivateAmount7d        string `json:"app_activate_amount_7d"`         // 七日LTV
	AppActivateAmount1dRoi     string `json:"app_activate_amount_1d_roi"`     // 当日广告付费ROI
	AppActivateAmount3dRoi     string `json:"app_activate_amount_3d_roi"`     // 三日广告付费ROI
	AppActivateAmount7dRoi     string `json:"app_activate_amount_7d_roi"`     // 七日广告付费ROI
	Retention1dCnt             string `json:"retention_1d_cnt"`               // 次留
	Retention3dCnt             string `json:"retention_3d_cnt"`               // 3日留存
	Retention7dCnt             string `json:"retention_7d_cnt"`               // 7日留存

	// 企微营销指标
	AddWechatCount    string `json:"add_wechat_count"`     // 添加企微量
	AddWechatCost     string `json:"add_wechat_cost"`      // 添加企微成本
	AddWechatSucCount string `json:"add_wechat_suc_count"` // 成功添加企微量
	AddWechatSucCost  string `json:"add_wechat_suc_cost"`  // 成功添加企微成本
	WechatTalkCount   string `json:"wechat_talk_count"`    // 企微开口量
	WechatTalkCost    string `json:"wechat_talk_cost"`     // 企微开口成本

	// 门店营销指标
	ShopPoiClickNum          string `json:"shop_poi_click_num"`           // 组件点击量
	ShopPoiPagePv            string `json:"shop_poi_page_pv"`             // 门店页面访问量
	ShopPoiPageVisitPrice    string `json:"shop_poi_page_visit_price"`    // 门店页面访问成本
	ShopPoiPageNavigateClick string `json:"shop_poi_page_navigate_click"` // 门店页面导航栏按钮点击量
}

// CreativityDTO 创意数据
type CreativityDTO struct {
	Data           DataReportDTO     `json:"data"`                // 数据指标
	BaseCampaign   BaseCampaignDTO   `json:"base_campaign_dto"`   // 计划属性
	BaseUnit       BaseUnitDTO       `json:"base_unit_dto"`       // 单元属性
	BaseCreativity BaseCreativityDTO `json:"base_creativity_dto"` // 创意属性
	HourlyData     []DataReportDTO   `json:"hourly_data"`         // 小时数据
}

// CreativityRealtimeReportResponse 创意层级实时报表API响应
type CreativityRealtimeReportResponse struct {
	Code           int             `json:"code"`            // 返回码
	Msg            string          `json:"msg"`             // 返回信息
	Success        bool            `json:"success"`         // 接口是否成功
	RequestID      string          `json:"request_id"`      // 请求ID
	Page           PageRespDTO     `json:"page"`            // 分页信息
	TotalData      DataReportDTO   `json:"total_data"`      // 汇总数据
	CreativityDtos []CreativityDTO `json:"creativity_dtos"` // 创意数据列表
}
