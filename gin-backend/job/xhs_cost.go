package job

import (
	"context"
	"gin-backend/internal/service"

	"go.uber.org/zap"
)

// XhsCostSyncJob 同步小红书成本定时任务
type XhsCostSyncJob struct {
	promotionReportService *service.PromotionReportService
}

// NewXhsCostSyncJob 创建小红书成本同步定时任务
func NewXhsCostSyncJob() *XhsCostSyncJob {
	return &XhsCostSyncJob{
		promotionReportService: service.NewPromotionReportService(),
	}
}

// Name 任务名称
func (j *XhsCostSyncJob) Name() string {
	return "sync-xhs-cost"
}

// Run 任务执行
func (j *XhsCostSyncJob) Run() error {
	ctx := context.Background()
	if err := j.promotionReportService.ImportXhsCost(ctx); err != nil {
		zap.L().Error("同步小红书成本数据失败", zap.Error(err))
		return err
	}
	zap.L().Info("同步小红书成本数据成功")
	return nil
}
