package vo

import (
	"gin-backend/internal/service/domain"
	"time"
)

// XHSCreativeReportListReq 小红书创意报表列表请求
type XHSCreativeReportListReq struct {
	// 筛选条件
	AccountID    *int64  `form:"account_id"`    // 账号ID
	AccountName  string  `form:"account_name"`  // 账号名称
	CampaignName string  `form:"campaign_name"` // 计划名称
	UnitName     string  `form:"unit_name"`     // 单元名称
	Title        string  `form:"title"`         // 标题
	Pwd          string  `form:"pwd"`           // 口令
	StartDate    string  `form:"start_date"`    // 开始日期 YYYY-MM-DD
	EndDate      string  `form:"end_date"`      // 结束日期 YYYY-MM-DD
	Placement    *int8   `form:"placement"`     // 广告类型
	
	// 分页参数
	Page     int `form:"page" binding:"min=1"`      // 页码，从1开始
	PageSize int `form:"page_size" binding:"min=1"` // 每页数量
}

// ToParam 转换为领域参数
func (req *XHSCreativeReportListReq) ToParam() domain.XHSCreativeReportListParam {
	param := domain.XHSCreativeReportListParam{
		AccountID:    req.AccountID,
		AccountName:  req.AccountName,
		CampaignName: req.CampaignName,
		UnitName:     req.UnitName,
		Title:        req.Title,
		Pwd:          req.Pwd,
		Placement:    req.Placement,
		Page:         req.Page,
		PageSize:     req.PageSize,
	}
	
	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			param.StartDate = &startDate
		}
	}
	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			param.EndDate = &endDate
		}
	}
	
	return param
}

// XHSCreativeReportItem 小红书创意报表列表项
type XHSCreativeReportItem struct {
	ID            int64     `json:"id"`             // 主键ID
	AccountName   string    `json:"account_name"`   // 账号名称
	Title         string    `json:"title"`          // 标题
	Pwd           string    `json:"pwd"`            // 口令
	ContentPeople string    `json:"content_people"` // 内容人员
	PitcherName   string    `json:"pitcher_name"`   // 投手姓名
	CampaignName  string    `json:"campaign_name"`  // 计划名称
	UnitName      string    `json:"unit_name"`      // 单元名称
	CreativityID  string    `json:"creativity_id"`  // 创意ID
	NoteID        string    `json:"note_id"`        // 笔记ID
	Time          time.Time `json:"time"`           // 统计时间
	Placement     int8      `json:"placement"`      // 广告类型
	
	// 基础指标
	Fee        float64 `json:"fee"`        // 消费金额
	Impression int64   `json:"impression"` // 展现量
	Click      int64   `json:"click"`      // 点击量
	CTR        float64 `json:"ctr"`        // 点击率
	ACP        float64 `json:"acp"`        // 平均点击成本
	CPM        float64 `json:"cpm"`        // 千次曝光成本
	
	// 互动指标
	Like        int64   `json:"like"`        // 点赞量
	Comment     int64   `json:"comment"`     // 评论量
	Collect     int64   `json:"collect"`     // 收藏量
	Follow      int64   `json:"follow"`      // 关注量
	Share       int64   `json:"share"`       // 分享量
	Interaction int64   `json:"interaction"` // 互动量
	CPI         float64 `json:"cpi"`         // 平均互动成本
	
	// 转化指标
	GoodsOrder        int64   `json:"goods_order"`         // 7日下单订单量
	RGMV              float64 `json:"rgmv"`                // 7日下单金额
	ROI               float64 `json:"roi"`                 // 7日下单ROI
	SuccessGoodsOrder int64   `json:"success_goods_order"` // 7日支付订单量
	PurchaseOrderROI7D float64 `json:"purchase_order_roi_7d"` // 7日支付ROI
}

// XHSCreativeReportListResp 小红书创意报表列表响应
type XHSCreativeReportListResp struct {
	List  []XHSCreativeReportItem `json:"list"`  // 报表列表
	Total int64                   `json:"total"` // 总数量
	Page  int                     `json:"page"`  // 当前页码
	Size  int                     `json:"size"`  // 每页数量
}

// XHSCreativeReportListRespFromResult 从领域结果转换为响应
func XHSCreativeReportListRespFromResult(result domain.XHSCreativeReportListResult) XHSCreativeReportListResp {
	items := make([]XHSCreativeReportItem, 0, len(result.List))
	
	for _, report := range result.List {
		item := XHSCreativeReportItem{
			ID:            report.Id,
			AccountName:   report.AccountName,
			Title:         report.Title,
			Pwd:           report.Pwd,
			ContentPeople: report.ContentPeople,
			PitcherName:   report.PitcherName,
			CampaignName:  report.CampaignName,
			UnitName:      report.UnitName,
			CreativityID:  report.CreativityID,
			NoteID:        report.NoteID,
			Time:          report.Time,
			Placement:     report.Placement,
			
			// 基础指标
			Fee:        report.Fee,
			Impression: report.Impression,
			Click:      report.Click,
			CTR:        report.CTR,
			ACP:        report.ACP,
			CPM:        report.CPM,
			
			// 互动指标
			Like:        report.Like,
			Comment:     report.Comment,
			Collect:     report.Collect,
			Follow:      report.Follow,
			Share:       report.Share,
			Interaction: report.Interaction,
			CPI:         report.CPI,
			
			// 转化指标
			GoodsOrder:         report.GoodsOrder,
			RGMV:               report.RGMV,
			ROI:                report.ROI,
			SuccessGoodsOrder:  report.SuccessGoodsOrder,
			PurchaseOrderROI7D: report.PurchaseOrderROI7D,
		}
		items = append(items, item)
	}
	
	return XHSCreativeReportListResp{
		List:  items,
		Total: result.Total,
		Page:  result.Page,
		Size:  result.Size,
	}
}

// XHSCreativeReportExportReq 小红书创意报表导出请求
type XHSCreativeReportExportReq struct {
	// 筛选条件（与列表请求相同）
	AccountID    *int64 `form:"account_id"`    // 账号ID
	AccountName  string `form:"account_name"`  // 账号名称
	CampaignName string `form:"campaign_name"` // 计划名称
	UnitName     string `form:"unit_name"`     // 单元名称
	Title        string `form:"title"`         // 标题
	Pwd          string `form:"pwd"`           // 口令
	StartDate    string `form:"start_date"`    // 开始日期 YYYY-MM-DD
	EndDate      string `form:"end_date"`      // 结束日期 YYYY-MM-DD
	Placement    *int8  `form:"placement"`     // 广告类型
	
	// 导出参数
	Format string `form:"format" binding:"oneof=xlsx csv"` // 导出格式：xlsx 或 csv
}

// ToParam 转换为领域参数
func (req *XHSCreativeReportExportReq) ToParam() domain.XHSCreativeReportExportParam {
	param := domain.XHSCreativeReportExportParam{
		AccountID:    req.AccountID,
		AccountName:  req.AccountName,
		CampaignName: req.CampaignName,
		UnitName:     req.UnitName,
		Title:        req.Title,
		Pwd:          req.Pwd,
		Placement:    req.Placement,
		Format:       req.Format,
	}
	
	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			param.StartDate = &startDate
		}
	}
	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			param.EndDate = &endDate
		}
	}
	
	return param
}

// XHSCreativeReportExportResp 小红书创意报表导出响应
type XHSCreativeReportExportResp struct {
	FileName string `json:"file_name"` // 文件名
	FileURL  string `json:"file_url"`  // 文件下载URL
	Message  string `json:"message"`   // 响应消息
}

// XHSCreativeReportExportRespFromResult 从领域结果转换为响应
func XHSCreativeReportExportRespFromResult(result domain.XHSCreativeReportExportResult) XHSCreativeReportExportResp {
	return XHSCreativeReportExportResp{
		FileName: result.FileName,
		FileURL:  result.FileURL,
		Message:  "导出成功",
	}
}

// XHSCreativeReportStatsResp 小红书创意报表统计响应
type XHSCreativeReportStatsResp struct {
	TotalReports   int64   `json:"total_reports"`   // 总报表数量
	TotalFee       float64 `json:"total_fee"`       // 总消费
	TotalClick     int64   `json:"total_click"`     // 总点击量
	TotalROI       float64 `json:"total_roi"`       // 总ROI
	AvgCTR         float64 `json:"avg_ctr"`         // 平均点击率
	AvgCPI         float64 `json:"avg_cpi"`         // 平均互动成本
	LastUpdateTime string  `json:"last_update_time"` // 最后更新时间
}

// XHSCreativeReportStatsRespFromResult 从领域结果转换为响应
func XHSCreativeReportStatsRespFromResult(result domain.XHSCreativeReportStatsResult) XHSCreativeReportStatsResp {
	return XHSCreativeReportStatsResp{
		TotalReports:   result.TotalReports,
		TotalFee:       result.TotalFee,
		TotalClick:     result.TotalClick,
		TotalROI:       result.TotalROI,
		AvgCTR:         result.AvgCTR,
		AvgCPI:         result.AvgCPI,
		LastUpdateTime: result.LastUpdateTime.Format("2006-01-02 15:04:05"),
	}
}
