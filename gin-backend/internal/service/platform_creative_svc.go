package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"strings"
	"time"

	"gorm.io/gorm"
)

// PlatformCreativeService 广告创意服务
type PlatformCreativeService struct {
	db *gorm.DB
}

// NewPlatformCreativeService 创建广告创意服务
func NewPlatformCreativeService() *PlatformCreativeService {
	return &PlatformCreativeService{
		db: global.DB,
	}
}

// List 获取广告创意列表
func (s *PlatformCreativeService) List(ctx context.Context, param domain.PlatformCreativeListParam) (domain.PlatformCreativeListResult, error) {
	// 参数验证
	if param.Page <= 0 {
		param.Page = 1
	}

	pageSize := param.PageSize
	if pageSize == 0 && param.Size > 0 {
		pageSize = param.Size
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100
	}

	var total int64
	var results []domain.PlatformCreativeEntity

	// 使用原生SQL查询以避免GORM的Preload和JOIN冲突问题
	var args []interface{}
	var whereConditions []string

	// 基础查询SQL
	baseSQL := `
		SELECT 
			c.id, c.name as title, c.data_id, c.platform_type, c.agent_id, c.media_id, 
			c.plan_id, c.group_id, c.remark as description, c.created_at, c.updated_at,
			COALESCE(p.name, '') as plan_name,
			COALESCE(g.name, '') as group_name,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name,
			COALESCE(p.market_target_name, '') as market_target_name
		FROM platform_creatives c
		LEFT JOIN platform_plans p ON p.id = c.plan_id
		LEFT JOIN platform_groups g ON g.id = c.group_id
		LEFT JOIN ad_agents a ON a.id = c.agent_id  
		LEFT JOIN ad_media m ON m.id = c.media_id`

	// 添加筛选条件
	if param.AgentID > 0 {
		whereConditions = append(whereConditions, "c.agent_id = ?")
		args = append(args, param.AgentID)
	}

	if param.MediaID > 0 {
		whereConditions = append(whereConditions, "c.media_id = ?")
		args = append(args, param.MediaID)
	}

	if param.PlatformType != "" {
		whereConditions = append(whereConditions, "c.platform_type = ?")
		args = append(args, param.PlatformType)
	}

	if param.MarketTargetName != "" {
		whereConditions = append(whereConditions, "p.market_target_name = ?")
		args = append(args, param.MarketTargetName)
	}

	if param.PlanKeyword != "" {
		whereConditions = append(whereConditions, "(p.name LIKE ? OR p.data_id = ?)")
		args = append(args, "%"+param.PlanKeyword+"%", param.PlanKeyword)
	}

	if param.GroupKeyword != "" {
		whereConditions = append(whereConditions, "(g.name LIKE ? OR g.data_id = ?)")
		args = append(args, "%"+param.GroupKeyword+"%", param.GroupKeyword)
	}

	if param.Keyword != "" {
		whereConditions = append(whereConditions, "(c.name LIKE ? OR c.data_id = ?)")
		args = append(args, "%"+param.Keyword+"%", param.Keyword)
	}

	// 构建完整的WHERE子句
	var fullSQL string
	countSQL := `
		SELECT COUNT(*) 
		FROM platform_creatives c
		LEFT JOIN platform_plans p ON p.id = c.plan_id
		LEFT JOIN platform_groups g ON g.id = c.group_id
		LEFT JOIN ad_agents a ON a.id = c.agent_id  
		LEFT JOIN ad_media m ON m.id = c.media_id`

	if len(whereConditions) > 0 {
		whereClause := " WHERE " + strings.Join(whereConditions, " AND ")
		fullSQL = baseSQL + whereClause
		countSQL = countSQL + whereClause
	} else {
		fullSQL = baseSQL
	}

	// 查询总数
	err := s.db.WithContext(ctx).Raw(countSQL, args...).Scan(&total).Error
	if err != nil {
		return domain.PlatformCreativeListResult{}, fmt.Errorf("查询广告创意总数失败: %v", err)
	}

	// 添加排序和分页
	fullSQL += " ORDER BY c.id DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (param.Page-1)*pageSize)

	// 查询列表数据
	var dbResults []struct {
		ID               int64        `gorm:"column:id"`
		Title            string       `gorm:"column:title"`
		DataID           string       `gorm:"column:data_id"`
		PlatformType     string       `gorm:"column:platform_type"`
		AgentID          int64        `gorm:"column:agent_id"`
		MediaID          int64        `gorm:"column:media_id"`
		PlanID           int64        `gorm:"column:plan_id"`
		GroupID          int64        `gorm:"column:group_id"`
		Description      string       `gorm:"column:description"`
		CreatedAt        sql.NullTime `gorm:"column:created_at"`
		UpdatedAt        sql.NullTime `gorm:"column:updated_at"`
		PlanName         string       `gorm:"column:plan_name"`
		GroupName        string       `gorm:"column:group_name"`
		AgentName        string       `gorm:"column:agent_name"`
		MediaName        string       `gorm:"column:media_name"`
		MarketTargetName string       `gorm:"column:market_target_name"`
	}

	err = s.db.WithContext(ctx).Raw(fullSQL, args...).Scan(&dbResults).Error
	if err != nil {
		return domain.PlatformCreativeListResult{}, fmt.Errorf("查询广告创意列表失败: %v", err)
	}

	// 转换结果
	results = make([]domain.PlatformCreativeEntity, 0, len(dbResults))
	for _, result := range dbResults {
		var createdAt, updatedAt int64
		if result.CreatedAt.Valid {
			createdAt = result.CreatedAt.Time.Unix()
		}
		if result.UpdatedAt.Valid {
			updatedAt = result.UpdatedAt.Time.Unix()
		}

		creative := domain.PlatformCreativeEntity{
			ID:               result.ID,
			Title:            result.Title,
			DataID:           result.DataID,
			PlatformType:     result.PlatformType,
			AgentID:          result.AgentID,
			MediaID:          result.MediaID,
			PlanID:           result.PlanID,
			GroupID:          result.GroupID,
			Description:      result.Description,
			PlanName:         result.PlanName,
			GroupName:        result.GroupName,
			AgentName:        result.AgentName,
			MediaName:        result.MediaName,
			MarketTargetName: result.MarketTargetName,
			CreatedAt:        createdAt,
			UpdatedAt:        updatedAt,
		}
		results = append(results, creative)
	}

	return domain.PlatformCreativeListResult{
		List:  results,
		Total: total,
	}, nil
}

// Create 创建广告创意
func (s *PlatformCreativeService) Create(ctx context.Context, param domain.PlatformCreativeCreateParam) (domain.PlatformCreativeEntity, error) {
	// 验证必填字段
	if param.Title == "" {
		return domain.PlatformCreativeEntity{}, errors.New("创意标题不能为空")
	}
	if param.DataID == "" {
		return domain.PlatformCreativeEntity{}, errors.New("创意ID不能为空")
	}
	if param.PlatformType == "" {
		return domain.PlatformCreativeEntity{}, errors.New("平台类型不能为空")
	}

	// 检查平台数据ID是否已存在
	var count int64
	err := s.db.WithContext(ctx).Model(&model.PlatformCreative{}).
		Where("platform_type = ? AND data_id = ?", param.PlatformType, param.DataID).
		Count(&count).Error
	if err != nil {
		return domain.PlatformCreativeEntity{}, fmt.Errorf("检查创意ID是否存在失败: %v", err)
	}

	if count > 0 {
		return domain.PlatformCreativeEntity{}, errors.New("该平台创意ID已存在")
	}

	// 检查关联数据是否存在
	if param.PlanID > 0 {
		var planCount int64
		err = s.db.WithContext(ctx).Model(&model.PlatformPlan{}).Where("id = ?", param.PlanID).Count(&planCount).Error
		if err != nil {
			return domain.PlatformCreativeEntity{}, fmt.Errorf("检查计划是否存在失败: %v", err)
		}
		if planCount == 0 {
			return domain.PlatformCreativeEntity{}, errors.New("关联的计划不存在")
		}
	}

	if param.GroupID > 0 {
		var groupCount int64
		err = s.db.WithContext(ctx).Model(&model.PlatformGroup{}).Where("id = ?", param.GroupID).Count(&groupCount).Error
		if err != nil {
			return domain.PlatformCreativeEntity{}, fmt.Errorf("检查广告组是否存在失败: %v", err)
		}
		if groupCount == 0 {
			return domain.PlatformCreativeEntity{}, errors.New("关联的广告组不存在")
		}
	}

	// 创建创意实体
	creative := model.PlatformCreative{
		PlatformType: param.PlatformType,
		AgentID:      param.AgentID,
		MediaID:      param.MediaID,
		DataID:       param.DataID,
		PlanID:       param.PlanID,
		GroupID:      param.GroupID,
		Name:         param.Title,
		Remark:       param.Description,
	}

	// 保存到数据库
	err = s.db.WithContext(ctx).Create(&creative).Error
	if err != nil {
		return domain.PlatformCreativeEntity{}, fmt.Errorf("创建广告创意失败: %v", err)
	}

	// 查询完整信息包括关联数据
	result, err := s.GetByID(ctx, creative.ID)
	if err != nil {
		return domain.PlatformCreativeEntity{}, err
	}

	return result, nil
}

// GetByID 根据ID获取广告创意详情
func (s *PlatformCreativeService) GetByID(ctx context.Context, id int64) (domain.PlatformCreativeEntity, error) {
	if id == 0 {
		return domain.PlatformCreativeEntity{}, errors.New("创意ID不能为空")
	}

	var result domain.PlatformCreativeEntity

	// 查询创意及关联数据
	query := `
		SELECT 
			c.id, c.name as title, c.data_id, c.platform_type, c.agent_id, c.media_id, 
			c.plan_id, c.group_id, c.remark as description, c.created_at, c.updated_at,
			COALESCE(p.name, '') as plan_name,
			COALESCE(g.name, '') as group_name,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name,
			COALESCE(p.market_target_name, '') as market_target_name
		FROM platform_creatives c
		LEFT JOIN platform_plans p ON p.id = c.plan_id
		LEFT JOIN platform_groups g ON g.id = c.group_id
		LEFT JOIN ad_agents a ON a.id = c.agent_id  
		LEFT JOIN ad_media m ON m.id = c.media_id
		WHERE c.id = ?
	`

	var record struct {
		ID               int64        `gorm:"column:id"`
		Title            string       `gorm:"column:title"`
		DataID           string       `gorm:"column:data_id"`
		PlatformType     string       `gorm:"column:platform_type"`
		AgentID          int64        `gorm:"column:agent_id"`
		MediaID          int64        `gorm:"column:media_id"`
		PlanID           int64        `gorm:"column:plan_id"`
		GroupID          int64        `gorm:"column:group_id"`
		Description      string       `gorm:"column:description"`
		CreatedAt        sql.NullTime `gorm:"column:created_at"`
		UpdatedAt        sql.NullTime `gorm:"column:updated_at"`
		PlanName         string       `gorm:"column:plan_name"`
		GroupName        string       `gorm:"column:group_name"`
		AgentName        string       `gorm:"column:agent_name"`
		MediaName        string       `gorm:"column:media_name"`
		MarketTargetName string       `gorm:"column:market_target_name"`
	}

	err := s.db.WithContext(ctx).Raw(query, id).Scan(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformCreativeEntity{}, errors.New("广告创意不存在")
		}
		return domain.PlatformCreativeEntity{}, fmt.Errorf("获取广告创意详情失败: %v", err)
	}

	// 转换为domain实体
	var createdAt, updatedAt int64
	if record.CreatedAt.Valid {
		createdAt = record.CreatedAt.Time.Unix()
	}
	if record.UpdatedAt.Valid {
		updatedAt = record.UpdatedAt.Time.Unix()
	}

	result = domain.PlatformCreativeEntity{
		ID:               record.ID,
		Title:            record.Title,
		DataID:           record.DataID,
		PlatformType:     record.PlatformType,
		AgentID:          record.AgentID,
		MediaID:          record.MediaID,
		PlanID:           record.PlanID,
		GroupID:          record.GroupID,
		Description:      record.Description,
		PlanName:         record.PlanName,
		GroupName:        record.GroupName,
		AgentName:        record.AgentName,
		MediaName:        record.MediaName,
		MarketTargetName: record.MarketTargetName,
		CreatedAt:        createdAt,
		UpdatedAt:        updatedAt,
	}

	return result, nil
}

// Update 更新广告创意
func (s *PlatformCreativeService) Update(ctx context.Context, param domain.PlatformCreativeUpdateParam) (domain.PlatformCreativeEntity, error) {
	if param.ID == 0 {
		return domain.PlatformCreativeEntity{}, errors.New("创意ID不能为空")
	}

	// 查询创意是否存在
	var creative model.PlatformCreative
	err := s.db.WithContext(ctx).First(&creative, param.ID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformCreativeEntity{}, errors.New("广告创意不存在")
		}
		return domain.PlatformCreativeEntity{}, fmt.Errorf("查询广告创意失败: %v", err)
	}

	// 更新字段
	if param.Title != "" {
		creative.Name = param.Title
	}
	if param.Description != "" {
		creative.Remark = param.Description
	}
	if param.DataID != "" {
		creative.DataID = param.DataID
	}

	// 保存更新
	err = s.db.WithContext(ctx).Save(&creative).Error
	if err != nil {
		return domain.PlatformCreativeEntity{}, fmt.Errorf("更新广告创意失败: %v", err)
	}

	// 重新查询完整信息
	result, err := s.GetByID(ctx, creative.ID)
	if err != nil {
		return domain.PlatformCreativeEntity{}, err
	}

	return result, nil
}

// Delete 删除广告创意
func (s *PlatformCreativeService) Delete(ctx context.Context, param domain.PlatformCreativeDeleteParam) (domain.PlatformCreativeEntity, error) {
	if param.ID == 0 {
		return domain.PlatformCreativeEntity{}, errors.New("创意ID不能为空")
	}

	// 先查询出详情信息，用于返回
	entity, err := s.GetByID(ctx, param.ID)
	if err != nil {
		return domain.PlatformCreativeEntity{}, err
	}

	// 执行删除
	err = s.db.WithContext(ctx).Delete(&model.PlatformCreative{}, param.ID).Error
	if err != nil {
		return domain.PlatformCreativeEntity{}, fmt.Errorf("删除广告创意失败: %v", err)
	}

	return entity, nil
}

// GetByDataId 根据平台和数据ID获取广告创意
func (s *PlatformCreativeService) GetByDataId(ctx context.Context, platformType, dataId string) (domain.PlatformCreativeEntity, error) {
	if platformType == "" || dataId == "" {
		return domain.PlatformCreativeEntity{}, errors.New("平台类型和创意ID不能为空")
	}

	// 查询创意ID
	var creative model.PlatformCreative
	err := s.db.WithContext(ctx).Where("platform_type = ? AND data_id = ?", platformType, dataId).First(&creative).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.PlatformCreativeEntity{}, errors.New("广告创意不存在")
		}
		return domain.PlatformCreativeEntity{}, fmt.Errorf("查询广告创意失败: %v", err)
	}

	// 获取完整信息
	return s.GetByID(ctx, creative.ID)
}

// GetByPlanId 根据计划ID获取广告创意列表
func (s *PlatformCreativeService) GetByPlanId(ctx context.Context, planId uint64) ([]domain.PlatformCreativeEntity, error) {
	if planId == 0 {
		return nil, errors.New("计划ID不能为空")
	}

	// 查询创意列表
	query := `
		SELECT 
			c.id, c.name as title, c.data_id, c.platform_type, c.agent_id, c.media_id, 
			c.plan_id, c.group_id, c.remark as description, c.created_at, c.updated_at,
			COALESCE(p.name, '') as plan_name,
			COALESCE(g.name, '') as group_name,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name,
			COALESCE(p.market_target_name, '') as market_target_name
		FROM platform_creatives c
		LEFT JOIN platform_plans p ON p.id = c.plan_id
		LEFT JOIN platform_groups g ON g.id = c.group_id
		LEFT JOIN ad_agents a ON a.id = c.agent_id  
		LEFT JOIN ad_media m ON m.id = c.media_id
		WHERE c.plan_id = ?
	`

	var dbResults []struct {
		ID               int64        `gorm:"column:id"`
		Title            string       `gorm:"column:title"`
		DataID           string       `gorm:"column:data_id"`
		PlatformType     string       `gorm:"column:platform_type"`
		AgentID          int64        `gorm:"column:agent_id"`
		MediaID          int64        `gorm:"column:media_id"`
		PlanID           int64        `gorm:"column:plan_id"`
		GroupID          int64        `gorm:"column:group_id"`
		Description      string       `gorm:"column:description"`
		CreatedAt        sql.NullTime `gorm:"column:created_at"`
		UpdatedAt        sql.NullTime `gorm:"column:updated_at"`
		PlanName         string       `gorm:"column:plan_name"`
		GroupName        string       `gorm:"column:group_name"`
		AgentName        string       `gorm:"column:agent_name"`
		MediaName        string       `gorm:"column:media_name"`
		MarketTargetName string       `gorm:"column:market_target_name"`
	}

	err := s.db.WithContext(ctx).Raw(query, planId).Scan(&dbResults).Error
	if err != nil {
		return nil, fmt.Errorf("获取计划的广告创意列表失败: %v", err)
	}

	// 转换结果
	results := make([]domain.PlatformCreativeEntity, 0, len(dbResults))
	for _, record := range dbResults {
		var createdAt, updatedAt int64
		if record.CreatedAt.Valid {
			createdAt = record.CreatedAt.Time.Unix()
		}
		if record.UpdatedAt.Valid {
			updatedAt = record.UpdatedAt.Time.Unix()
		}

		creative := domain.PlatformCreativeEntity{
			ID:               record.ID,
			Title:            record.Title,
			DataID:           record.DataID,
			PlatformType:     record.PlatformType,
			AgentID:          record.AgentID,
			MediaID:          record.MediaID,
			PlanID:           record.PlanID,
			GroupID:          record.GroupID,
			Description:      record.Description,
			PlanName:         record.PlanName,
			GroupName:        record.GroupName,
			AgentName:        record.AgentName,
			MediaName:        record.MediaName,
			MarketTargetName: record.MarketTargetName,
			CreatedAt:        createdAt,
			UpdatedAt:        updatedAt,
		}
		results = append(results, creative)
	}

	return results, nil
}

// GetByGroupId 根据广告组ID获取广告创意列表
func (s *PlatformCreativeService) GetByGroupId(ctx context.Context, groupId uint64) ([]domain.PlatformCreativeEntity, error) {
	if groupId == 0 {
		return nil, errors.New("广告组ID不能为空")
	}

	// 查询创意列表
	query := `
		SELECT 
			c.id, c.name as title, c.data_id, c.platform_type, c.agent_id, c.media_id, 
			c.plan_id, c.group_id, c.remark as description, c.created_at, c.updated_at,
			COALESCE(p.name, '') as plan_name,
			COALESCE(g.name, '') as group_name,
			COALESCE(a.name, '') as agent_name,
			COALESCE(m.name, '') as media_name,
			COALESCE(p.market_target_name, '') as market_target_name
		FROM platform_creatives c
		LEFT JOIN platform_plans p ON p.id = c.plan_id
		LEFT JOIN platform_groups g ON g.id = c.group_id
		LEFT JOIN ad_agents a ON a.id = c.agent_id  
		LEFT JOIN ad_media m ON m.id = c.media_id
		WHERE c.group_id = ?
	`

	var dbResults []struct {
		ID               int64        `gorm:"column:id"`
		Title            string       `gorm:"column:title"`
		DataID           string       `gorm:"column:data_id"`
		PlatformType     string       `gorm:"column:platform_type"`
		AgentID          int64        `gorm:"column:agent_id"`
		MediaID          int64        `gorm:"column:media_id"`
		PlanID           int64        `gorm:"column:plan_id"`
		GroupID          int64        `gorm:"column:group_id"`
		Description      string       `gorm:"column:description"`
		CreatedAt        sql.NullTime `gorm:"column:created_at"`
		UpdatedAt        sql.NullTime `gorm:"column:updated_at"`
		PlanName         string       `gorm:"column:plan_name"`
		GroupName        string       `gorm:"column:group_name"`
		AgentName        string       `gorm:"column:agent_name"`
		MediaName        string       `gorm:"column:media_name"`
		MarketTargetName string       `gorm:"column:market_target_name"`
	}

	err := s.db.WithContext(ctx).Raw(query, groupId).Scan(&dbResults).Error
	if err != nil {
		return nil, fmt.Errorf("获取广告组的创意列表失败: %v", err)
	}

	// 转换结果
	results := make([]domain.PlatformCreativeEntity, 0, len(dbResults))
	for _, record := range dbResults {
		var createdAt, updatedAt int64
		if record.CreatedAt.Valid {
			createdAt = record.CreatedAt.Time.Unix()
		}
		if record.UpdatedAt.Valid {
			updatedAt = record.UpdatedAt.Time.Unix()
		}

		creative := domain.PlatformCreativeEntity{
			ID:               record.ID,
			Title:            record.Title,
			DataID:           record.DataID,
			PlatformType:     record.PlatformType,
			AgentID:          record.AgentID,
			MediaID:          record.MediaID,
			PlanID:           record.PlanID,
			GroupID:          record.GroupID,
			Description:      record.Description,
			PlanName:         record.PlanName,
			GroupName:        record.GroupName,
			AgentName:        record.AgentName,
			MediaName:        record.MediaName,
			MarketTargetName: record.MarketTargetName,
			CreatedAt:        createdAt,
			UpdatedAt:        updatedAt,
		}
		results = append(results, creative)
	}

	return results, nil
}

// GetStatistics 获取创意统计信息
func (s *PlatformCreativeService) GetStatistics(ctx context.Context) (map[string]any, error) {
	var totalCount int64
	err := s.db.WithContext(ctx).Model(&model.PlatformCreative{}).Count(&totalCount).Error
	if err != nil {
		return nil, fmt.Errorf("获取广告创意统计数据失败: %v", err)
	}

	var platformCounts []struct {
		PlatformType string `json:"platform_type"`
		Count        int64  `json:"count"`
	}

	err = s.db.WithContext(ctx).Model(&model.PlatformCreative{}).
		Select("platform_type, COUNT(*) as count").
		Group("platform_type").
		Scan(&platformCounts).Error
	if err != nil {
		return nil, fmt.Errorf("获取平台统计数据失败: %v", err)
	}

	// 转换平台数据为map结构
	platformStats := make(map[string]int64)
	for _, p := range platformCounts {
		platformStats[p.PlatformType] = p.Count
	}

	// 返回统计数据
	return map[string]any{
		"total":     totalCount,
		"platforms": platformStats,
		"today": map[string]any{
			"created": s.getTodayCreatedCount(ctx),
		},
	}, nil
}

// getTodayCreatedCount 获取今日新增创意数量
func (s *PlatformCreativeService) getTodayCreatedCount(ctx context.Context) int64 {
	now := time.Now()
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	var count int64
	s.db.WithContext(ctx).Model(&model.PlatformCreative{}).
		Where("created_at >= ?", startOfDay).
		Count(&count)

	return count
}
