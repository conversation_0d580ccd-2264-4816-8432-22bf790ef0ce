package vo

import (
	"gin-backend/internal/service/domain"
)

// SlotListReq 获取资源位列表请求
type SlotListReq struct {
	Name        string `form:"name" json:"name"`
	Type        string `form:"type" json:"type"`
	AuditStatus string `form:"audit_status" json:"audit_status"`
	MediaID     int    `form:"media_id" json:"media_id"`
	UserID      int    `form:"user_id" json:"user_id"`
	Page        int    `form:"page" json:"page" binding:"required,min=1"`
	Size        int    `form:"size" json:"size" binding:"required,min=1,max=100"`
}

// ToSlotListParam 转换为资源位列表参数
func (r SlotListReq) ToSlotListParam() domain.SlotListParam {
	return domain.SlotListParam{
		Name:        r.Name,
		Type:        r.Type,
		AuditStatus: r.AuditStatus,
		MediaID:     r.MediaID,
		UserID:      r.User<PERSON>,
		Page:        r.<PERSON>,
		Size:        r.<PERSON>,
	}
}

// SlotListResp 获取资源位列表响应
type SlotListResp struct {
	Total int64          `json:"total"`
	List  []SlotItemResp `json:"list"`
}

// SlotItemResp 资源位列表项
type SlotItemResp struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	MediaID     int64  `json:"media_id"`
	MediaName   string `json:"media_name"`
	Description string `json:"description"`
	AuditStatus string `json:"audit_status"`
	CreatedAt   string `json:"created_at"`
	CreatedBy   string `json:"created_by"`
	UpdatedAt   string `json:"updated_at"`
	UpdatedBy   string `json:"updated_by"`
}

// FromDomainResult 从领域层结果转换为视图层响应
func FromDomainResult(result domain.SlotListResult) SlotListResp {
	resp := SlotListResp{
		Total: result.Total,
		List:  make([]SlotItemResp, 0, len(result.List)),
	}

	for _, entity := range result.List {
		resp.List = append(resp.List, SlotItemResp{
			ID:          entity.ID,
			Name:        entity.Name,
			Type:        entity.Type,
			MediaID:     entity.MediaID,
			MediaName:   entity.MediaName,
			Description: entity.Description,
			AuditStatus: entity.AuditStatus,
			CreatedAt:   entity.CreatedAt,
			CreatedBy:   entity.CreatedBy,
			UpdatedAt:   entity.UpdatedAt,
			UpdatedBy:   entity.UpdatedBy,
		})
	}

	return resp
}

// SlotCreateReq 创建资源位请求
type SlotCreateReq struct {
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	MediaID     int64 `json:"media_id" binding:"required"`
	Description string `json:"description"`
}

// ToCreateEntity 转换为创建资源位实体
func (r SlotCreateReq) ToCreateEntity() domain.SlotCreateEntity {
	return domain.SlotCreateEntity{
		Name:        r.Name,
		Type:        r.Type,
		MediaID:     r.MediaID,
		Description: r.Description,
	}
}

// SlotCreateResp 创建资源位响应
type SlotCreateResp struct {
	ID int64 `json:"id"`
}

// SlotUpdateReq 更新资源位请求
type SlotUpdateReq struct {
	ID          int64 `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Type        string `json:"type" binding:"required"`
	MediaID     int64 `json:"media_id" binding:"required"`
	Description string `json:"description"`
}

// ToUpdateEntity 转换为更新资源位实体
func (r SlotUpdateReq) ToUpdateEntity() domain.SlotUpdateEntity {
	return domain.SlotUpdateEntity{
		ID:          r.ID,
		Name:        r.Name,
		Type:        r.Type,
		MediaID:     r.MediaID,
		Description: r.Description,
	}
}

// SlotUpdateResp 更新资源位响应
type SlotUpdateResp struct {
	ID int64 `json:"id"`
}

// SlotDeleteResp 删除资源位响应
type SlotDeleteResp struct {
	ID int64 `json:"id"`
}

// SlotAuditReq 审核资源位请求
type SlotAuditReq struct {
	ID          int64 `json:"id" binding:"required"`
	AuditStatus string `json:"audit_status" binding:"required,oneof=approved rejected"`
	AuditRemark string `json:"audit_remark"`
}

// ToAuditEntity 转换为审核资源位实体
func (r SlotAuditReq) ToAuditEntity() domain.SlotAuditEntity {
	return domain.SlotAuditEntity{
		ID:          r.ID,
		AuditStatus: r.AuditStatus,
		AuditRemark: r.AuditRemark,
	}
}

// SlotAuditResp 审核资源位响应
type SlotAuditResp struct {
	ID int64 `json:"id"`
}
