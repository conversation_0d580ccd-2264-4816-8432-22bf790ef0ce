package bootstrap

import (
	"context"
	"fmt"
	"gin-backend/internal/config"
	"gin-backend/internal/global"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// InitRedis 初始化Redis客户端
func InitRedis() {
	cfg := config.AppConfig.Redis

	// 构建Redis连接地址
	addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)

	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: cfg.Password,
		DB:       cfg.DB,
		PoolSize: 10, // 默认连接池大小
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.Ping(ctx).Result()
	if err != nil {
		zap.L().Error("Redis连接失败", zap.Error(err))
		return
	}

	zap.L().Info("Redis连接成功")
	global.RegisterCache(client)
	return
}
