package domain

// DataScopeParam 数据权限范围查询参数
type DataScopeParam struct {
	UserID int64  // 用户ID
	Module string // 模块名称
}

// UserDepartmentParam 用户部门查询参数
type UserDepartmentParam struct {
	UserID int64 // 用户ID
}

// DepartmentUsersParam 部门用户查询参数
type DepartmentUsersParam struct {
	Department string // 部门名称
}

// PermissionParam 权限查询参数
type PermissionParam struct {
	RoleID int64 // 角色ID
}

// Permission 权限实体
type Permission struct {
	ID          int64  // 权限ID
	Name        string // 权限名称
	Code        string // 权限代码
	Module      string // 所属模块
	Type        string // 权限类型
	DataScope   string // 数据权限范围
	Description string // 描述
	Status      int    // 状态：1-启用 2-禁用
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}
