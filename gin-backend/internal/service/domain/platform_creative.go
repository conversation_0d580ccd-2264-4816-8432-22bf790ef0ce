package domain

// PlatformCreativeListParam 广告创意列表查询参数
type PlatformCreativeListParam struct {
	Page             int    // 页码
	PageSize         int    // 每页数量
	Size             int    // 兼容性参数，同 PageSize
	AgentID          int    // 代理ID
	MediaID          int    // 媒体ID
	MarketTargetName string // 营销目标名称
	PlanKeyword      string // 计划关键词
	GroupKeyword     string // 广告组关键词
	Keyword          string // 通用关键词
	WithPlan         bool   // 是否加载计划信息
	PlatformType     string // 平台类型
}

// PlatformCreativeEntity 广告创意实体
type PlatformCreativeEntity struct {
	ID               int64  // 创意ID
	AgentID          int64  // 代理ID
	MediaID          int64  // 媒体ID
	PlanID           int64  // 计划ID
	GroupID          int64  // 广告组ID
	PlatformType     string // 平台类型
	DataID           string // 平台创意ID
	Title            string // 创意标题
	Description      string // 创意描述
	ImageURL         string // 图片URL
	VideoURL         string // 视频URL
	LandingPageURL   string // 落地页URL
	Status           int    // 状态
	PlanName         string // 计划名称
	GroupName        string // 组名称
	AgentName        string // 代理名称
	MediaName        string // 媒体名称
	MarketTargetName string // 营销目标名称
	CreatedAt        int64  // 创建时间
	UpdatedAt        int64  // 更新时间
}

// PlatformCreativeCreateParam 创建广告创意参数
type PlatformCreativeCreateParam struct {
	AgentID        int64  // 代理ID
	MediaID        int64  // 媒体ID
	PlanID         int64  // 计划ID
	GroupID        int64  // 组ID
	PlatformType   string // 平台类型
	DataID         string // 平台创意ID
	Title          string // 创意标题
	Description    string // 创意描述
	ImageURL       string // 图片URL
	VideoURL       string // 视频URL
	LandingPageURL string // 落地页URL
	Status         int    // 状态
}

// PlatformCreativeUpdateParam 更新广告创意参数
type PlatformCreativeUpdateParam struct {
	ID             int64  // 创意ID
	Title          string // 创意标题
	Description    string // 创意描述
	ImageURL       string // 图片URL
	VideoURL       string // 视频URL
	LandingPageURL string // 落地页URL
	Status         int    // 状态
	DataID         string // 平台创意ID
}

// PlatformCreativeDeleteParam 删除广告创意参数
type PlatformCreativeDeleteParam struct {
	ID int64 // 创意ID
}

// PlatformCreativeListResult 广告创意列表结果
type PlatformCreativeListResult struct {
	Total int64                    // 总数
	List  []PlatformCreativeEntity // 列表项
}
