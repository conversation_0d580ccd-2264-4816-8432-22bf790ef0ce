package xiaohongshu

import (
	"fmt"
	"log"
	"time"
)

// TokenManager 令牌管理器示例
type TokenManager struct {
	client       *Client
	accessToken  string
	refreshToken string
	expiresAt    time.Time
}

// NewTokenManager 创建令牌管理器
func NewTokenManager(appId, secret string, isProd bool) *TokenManager {
	config := &Config{
		AppId:  appId,
		Secret: secret,
		IsProd: isProd,
	}

	return &TokenManager{
		client: NewClient(config),
	}
}

// InitializeWithAuthCode 使用授权码初始化令牌
func (tm *TokenManager) InitializeWithAuthCode(authCode string) error {
	tokenResp, err := tm.client.GetAccessToken(authCode)
	if err != nil {
		return fmt.Errorf("获取访问令牌失败: %w", err)
	}

	if !tokenResp.Success {
		return fmt.Errorf("API返回错误: code=%d, msg=%s", tokenResp.Code, tokenResp.Msg)
	}

	tm.accessToken = tokenResp.Data.AccessToken
	tm.refreshToken = tokenResp.Data.RefreshToken
	tm.expiresAt = time.Now().Add(time.Duration(tokenResp.Data.AccessTokenExpiresIn) * time.Second)

	log.Printf("令牌初始化成功，过期时间: %v", tm.expiresAt)
	return nil
}

// GetValidAccessToken 获取有效的访问令牌（自动刷新）
func (tm *TokenManager) GetValidAccessToken() (string, error) {
	// 检查令牌是否即将过期（提前5分钟刷新）
	if time.Now().Add(5 * time.Minute).After(tm.expiresAt) {
		log.Println("访问令牌即将过期，正在刷新...")
		if err := tm.refreshAccessToken(); err != nil {
			return "", fmt.Errorf("刷新访问令牌失败: %w", err)
		}
	}

	return tm.accessToken, nil
}

// refreshAccessToken 刷新访问令牌
func (tm *TokenManager) refreshAccessToken() error {
	tokenResp, err := tm.client.RefreshToken(tm.refreshToken)
	if err != nil {
		return fmt.Errorf("刷新令牌请求失败: %w", err)
	}

	if !tokenResp.Success {
		return fmt.Errorf("刷新令牌API返回错误: code=%d, msg=%s", tokenResp.Code, tokenResp.Msg)
	}

	tm.accessToken = tokenResp.Data.AccessToken
	tm.refreshToken = tokenResp.Data.RefreshToken
	tm.expiresAt = time.Now().Add(time.Duration(tokenResp.Data.AccessTokenExpiresIn) * time.Second)

	log.Printf("令牌刷新成功，新的过期时间: %v", tm.expiresAt)
	return nil
}

// IsTokenValid 检查令牌是否有效
func (tm *TokenManager) IsTokenValid() bool {
	return tm.accessToken != "" && time.Now().Before(tm.expiresAt)
}

// GetTokenInfo 获取令牌信息
func (tm *TokenManager) GetTokenInfo() (accessToken, refreshToken string, expiresAt time.Time) {
	return tm.accessToken, tm.refreshToken, tm.expiresAt
}

// 使用示例函数
func ExampleTokenManager() {
	// 1. 创建令牌管理器
	manager := NewTokenManager("your_app_id", "your_secret", false)

	// 2. 使用授权码初始化
	authCode := "d6a0b18531a2b9599a2c1e2361659c00"
	if err := manager.InitializeWithAuthCode(authCode); err != nil {
		log.Printf("初始化失败: %v", err)
		return
	}

	// 3. 获取有效的访问令牌（会自动刷新）
	accessToken, err := manager.GetValidAccessToken()
	if err != nil {
		log.Printf("获取访问令牌失败: %v", err)
		return
	}

	fmt.Printf("当前访问令牌: %s\n", accessToken)

	// 4. 检查令牌状态
	if manager.IsTokenValid() {
		fmt.Println("令牌有效")
	} else {
		fmt.Println("令牌无效")
	}

	// 5. 获取令牌详细信息
	token, refresh, expires := manager.GetTokenInfo()
	fmt.Printf("访问令牌: %s\n", token)
	fmt.Printf("刷新令牌: %s\n", refresh)
	fmt.Printf("过期时间: %v\n", expires)
}

// SimpleUsageExample 简单使用示例
func SimpleUsageExample() {
	// 创建配置
	config := &Config{
		AppId:  "3",
		Secret: "1234abc",
		IsProd: true,
	}

	// 创建客户端
	client := NewClient(config)

	// 获取访问令牌
	authCode := "d6a0b18531a2b9599a2c1e2361659c00"
	tokenResp, err := client.GetAccessToken(authCode)
	if err != nil {
		log.Printf("获取访问令牌失败: %v", err)
		return
	}

	if tokenResp.Success {
		fmt.Printf("获取令牌成功:\n")
		fmt.Printf("  访问令牌: %s\n", tokenResp.Data.AccessToken)
		fmt.Printf("  广告主ID: %d\n", tokenResp.Data.AdvertiserId)
		fmt.Printf("  广告主名称: %s\n", tokenResp.Data.ApprovalAdvertisers[0].AdvertiserName)

		// 刷新令牌
		newTokenResp, err := client.RefreshToken(tokenResp.Data.RefreshToken)
		if err != nil {
			log.Printf("刷新令牌失败: %v", err)
			return
		}

		if newTokenResp.Success {
			fmt.Printf("刷新令牌成功，新的访问令牌: %s\n", newTokenResp.Data.AccessToken)
		}
	}
}
