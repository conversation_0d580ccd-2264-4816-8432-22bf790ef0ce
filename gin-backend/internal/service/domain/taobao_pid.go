package domain

import "time"

// TaobaoPidEntity 淘联链接实体
type TaobaoPidEntity struct {
	ID           int64
	AccountName  string
	MemberID     int64
	ZoneName     string
	PID          string
	IsUsed       int
	UsedAt       time.Time
	UsedBy       int64
	AdSlotPlanID int64
	AdCreativeID int64
	ActType      int
	CreatedAt    time.Time
	UpdatedAt    time.Time
	DeletedAt    time.Time
}

// TaobaoPidListParam 获取淘联链接列表参数
type TaobaoPidListParam struct {
	Page    int
	Size    int
	Keyword string
	IsUsed  int
	ActType int
	Sort    string
}

// TaobaoPidCreateParam 创建淘联链接参数
type TaobaoPidCreateParam struct {
	ZoneName string
	PID      string
	ActType  int
}

// TaobaoPidUpdateParam 更新淘联链接参数
type TaobaoPidUpdateParam struct {
	ID       int64
	ZoneName string
	PID      string
	ActType  int
}

// TaobaoPidListEntity 淘联链接列表结果实体
type TaobaoPidListEntity struct {
	Items       []TaobaoPidEntity
	Total       int64
	UsedCount   int64
	UnusedCount int64
}

// 活动类型常量
const (
	ActTypeFliggy  = 1 // 飞猪
	ActTypeWelfare = 2 // 福利购
)

// 使用状态常量
const (
	PidUnused = 0 // 未使用
	PidUsed   = 1 // 已使用
)
