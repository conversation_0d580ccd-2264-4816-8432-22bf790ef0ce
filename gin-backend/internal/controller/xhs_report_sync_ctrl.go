package controller

import (
	"gin-backend/internal/service"
	"gin-backend/job"
	"gin-backend/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// XHSReportSyncController 小红书报表同步控制器
type XHSReportSyncController struct {
	syncJob         *job.XHSCreativeReportSyncJob
	realtimeSyncJob *job.XHSRealtimeReportSyncJob
	noteSyncJob     *job.XHSNoteSyncJob
}

// NewXHSReportSyncController 创建小红书报表同步控制器
func NewXHSReportSyncController() *XHSReportSyncController {
	return &XHSReportSyncController{
		syncJob:         job.NewXHSCreativeReportSyncJob(),
		realtimeSyncJob: job.NewXHSRealtimeReportSyncJob(),
		noteSyncJob:     job.NewXHSNoteSyncJob(),
	}
}

// SyncYesterdayRequest 同步昨天数据请求
type SyncYesterdayRequest struct{}

// SyncYesterdayResponse 同步昨天数据响应
type SyncYesterdayResponse struct {
	Message string `json:"message"`
}

// SyncSpecificDateRequest 同步指定日期数据请求
type SyncSpecificDateRequest struct {
	Date string `json:"date" binding:"required"` // 格式: 2006-01-02
}

// SyncSpecificDateResponse 同步指定日期数据响应
type SyncSpecificDateResponse struct {
	Date    string `json:"date"`
	Message string `json:"message"`
}

// SyncDateRangeRequest 同步日期范围数据请求
type SyncDateRangeRequest struct {
	StartDate string `json:"start_date" binding:"required"` // 格式: 2006-01-02
	EndDate   string `json:"end_date" binding:"required"`   // 格式: 2006-01-02
}

// SyncDateRangeResponse 同步日期范围数据响应
type SyncDateRangeResponse struct {
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Message   string `json:"message"`
}

// SyncRealtimeRequest 同步实时数据请求
type SyncRealtimeRequest struct{}

// SyncRealtimeResponse 同步实时数据响应
type SyncRealtimeResponse struct {
	Message string `json:"message"`
}

// SyncNotesRequest 同步笔记数据请求
type SyncNotesRequest struct{}

// SyncNotesResponse 同步笔记数据响应
type SyncNotesResponse struct {
	Message string `json:"message"`
}

// SyncYesterday 同步昨天的创意报表数据
// @Summary 同步昨天的小红书创意报表数据
// @Description 手动触发同步昨天的小红书创意报表数据
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=SyncYesterdayResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/yesterday [post]
func (ctrl *XHSReportSyncController) SyncYesterday(c *gin.Context) {
	zap.L().Info("手动触发同步昨天的小红书创意报表数据")

	// 异步执行同步任务
	go func() {
		if err := ctrl.syncJob.Run(); err != nil {
			zap.L().Error("同步昨天数据失败", zap.Error(err))
		}
	}()

	response.Success(c, SyncYesterdayResponse{
		Message: "同步任务已启动，请查看日志了解执行结果",
	})
}

// SyncSpecificDate 同步指定日期的创意报表数据
// @Summary 同步指定日期的小红书创意报表数据
// @Description 手动触发同步指定日期的小红书创意报表数据
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Param request body SyncSpecificDateRequest true "同步指定日期请求"
// @Success 200 {object} response.Response{data=SyncSpecificDateResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/date [post]
func (ctrl *XHSReportSyncController) SyncSpecificDate(c *gin.Context) {
	var req SyncSpecificDateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		response.InvalidParam(c, "日期格式错误，请使用 YYYY-MM-DD 格式")
		return
	}

	// 检查日期不能是未来日期
	if date.After(time.Now()) {
		response.InvalidParam(c, "不能同步未来日期的数据")
		return
	}

	// 检查日期不能太久远（比如超过90天）
	if time.Since(date) > 90*24*time.Hour {
		response.InvalidParam(c, "不能同步超过90天前的数据")
		return
	}

	zap.L().Info("手动触发同步指定日期的小红书创意报表数据", zap.String("date", req.Date))

	// 异步执行同步任务
	go func() {
		if err := ctrl.syncJob.SyncSpecificDate(date); err != nil {
			zap.L().Error("同步指定日期数据失败",
				zap.String("date", req.Date),
				zap.Error(err))
		}
	}()

	response.Success(c, SyncSpecificDateResponse{
		Date:    req.Date,
		Message: "同步任务已启动，请查看日志了解执行结果",
	})
}

// SyncDateRange 同步日期范围内的创意报表数据
// @Summary 同步日期范围内的小红书创意报表数据
// @Description 手动触发同步日期范围内的小红书创意报表数据（用于批量补数据）
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Param request body SyncDateRangeRequest true "同步日期范围请求"
// @Success 200 {object} response.Response{data=SyncDateRangeResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/range [post]
func (ctrl *XHSReportSyncController) SyncDateRange(c *gin.Context) {
	var req SyncDateRangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		response.InvalidParam(c, "开始日期格式错误，请使用 YYYY-MM-DD 格式")
		return
	}

	// 解析结束日期
	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		response.InvalidParam(c, "结束日期格式错误，请使用 YYYY-MM-DD 格式")
		return
	}

	// 验证日期范围
	if startDate.After(endDate) {
		response.InvalidParam(c, "开始日期不能晚于结束日期")
		return
	}

	// 检查日期不能是未来日期
	if endDate.After(time.Now()) {
		response.InvalidParam(c, "结束日期不能是未来日期")
		return
	}

	// 检查日期范围不能太大（比如超过30天）
	if endDate.Sub(startDate) > 30*24*time.Hour {
		response.InvalidParam(c, "日期范围不能超过30天")
		return
	}

	// 检查开始日期不能太久远（比如超过90天）
	if time.Since(startDate) > 90*24*time.Hour {
		response.InvalidParam(c, "开始日期不能超过90天前")
		return
	}

	zap.L().Info("手动触发同步日期范围内的小红书创意报表数据",
		zap.String("start_date", req.StartDate),
		zap.String("end_date", req.EndDate))

	// 异步执行同步任务
	go func() {
		if err := ctrl.syncJob.SyncDateRange(startDate, endDate); err != nil {
			zap.L().Error("同步日期范围数据失败",
				zap.String("start_date", req.StartDate),
				zap.String("end_date", req.EndDate),
				zap.Error(err))
		}
	}()

	response.Success(c, SyncDateRangeResponse{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Message:   "同步任务已启动，请查看日志了解执行结果",
	})
}

// GetSyncStatus 获取同步状态
// @Summary 获取小红书创意报表数据同步状态
// @Description 获取小红书创意报表数据的同步状态和统计信息
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/status [get]
func (ctrl *XHSReportSyncController) GetSyncStatus(c *gin.Context) {
	// 这里可以添加获取同步状态的逻辑
	// 比如查询数据库中最新的同步时间、数据量等
	reportService := service.NewXHSCreativeReportService()

	// 获取最近7天的数据统计
	stats := map[string]interface{}{
		"last_sync_time": "2024-01-01 02:00:00", // 这里应该从数据库查询
		"total_accounts": 0,                     // 已授权账号数量
		"total_reports":  0,                     // 总报表数量
		"last_7_days":    0,                     // 最近7天数据量
		"status":         "running",             // 同步状态
	}

	// TODO: 实现具体的状态查询逻辑
	_ = reportService

	response.Success(c, stats)
}

// SyncRealtime 同步当前小时的实时数据
// @Summary 同步当前小时的小红书创意实时数据
// @Description 手动触发同步当前小时的小红书创意实时数据
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=SyncRealtimeResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/realtime [post]
func (ctrl *XHSReportSyncController) SyncRealtime(c *gin.Context) {
	zap.L().Info("手动触发同步当前小时的小红书创意实时数据")

	// 异步执行同步任务
	go func() {
		if err := ctrl.realtimeSyncJob.RunManual(); err != nil {
			zap.L().Error("同步实时数据失败", zap.Error(err))
		}
	}()

	response.Success(c, SyncRealtimeResponse{
		Message: "实时数据同步任务已启动，请查看日志了解执行结果",
	})
}

// SyncNotes 同步笔记列表数据
// @Summary 同步小红书笔记列表数据
// @Description 手动触发同步小红书笔记列表数据
// @Tags 小红书报表同步
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=SyncNotesResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-reports/sync/notes [post]
func (ctrl *XHSReportSyncController) SyncNotes(c *gin.Context) {
	zap.L().Info("手动触发同步小红书笔记列表数据")

	// 异步执行同步任务
	go func() {
		if err := ctrl.noteSyncJob.RunManual(); err != nil {
			zap.L().Error("同步笔记数据失败", zap.Error(err))
		}
	}()

	response.Success(c, SyncNotesResponse{
		Message: "笔记数据同步任务已启动，请查看日志了解执行结果",
	})
}
