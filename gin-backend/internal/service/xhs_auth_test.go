package service

import (
	"context"
	"gin-backend/internal/config"
	"gin-backend/internal/service/domain"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestXHSAuthService_GetAuthUrl 测试获取授权链接
func TestXHSAuthService_GetAuthUrl(t *testing.T) {
	// 设置测试配置
	config.AppConfig = &config.Config{
		Xiaohongshu: config.XiaohongshuConfig{
			AppId:       "test_app_id",
			Secret:      "test_secret",
			RedirectUri: "http://test.example.com/callback",
			IsProd:      false,
		},
	}

	service := NewXHSAuthService()
	ctx := context.Background()

	// 测试获取授权链接
	result, err := service.GetAuthUrl(ctx)

	// 验证结果
	assert.NoError(t, err)
	assert.NotEmpty(t, result.AuthUrl)
	assert.Equal(t, "test_app_id", result.AppId)
	assert.Equal(t, "http://test.example.com/callback", result.RedirectUri)
	assert.NotEmpty(t, result.State)
	assert.Len(t, result.Scopes, 4) // 默认4个授权范围

	// 验证授权链接格式
	assert.Contains(t, result.AuthUrl, "https://ad-market.xiaohongshu.com/auth")
	assert.Contains(t, result.AuthUrl, "appId=test_app_id")
	assert.Contains(t, result.AuthUrl, "redirectUri=http%3A%2F%2Ftest.example.com%2Fcallback")
	assert.Contains(t, result.AuthUrl, "scope=")
	assert.Contains(t, result.AuthUrl, "state=")

	// 验证授权范围
	expectedScopes := []domain.XHSAuthScope{
		domain.ScopeReportService,
		domain.ScopeAdQuery,
		domain.ScopeAdManage,
		domain.ScopeAccountManage,
	}
	assert.ElementsMatch(t, expectedScopes, result.Scopes)
}

// TestXHSAuthService_GetAuthUrlWithCustomParams 测试自定义参数的授权链接
func TestXHSAuthService_GetAuthUrlWithCustomParams(t *testing.T) {
	// 设置测试配置
	config.AppConfig = &config.Config{
		Xiaohongshu: config.XiaohongshuConfig{
			AppId:       "test_app_id",
			Secret:      "test_secret",
			RedirectUri: "http://test.example.com/callback",
			IsProd:      false,
		},
	}

	service := NewXHSAuthService()
	ctx := context.Background()

	// 自定义参数
	customScopes := []domain.XHSAuthScope{
		domain.ScopeReportService,
		domain.ScopeAdQuery,
	}
	customState := "custom_test_state"

	// 测试获取自定义授权链接
	result, err := service.GetAuthUrlWithCustomParams(ctx, customScopes, customState)

	// 验证结果
	assert.NoError(t, err)
	assert.NotEmpty(t, result.AuthUrl)
	assert.Equal(t, "test_app_id", result.AppId)
	assert.Equal(t, "http://test.example.com/callback", result.RedirectUri)
	assert.Equal(t, customState, result.State)
	assert.ElementsMatch(t, customScopes, result.Scopes)

	// 验证授权链接包含自定义参数
	assert.Contains(t, result.AuthUrl, "state=custom_test_state")
	assert.Contains(t, result.AuthUrl, "report_service")
	assert.Contains(t, result.AuthUrl, "ad_query")
	assert.NotContains(t, result.AuthUrl, "ad_manage")
	assert.NotContains(t, result.AuthUrl, "account_manage")
}

// TestXHSAuthService_HandleAuthCallback 测试处理授权回调
func TestXHSAuthService_HandleAuthCallback(t *testing.T) {
	service := NewXHSAuthService()
	ctx := context.Background()

	// 测试成功回调
	successParam := domain.XHSAuthCallbackParam{
		Code:  "test_auth_code",
		State: "xhs_auth_state_test",
		Error: "",
	}

	err := service.HandleAuthCallback(ctx, successParam)
	assert.NoError(t, err)

	// 测试失败回调
	errorParam := domain.XHSAuthCallbackParam{
		Code:  "",
		State: "xhs_auth_state_test",
		Error: "access_denied",
	}

	err = service.HandleAuthCallback(ctx, errorParam)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "access_denied")

	// 测试无效状态参数
	invalidStateParam := domain.XHSAuthCallbackParam{
		Code:  "test_auth_code",
		State: "invalid_state",
		Error: "",
	}

	err = service.HandleAuthCallback(ctx, invalidStateParam)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "状态参数验证失败")
}

// TestXHSAuthService_GetAccessToken 测试获取访问令牌
func TestXHSAuthService_GetAccessToken(t *testing.T) {
	// 设置测试配置
	config.AppConfig = &config.Config{
		Xiaohongshu: config.XiaohongshuConfig{
			AppId:       "test_app_id",
			Secret:      "test_secret",
			RedirectUri: "http://test.example.com/callback",
			IsProd:      false,
		},
	}

	service := NewXHSAuthService()
	ctx := context.Background()

	// 测试获取访问令牌
	code := "test_auth_code"
	result, err := service.GetAccessToken(ctx, code)

	// 验证结果
	assert.NoError(t, err)
	assert.NotEmpty(t, result.AccessToken)
	assert.NotEmpty(t, result.RefreshToken)
	assert.Equal(t, "Bearer", result.TokenType)
	assert.Equal(t, int64(7200), result.ExpiresIn)
	assert.NotEmpty(t, result.Scope)

	// 验证令牌包含授权码
	assert.Contains(t, result.AccessToken, code)
	assert.Contains(t, result.RefreshToken, code)
}

// TestXHSAuthService_ValidateConfig 测试配置验证
func TestXHSAuthService_ValidateConfig(t *testing.T) {
	service := NewXHSAuthService()

	// 测试完整配置
	config.AppConfig = &config.Config{
		Xiaohongshu: config.XiaohongshuConfig{
			AppId:       "test_app_id",
			Secret:      "test_secret",
			RedirectUri: "http://test.example.com/callback",
			IsProd:      false,
		},
	}

	err := service.ValidateConfig()
	assert.NoError(t, err)

	// 测试缺少AppId
	config.AppConfig.Xiaohongshu.AppId = ""
	err = service.ValidateConfig()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "应用ID未配置")

	// 测试缺少Secret
	config.AppConfig.Xiaohongshu.AppId = "test_app_id"
	config.AppConfig.Xiaohongshu.Secret = ""
	err = service.ValidateConfig()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "应用密钥未配置")

	// 测试缺少RedirectUri
	config.AppConfig.Xiaohongshu.Secret = "test_secret"
	config.AppConfig.Xiaohongshu.RedirectUri = ""
	err = service.ValidateConfig()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "重定向URI未配置")
}

// TestFormatXHSAuthScopes 测试授权范围格式化
func TestFormatXHSAuthScopes(t *testing.T) {
	scopes := []domain.XHSAuthScope{
		domain.ScopeReportService,
		domain.ScopeAdQuery,
	}

	result := domain.FormatXHSAuthScopes(scopes)
	expected := `["report_service","ad_query"]`
	assert.Equal(t, expected, result)
}

// TestParseXHSAuthScopes 测试授权范围解析
func TestParseXHSAuthScopes(t *testing.T) {
	// 测试正常解析
	scopeStr := `["report_service","ad_query"]`
	scopes, err := domain.ParseXHSAuthScopes(scopeStr)
	assert.NoError(t, err)
	assert.Len(t, scopes, 2)
	assert.Contains(t, scopes, domain.ScopeReportService)
	assert.Contains(t, scopes, domain.ScopeAdQuery)

	// 测试无效范围
	invalidScopeStr := `["invalid_scope"]`
	_, err = domain.ParseXHSAuthScopes(invalidScopeStr)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "无效的授权范围")

	// 测试空字符串
	_, err = domain.ParseXHSAuthScopes("")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "授权范围字符串为空")
}

// TestBuildXHSAuthUrl 测试构建授权链接
func TestBuildXHSAuthUrl(t *testing.T) {
	param := domain.GetXHSAuthUrlParam{
		AppId:       "test_app_id",
		RedirectUri: "http://test.example.com/callback",
		Scopes: []domain.XHSAuthScope{
			domain.ScopeReportService,
			domain.ScopeAdQuery,
		},
		State: "test_state",
	}

	authUrl, err := domain.BuildXHSAuthUrl(param)
	assert.NoError(t, err)
	assert.NotEmpty(t, authUrl)

	// 验证URL格式
	assert.True(t, strings.HasPrefix(authUrl, "https://ad-market.xiaohongshu.com/auth?"))
	assert.Contains(t, authUrl, "appId=test_app_id")
	assert.Contains(t, authUrl, "redirectUri=http%3A//test.example.com/callback")
	assert.Contains(t, authUrl, "scope=")
	assert.Contains(t, authUrl, "state=test_state")
}

// TestGenerateXHSAuthState 测试生成状态参数
func TestGenerateXHSAuthState(t *testing.T) {
	state := domain.GenerateXHSAuthState()
	assert.NotEmpty(t, state)
	assert.True(t, strings.HasPrefix(state, "xhs_auth_state_"))
}

// TestValidateXHSAuthState 测试验证状态参数
func TestValidateXHSAuthState(t *testing.T) {
	// 测试有效状态
	validState := "xhs_auth_state_test"
	assert.True(t, domain.ValidateXHSAuthState(validState))

	// 测试无效状态
	invalidState := "invalid_state"
	assert.False(t, domain.ValidateXHSAuthState(invalidState))
}
