package controller

import (
	"strconv"

	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"

	"github.com/gin-gonic/gin"
)

// CostController 费用管理控制器
type CostController struct {
	costService       *service.CostService
	permissionService *service.PermissionService
	roleService       *service.RoleService
	taskService       *service.TaskService
}

// NewCostController 创建费用管理控制器实例
func NewCostController() *CostController {
	return &CostController{
		costService:       service.NewCostService(),
		permissionService: service.NewPermissionService(),
		roleService:       service.NewRoleService(),
		taskService:       service.NewTaskService(),
	}
}

// Create 创建费用记录
// @Summary 创建费用记录
// @Description 创建投放计划费用记录
// @Tags 费用管理
// @Accept json
// @Produce json
// @Param request body vo.CostCreateReq true "创建费用记录请求"
// @Success 200 {object} response.Response "创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost [post]
func (c *CostController) Create(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:create"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.CostCreateReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为领域实体
	entity := req.ToDomain()

	// 创建费用记录
	if err := c.costService.CreateCost(entity, userEntity.ID); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Update 更新费用记录
// @Summary 更新费用记录
// @Description 更新投放计划费用记录
// @Tags 费用管理
// @Accept json
// @Produce json
// @Param request body vo.CostUpdateReq true "更新费用记录请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost [put]
func (c *CostController) Update(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:edit"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.CostUpdateReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为领域实体
	entity := req.ToDomain()

	// 更新费用记录
	if err := c.costService.UpdateCost(entity, userEntity.ID); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// Delete 删除费用记录
// @Summary 删除费用记录
// @Description 删除投放计划费用记录
// @Tags 费用管理
// @Produce json
// @Param id path int true "费用记录ID"
// @Success 200 {object} response.Response "删除成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost/{id} [delete]
func (c *CostController) Delete(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:delete"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 获取ID参数
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的费用记录ID")
		return
	}

	// 删除费用记录
	if err := c.costService.DeleteCost(id); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// List 获取费用记录列表
// @Summary 获取费用记录列表
// @Description 分页获取投放计划费用记录列表
// @Tags 费用管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param plan_id query int false "投放计划ID"
// @Param media_id query int false "媒体ID"
// @Param ad_slot_id query int false "广告位ID"
// @Param ad_product_id query int false "产品ID"
// @Param user_id query int false "媒介ID"
// @Param start_date query string false "开始日期(YYYY-MM-DD)"
// @Param end_date query string false "结束日期(YYYY-MM-DD)"
// @Param audit_status query string false "审核状态(pending/approved/rejected)"
// @Success 200 {object} response.Response{data=vo.CostListResp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost [get]
func (c *CostController) List(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.CostListReq

	// 参数绑定和验证
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为领域参数
	param := req.ToDomain()

	// 获取费用记录列表
	result, err := c.costService.GetCostList(param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为视图对象
	responseVO := vo.FromDomainToCostListResp(result)

	response.Success(ctx, responseVO)
}

// GetByID 获取费用记录详情
// @Summary 获取费用记录详情
// @Description 根据ID获取投放计划费用记录详情
// @Tags 费用管理
// @Produce json
// @Param id path int true "费用记录ID"
// @Success 200 {object} response.Response{data=vo.CostItem} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost/{id} [get]
func (c *CostController) GetByID(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 获取ID参数
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "无效的费用记录ID")
		return
	}

	// 获取费用记录详情
	entity, err := c.costService.GetCostByID(id)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为视图对象
	responseVO := vo.FromDomainToCostItem(entity)

	response.Success(ctx, responseVO)
}

// Audit 审核费用记录
// @Summary 审核费用记录
// @Description 审核投放计划费用记录
// @Tags 费用管理
// @Accept json
// @Produce json
// @Param request body vo.CostAuditReq true "审核费用记录请求"
// @Success 200 {object} response.Response "审核成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost/audit [post]
func (c *CostController) Audit(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:audit"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	var req vo.CostAuditReq

	// 参数绑定和验证
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 转换为领域实体
	entity := req.ToDomain()

	// 审核费用记录
	if err := c.costService.AuditCost(entity, userEntity.ID); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// UpdateCostCompatible 兼容原项目的更新费用接口
// @Summary 更新推广成本（兼容接口）
// @Description 兼容原项目的更新推广成本接口
// @Tags 费用管理
// @Accept json
// @Produce json
// @Param request body gin.H true "更新成本请求"
// @Success 200 {object} response.Response "更新成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/reports/cost/update [post]
func (c *CostController) UpdateCostCompatible(ctx *gin.Context) {
	var reqData gin.H

	// 参数绑定
	if err := ctx.ShouldBindJSON(&reqData); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 将兼容格式转换为新格式
	var req domain.CostUpdateEntity

	// 检查必需字段
	if id, ok := reqData["id"]; ok {
		if idVal, ok := id.(float64); ok {
			req.ID = int64(idVal)
		}
	}
	if groupID, ok := reqData["groupId"]; ok {
		if id, ok := groupID.(float64); ok {
			req.PlanID = int64(id)
		}
	}
	if reportDate, ok := reqData["reportDate"]; ok {
		if date, ok := reportDate.(string); ok {
			// 如果是YYYYMMDD格式，转换为YYYY-MM-DD
			if len(date) == 8 {
				req.Date = date[:4] + "-" + date[4:6] + "-" + date[6:8]
			} else {
				req.Date = date
			}
		}
	}
	if cost, ok := reqData["cost"]; ok {
		if c, ok := cost.(float64); ok {
			req.Cost = c
		}
	}

	// 其他字段的默认值处理
	if req.ID == 0 || req.PlanID == 0 || req.Date == "" || req.Cost < 0 {
		response.InvalidParam(ctx, "缺少必需参数")
		return
	}

	// 这里需要根据业务逻辑设置其他字段的默认值
	// 由于原接口可能不包含所有字段，需要查询现有记录或设置默认值
	req.MediaID = 1     // 默认媒体ID
	req.AdSlotID = 1    // 默认广告位ID
	req.AdProductID = 1 // 默认产品ID
	req.UserID = 1      // 默认用户ID

	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:edit"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 调用更新服务
	if err := c.costService.UpdateCost(req, userEntity.ID); err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.Success(ctx, nil)
}

// GetCostListCompatible 兼容原项目的获取费用列表接口
// @Summary 获取推广成本列表（兼容接口）
// @Description 兼容原项目的获取推广成本列表接口
// @Tags 费用管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param groupId query int false "口令组ID"
// @Param categoryId query int false "分类ID"
// @Param startDate query string false "开始日期(YYYYMMDD)"
// @Param endDate query string false "结束日期(YYYYMMDD)"
// @Success 200 {object} response.Response "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/delivery/reports/cost/list [get]
func (c *CostController) GetCostListCompatible(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}
	// 兼容原项目的参数格式
	param := domain.CostListParam{
		Page:     1,
		PageSize: 10,
	}

	// 解析查询参数
	if page := ctx.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			param.Page = p
		}
	}
	if pageSize := ctx.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			param.PageSize = ps
		}
	}

	// 口令组ID参数处理
	if groupID := ctx.Query("groupId"); groupID != "" {
		if gid, err := strconv.ParseInt(groupID, 10, 64); err == nil {
			param.GroupID = gid
			param.PlanID = gid // 同时设置PlanID
		}
	}

	// 分类ID参数处理
	if categoryID := ctx.Query("categoryId"); categoryID != "" {
		if cid, err := strconv.ParseInt(categoryID, 10, 64); err == nil && cid > 0 {
			param.CategoryID = cid
		}
	}

	// 日期参数处理 - 从YYYYMMDD格式转换为YYYY-MM-DD格式
	if startDate := ctx.Query("startDate"); startDate != "" && len(startDate) == 8 {
		// 20241201 -> 2024-12-01
		if len(startDate) == 8 {
			param.StartDate = startDate[:4] + "-" + startDate[4:6] + "-" + startDate[6:8]
		}
	}
	if endDate := ctx.Query("endDate"); endDate != "" && len(endDate) == 8 {
		// 20241201 -> 2024-12-01
		if len(endDate) == 8 {
			param.EndDate = endDate[:4] + "-" + endDate[4:6] + "-" + endDate[6:8]
		}
	}

	// 获取费用记录列表
	result, err := c.costService.GetCostList(param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为兼容格式，完全按照原项目的下划线字段名和日期格式
	compatibleList := make([]gin.H, len(result.List))
	for i, item := range result.List {
		compatibleList[i] = gin.H{
			"id":            item.ID,
			"group_id":      item.PlanID,                  // 使用真实的口令组ID
			"group_name":    item.GroupName,               // 使用真实的口令组名称
			"report_date":   item.Date.Format("20060102"), // date -> report_date (YYYYMMDD格式)
			"category_name": item.CategoryName,            // 使用真实的分类名称
			"category_id":   item.CategoryID,              // 使用真实的分类ID
			"cost":          item.Cost,
			"update_at":     item.UpdatedAt.Format("2006-01-02 15:04:05"), // 格式化更新时间
		}
	}

	// 按照原项目的响应格式返回
	compatibleResp := gin.H{
		"list":  compatibleList,
		"total": result.Total,
		"page":  result.Page,
		"size":  result.PageSize, // pageSize -> size
	}

	response.Success(ctx, compatibleResp)
}

// ImportCost 导入费用记录
// @Summary 导入费用记录
// @Description 导入投放计划费用记录
// @Tags 费用管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "Excel文件"
// @Param category_id formData int true "分类ID"
// @Success 200 {object} response.Response "导入成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost/import [post]
func (c *CostController) ImportCost(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:import"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.InvalidParam(ctx, "获取上传文件失败: "+err.Error())
		return
	}

	// 获取分类ID
	categoryIdStr := ctx.PostForm("category_id")
	categoryId, err := strconv.ParseUint(categoryIdStr, 10, 64)
	if err != nil {
		response.InvalidParam(ctx, "分类ID格式不正确")
		return
	}

	// 创建导入参数
	importParam := domain.CostImportParam{
		CategoryID: int64(categoryId),
		File:       file,
		UserID:     userEntity.ID,
	}

	// 导入数据
	hasTask, err := c.costService.ImportCost(ctx, importParam)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 根据是否创建任务返回不同的提示
	var tip string
	if hasTask {
		tip = "导入成功，等待任务执行完成后，报表数据才会准确"
	} else {
		tip = "导入成功"
	}

	response.Success(ctx, gin.H{"tip": tip, "has_task": hasTask})
}

// GetTaskList 获取任务列表
// @Summary 获取任务列表
// @Description 获取费用导入的任务列表
// @Tags 费用管理
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param status query string false "任务状态"
// @Success 200 {object} response.Response{data=vo.TaskListResp} "获取成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/v1/cost/tasks [get]
func (c *CostController) GetTaskList(ctx *gin.Context) {
	// 权限检查
	userEntity := middleware.GetCurrentUser(ctx)
	if ok, _ := c.roleService.CheckUserPermission(ctx, userEntity.ID, "delivery:cost:view"); !ok {
		response.Error(ctx, response.CodeForbidden, "没有权限")
		return
	}

	// 解析请求参数
	var req vo.TaskListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(ctx, err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构造服务层参数
	param := domain.TaskListParam{
		Page:     req.Page,
		PageSize: req.PageSize,
		Status:   req.Status,
	}

	// 获取任务列表
	result, err := c.taskService.GetTaskList(ctx, param)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	// 转换为视图对象并返回
	resp := vo.ToTaskListResp(result)
	response.Success(ctx, resp)
}
