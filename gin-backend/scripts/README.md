# 权限系统配置脚本使用指南

## 概述

本目录包含完整的权限系统配置脚本，用于管理基于 GoFrame 项目的权限系统。

## 脚本文件说明

### 1. 主要配置脚本

#### `reset_permission_system_safe.sql`
- **用途**: 权限系统安全重置脚本
- **功能**: 清空现有权限数据并重新初始化完整的权限系统
- **包含**: 角色、权限、角色权限分配
- **使用场景**: 首次部署或完全重建权限系统

#### `add_daily_report_permissions.sql`
- **用途**: 日报表权限增量更新脚本  
- **功能**: 为现有权限系统添加日报表相关权限
- **使用场景**: 增量添加日报表功能权限

#### `add_weekly_report_permissions.sql`
- **用途**: 周报表权限增量更新脚本
- **功能**: 为现有权限系统添加周报表相关权限
- **使用场景**: 增量添加周报表功能权限

### 2. 验证脚本

#### `verify_permissions.sql`
- **用途**: 权限系统完整性验证脚本
- **功能**: 检查权限系统的健康度和配置正确性
- **使用场景**: 权限配置后的验证检查

## 使用流程

### 初次部署

1. **检查数据库环境**
   ```sql
   -- 确保数据库存在必要的表结构
   SHOW TABLES LIKE '%roles%';
   SHOW TABLES LIKE '%permissions%';
   ```

2. **执行主重置脚本**
   ```bash
   mysql -u username -p database_name < reset_permission_system_safe.sql
   ```

3. **验证权限系统**
   ```bash
   mysql -u username -p database_name < verify_permissions.sql
   ```

### 增量更新

#### 添加日报表权限
```bash
mysql -u username -p database_name < add_daily_report_permissions.sql
```

#### 添加周报表权限
```bash
mysql -u username -p database_name < add_weekly_report_permissions.sql
```

### 验证配置
```bash
mysql -u username -p database_name < verify_permissions.sql
```

## 权限系统架构

### 角色定义

| 角色编码 | 角色名称 | 权限范围 | 主要职责 |
|---------|---------|---------|---------|
| `super_admin` | 超级管理员 | 全部权限 | 系统完全管理 |
| `manager` | 管理层 | 大部分管理权限 | 业务管理 |
| `media_specialist` | 媒介 | 媒体代理管理 | 媒体关系维护 |
| `operator` | 运营 | 投放计划管理 | 日常运营 |
| `advertiser` | 投手 | 投放执行 | 广告投放 |
| `finance` | 财务 | 财务数据权限 | 财务管理 |
| `tech` | 技术 | 系统技术权限 | 技术支持 |
| `product` | 产品 | 产品规划权限 | 产品管理 |
| `bi_analyst` | BI分析师 | 数据分析权限 | 数据分析 |

### 权限模块

| 模块 | 权限前缀 | 包含功能 |
|-----|---------|---------|
| 仪表盘 | `dashboard:` | 数据总览、统计图表 |
| 报表系统 | `report:` | 日报表、周报表、自定义报表 |
| 代理管理 | `agent:` | 代理商管理 |
| 媒体管理 | `media:` | 媒体资源管理 |
| 资源位管理 | `slot:` | 广告位管理 |
| 投放计划 | `plan:` | 投放策略制定 |
| 投放管理 | `delivery:` | 投放执行和监控 |
| 创意管理 | `creative:` | 创意素材管理 |
| 财务管理 | `finance:` | 财务数据管理 |
| 系统管理 | `system:` | 用户、角色、权限管理 |
| 插件管理 | `plugin:` | 系统插件管理 |

### 权限类型

#### 功能权限 (type='function')
- 页面访问权限
- 操作执行权限
- 功能使用权限

#### 数据权限 (type='data')
- `all`: 全部数据范围
- `dept`: 部门数据范围  
- `self`: 个人数据范围

## 报表权限详解

### 日报表权限
- `report:daily:view` - 查看日报表页面
- `report:daily:query` - 查询日报表数据
- `report:daily:detail` - 查看详情数据
- `report:daily:export` - 导出日报表数据
- `report:daily:data:all/dept/self` - 数据范围权限

### 周报表权限
- `report:weekly:view` - 查看周报表页面
- `report:weekly:query` - 查询周报表数据
- `report:weekly:detail` - 查看详情数据
- `report:weekly:export` - 导出周报表数据
- `report:weekly:data:all/dept/self` - 数据范围权限

## 权限验证检查项

验证脚本会检查以下内容：

1. **基础表检查** - 确保必要的表存在并有数据
2. **权限模块统计** - 按模块统计权限数量
3. **角色权限统计** - 检查各角色权限配置
4. **报表权限检查** - 专门检查报表权限完整性
5. **权限分配矩阵** - 查看角色权限分配情况
6. **数据权限范围** - 检查数据权限配置
7. **问题检查** - 发现潜在的配置问题
8. **健康度评估** - 综合评估权限系统状态

## 注意事项

### 执行前准备
1. **备份数据库** - 执行任何权限脚本前都要备份
2. **确认环境** - 确保在正确的数据库环境中执行
3. **权限检查** - 确保执行用户有足够的数据库权限

### 安全考虑
1. **增量更新** - 生产环境建议使用增量脚本而非重置脚本
2. **验证检查** - 每次权限变更后都要运行验证脚本
3. **权限最小化** - 按照最小权限原则分配角色权限

### 常见问题

#### 权限重复插入
使用 `INSERT IGNORE` 防止重复插入权限记录

#### 外键约束
脚本会自动处理外键约束，但建议在低峰期执行

#### 权限不生效
检查以下项目：
1. 权限是否正确分配给角色
2. 用户是否关联了正确的角色
3. 权限状态是否为启用状态

## 扩展说明

### 添加新模块权限
1. 在 `permissions` 表中添加新权限
2. 为相关角色分配权限
3. 运行验证脚本检查配置

### 添加新角色
1. 在 `roles` 表中添加角色
2. 分配合适的权限
3. 验证权限配置正确性

## 技术支持

如果在使用过程中遇到问题，请检查：
1. 数据库连接是否正常
2. 表结构是否完整
3. 权限配置是否正确
4. 验证脚本的检查结果

建议定期运行验证脚本来维护权限系统的健康状态。 