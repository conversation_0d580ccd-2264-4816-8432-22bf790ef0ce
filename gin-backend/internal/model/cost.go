package model

import (
	"time"

	"gorm.io/gorm"
)

// AdSlotPlanCost 投放计划成本
type AdSlotPlanCost struct {
	ID           int64          `gorm:"column:id;primaryKey;autoIncrement"`
	PlanID       int64          `gorm:"column:plan_id;not null;index"`                               // 投放计划ID
	Date         time.Time      `gorm:"column:date;type:date;not null;index"`                        // 统计日期
	MediaID      int64          `gorm:"column:media_id;not null;index"`                              // 媒体ID
	AdSlotID     int64          `gorm:"column:ad_slot_id;not null;index"`                            // 广告位ID
	AdProductID  int64          `gorm:"column:ad_product_id;not null;index"`                         // 产品ID
	UserID       int64          `gorm:"column:user_id;not null;index"`                               // 媒介ID
	Clicks       int            `gorm:"column:clicks;default:0"`                                     // 点击量
	Cost         float64        `gorm:"column:cost;type:decimal(10,2);default:0.00"`                 // 成本
	Remark       string         `gorm:"column:remark;type:varchar(255)"`                             // 备注
	AuditStatus  string         `gorm:"column:audit_status;type:varchar(255);default:pending;index"` // 审核状态
	AuditBy      int64          `gorm:"column:audit_by"`                                             // 审核人
	AuditAt      time.Time      `gorm:"column:audit_at"`                                             // 审核时间
	RejectReason string         `gorm:"column:reject_reason;type:varchar(255)"`                      // 拒绝原因
	CreatedBy    int64          `gorm:"column:created_by"`                                           // 创建人
	UpdatedBy    int64          `gorm:"column:updated_by"`                                           // 更新人
	CreatedAt    time.Time      `gorm:"column:created_at"`                                           // 创建时间
	UpdatedAt    time.Time      `gorm:"column:updated_at"`                                           // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index"`                                     // 删除时间

	// 关联关系
	Plan      *AdSlotPlan `gorm:"foreignKey:PlanID"`      // 投放计划
	Media     *Media      `gorm:"foreignKey:MediaID"`     // 媒体
	AdSlot    *Slot       `gorm:"foreignKey:AdSlotID"`    // 广告位
	AdProduct *AdProduct  `gorm:"foreignKey:AdProductID"` // 产品
	User      *User       `gorm:"foreignKey:UserID"`      // 用户
}

// TableName 指定表名
func (AdSlotPlanCost) TableName() string {
	return "ad_slot_plan_costs"
}
