package model

import "time"

type XHSCreativeReports struct {
	Id        int64     // 主键ID
	CreatedAt time.Time // 创建时间
	UpdatedAt time.Time // 更新时间

	AccountId     int64  // 归属账号ID
	AccountName   string // 归属账号
	Pwd           string // 口令
	Title         string // 标题
	ContentPeople string // 内容人员
	PitcherName   string // 投放人员

	// ================= 业务字段 =================
	CampaignID      string `json:"campaign_id"`      // 计划ID
	CampaignName    string `json:"campaign_name"`    // 计划名称
	UnitID          string `json:"unit_id"`          // 单元ID
	UnitName        string `json:"unit_name"`        // 单元名称
	CreativityID    string `json:"creativity_id"`    // 创意ID
	CreativityName  string `json:"creativity_name"`  // 创意名称
	CreativityImage string `json:"creativity_image"` // 创意图片
	NoteID          string `json:"note_id"`          // 笔记ID
	Time            string `json:"time"`             // 时间
	Placement       string `json:"placement"`        // 广告类型
	OptimizeTarget  string `json:"optimize_target"`  // 优化目标
	PromotionTarget string `json:"promotion_target"` // 推广标的
	BiddingStrategy string `json:"bidding_strategy"` // 出价方式
	BuildType       string `json:"build_type"`       // 搭建类型
	MarketingTarget string `json:"marketing_target"` // 营销诉求
	PageID          string `json:"page_id"`          // 落地页ID
	ItemID          string `json:"item_id"`          // 商品ID
	LiveRedID       string `json:"live_red_id"`      // 直播间ID
	CountryName     string `json:"country_name"`     // 国家
	Province        string `json:"province"`         // 省份
	City            string `json:"city"`             // 城市
	NoteUserID      string `json:"note_user_id"`     // 笔记作者ID

	// ================= 基础指标 =================
	Fee        float64 `json:"fee"`        // 消费金额（单位：元）
	Impression int64   `json:"impression"` // 展现量
	Click      int64   `json:"click"`      // 点击量
	CTR        float64 `json:"ctr"`        // 点击率
	ACP        float64 `json:"acp"`        // 平均点击成本
	CPM        float64 `json:"cpm"`        // 平均千次曝光成本

	// ================= 笔记互动指标 =================
	Like              int64   `json:"like"`                // 点赞量
	Comment           int64   `json:"comment"`             // 评论量
	Collect           int64   `json:"collect"`             // 收藏量
	Follow            int64   `json:"follow"`              // 关注量
	Share             int64   `json:"share"`               // 分享量
	Interaction       int64   `json:"interaction"`         // 互动量
	CPI               float64 `json:"cpi"`                 // 平均互动成本
	ActionButtonClick int64   `json:"action_button_click"` // 行动按钮点击量
	ActionButtonCTR   float64 `json:"action_button_ctr"`   // 行动按钮点击率
	Screenshot        int64   `json:"screenshot"`          // 截图次数
	PicSave           int64   `json:"pic_save"`            // 保存图片次数
	ReservePV         int64   `json:"reserve_pv"`          // 预告组件点击量

	// ================= 直播间互动指标 =================
	ClkLiveEntryPV       int64   `json:"clk_live_entry_pv"`         // 直播间观看次数
	ClkLiveEntryPVCost   float64 `json:"clk_live_entry_pv_cost"`    // 直播间观看成本
	ClkLiveAvgViewTime   string  `json:"clk_live_avg_view_time"`    // 直播间人均停留时长(分钟)
	ClkLiveAllFollow     int64   `json:"clk_live_all_follow"`       // 直播间新增粉丝量
	ClkLive5sEntryPV     int64   `json:"clk_live_5s_entry_pv"`      // 直播间有效观看次数
	ClkLive5sEntryUVCost float64 `json:"clk_live_5s_entry_uv_cost"` // 直播间有效观看成本
	ClkLiveComment       int64   `json:"clk_live_comment"`          // 直播间评论次数

	// ================= 笔记种草指标 =================
	SearchCmtClick        int64   `json:"search_cmt_click"`          // 搜索组件点击量
	SearchCmtClickCVR     float64 `json:"search_cmt_click_cvr"`      // 搜索组件点击转化率
	SearchCmtAfterRead    int64   `json:"search_cmt_after_read"`     // 搜后阅读量
	SearchCmtAfterReadAvg float64 `json:"search_cmt_after_read_avg"` // 平均搜后阅读笔记篇数
	IUserNum              int64   `json:"i_user_num"`                // 新增种草人群
	TIUserNum             int64   `json:"ti_user_num"`               // 新增深度种草人群
	IUserPrice            float64 `json:"i_user_price"`              // 新增种草人群成本
	TIUserPrice           float64 `json:"ti_user_price"`             // 新增深度种草人群成本

	// ================= 电商转化指标/购买兴趣 =================
	GoodsVisit       int64   `json:"goods_visit"`        // 进店访问量
	GoodsVisitPrice  float64 `json:"goods_visit_price"`  // 进店访问成本
	SellerVisit      int64   `json:"seller_visit"`       // 商品访客量
	SellerVisitPrice float64 `json:"seller_visit_price"` // 商品访客成本
	ShoppingCartAdd  int64   `json:"shopping_cart_add"`  // 商品加购量
	AddCartPrice     float64 `json:"add_cart_price"`     // 商品加购成本

	// ================= 电商转化指标/7日转化 =================
	PresaleOrderNum7D    int64   `json:"presale_order_num_7d"`    // 7日预售订单量
	PresaleOrderGMV7D    string  `json:"presale_order_gmv_7d"`    // 7日预售订单金额
	GoodsOrder           int64   `json:"goods_order"`             // 7日下单订单量
	GoodsOrderPrice      float64 `json:"goods_order_price"`       // 7日下单订单成本
	RGMV                 float64 `json:"rgmv"`                    // 7日下单金额
	ROI                  float64 `json:"roi"`                     // 7日下单ROI
	SuccessGoodsOrder    int64   `json:"success_goods_order"`     // 7日支付订单量
	ClickOrderCVR        float64 `json:"click_order_cvr"`         // 7日支付转化率
	PurchaseOrderPrice7D float64 `json:"purchase_order_price_7d"` // 7日支付订单成本
	PurchaseOrderGMV7D   float64 `json:"purchase_order_gmv_7d"`   // 7日支付金额
	PurchaseOrderROI7D   float64 `json:"purchase_order_roi_7d"`   // 7日支付ROI

	// ================= 电商转化指标/直播间转化 =================
	ClkLiveRoomOrderNum  int64   `json:"clk_live_room_order_num"` // 直播间支付订单量
	LiveAverageOrderCost float64 `json:"live_average_order_cost"` // 直播间支付订单成本
	ClkLiveRoomRGMV      float64 `json:"clk_live_room_rgmv"`      // 直播间支付金额
	ClkLiveRoomROI       float64 `json:"clk_live_room_roi"`       // 直播间支付ROI

	// ================= 销售线索指标 =================
	Leads                 int64   `json:"leads"`                   // 表单提交量
	LeadsCPL              float64 `json:"leads_cpl"`               // 表单成本
	LandingPageVisit      int64   `json:"landing_page_visit"`      // 落地页访问量(行为时间)
	LeadsButtonImpression int64   `json:"leads_button_impression"` // 表单按钮曝光量(行为时间)
	ValidLeads            int64   `json:"valid_leads"`             // 有效表单
	ValidLeadsCPL         float64 `json:"valid_leads_cpl"`         // 有效表单成本
	LeadsCVR              float64 `json:"leads_cvr"`               // 表单转化率
	PhoneCallCnt          int64   `json:"phone_call_cnt"`          // 电话拨打
	PhoneCallSuccCnt      int64   `json:"phone_call_succ_cnt"`     // 电话接通
	WechatCopyCnt         int64   `json:"wechat_copy_cnt"`         // 微信复制
	WechatCopySuccCnt     int64   `json:"wechat_copy_succ_cnt"`    // 微信加为好友
	IdentityCertiCnt      int64   `json:"identity_certi_cnt"`      // 身份认证
	CommodityBuyCnt       int64   `json:"commodity_buy_cnt"`       // 商品购买

	// ================= 私信营销指标 =================
	MessageUser            int64   `json:"message_user"`               // 私信咨询人数
	Message                int64   `json:"message"`                    // 私信咨询条数
	MessageConsult         int64   `json:"message_consult"`            // 私信咨询数
	MessageFstReplyTimeAvg string  `json:"message_fst_reply_time_avg"` // 平均响应时长(分)
	InitiativeMessage      int64   `json:"initiative_message"`         // 私信开口数
	MessageConsultCPL      float64 `json:"message_consult_cpl"`        // 私信咨询成本
	InitiativeMessageCPL   float64 `json:"initiative_message_cpl"`     // 私信开口成本
	MsgLeadsNum            int64   `json:"msg_leads_num"`              // 私信留资数
	MsgLeadsCost           float64 `json:"msg_leads_cost"`             // 私信留资成本

	// ================= 行业商品销量指标 =================
	ExternalGoodsVisit7       int64   `json:"external_goods_visit_7"`        // 行业商品点击量(7日)
	ExternalGoodsVisitPrice7  float64 `json:"external_goods_visit_price_7"`  // 行业商品点击成本(7日)
	ExternalGoodsVisitRate7   float64 `json:"external_goods_visit_rate_7"`   // 行业商品点击转化率(7日)
	ExternalGoodsOrder7       int64   `json:"external_goods_order_7"`        // 行业商品成交订单量(7日)
	ExternalRGMV7             float64 `json:"external_rgmv_7"`               // 行业商品GMV(7日)
	ExternalGoodsOrderPrice7  float64 `json:"external_goods_order_price_7"`  // 行业商品成交订单成本(7日)
	ExternalGoodsOrderRate7   float64 `json:"external_goods_order_rate_7"`   // 行业商品成交订单转化率(7日)
	ExternalROI7              float64 `json:"external_roi_7"`                // 行业商品ROI(7日)
	ExternalGoodsOrder15      int64   `json:"external_goods_order_15"`       // 行业商品成交订单量(15日)
	ExternalRGMV15            float64 `json:"external_rgmv_15"`              // 行业商品GMV(15日)
	ExternalGoodsOrderPrice15 float64 `json:"external_goods_order_price_15"` // 行业商品成交订单成本(15日)
	ExternalGoodsOrderRate15  float64 `json:"external_goods_order_rate_15"`  // 行业商品成交订单转化率(15日)
	ExternalROI15             float64 `json:"external_roi_15"`               // 行业商品ROI(15日)
	ExternalGoodsOrder30      int64   `json:"external_goods_order_30"`       // 行业商品成交订单量(30日)
	ExternalRGMV30            float64 `json:"external_rgmv_30"`              // 行业商品GMV(30日)
	ExternalGoodsOrderPrice30 float64 `json:"external_goods_order_price_30"` // 行业商品成交订单成本(30日)
	ExternalGoodsOrderRate30  float64 `json:"external_goods_order_rate_30"`  // 行业商品成交订单转化率(30日)
	ExternalROI30             float64 `json:"external_roi_30"`               // 行业商品ROI(30日)

	// ================= 外链专属指标 =================
	ExternalLeads    string `json:"external_leads"`     // 外链转化数
	ExternalLeadsCPL string `json:"external_leads_cpl"` // 平均外链转化成本

	// ================= 关键词指标 =================
	WordAvgLocation         string `json:"word_avg_location"`          // 平均位次
	WordImpressionRankFirst string `json:"word_impression_rank_first"` // 首位曝光排名
	WordImpressionRateFirst string `json:"word_impression_rate_first"` // 首位曝光占比
	WordImpressionRankThird string `json:"word_impression_rank_third"` // 前三位曝光排名
	WordImpressionRateThird string `json:"word_impression_rate_third"` // 前三位曝光占比
	WordClickRankFirst      string `json:"word_click_rank_first"`      // 首位点击排名
	WordClickRateFirst      string `json:"word_click_rate_first"`      // 首位点击占比
	WordClickRateThird      string `json:"word_click_rate_third"`      // 前三位点击占比
	WordClickRankThird      string `json:"word_click_rank_third"`      // 前三位点击排名
	WordImpressionRankAll   string `json:"word_impression_rank_all"`   // 全坑位曝光排名
	WordImpressionRateAll   string `json:"word_impression_rate_all"`   // 全坑位曝光占比
	WordClickRankAll        string `json:"word_click_rank_all"`        // 全坑位点击排名
	WordClickRateAll        string `json:"word_click_rate_all"`        // 全坑位点击占比

	// ================= APP内转化数据指标 =================
	InvokeAppOpenCnt            string `json:"invoke_app_open_cnt"`             // APP打开量(唤起)
	InvokeAppOpenCost           string `json:"invoke_app_open_cost"`            // APP打开成本(唤起)
	InvokeAppEnterStoreCnt      string `json:"invoke_app_enter_store_cnt"`      // APP进店量(唤起)
	InvokeAppEnterStoreCost     string `json:"invoke_app_enter_store_cost"`     // APP进店成本(唤起)
	InvokeAppEngagementCnt      string `json:"invoke_app_engagement_cnt"`       // APP互动量(唤起)
	InvokeAppEngagementCost     string `json:"invoke_app_engagement_cost"`      // APP互动成本(唤起)
	InvokeAppPaymentCnt         string `json:"invoke_app_payment_cnt"`          // APP支付次数(唤起)
	InvokeAppPaymentCost        string `json:"invoke_app_payment_cost"`         // APP订单支付成本(唤起)
	SearchInvokeButtonClickCnt  string `json:"search_invoke_button_click_cnt"`  // APP打开按钮点击量(唤起)
	SearchInvokeButtonClickCost string `json:"search_invoke_button_click_cost"` // APP打开按钮点击成本(唤起)
	InvokeAppPaymentROI         string `json:"invoke_app_payment_roi"`          // APP支付ROI(唤起)
	InvokeAppPaymentAmount      string `json:"invoke_app_payment_amount"`       // APP支付金额(唤起)
	InvokeAppPaymentUnitPrice   string `json:"invoke_app_payment_unit_price"`   // APP支付单价(唤起)

	// ================= 京东站外店铺行为指标 =================
	JDActiveUserNum    string `json:"jd_active_user_num"`     // 京东站外活跃行为量
	JDActiveUserNumCVR string `json:"jd_active_user_num_cvr"` // 京东站外转化率
	JDActiveUserNumCPL string `json:"jd_active_user_num_cpl"` // 京东站外转化成本

	// ================= 应用下载指标 =================
	AppDownloadButtonClickCnt  string `json:"app_download_button_click_cnt"`  // APP下载按钮点击
	AppDownloadButtonClickCTR  string `json:"app_download_button_click_ctr"`  // APP下载按钮点击率
	AppDownloadButtonClickCost string `json:"app_download_button_click_cost"` // APP下载按钮点击成本
	AppActivateCnt             string `json:"app_activate_cnt"`               // 激活数
	AppActivateCost            string `json:"app_activate_cost"`              // 激活成本
	AppActivateCTR             string `json:"app_activate_ctr"`               // 激活率
	AppRegisterCnt             string `json:"app_register_cnt"`               // 注册数
	AppRegisterCost            string `json:"app_register_cost"`              // 注册成本
	AppRegisterCTR             string `json:"app_register_ctr"`               // 注册率
	FirstAppPayCnt             string `json:"first_app_pay_cnt"`              // 首次付费数
	FirstAppPayCost            string `json:"first_app_pay_cost"`             // 首次付费成本
	FirstAppPayCTR             string `json:"first_app_pay_ctr"`              // 首次付费率
	CurrentAppPayCnt           string `json:"current_app_pay_cnt"`            // 当日付费次数
	CurrentAppPayCost          string `json:"current_app_pay_cost"`           // 当日付费成本
	AppKeyActionCnt            string `json:"app_key_action_cnt"`             // 关键行为数
	AppKeyActionCost           string `json:"app_key_action_cost"`            // 关键行为成本
	AppKeyActionCTR            string `json:"app_key_action_ctr"`             // 关键行为率
	AppPayCnt7D                string `json:"app_pay_cnt_7d"`                 // 7日付费次数
	AppPayCost7D               string `json:"app_pay_cost_7d"`                // 7日付费成本
	AppPayAmount               string `json:"app_pay_amount"`                 // 付费金额
	AppPayROI                  string `json:"app_pay_roi"`                    // 付费ROI
	AppActivateAmount1D        string `json:"app_activate_amount_1d"`         // 当日LTV
	AppActivateAmount3D        string `json:"app_activate_amount_3d"`         // 三日LTV
	AppActivateAmount7D        string `json:"app_activate_amount_7d"`         // 七日LTV
	AppActivateAmount1DROI     string `json:"app_activate_amount_1d_roi"`     // 当日广告付费ROI
	AppActivateAmount3DROI     string `json:"app_activate_amount_3d_roi"`     // 三日广告付费ROI
	AppActivateAmount7DROI     string `json:"app_activate_amount_7d_roi"`     // 七日广告付费ROI
	Retention1DCnt             string `json:"retention_1d_cnt"`               // 次留
	Retention3DCnt             string `json:"retention_3d_cnt"`               // 3日留存
	Retention7DCnt             string `json:"retention_7d_cnt"`               // 7日留存

	// ================= 企微营销指标 =================
	AddWechatCount    string `json:"add_wechat_count"`     // 添加企微量
	AddWechatCost     string `json:"add_wechat_cost"`      // 添加企微成本
	AddWechatSucCount string `json:"add_wechat_suc_count"` // 成功添加企微量
	AddWechatSucCost  string `json:"add_wechat_suc_cost"`  // 成功添加企微成本
	WechatTalkCount   string `json:"wechat_talk_count"`    // 企微开口量
	WechatTalkCost    string `json:"wechat_talk_cost"`     // 企微开口成本

	// ================= 门店营销指标 =================
	ShopPoiClickNum          string `json:"shop_poi_click_num"`           // 组件点击量
	ShopPoiPagePV            string `json:"shop_poi_page_pv"`             // 门店页面访问量
	ShopPoiPageVisitPrice    string `json:"shop_poi_page_visit_price"`    // 门店页面访问成本
	ShopPoiPageNavigateClick string `json:"shop_poi_page_navigate_click"` // 门店页面导航栏按钮点击量
}

// TableName 设置表名
func (XHSCreativeReports) TableName() string {
	return "xhs_creative_reports"
}
