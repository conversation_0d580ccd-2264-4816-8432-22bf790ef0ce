package controller

import (
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/middleware"
	"gin-backend/internal/service"
	"gin-backend/pkg/response"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// PlatformGroupController 广告组控制器
type PlatformGroupController struct {
	service service.PlatformGroupService
}

// NewPlatformGroupController 创建广告组控制器
func NewPlatformGroupController() PlatformGroupController {
	return PlatformGroupController{
		service: service.NewPlatformGroupService(),
	}
}

// RegisterRoutes 注册路由
func (c PlatformGroupController) RegisterRoutes(router *gin.Engine) {
	v1 := router.Group("/api/v1")
	v1.Use(middleware.JWTAuth()) // 需要认证

	deliveryGroup := v1.Group("/delivery")
	{
		groupsGroup := deliveryGroup.Group("/groups")
		{
			groupsGroup.GET("/list", c.List)        // 获取广告组列表
			groupsGroup.POST("/create", c.Create)   // 创建广告组
			groupsGroup.GET("/detail", c.GetDetail) // 获取广告组详情 (查询参数 ?id=123)
			groupsGroup.PUT("/update", c.Update)    // 更新广告组 (ID在请求体中)
			groupsGroup.DELETE("/delete", c.Delete) // 删除广告组 (查询参数 ?id=123)
		}
	}
}

// List 获取广告组列表
// @Summary 获取广告组列表
// @Description 获取广告组列表，支持分页和筛选
// @Tags 广告组管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param size query int false "每页数量(兼容参数)" default(10)
// @Param agentId query int false "代理ID"
// @Param mediaId query int false "媒体ID"
// @Param marketTargetName query string false "营销目标名称"
// @Param planKeyword query string false "计划关键词"
// @Param keyword query string false "关键词"
// @Param withPlan query bool false "是否包含计划信息" default(false)
// @Param platformType query string false "平台类型"
// @Success 200 {object} response.Response{data=vo.PlatformGroupListResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/groups/list [get]
func (c PlatformGroupController) List(ctx *gin.Context) {
	var req vo.PlatformGroupListReq

	// 绑定查询参数
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 转换为domain层查询参数并调用服务层
	result, err := c.service.List(ctx.Request.Context(), req.ToQueryParam())
	if err != nil {
		response.Error(ctx, http.StatusInternalServerError, "获取广告组列表失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	var voResult vo.PlatformGroupListResp
	voResult.FromPageEntity(result)

	response.Success(ctx, voResult)
}

// Create 创建广告组
// @Summary 创建广告组
// @Description 创建新的广告组
// @Tags 广告组管理
// @Accept json
// @Produce json
// @Param request body vo.PlatformGroupCreateReq true "创建广告组请求"
// @Success 200 {object} response.Response{data=vo.PlatformGroupCreateResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/groups/create [post]
func (c PlatformGroupController) Create(ctx *gin.Context) {
	var req vo.PlatformGroupCreateReq

	// 绑定请求体参数
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	result, err := c.service.Create(ctx.Request.Context(), req.ToCreateEntity())
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "创建广告组失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	var voResult vo.PlatformGroupCreateResp
	voResult.FromIDEntity(result)

	response.Success(ctx, voResult)
}

// GetDetail 获取广告组详情
// @Summary 获取广告组详情
// @Description 根据ID获取广告组详情
// @Tags 广告组管理
// @Accept json
// @Produce json
// @Param id query int true "广告组ID"
// @Success 200 {object} response.Response{data=vo.PlatformGroupResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/groups/detail [get]
func (c PlatformGroupController) GetDetail(ctx *gin.Context) {
	// 从查询参数获取ID
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "参数错误: 缺少ID参数")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: 无效的ID")
		return
	}

	// 调用服务层
	result, err := c.service.GetByID(ctx.Request.Context(), id)
	if err != nil {
		response.Error(ctx, http.StatusNotFound, "获取广告组详情失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	var voResult vo.PlatformGroupResp
	voResult.FromEntity(result)

	response.Success(ctx, voResult)
}

// Update 更新广告组
// @Summary 更新广告组
// @Description 更新广告组信息
// @Tags 广告组管理
// @Accept json
// @Produce json
// @Param request body vo.PlatformGroupUpdateReq true "更新广告组请求"
// @Success 200 {object} response.Response{data=vo.PlatformGroupUpdateResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/groups/update [put]
func (c PlatformGroupController) Update(ctx *gin.Context) {
	var req vo.PlatformGroupUpdateReq

	// 绑定请求体参数（ID在请求体中）
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数绑定失败: "+err.Error())
		return
	}

	// 调用服务层
	result, err := c.service.Update(ctx.Request.Context(), req.ToUpdateEntity())
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "更新广告组失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	var voResult vo.PlatformGroupUpdateResp
	voResult.FromIDEntity(result)

	response.Success(ctx, voResult)
}

// Delete 删除广告组
// @Summary 删除广告组
// @Description 根据ID删除广告组
// @Tags 广告组管理
// @Accept json
// @Produce json
// @Param id query int true "广告组ID"
// @Success 200 {object} response.Response{data=vo.PlatformGroupDeleteResp}
// @Failure 400 {object} response.Response
// @Router /api/v1/delivery/groups/delete [delete]
func (c PlatformGroupController) Delete(ctx *gin.Context) {
	// 从查询参数获取ID
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(ctx, http.StatusBadRequest, "参数错误: 缺少ID参数")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "参数错误: 无效的ID")
		return
	}

	var req vo.PlatformGroupDeleteReq
	req.ID = id

	// 调用服务层
	result, err := c.service.Delete(ctx.Request.Context(), req.ToDeleteEntity())
	if err != nil {
		response.Error(ctx, http.StatusBadRequest, "删除广告组失败: "+err.Error())
		return
	}

	// 转换为VO层响应
	var voResult vo.PlatformGroupDeleteResp
	voResult.FromIDEntity(result)

	response.Success(ctx, voResult)
}
