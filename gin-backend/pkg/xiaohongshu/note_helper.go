package xiaohongshu

import (
	"fmt"
	"time"
)

// NoteListHelper 笔记列表辅助工具
type NoteListHelper struct {
	client *Client
}

// NewNoteListHelper 创建笔记列表辅助工具
func NewNoteListHelper(client *Client) *NoteListHelper {
	return &NoteListHelper{
		client: client,
	}
}

// GetNoteListWithDefaults 使用默认参数获取笔记列表
func (h *NoteListHelper) GetNoteListWithDefaults(accessToken string, advertiserID int64, noteType int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     noteType,
		Page:         1,
		PageSize:     20,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetMyNotes 获取我的笔记
func (h *NoteListHelper) GetMyNotes(accessToken string, advertiserID int64, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     1, // 我的笔记
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetCooperationNotes 获取合作笔记
func (h *NoteListHelper) GetCooperationNotes(accessToken string, advertiserID int64, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     2, // 合作笔记
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetStaffNotes 获取员工笔记
func (h *NoteListHelper) GetStaffNotes(accessToken string, advertiserID int64, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     6, // 员工笔记
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetAuthorizedNotes 获取授权笔记
func (h *NoteListHelper) GetAuthorizedNotes(accessToken string, advertiserID int64, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     11, // 授权笔记
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// SearchNotesByKeyword 根据关键词搜索笔记
func (h *NoteListHelper) SearchNotesByKeyword(accessToken string, advertiserID int64, noteType int, keyword string, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     noteType,
		Keyword:      keyword,
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetNotesByContentType 根据内容类型获取笔记
func (h *NoteListHelper) GetNotesByContentType(accessToken string, advertiserID int64, noteType, contentType int, page, pageSize int) (*NoteListResponse, error) {
	request := &NoteListRequest{
		AdvertiserID:    advertiserID,
		NoteType:        noteType,
		NoteContentType: contentType, // 1:图文, 2:视频
		Page:            page,
		PageSize:        pageSize,
		BaseOnly:        false,
		OrderField:      "create_time",
		OrderType:       "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetHighQualityNotes 获取优质笔记（通过筛选）
func (h *NoteListHelper) GetHighQualityNotes(accessToken string, advertiserID int64, noteType int, page, pageSize int) (*NoteListResponse, error) {
	// 先获取笔记列表
	response, err := h.GetNoteListWithPagination(accessToken, advertiserID, noteType, page, pageSize)
	if err != nil {
		return nil, err
	}
	
	// 筛选优质笔记
	var highQualityNotes []BaseNoteItem
	for _, note := range response.Data.Notes {
		if note.HighQuality == 1 {
			highQualityNotes = append(highQualityNotes, note)
		}
	}
	
	// 构造新的响应
	filteredResponse := &NoteListResponse{
		Code:    response.Code,
		Msg:     response.Msg,
		Success: response.Success,
		Data: struct {
			Total int            `json:"total"`
			Notes []BaseNoteItem `json:"notes"`
		}{
			Total: len(highQualityNotes),
			Notes: highQualityNotes,
		},
	}
	
	return filteredResponse, nil
}

// GetNoteListWithPagination 分页获取笔记列表
func (h *NoteListHelper) GetNoteListWithPagination(accessToken string, advertiserID int64, noteType, page, pageSize int) (*NoteListResponse, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}
	
	request := &NoteListRequest{
		AdvertiserID: advertiserID,
		NoteType:     noteType,
		Page:         page,
		PageSize:     pageSize,
		BaseOnly:     false,
		OrderField:   "create_time",
		OrderType:    "desc",
	}
	
	return h.client.GetNoteList(accessToken, request)
}

// GetAllNotes 获取所有笔记（自动分页）
func (h *NoteListHelper) GetAllNotes(accessToken string, advertiserID int64, noteType int) ([]BaseNoteItem, error) {
	var allNotes []BaseNoteItem
	page := 1
	pageSize := 100 // 使用最大页面大小
	
	for {
		response, err := h.GetNoteListWithPagination(accessToken, advertiserID, noteType, page, pageSize)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页笔记失败: %w", page, err)
		}
		
		allNotes = append(allNotes, response.Data.Notes...)
		
		// 如果当前页的笔记数量小于页面大小，说明已经是最后一页
		if len(response.Data.Notes) < pageSize {
			break
		}
		
		page++
		
		// 添加延迟以避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}
	
	return allNotes, nil
}

// ValidateNoteListRequest 验证笔记列表请求参数
func ValidateNoteListRequest(request *NoteListRequest) error {
	if request.AdvertiserID <= 0 {
		return fmt.Errorf("广告主ID必须大于0")
	}
	
	validNoteTypes := map[int]bool{1: true, 2: true, 4: true, 6: true, 11: true}
	if !validNoteTypes[request.NoteType] {
		return fmt.Errorf("无效的笔记类型: %d，有效值为: 1(我的笔记), 2(合作笔记), 4(主理人笔记), 6(员工笔记), 11(授权笔记)", request.NoteType)
	}
	
	if request.Page < 0 {
		return fmt.Errorf("页码不能小于0")
	}
	
	if request.PageSize < 0 || request.PageSize > 100 {
		return fmt.Errorf("页面大小必须在0-100之间")
	}
	
	if request.NoteContentType != 0 && request.NoteContentType != 1 && request.NoteContentType != 2 {
		return fmt.Errorf("无效的笔记内容类型: %d，有效值为: 1(图文), 2(视频)", request.NoteContentType)
	}
	
	validOrderFields := map[string]bool{
		"read_count":     true,
		"interact_count": true,
		"read_rate":      true,
		"interact_rate":  true,
		"create_time":    true,
	}
	if request.OrderField != "" && !validOrderFields[request.OrderField] {
		return fmt.Errorf("无效的排序字段: %s", request.OrderField)
	}
	
	if request.OrderType != "" && request.OrderType != "asc" && request.OrderType != "desc" {
		return fmt.Errorf("无效的排序方式: %s，有效值为: asc, desc", request.OrderType)
	}
	
	return nil
}
