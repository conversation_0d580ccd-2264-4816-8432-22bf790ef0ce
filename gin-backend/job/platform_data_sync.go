package job

import (
	"context"
	"gin-backend/internal/global"
	"gin-backend/internal/service"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// PlatformDataSyncJob 同步平台数据定时任务
type PlatformDataSyncJob struct {
	db              *gorm.DB
	platformSyncSvc *service.PlatformSyncService
}

// NewPlatformDataSyncJob 创建同步平台数据定时任务
func NewPlatformDataSyncJob() *PlatformDataSyncJob {
	return &PlatformDataSyncJob{
		db:              global.DB,
		platformSyncSvc: service.NewPlatformSyncService(),
	}
}

// Name 任务名称
func (j *PlatformDataSyncJob) Name() string {
	return "sync-platform-data"
}

// Run 任务执行
func (j *PlatformDataSyncJob) Run() error {
	ctx := context.Background()

	// 调用service层的同步方法
	if err := j.platformSyncSvc.SyncPlatformData(ctx); err != nil {
		zap.L().Error("同步平台数据失败", zap.Error(err))
		return err
	}

	return nil
}
