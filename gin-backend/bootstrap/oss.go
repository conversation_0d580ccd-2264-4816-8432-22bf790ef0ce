package bootstrap

import (
	"gin-backend/internal/config"
	"gin-backend/internal/global"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"go.uber.org/zap"
)

func InitOssClient() {

	cfg := config.AppConfig.OSS

	// 创建OSS客户端实例
	client, err := oss.New(cfg.Endpoint, cfg.AccessKeyID, cfg.AccessKeySecret)

	if err != nil {
		zap.L().Error("oss 初始化失败", zap.Error(err))
		return
	}

	global.RegisterOssClient(client)
}
