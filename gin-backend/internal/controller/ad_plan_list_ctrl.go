package controller

import (
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AdPlanListController 计划列表控制器
type AdPlanListController struct {
	planListService *service.AdPlanListService
}

// NewAdPlanListController 创建计划列表控制器
func NewAdPlanListController() *AdPlanListController {
	return &AdPlanListController{
		planListService: service.NewAdPlanListService(),
	}
}

// GetPlanListRequest 获取计划列表请求
type GetPlanListRequest struct {
	AccountID   *int64 `form:"account_id"`   // 账号ID
	AccountName string `form:"account_name"` // 账号名称
	PlanName    string `form:"plan_name"`    // 计划名称
	StartDate   string `form:"start_date"`   // 开始日期 YYYY-MM-DD
	EndDate     string `form:"end_date"`     // 结束日期 YYYY-MM-DD
	Page        int    `form:"page"`         // 页码
	PageSize    int    `form:"page_size"`    // 每页数量
}

// GetPlanList 获取计划列表
// @Summary 获取计划列表
// @Description 获取计划列表，支持账号查询、时间筛选，根据时间维度聚合数据
// @Tags 计划列表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param plan_name query string false "计划名称"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param page query int false "页码，默认1"
// @Param page_size query int false "每页数量，默认20"
// @Success 200 {object} response.Response{data=domain.AdPlanListResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-plans [get]
func (ctrl *AdPlanListController) GetPlanList(c *gin.Context) {
	var req GetPlanListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 转换参数
	param := domain.AdPlanListParam{
		AccountID:   req.AccountID,
		AccountName: req.AccountName,
		PlanName:    req.PlanName,
		Page:        req.Page,
		PageSize:    req.PageSize,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 调用服务
	result, err := ctrl.planListService.GetPlanList(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取计划列表失败", zap.Error(err))
		response.Error(c, 500, "获取计划列表失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// GetPlanStatsRequest 获取计划统计请求
type GetPlanStatsRequest struct {
	AccountID   *int64 `form:"account_id"`   // 账号ID
	AccountName string `form:"account_name"` // 账号名称
	StartDate   string `form:"start_date"`   // 开始日期 YYYY-MM-DD
	EndDate     string `form:"end_date"`     // 结束日期 YYYY-MM-DD
}

// GetPlanStats 获取计划统计信息
// @Summary 获取计划统计信息
// @Description 获取计划统计信息，包括总数量、总消费、总点击等
// @Tags 计划列表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Success 200 {object} response.Response{data=domain.AdPlanStatsResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-plans/stats [get]
func (ctrl *AdPlanListController) GetPlanStats(c *gin.Context) {
	var req GetPlanStatsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 转换参数
	param := domain.AdPlanStatsParam{
		AccountID:   req.AccountID,
		AccountName: req.AccountName,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 调用服务
	result, err := ctrl.planListService.GetPlanStats(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取计划统计失败", zap.Error(err))
		response.Error(c, 500, "获取计划统计失败: "+err.Error())
		return
	}

	response.Success(c, result)
}

// ExportPlanListRequest 导出计划列表请求
type ExportPlanListRequest struct {
	AccountID   *int64 `json:"account_id"`   // 账号ID
	AccountName string `json:"account_name"` // 账号名称
	PlanName    string `json:"plan_name"`    // 计划名称
	StartDate   string `json:"start_date"`   // 开始日期 YYYY-MM-DD
	EndDate     string `json:"end_date"`     // 结束日期 YYYY-MM-DD
	Format      string `json:"format"`       // 导出格式：xlsx 或 csv
}

// ExportPlanList 导出计划列表
// @Summary 导出计划列表
// @Description 导出计划列表数据，支持Excel和CSV格式
// @Tags 计划列表
// @Accept json
// @Produce json
// @Param request body ExportPlanListRequest true "导出参数"
// @Success 200 {object} response.Response{data=domain.AdPlanExportResult}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-plans/export [post]
func (ctrl *AdPlanListController) ExportPlanList(c *gin.Context) {
	var req ExportPlanListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, 400, "参数错误: "+err.Error())
		return
	}

	// 设置默认格式
	if req.Format == "" {
		req.Format = "xlsx"
	}

	// 转换参数
	param := domain.AdPlanExportParam{
		AccountID:   req.AccountID,
		AccountName: req.AccountName,
		PlanName:    req.PlanName,
		Format:      req.Format,
	}

	// 解析日期
	if req.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			response.Error(c, 400, "开始日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.StartDate = &startDate
		}
	}

	if req.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			response.Error(c, 400, "结束日期格式错误，请使用YYYY-MM-DD格式")
			return
		} else {
			param.EndDate = &endDate
		}
	}

	// 异步执行导出任务
	go func() {
		result, err := ctrl.planListService.ExportPlanList(c.Request.Context(), param)
		if err != nil {
			zap.L().Error("导出计划列表失败", zap.Error(err))
		} else {
			zap.L().Info("计划列表导出完成",
				zap.String("file_name", result.FileName),
				zap.String("file_url", result.FileURL))
		}
	}()

	response.Success(c, map[string]string{
		"message": "导出任务已启动，请稍后查看导出结果",
	})
}

// GetPlanDetail 获取计划详情
// @Summary 获取计划详情
// @Description 根据计划ID获取计划详细信息
// @Tags 计划列表
// @Accept json
// @Produce json
// @Param plan_id path string true "计划ID"
// @Success 200 {object} response.Response{data=domain.AdPlanListItem}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-plans/{plan_id} [get]
func (ctrl *AdPlanListController) GetPlanDetail(c *gin.Context) {
	planID := c.Param("plan_id")
	if planID == "" {
		response.Error(c, 400, "计划ID不能为空")
		return
	}

	// 这里可以实现获取单个计划详情的逻辑
	// 暂时返回一个示例响应
	detail := domain.AdPlanListItem{
		PlanID:      planID,
		PlanName:    "示例计划",
		AccountName: "示例账号",
		Consumption: 1000.0,
		ActualCost:  1000.0,
		Impressions: 10000,
		Clicks:      100,
		ClickRate:   1.0,
		LastUpdate:  time.Now().Format("2006-01-02 15:04:05"),
	}

	response.Success(c, detail)
}

// GetAccountList 获取账号列表（用于下拉选择）
// @Summary 获取账号列表
// @Description 获取可用的账号列表，用于筛选条件
// @Tags 计划列表
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]map[string]interface{}}
// @Failure 500 {object} response.Response
// @Router /api/v1/ad-plans/accounts [get]
func (ctrl *AdPlanListController) GetAccountList(c *gin.Context) {
	// 这里可以实现获取账号列表的逻辑
	// 暂时返回一个示例响应
	accounts := []map[string]interface{}{
		{"id": 1, "name": "全部", "value": ""},
		{"id": 2, "name": "账号1", "value": "1"},
		{"id": 3, "name": "账号2", "value": "2"},
	}

	response.Success(c, accounts)
}
