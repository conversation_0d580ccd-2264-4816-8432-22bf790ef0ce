package controller

import (
	"errors"
	"gin-backend/internal/controller/vo"
	"gin-backend/internal/service"
	"gin-backend/internal/service/domain"
	"gin-backend/pkg/response"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// XHSCreativeReportController 小红书创意报表控制器
type XHSCreativeReportController struct {
	reportService *service.XHSCreativeReportService
}

// NewXHSCreativeReportController 创建小红书创意报表控制器
func NewXHSCreativeReportController() *XHSCreativeReportController {
	return &XHSCreativeReportController{
		reportService: service.NewXHSCreativeReportService(),
	}
}

// GetReportList 获取创意报表列表
// @Summary 获取小红书创意报表列表
// @Description 获取小红书创意报表列表，支持多维度筛选和分页
// @Tags 小红书创意报表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param campaign_name query string false "计划名称"
// @Param unit_name query string false "单元名称"
// @Param title query string false "标题"
// @Param pwd query string false "口令"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param placement query int false "广告类型"
// @Param page query int true "页码，从1开始" default(1)
// @Param page_size query int true "每页数量" default(20)
// @Success 200 {object} response.Response{data=vo.XHSCreativeReportListResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-creative-reports [get]
func (ctrl *XHSCreativeReportController) GetReportList(c *gin.Context) {
	var req vo.XHSCreativeReportListReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// 转换为领域参数
	param := req.ToParam()

	// 调用服务层
	result, err := ctrl.reportService.GetReportList(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("获取创意报表列表失败", zap.Error(err))
		response.ServerError(c, "获取报表列表失败: "+err.Error())
		return
	}

	// 转换为响应
	resp := vo.XHSCreativeReportListRespFromResult(result)
	response.Success(c, resp)
}

// GetReportStats 获取创意报表统计信息
// @Summary 获取小红书创意报表统计信息
// @Description 获取小红书创意报表的统计信息，包括总数、总消费、总ROI等
// @Tags 小红书创意报表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param campaign_name query string false "计划名称"
// @Param unit_name query string false "单元名称"
// @Param title query string false "标题"
// @Param pwd query string false "口令"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param placement query int false "广告类型"
// @Success 200 {object} response.Response{data=vo.XHSCreativeReportStatsResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-creative-reports/stats [get]
func (ctrl *XHSCreativeReportController) GetReportStats(c *gin.Context) {
	// 解析查询参数
	param := struct {
		AccountID    *int64 `form:"account_id"`
		AccountName  string `form:"account_name"`
		CampaignName string `form:"campaign_name"`
		UnitName     string `form:"unit_name"`
		Title        string `form:"title"`
		Pwd          string `form:"pwd"`
		StartDate    string `form:"start_date"`
		EndDate      string `form:"end_date"`
		Placement    *int8  `form:"placement"`
	}{}

	if err := c.ShouldBindQuery(&param); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 转换为领域参数
	statsParam := domain.XHSCreativeReportStatsParam{
		AccountID:    param.AccountID,
		AccountName:  param.AccountName,
		CampaignName: param.CampaignName,
		UnitName:     param.UnitName,
		Title:        param.Title,
		Pwd:          param.Pwd,
		Placement:    param.Placement,
	}

	// 解析日期
	if param.StartDate != "" {
		if startDate, err := time.Parse("2006-01-02", param.StartDate); err == nil {
			statsParam.StartDate = &startDate
		}
	}
	if param.EndDate != "" {
		if endDate, err := time.Parse("2006-01-02", param.EndDate); err == nil {
			statsParam.EndDate = &endDate
		}
	}

	// 调用服务层
	result, err := ctrl.reportService.GetReportStats(c.Request.Context(), statsParam)
	if err != nil {
		zap.L().Error("获取创意报表统计信息失败", zap.Error(err))
		response.ServerError(c, "获取统计信息失败: "+err.Error())
		return
	}

	// 转换为响应
	resp := vo.XHSCreativeReportStatsRespFromResult(result)
	response.Success(c, resp)
}

// ExportReports 导出创意报表数据
// @Summary 导出小红书创意报表数据
// @Description 导出小红书创意报表数据为Excel或CSV格式
// @Tags 小红书创意报表
// @Accept json
// @Produce json
// @Param account_id query int false "账号ID"
// @Param account_name query string false "账号名称"
// @Param campaign_name query string false "计划名称"
// @Param unit_name query string false "单元名称"
// @Param title query string false "标题"
// @Param pwd query string false "口令"
// @Param start_date query string false "开始日期 YYYY-MM-DD"
// @Param end_date query string false "结束日期 YYYY-MM-DD"
// @Param placement query int false "广告类型"
// @Param format query string true "导出格式" Enums(xlsx, csv)
// @Success 200 {object} response.Response{data=vo.XHSCreativeReportExportResp}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-creative-reports/export [get]
func (ctrl *XHSCreativeReportController) ExportReports(c *gin.Context) {
	var req vo.XHSCreativeReportExportReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.InvalidParam(c, "参数验证失败: "+err.Error())
		return
	}

	// 转换为领域参数
	param := req.ToParam()

	// 调用服务层
	result, err := ctrl.reportService.ExportReports(c.Request.Context(), param)
	if err != nil {
		zap.L().Error("导出创意报表数据失败", zap.Error(err))
		response.ServerError(c, "导出数据失败: "+err.Error())
		return
	}

	// 转换为响应
	resp := vo.XHSCreativeReportExportRespFromResult(result)
	response.Success(c, resp)
}

// GetReportDetail 获取创意报表详情
// @Summary 获取小红书创意报表详情
// @Description 根据ID获取小红书创意报表的详细信息
// @Tags 小红书创意报表
// @Accept json
// @Produce json
// @Param id path int true "报表ID"
// @Success 200 {object} response.Response{data=model.XHSCreativeReports}
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/xhs-creative-reports/{id} [get]
func (ctrl *XHSCreativeReportController) GetReportDetail(c *gin.Context) {
	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.InvalidParam(c, "无效的报表ID")
		return
	}

	// 调用服务层
	report, err := ctrl.reportService.GetReportDetail(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c, "报表不存在")
		} else {
			zap.L().Error("获取报表详情失败", zap.Error(err))
			response.ServerError(c, "获取报表详情失败")
		}
		return
	}

	response.Success(c, report)
}
