# 计划列表API文档

## 功能概述

计划列表功能提供了完整的广告计划管理和查询能力，支持账号筛选、时间筛选、数据导出等功能。根据时间维度的不同，系统会自动选择最优的数据查询策略：

- **单日查询**：直接从 `ad_plan_stats` 统计表查询，性能更优
- **多日查询**：从 `xhs_creative_reports` 创意报表聚合数据，确保数据准确性

## API接口列表

### 1. 获取计划列表

**接口地址**：`GET /api/v1/ad-plans`

**功能描述**：获取计划列表，支持多维度筛选和分页查询

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称（模糊查询） |
| plan_name | string | 否 | 计划名称（模糊查询） |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| page | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认20 |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "plan_id": "*********",
        "plan_name": "闪购客流_加时计划_沈心怡_0605_12-20",
        "account_name": "营销树长大",
        "consumption": 33366.68,
        "actual_cost": 23366.68,
        "impressions": 1272,
        "clicks": 25,
        "click_rate": 2.00,
        "last_update": "2024-06-16 12:00:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

### 2. 获取计划统计信息

**接口地址**：`GET /api/v1/ad-plans/stats`

**功能描述**：获取计划的汇总统计信息

**请求参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_plans": 156,
    "total_cost": 125000.50,
    "total_clicks": 8500,
    "total_impressions": 450000,
    "avg_click_rate": 1.89,
    "last_update_time": "2024-06-16T12:00:00Z"
  }
}
```

### 3. 导出计划列表

**接口地址**：`POST /api/v1/ad-plans/export`

**功能描述**：导出计划列表数据，支持Excel和CSV格式

**请求参数**：
```json
{
  "account_id": 1,
  "account_name": "测试账号",
  "plan_name": "测试计划",
  "start_date": "2024-06-01",
  "end_date": "2024-06-16",
  "format": "xlsx"
}
```

**参数说明**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | int | 否 | 账号ID |
| account_name | string | 否 | 账号名称 |
| plan_name | string | 否 | 计划名称 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |
| format | string | 否 | 导出格式：xlsx 或 csv，默认xlsx |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "message": "导出任务已启动，请稍后查看导出结果"
  }
}
```

### 4. 获取计划详情

**接口地址**：`GET /api/v1/ad-plans/{plan_id}`

**功能描述**：根据计划ID获取计划详细信息

**路径参数**：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| plan_id | string | 是 | 计划ID |

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "plan_id": "*********",
    "plan_name": "示例计划",
    "account_name": "示例账号",
    "consumption": 1000.0,
    "actual_cost": 1000.0,
    "impressions": 10000,
    "clicks": 100,
    "click_rate": 1.0,
    "last_update": "2024-06-16 12:00:00"
  }
}
```

### 5. 获取账号列表

**接口地址**：`GET /api/v1/ad-plans/accounts`

**功能描述**：获取可用的账号列表，用于筛选条件

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {"id": 1, "name": "全部", "value": ""},
    {"id": 2, "name": "账号1", "value": "1"},
    {"id": 3, "name": "账号2", "value": "2"}
  ]
}
```

## 数据聚合策略

### 单日查询（start_date = end_date）

当查询的开始日期和结束日期相同时，系统认为是单日查询，会直接从 `ad_plan_stats` 统计表获取数据：

**优势**：
- 查询性能更优
- 数据已预聚合，响应速度快
- 适合实时查看当日数据

**数据来源**：`ad_plan_stats` 表

### 多日查询（start_date ≠ end_date）

当查询跨越多个日期时，系统会从 `xhs_creative_reports` 创意报表表聚合数据：

**优势**：
- 数据更准确，基于原始创意数据聚合
- 支持任意时间范围查询
- 按计划ID进行跨时间维度聚合

**聚合逻辑**：
```sql
SELECT 
  campaign_id as plan_id,
  MAX(campaign_name) as plan_name,
  MAX(account_name) as account_name,
  SUM(fee) as consumption,
  SUM(fee) as actual_cost,
  SUM(impression) as impressions,
  SUM(click) as clicks,
  CASE 
    WHEN SUM(impression) > 0 THEN (SUM(click) * 100.0 / SUM(impression))
    ELSE 0 
  END as click_rate,
  MAX(updated_at) as last_update
FROM xhs_creative_reports
WHERE DATE(time) BETWEEN ? AND ?
GROUP BY campaign_id
ORDER BY consumption DESC
```

## 筛选条件说明

### 账号筛选
- **account_id**：精确匹配账号ID
- **account_name**：模糊匹配账号名称

### 计划筛选
- **plan_name**：模糊匹配计划名称

### 时间筛选
- **start_date**：开始日期（包含）
- **end_date**：结束日期（包含）
- 支持单日查询和多日查询
- 日期格式：YYYY-MM-DD

### 分页参数
- **page**：页码，从1开始
- **page_size**：每页数量，范围1-1000

## 导出功能

### 支持格式
- **xlsx**：Excel格式，适合数据分析
- **csv**：CSV格式，适合数据导入

### 导出限制
- 最大导出10,000条记录
- 时间范围不超过365天
- 异步处理，避免请求超时

### 导出字段
- 计划编号（plan_id）
- 计划名称（plan_name）
- 账号名称（account_name）
- 消费（consumption）
- 实际消费（actual_cost）
- 展现量（impressions）
- 点击量（clicks）
- 点击率（click_rate）
- 最后更新时间（last_update）

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | 未授权 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 查询今日计划数据
```bash
curl -X GET "http://localhost:8080/api/v1/ad-plans?start_date=2024-06-16&end_date=2024-06-16&page=1&page_size=20" \
  -H "Authorization: Bearer your_token"
```

### 查询指定账号的计划数据
```bash
curl -X GET "http://localhost:8080/api/v1/ad-plans?account_id=1&start_date=2024-06-01&end_date=2024-06-16" \
  -H "Authorization: Bearer your_token"
```

### 导出计划数据
```bash
curl -X POST "http://localhost:8080/api/v1/ad-plans/export" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "start_date": "2024-06-01",
    "end_date": "2024-06-16",
    "format": "xlsx"
  }'
```

## 性能优化建议

1. **使用单日查询**：当只需要查看某一天的数据时，建议使用单日查询以获得更好的性能
2. **合理设置分页**：避免一次查询过多数据，建议每页20-50条
3. **缓存账号列表**：账号列表变化不频繁，可以在前端缓存
4. **异步导出**：大量数据导出采用异步处理，避免前端等待超时
