package domain

// 角色领域模型

// RoleListParam 获取角色列表参数
type RoleListParam struct {
	Page     int    // 页码
	PageSize int    // 每页数量
	Name     string // 角色名称查询条件
	Code     string // 角色编码查询条件
	Status   int    // 状态：1-启用，0-禁用
}

// RoleEntity 角色实体
type RoleEntity struct {
	ID          int64  // 角色ID
	Name        string // 角色名称
	Code        string // 角色编码
	Description string // 角色描述
	Status      int    // 状态：1-启用，0-禁用
	IsSystem    int    // 是否系统角色：1-是，0-否
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// RoleListEntity 角色列表返回结果
type RoleListEntity struct {
	Total int64        // 总记录数
	List  []RoleEntity // 角色列表
}

// RoleOption 角色选项
type RoleOption struct {
	ID   int64  // 角色ID
	Name string // 角色名称
	Code string // 角色编码（可选）
}

// RoleOptionsEntity 角色选项列表
type RoleOptionsEntity struct {
	Options []RoleOption // 选项列表
}

// PermissionModule 权限模块
type PermissionModule struct {
	Name string // 模块名称
	Code string // 模块编码
}

// PermissionModulesEntity 权限模块列表
type PermissionModulesEntity struct {
	Modules []PermissionModule // 模块列表
}

// RolePermissionEntity 角色权限
type RolePermissionEntity struct {
	PermissionIDs []int64 // 权限ID列表
}

// PermissionLogEntity 权限日志实体
type PermissionLogEntity struct {
	ID           int64  // 日志ID
	RoleID       int64  // 角色ID
	RoleName     string // 角色名称
	PermissionID int64  // 权限ID
	Permission   string // 权限名称
	Action       string // 操作类型：assign-分配，revoke-撤销
	OperatorID   int64  // 操作人ID
	OperatorName string // 操作人名称
	CreatedAt    string // 创建时间
}

// PermissionLogListEntity 权限日志列表返回结果
type PermissionLogListEntity struct {
	Total int64                 // 总记录数
	List  []PermissionLogEntity // 日志列表
}

// PermissionListParam 获取权限列表参数
type PermissionListParam struct {
	Page      int    // 页码
	PageSize  int    // 每页数量
	Name      string // 权限名称查询条件
	Code      string // 权限编码查询条件
	Module    string // 所属模块查询条件
	Type      string // 权限类型查询条件
	DataScope string // 数据范围查询条件
	Status    int    // 状态：1-启用，0-禁用
}

// PermissionEntity 权限实体
type PermissionEntity struct {
	ID          int64  // 权限ID
	Name        string // 权限名称
	Code        string // 权限编码
	Module      string // 所属模块
	Type        string // 权限类型：function-功能权限，data-数据权限
	DataScope   string // 数据范围：all-全部，dept-部门，self-个人
	Description string // 权限描述
	Status      int    // 状态：1-启用，0-禁用
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// PermissionListEntity 权限列表返回结果
type PermissionListEntity struct {
	Total int64              // 总记录数
	List  []PermissionEntity // 权限列表
}

// PermissionLogListParam 权限日志列表参数
type PermissionLogListParam struct {
	Page         int    // 页码
	PageSize     int    // 每页数量
	RoleID       int64  // 角色ID查询条件
	OperatorID   int64  // 操作人ID查询条件
	Action       string // 操作类型查询条件
	PermissionID int64  // 权限ID查询条件
}

// AssignPermissionEntity 分配权限参数
type AssignPermissionEntity struct {
	RoleID        int64   // 角色ID
	PermissionIDs []int64 // 权限ID列表
}

// RevokePermissionEntity 撤销权限参数
type RevokePermissionEntity struct {
	RoleID        int64   // 角色ID
	PermissionIDs []int64 // 权限ID列表
}

// UserPermissionEntity 用户权限
type UserPermissionEntity struct {
	Permissions []string     // 用户拥有的权限编码列表
	Roles       []RoleEntity // 用户所属角色列表
}
