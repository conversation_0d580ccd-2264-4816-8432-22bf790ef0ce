package domain

import (
	"time"
)

// TaskEntity 任务实体
type TaskEntity struct {
	ID        int64     // 任务ID
	Name      string    // 任务名称
	Status    string    // 任务状态
	CreatedAt time.Time // 创建时间
}

// TaskListParam 任务列表查询参数
type TaskListParam struct {
	Page     int    // 页码
	PageSize int    // 每页数量
	Status   string // 任务状态
}

// TaskListResult 任务列表结果
type TaskListResult struct {
	List     []TaskEntity // 任务列表
	Total    int64        // 总数量
	Page     int          // 当前页码
	PageSize int          // 每页数量
}
