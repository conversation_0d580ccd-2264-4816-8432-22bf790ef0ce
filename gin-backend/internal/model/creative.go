package model

import (
	"time"
)

// Creative 创意
type Creative struct {
	ID              int64     `gorm:"primaryKey;autoIncrement"`
	Name            string    `gorm:"column:name;type:varchar(200)"`            // 创意名称
	ImageURL        string    `gorm:"column:image_url;type:varchar(500)"`       // 素材图片地址
	ImageArea       string    `gorm:"column:image_area;type:varchar(100)"`      // 图片区域
	BackgroundColor string    `gorm:"column:background_color;type:varchar(50)"` // 背景颜色
	HotAreas        string    `gorm:"column:hot_areas;type:text"`               // 热区JSON字符串（数据库存储）
	CreatedAt       time.Time `gorm:"column:created_at;autoCreateTime"`         // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;autoUpdateTime"`         // 更新时间
}

func (Creative) TableName() string {
	return "ad_creatives"
}
