package service

import (
	"fmt"
	"gin-backend/internal/global"
	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
	"regexp"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

type TaobaoPidService struct {
	db *gorm.DB
}

func NewTaobaoPidService() *TaobaoPidService {
	return &TaobaoPidService{
		db: global.DB,
	}
}

// GetList 获取淘联链接列表
func (s *TaobaoPidService) GetList(param domain.TaobaoPidListParam) (domain.TaobaoPidListEntity, error) {
	// 设置默认值
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.Size <= 0 {
		param.Size = 10
	}
	if param.Size > 100 {
		param.Size = 100
	}
	if param.IsUsed == 0 && param.Keyword == "" && param.ActType == 0 {
		param.IsUsed = -1 // 默认显示全部
	}

	var pids []*model.TaobaoPid
	var total int64
	var usedCount int64
	var unusedCount int64

	// 基础查询
	query := s.db.Model(&model.TaobaoPid{}).Where("deleted_at IS NULL")

	// 统计已使用和未使用数量
	if err := s.db.Model(&model.TaobaoPid{}).Where("deleted_at IS NULL AND is_used = ?", domain.PidUsed).Count(&usedCount).Error; err != nil {
		return domain.TaobaoPidListEntity{}, fmt.Errorf("获取淘联链接列表失败: %v", err)
	}
	if err := s.db.Model(&model.TaobaoPid{}).Where("deleted_at IS NULL AND is_used = ?", domain.PidUnused).Count(&unusedCount).Error; err != nil {
		return domain.TaobaoPidListEntity{}, fmt.Errorf("获取淘联链接列表失败: %v", err)
	}

	// 应用筛选条件
	if param.IsUsed != -1 {
		query = query.Where("is_used = ?", param.IsUsed)
	}
	if param.Keyword != "" {
		query = query.Where("zone_name LIKE ? OR pid LIKE ?", "%"+param.Keyword+"%", "%"+param.Keyword+"%")
	}
	if param.ActType > 0 {
		query = query.Where("act_type = ?", param.ActType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return domain.TaobaoPidListEntity{}, fmt.Errorf("获取淘联链接列表失败: %v", err)
	}

	// 排序
	orderBy := "id ASC"
	if param.Sort != "" {
		if param.Sort[0] == '+' {
			orderBy = param.Sort[1:] + " ASC"
		} else if param.Sort[0] == '-' {
			orderBy = param.Sort[1:] + " DESC"
		}
	}

	// 分页查询
	offset := (param.Page - 1) * param.Size
	err = query.Order(orderBy).Offset(offset).Limit(param.Size).Find(&pids).Error
	if err != nil {
		return domain.TaobaoPidListEntity{}, fmt.Errorf("获取淘联链接列表失败: %v", err)
	}

	// 转换为领域实体
	var items []domain.TaobaoPidEntity
	for _, pid := range pids {
		items = append(items, domain.TaobaoPidEntity{
			ID:           pid.ID,
			AccountName:  pid.AccountName,
			MemberID:     pid.MemberID,
			ZoneName:     pid.ZoneName,
			PID:          pid.PID,
			IsUsed:       pid.IsUsed,
			UsedAt:       pid.UsedAt,
			UsedBy:       pid.UsedBy,
			AdSlotPlanID: pid.AdSlotPlanID,
			AdCreativeID: pid.AdCreativeID,
			ActType:      pid.ActType,
			CreatedAt:    pid.CreatedAt,
			UpdatedAt:    pid.UpdatedAt,
			DeletedAt:    pid.DeletedAt,
		})
	}

	return domain.TaobaoPidListEntity{
		Items:       items,
		Total:       total,
		UsedCount:   usedCount,
		UnusedCount: unusedCount,
	}, nil
}

// Create 创建淘联链接
func (s *TaobaoPidService) Create(param domain.TaobaoPidCreateParam) (int64, error) {
	// 从PID中提取会员ID
	memberIDStr := s.extractMemberIDFromPID(param.PID)
	if memberIDStr == "" {
		return 0, fmt.Errorf("无法从PID中提取会员ID")
	}

	// 根据会员ID确定账户名称
	accountName := s.getAccountNameByMemberID(memberIDStr)

	// 将会员ID字符串转换为uint64
	memberID, err := s.parseMemberID(memberIDStr)
	if err != nil {
		return 0, fmt.Errorf("会员ID格式错误: %v", err)
	}

	// 检查PID是否已存在
	var count int64
	query := s.db.Model(&model.TaobaoPid{}).Where("pid = ? AND deleted_at IS NULL", param.PID)
	err = query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("检查PID是否存在失败: %v", err)
	}
	if count > 0 {
		return 0, fmt.Errorf("该推广位ID已存在")
	}

	// 构建数据模型
	pid := &model.TaobaoPid{
		AccountName: accountName,
		MemberID:    memberID,
		ZoneName:    param.ZoneName,
		PID:         param.PID,
		ActType:     param.ActType,
		IsUsed:      domain.PidUnused,
	}

	// 创建数据
	err = s.db.Create(pid).Error
	if err != nil {
		return 0, fmt.Errorf("创建淘联链接失败: %v", err)
	}

	return pid.ID, nil
}

// GetByID 根据ID获取淘联链接
func (s *TaobaoPidService) GetByID(id int64) (domain.TaobaoPidEntity, error) {
	var pid model.TaobaoPid
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&pid).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return domain.TaobaoPidEntity{}, fmt.Errorf("记录不存在")
		}
		return domain.TaobaoPidEntity{}, fmt.Errorf("获取淘联链接失败: %v", err)
	}

	return domain.TaobaoPidEntity{
		ID:           pid.ID,
		AccountName:  pid.AccountName,
		MemberID:     pid.MemberID,
		ZoneName:     pid.ZoneName,
		PID:          pid.PID,
		IsUsed:       pid.IsUsed,
		UsedAt:       pid.UsedAt,
		UsedBy:       pid.UsedBy,
		AdSlotPlanID: pid.AdSlotPlanID,
		AdCreativeID: pid.AdCreativeID,
		ActType:      pid.ActType,
		CreatedAt:    pid.CreatedAt,
		UpdatedAt:    pid.UpdatedAt,
		DeletedAt:    pid.DeletedAt,
	}, nil
}

// Update 更新淘联链接
func (s *TaobaoPidService) Update(param domain.TaobaoPidUpdateParam) error {
	// 检查记录是否存在
	var pid model.TaobaoPid
	err := s.db.Where("id = ? AND deleted_at IS NULL", param.ID).First(&pid).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("记录不存在")
		}
		return fmt.Errorf("获取淘联链接失败: %v", err)
	}

	// 检查是否已使用
	if pid.IsUsed == domain.PidUsed {
		return fmt.Errorf("该推广位已被使用，无法修改")
	}

	// 检查PID是否与其他记录重复
	var count int64
	query := s.db.Model(&model.TaobaoPid{}).Where("pid = ? AND deleted_at IS NULL AND id != ?", param.PID, param.ID)
	err = query.Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查PID是否存在失败: %v", err)
	}
	if count > 0 {
		return fmt.Errorf("该推广位ID已存在")
	}

	// 从PID中提取会员ID
	memberIDStr := s.extractMemberIDFromPID(param.PID)
	if memberIDStr == "" {
		return fmt.Errorf("无法从PID中提取会员ID")
	}

	// 根据会员ID确定账户名称
	accountName := s.getAccountNameByMemberID(memberIDStr)

	// 将会员ID字符串转换为uint64
	memberID, err := s.parseMemberID(memberIDStr)
	if err != nil {
		return fmt.Errorf("会员ID格式错误: %v", err)
	}

	// 更新数据
	pid.AccountName = accountName
	pid.MemberID = memberID
	pid.ZoneName = param.ZoneName
	pid.PID = param.PID
	pid.ActType = param.ActType

	err = s.db.Save(&pid).Error
	if err != nil {
		return fmt.Errorf("更新淘联链接失败: %v", err)
	}

	return nil
}

// Delete 删除淘联链接
func (s *TaobaoPidService) Delete(id uint64) error {
	// 检查记录是否存在
	var pid model.TaobaoPid
	err := s.db.Where("id = ? AND deleted_at IS NULL", id).First(&pid).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("记录不存在")
		}
		return fmt.Errorf("获取淘联链接失败: %v", err)
	}

	// 检查是否已使用
	if pid.IsUsed == domain.PidUsed {
		return fmt.Errorf("该推广位已被使用，无法删除")
	}

	// 软删除
	err = s.db.Where("id = ?", id).Delete(&model.TaobaoPid{}).Error
	if err != nil {
		return fmt.Errorf("删除淘联链接失败: %v", err)
	}

	return nil
}

// extractMemberIDFromPID 从PID中提取会员ID
func (s *TaobaoPidService) extractMemberIDFromPID(pidStr string) string {
	// PID格式: mm_12345678_98765432_12345678901 (会员ID是第二部分)
	if !strings.Contains(pidStr, "mm_") {
		return ""
	}

	parts := strings.Split(pidStr, "_")
	if len(parts) < 3 {
		return ""
	}

	return parts[1]
}

// getAccountNameByMemberID 根据会员ID获取账户名称
func (s *TaobaoPidService) getAccountNameByMemberID(memberID string) string {
	// 示例实现，实际应根据业务需求查询对应的账户名称
	// 这里简单返回固定的前缀加会员ID
	return "联盟账号_" + memberID
}

// parseMemberID 将会员ID字符串转换为uint64
func (s *TaobaoPidService) parseMemberID(memberIDStr string) (int64, error) {
	// 验证会员ID格式，应该是纯数字
	matched, err := regexp.MatchString(`^\d+$`, memberIDStr)
	if err != nil || !matched {
		return 0, fmt.Errorf("会员ID格式错误")
	}

	// 转换为uint64
	memberID, err := strconv.ParseInt(memberIDStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return memberID, nil
}
