package router

import (
	"gin-backend/internal/controller"

	"github.com/gin-gonic/gin"
)

// SetupAdAccountRebateRoutes 设置账号返利比例路由
func SetupAdAccountRebateRoutes(router *gin.RouterGroup) {
	ctrl := controller.NewAdAccountRebateController()

	// 账号返利比例相关路由
	rebates := router.Group("/account-rebates")
	{
		// 获取账号返利比例列表
		rebates.GET("", ctrl.GetAccountRebateList)
		
		// 变更账号返利比例
		rebates.POST("/change", ctrl.ChangeAccountRebate)
		
		// 获取变更记录
		rebates.GET("/history", ctrl.GetAccountRebateHistory)
		
		// 获取统计信息
		rebates.GET("/stats", ctrl.GetAccountRebateStats)
		
		// 获取变更类型列表
		rebates.GET("/change-types", ctrl.GetChangeTypes)
		
		// 获取账号当前返利比例
		rebates.GET("/:account_id/current", ctrl.GetAccountCurrentRebate)
		
		// 删除账号返利比例
		rebates.DELETE("/:account_id", ctrl.DeleteAccountRebate)
	}
}
