package service

import (
	"strconv"

	"gin-backend/internal/model"
	"gin-backend/internal/service/domain"
)

// dashboardFilterService 仪表盘过滤条件服务
type dashboardFilterService struct {
	dashboard *DashboardService
}

// newDashboardFilterService 创建仪表盘过滤条件服务实例
func newDashboardFilterService(d *DashboardService) *dashboardFilterService {
	return &dashboardFilterService{
		dashboard: d,
	}
}

// GetFilterOptions 获取筛选选项
func (s *dashboardFilterService) GetFilterOptions() (domain.FilterOptionsEntity, error) {
	// 获取媒体列表
	mediaService := newDashboardMediaService(s.dashboard)
	mediaList, err := mediaService.GetAllMediaList()
	if err != nil {
		return domain.FilterOptionsEntity{}, err
	}

	// 获取产品列表
	productList, err := s.getProductList()
	if err != nil {
		return domain.FilterOptionsEntity{}, err
	}

	// 转换为Domain实体
	domainMediaList := make([]domain.MediaItem, 0, len(mediaList))
	for _, item := range mediaList {
		domainMediaList = append(domainMediaList, domain.MediaItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}

	domainProductList := make([]domain.ProductItem, 0, len(productList))
	for _, item := range productList {
		domainProductList = append(domainProductList, domain.ProductItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}

	return domain.FilterOptionsEntity{
		ShowMediaList: len(mediaList) > 0,
		MediaList:     domainMediaList,
		ProductList:   domainProductList,
	}, nil
}

// GetDynamicFilterOptions 获取动态筛选选项
func (s *dashboardFilterService) GetDynamicFilterOptions(req domain.DynamicFilterParam, userID int, userRole int) (domain.DynamicFilterEntity, error) {
	var list []struct {
		ID   string
		Name string
	}

	// 根据类型获取不同的筛选选项
	switch req.Type {
	case "user":
		// 获取用户列表
		users, err := s.getUserList()
		if err != nil {
			return domain.DynamicFilterEntity{}, err
		}
		list = users
	case "media":
		// 获取媒体列表
		mediaService := newDashboardMediaService(s.dashboard)
		mediaParams := domain.DashboardMediaListParam{
			UserID:   req.UserID,
			Type:     req.CooperationType,
			Category: req.Category,
		}
		result, err := mediaService.GetMediaList(mediaParams, userID, userRole)
		if err != nil {
			return domain.DynamicFilterEntity{}, err
		}
		for _, item := range result.List {
			list = append(list, struct {
				ID   string
				Name string
			}{
				ID:   item.ID,
				Name: item.Name,
			})
		}
	case "plan":
		// 获取计划列表
		mediaService := newDashboardMediaService(s.dashboard)
		planParams := domain.DashboardPlansParam{
			MediaAccountID: req.MediaAccountID,
		}
		result, err := mediaService.GetPlans(planParams, userID, userRole)
		if err != nil {
			return domain.DynamicFilterEntity{}, err
		}
		for _, item := range result.List {
			list = append(list, struct {
				ID   string
				Name string
			}{
				ID:   item.ID,
				Name: item.Name,
			})
		}
	}

	// 转换为Domain实体
	domainList := make([]domain.DashboardFilterItem, 0, len(list))
	for _, item := range list {
		domainList = append(domainList, domain.DashboardFilterItem{
			ID:   item.ID,
			Name: item.Name,
		})
	}

	return domain.DynamicFilterEntity{
		List: domainList,
	}, nil
}

// getUserList 获取用户列表
func (s *dashboardFilterService) getUserList() ([]struct {
	ID   string
	Name string
}, error) {
	var users []model.User
	if err := s.dashboard.db.Select("id, name, real_name").
		Where("status = ?", 1).
		Find(&users).Error; err != nil {
		return nil, err
	}

	var result []struct {
		ID   string
		Name string
	}
	for _, user := range users {
		result = append(result, struct {
			ID   string
			Name string
		}{
			ID:   strconv.FormatUint(uint64(user.ID), 10),
			Name: user.RealName,
		})
	}

	return result, nil
}

// getProductList 获取产品列表
func (s *dashboardFilterService) getProductList() ([]struct {
	ID   string
	Name string
}, error) {
	var products []model.AdProduct
	if err := s.dashboard.db.Select("id, name").
		Where("status = ?", "active").
		Find(&products).Error; err != nil {
		return nil, err
	}

	var result []struct {
		ID   string
		Name string
	}
	for _, product := range products {
		result = append(result, struct {
			ID   string
			Name string
		}{
			ID:   strconv.FormatUint(uint64(product.ID), 10),
			Name: product.Name,
		})
	}

	return result, nil
}
