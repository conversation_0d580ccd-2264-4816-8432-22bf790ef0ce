package vo

import (
	"time"

	"gin-backend/internal/service/domain"
)

// 审核状态常量
const (
	CostAuditStatusPending  = domain.CostAuditStatusPending  // 待审核
	CostAuditStatusApproved = domain.CostAuditStatusApproved // 已通过
	CostAuditStatusRejected = domain.CostAuditStatusRejected // 已拒绝
)

// CostCreateReq 创建费用记录请求
type CostCreateReq struct {
	PlanID      int64   `json:"plan_id" binding:"required"`       // 投放计划ID
	MediaID     int64   `json:"media_id" binding:"required"`      // 媒体ID
	AdSlotID    int64   `json:"ad_slot_id" binding:"required"`    // 广告位ID
	AdProductID int64   `json:"ad_product_id" binding:"required"` // 产品ID
	UserID      int64   `json:"user_id" binding:"required"`       // 媒介ID
	Date        string  `json:"date" binding:"required"`          // 日期
	Cost        float64 `json:"cost" binding:"required"`          // 费用
	Clicks      int     `json:"clicks"`                           // 点击量
	Remark      string  `json:"remark"`                           // 备注
}

// ToDomain 将VO转换为领域对象
func (r CostCreateReq) ToDomain() domain.CostCreateEntity {
	return domain.CostCreateEntity{
		PlanID:      r.PlanID,
		MediaID:     r.MediaID,
		AdSlotID:    r.AdSlotID,
		AdProductID: r.AdProductID,
		UserID:      r.UserID,
		Date:        r.Date,
		Cost:        r.Cost,
		Clicks:      r.Clicks,
		Remark:      r.Remark,
	}
}

// CostUpdateReq 更新费用记录请求
type CostUpdateReq struct {
	ID          int64   `json:"id" binding:"required"`            // 费用记录ID
	PlanID      int64   `json:"plan_id" binding:"required"`       // 投放计划ID
	MediaID     int64   `json:"media_id" binding:"required"`      // 媒体ID
	AdSlotID    int64   `json:"ad_slot_id" binding:"required"`    // 广告位ID
	AdProductID int64   `json:"ad_product_id" binding:"required"` // 产品ID
	UserID      int64   `json:"user_id" binding:"required"`       // 媒介ID
	Date        string  `json:"date" binding:"required"`          // 日期
	Cost        float64 `json:"cost" binding:"required"`          // 费用
	Clicks      int     `json:"clicks"`                           // 点击量
	Remark      string  `json:"remark"`                           // 备注
}

// ToDomain 将VO转换为领域对象
func (r CostUpdateReq) ToDomain() domain.CostUpdateEntity {
	return domain.CostUpdateEntity{
		ID:          r.ID,
		PlanID:      r.PlanID,
		MediaID:     r.MediaID,
		AdSlotID:    r.AdSlotID,
		AdProductID: r.AdProductID,
		UserID:      r.UserID,
		Date:        r.Date,
		Cost:        r.Cost,
		Clicks:      r.Clicks,
		Remark:      r.Remark,
	}
}

// CostListReq 费用记录列表查询参数
type CostListReq struct {
	Page        int    `form:"page" json:"page"`                   // 页码
	PageSize    int    `form:"pageSize" json:"pageSize"`           // 每页数量
	GroupID     int64  `form:"plan_id" json:"plan_id"`             // 投放计划ID
	MediaID     int64  `form:"media_id" json:"media_id"`           // 媒体ID
	AdSlotID    int64  `form:"ad_slot_id" json:"ad_slot_id"`       // 广告位ID
	AdProductID int64  `form:"ad_product_id" json:"ad_product_id"` // 产品ID
	UserID      int64  `form:"user_id" json:"user_id"`             // 媒介ID
	StartDate   string `form:"start_date" json:"start_date"`       // 开始日期
	EndDate     string `form:"end_date" json:"end_date"`           // 结束日期
	AuditStatus string `form:"audit_status" json:"audit_status"`   // 审核状态
	CategoryID  int64  `form:"category_id" json:"category_id"`     // 分类ID
}

// ToDomain 将VO转换为领域对象
func (r CostListReq) ToDomain() domain.CostListParam {
	return domain.CostListParam{
		Page:        r.Page,
		PageSize:    r.PageSize,
		PlanID:      r.GroupID,
		GroupID:     r.GroupID,
		MediaID:     r.MediaID,
		AdSlotID:    r.AdSlotID,
		AdProductID: r.AdProductID,
		UserID:      r.UserID,
		StartDate:   r.StartDate,
		EndDate:     r.EndDate,
		AuditStatus: r.AuditStatus,
		CategoryID:  r.CategoryID,
	}
}

// CostItem 费用记录项
type CostItem struct {
	ID            int64     `json:"id"`              // 费用记录ID
	GroupID       int64     `json:"group_id"`        // 投放计划ID
	GroupName     string    `json:"group_name"`      // 投放计划名称
	MediaID       int64     `json:"media_id"`        // 媒体ID
	MediaName     string    `json:"media_name"`      // 媒体名称
	AdSlotID      int64     `json:"ad_slot_id"`      // 广告位ID
	AdSlotName    string    `json:"ad_slot_name"`    // 广告位名称
	AdProductID   int64     `json:"ad_product_id"`   // 产品ID
	AdProductName string    `json:"ad_product_name"` // 产品名称
	UserID        int64     `json:"user_id"`         // 媒介ID
	UserName      string    `json:"user_name"`       // 媒介名称
	Date          time.Time `json:"date"`            // 日期
	Cost          float64   `json:"cost"`            // 费用
	Clicks        int       `json:"clicks"`          // 点击量
	Remark        string    `json:"remark"`          // 备注
	AuditStatus   string    `json:"audit_status"`    // 审核状态
	AuditorID     int64     `json:"auditor_id"`      // 审核人ID
	AuditorName   string    `json:"auditor_name"`    // 审核人名称
	AuditTime     time.Time `json:"audit_time"`      // 审核时间
	AuditRemark   string    `json:"audit_remark"`    // 审核备注
	CreatorID     int64     `json:"creator_id"`      // 创建人ID
	CreatorName   string    `json:"creator_name"`    // 创建人名称
	UpdaterID     int64     `json:"updater_id"`      // 更新人ID
	UpdaterName   string    `json:"updater_name"`    // 更新人名称
	CreatedAt     time.Time `json:"created_at"`      // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`      // 更新时间
	CategoryID    int64     `json:"category_id"`     // 分类ID
	CategoryName  string    `json:"category_name"`   // 分类名称
}

// FromDomainToCostItem 将领域对象转换为VO
func FromDomainToCostItem(entity domain.CostEntity) CostItem {
	return CostItem{
		ID:            entity.ID,
		GroupID:       entity.PlanID,
		GroupName:     entity.GroupName,
		MediaID:       entity.MediaID,
		MediaName:     entity.MediaName,
		AdSlotID:      entity.AdSlotID,
		AdSlotName:    entity.AdSlotName,
		AdProductID:   entity.AdProductID,
		AdProductName: entity.AdProductName,
		UserID:        entity.UserID,
		UserName:      entity.UserName,
		Date:          entity.Date,
		Cost:          entity.Cost,
		Clicks:        entity.Clicks,
		Remark:        entity.Remark,
		AuditStatus:   entity.AuditStatus,
		AuditorID:     entity.AuditorID,
		AuditorName:   entity.AuditorName,
		AuditTime:     entity.AuditTime,
		AuditRemark:   entity.AuditRemark,
		CreatorID:     entity.CreatorID,
		CreatorName:   entity.CreatorName,
		UpdaterID:     entity.UpdaterID,
		UpdaterName:   entity.UpdaterName,
		CreatedAt:     entity.CreatedAt,
		UpdatedAt:     entity.UpdatedAt,
		CategoryID:    entity.CategoryID,
		CategoryName:  entity.CategoryName,
	}
}

// CostListResp 费用记录列表响应
type CostListResp struct {
	Total    int64      `json:"total"`    // 总数
	Page     int        `json:"page"`     // 当前页码
	PageSize int        `json:"pageSize"` // 每页数量
	List     []CostItem `json:"list"`     // 费用记录列表
}

// FromDomainToCostListResp 将领域对象转换为VO
func FromDomainToCostListResp(result domain.CostListResult) CostListResp {
	items := make([]CostItem, 0, len(result.List))
	for _, entity := range result.List {
		items = append(items, FromDomainToCostItem(entity))
	}

	return CostListResp{
		Total:    result.Total,
		Page:     result.Page,
		PageSize: result.PageSize,
		List:     items,
	}
}

// CostAuditReq 审核费用记录请求
type CostAuditReq struct {
	ID           int64  `json:"id" binding:"required"`                             // 费用记录ID
	AuditStatus  string `json:"status" binding:"required,oneof=approved rejected"` // 审核状态
	RejectReason string `json:"remark"`                                            // 拒绝原因
}

// ToDomain 将VO转换为领域对象
func (r CostAuditReq) ToDomain() domain.CostAuditEntity {
	return domain.CostAuditEntity{
		ID:           r.ID,
		AuditStatus:  r.AuditStatus,
		RejectReason: r.RejectReason,
	}
}
